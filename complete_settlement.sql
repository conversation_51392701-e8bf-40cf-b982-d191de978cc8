-- 完成订单ORD202507311203108006的结算处理
-- 前面的步骤已经完成：
-- 1. ✅ 启用了自动分账开关
-- 2. ✅ 将审核记录标记为已通过
-- 现在需要完成实际的结算分账

-- 连接命令: mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod

-- 1. 验证当前状态
SELECT '=== 当前状态验证 ===' as info;
SELECT 
    o.order_no,
    o.status,
    o.settlement_status,
    o.amount,
    o.attendant_amount,
    ai.status as income_status,
    ai.settlement_batch_id,
    osr.review_status,
    osr.reviewed_at
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id
LEFT JOIN order_settlement_reviews osr ON o.id = osr.order_id
WHERE o.order_no = 'ORD202507311203108006';

-- 2. 创建结算批次
SELECT '=== 创建结算批次 ===' as info;
SET @batch_no = CONCAT('BATCH_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));

INSERT INTO settlement_batches (
    batch_no,
    settlement_date,
    total_orders,
    total_amount,
    platform_amount,
    attendant_amount,
    status,
    processed_at,
    created_at,
    updated_at
) VALUES (
    @batch_no,
    CURDATE(),
    1,      -- 1个订单
    0.01,   -- 总金额
    0.00,   -- 平台金额
    0.01,   -- 陪诊师金额
    1,      -- 已处理状态
    NOW(),
    NOW(),
    NOW()
);

-- 3. 更新陪诊师收入记录
SELECT '=== 更新陪诊师收入记录 ===' as info;
UPDATE attendant_income 
SET 
    settlement_batch_id = @batch_no,
    status = 2,  -- 已结算
    settle_time = NOW(),
    updated_at = NOW()
WHERE order_id = 10017;

-- 4. 更新订单结算状态
SELECT '=== 更新订单结算状态 ===' as info;
UPDATE orders 
SET 
    settlement_status = 2,  -- 已结算（假设2是已结算状态）
    settlement_time = NOW(),
    updated_at = NOW()
WHERE id = 10017;

-- 5. 验证结算完成结果
SELECT '=== 结算完成验证 ===' as info;
SELECT 
    o.order_no,
    o.status,
    o.settlement_status,
    o.settlement_time,
    ai.status as income_status,
    ai.settlement_batch_id,
    ai.settle_time,
    sb.batch_no,
    sb.status as batch_status,
    sb.processed_at
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id
LEFT JOIN settlement_batches sb ON ai.settlement_batch_id = sb.batch_no
WHERE o.order_no = 'ORD202507311203108006';

-- 6. 检查陪诊师ID 13的收入汇总
SELECT '=== 陪诊师收入汇总 ===' as info;
SELECT 
    attendant_id,
    COUNT(*) as total_orders,
    SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as pending_amount,
    SUM(CASE WHEN status = 2 THEN amount ELSE 0 END) as settled_amount,
    SUM(amount) as total_amount
FROM attendant_income 
WHERE attendant_id = 13
GROUP BY attendant_id;
