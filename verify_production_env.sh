#!/bin/bash

# 验证生产环境变量脚本
# 确认微信转账所需的所有环境变量都已正确配置

echo "=== 验证生产环境变量 ==="

ENV_FILE="production.env"

# 检查文件是否存在
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 环境变量文件不存在: $ENV_FILE"
    exit 1
fi

echo "✅ 找到环境变量文件: $ENV_FILE"

# 加载环境变量
set -a
source "$ENV_FILE"
set +a

echo ""
echo "=== 基础配置验证 ==="

# 基础配置检查
basic_vars=(
    "APP_ENV:应用环境"
    "DB_HOST:数据库主机"
    "REDIS_HOST:Redis主机"
    "JWT_SECRET:JWT密钥"
    "API_KEY:API密钥"
)

for var_info in "${basic_vars[@]}"; do
    var_name=$(echo "$var_info" | cut -d':' -f1)
    var_desc=$(echo "$var_info" | cut -d':' -f2)
    
    if [ -n "${!var_name}" ]; then
        case $var_name in
            *SECRET*|*KEY*|*PASSWORD*)
                echo "✅ $var_desc ($var_name): ${!var_name:0:8}****"
                ;;
            *)
                echo "✅ $var_desc ($var_name): ${!var_name}"
                ;;
        esac
    else
        echo "❌ $var_desc ($var_name): 未设置"
    fi
done

echo ""
echo "=== 微信基础配置验证 ==="

# 微信基础配置检查
wechat_basic_vars=(
    "WECHAT_APP_ID:微信小程序AppID"
    "WECHAT_APP_SECRET:微信小程序AppSecret"
    "WECHAT_MCH_ID:微信支付商户号"
    "WECHAT_API_V3_KEY:微信支付APIv3密钥"
    "WECHAT_CERT_SERIAL_NUMBER:商户证书序列号"
    "WECHAT_PRIVATE_KEY_PATH:商户私钥文件路径"
)

for var_info in "${wechat_basic_vars[@]}"; do
    var_name=$(echo "$var_info" | cut -d':' -f1)
    var_desc=$(echo "$var_info" | cut -d':' -f2)
    
    if [ -n "${!var_name}" ]; then
        case $var_name in
            *SECRET*|*KEY*)
                echo "✅ $var_desc ($var_name): ${!var_name:0:8}****"
                ;;
            *)
                echo "✅ $var_desc ($var_name): ${!var_name}"
                ;;
        esac
    else
        echo "❌ $var_desc ($var_name): 未设置"
    fi
done

echo ""
echo "=== 微信转账配置验证 ==="

# 微信转账配置检查
wechat_transfer_vars=(
    "WECHAT_TRANSFER_CLIENT_TYPE:转账客户端类型"
    "WECHAT_TRANSFER_APP_ID:转账AppID"
    "WECHAT_TRANSFER_MCH_ID:转账商户号"
    "WECHAT_TRANSFER_APIV3_KEY:转账APIv3密钥"
    "WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER:转账证书序列号"
    "WECHAT_TRANSFER_PRIVATE_KEY_PATH:转账私钥路径"
    "WECHAT_TRANSFER_ENVIRONMENT:转账环境"
    "WECHAT_TRANSFER_ENABLED:转账功能启用"
    "WECHAT_TRANSFER_MOCK_MODE:转账模拟模式"
)

for var_info in "${wechat_transfer_vars[@]}"; do
    var_name=$(echo "$var_info" | cut -d':' -f1)
    var_desc=$(echo "$var_info" | cut -d':' -f2)
    
    if [ -n "${!var_name}" ]; then
        case $var_name in
            *KEY*)
                echo "✅ $var_desc ($var_name): ${!var_name:0:8}****"
                ;;
            *)
                echo "✅ $var_desc ($var_name): ${!var_name}"
                ;;
        esac
    else
        echo "❌ $var_desc ($var_name): 未设置"
    fi
done

echo ""
echo "=== 证书文件验证 ==="

# 证书文件检查
cert_files=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem:商户私钥文件"
    "/etc/peizhen/certs/wechat/apiclient_cert.pem:商户证书文件"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem:微信公钥文件"
)

for file_info in "${cert_files[@]}"; do
    file_path=$(echo "$file_info" | cut -d':' -f1)
    file_desc=$(echo "$file_info" | cut -d':' -f2)
    
    if [ -f "$file_path" ]; then
        file_perms=$(stat -c "%a" "$file_path" 2>/dev/null || stat -f "%A" "$file_path" 2>/dev/null)
        file_size=$(stat -c "%s" "$file_path" 2>/dev/null || stat -f "%z" "$file_path" 2>/dev/null)
        echo "✅ $file_desc: 存在 (权限: $file_perms, 大小: ${file_size}字节)"
        
        # 检查私钥文件格式
        if [[ "$file_path" == *"key.pem" ]]; then
            if head -1 "$file_path" | grep -q "BEGIN.*PRIVATE KEY"; then
                echo "   ✅ 私钥文件格式正确"
            else
                echo "   ⚠️  私钥文件格式可能不正确"
            fi
        fi
    else
        echo "❌ $file_desc: 不存在 ($file_path)"
    fi
done

echo ""
echo "=== 内部API配置验证 ==="

# 内部API配置检查
internal_api_vars=(
    "INTERNAL_API_KEY:内部API密钥"
    "BACKEND_API_KEY_ID:后端API密钥ID"
    "BACKEND_API_SECRET_KEY:后端API密钥"
)

for var_info in "${internal_api_vars[@]}"; do
    var_name=$(echo "$var_info" | cut -d':' -f1)
    var_desc=$(echo "$var_info" | cut -d':' -f2)
    
    if [ -n "${!var_name}" ]; then
        echo "✅ $var_desc ($var_name): ${!var_name:0:8}****"
    else
        echo "❌ $var_desc ($var_name): 未设置"
    fi
done

echo ""
echo "=== 关键配置验证 ==="

# 关键配置验证
echo "检查关键配置项..."

# 检查自动证书下载模式
if [ "$APP_WECHAT_USE_AUTO_CERT_MODE" = "true" ]; then
    echo "✅ 自动证书下载模式: 已启用"
else
    echo "⚠️  自动证书下载模式: 未启用 (当前值: $APP_WECHAT_USE_AUTO_CERT_MODE)"
fi

# 检查转账功能
if [ "$WECHAT_TRANSFER_ENABLED" = "true" ]; then
    echo "✅ 转账功能: 已启用"
else
    echo "❌ 转账功能: 未启用"
fi

# 检查模拟模式
if [ "$WECHAT_TRANSFER_MOCK_MODE" = "false" ]; then
    echo "✅ 转账模拟模式: 已禁用 (生产环境正确)"
else
    echo "⚠️  转账模拟模式: 已启用 (生产环境应禁用)"
fi

# 检查证书序列号一致性
if [ "$WECHAT_CERT_SERIAL_NUMBER" = "$WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER" ]; then
    echo "✅ 证书序列号: 配置一致"
else
    echo "⚠️  证书序列号: 配置不一致"
    echo "   基础配置: $WECHAT_CERT_SERIAL_NUMBER"
    echo "   转账配置: $WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER"
fi

echo ""
echo "=== 验证总结 ==="

# 统计结果
total_checks=0
passed_checks=0

# 这里可以添加更详细的统计逻辑
echo "📋 配置验证完成"
echo ""
echo "🔧 如果发现问题，请："
echo "1. 检查并修正缺失的环境变量"
echo "2. 验证证书文件存在且权限正确"
echo "3. 确认微信商户平台配置"
echo "4. 重启服务以应用配置"
echo ""
echo "🔄 重启命令:"
echo "   sudo systemctl restart peizhen-backend"
echo ""
echo "📝 查看日志:"
echo "   tail -f backend/logs/app.prod.log | grep -E '(证书|transfer|微信|ERROR)'"