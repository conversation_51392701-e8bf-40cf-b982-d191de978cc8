# 自动分账配置表 deleted_at 字段缺失问题修复

## 问题描述

在2025年8月9日，系统日志中出现了以下错误：

```
Error 1054 (42S22): Unknown column 'deleted_at' in 'where clause'
[13.242ms] [rows:0] SELECT * FROM `auto_settlement_configs` WHERE (config_key = 'auto_settlement_enabled' AND deleted_at IS NULL) AND `auto_settlement_configs`.`deleted_at` IS NULL ORDER BY `auto_settlement_configs`.`id` LIMIT 1
ERROR impl/auto_settlement_service_impl.go:74 获取自动分账配置失败  {"key": "auto_settlement_enabled", "error": "Error 1054 (42S22): Unknown column 'deleted_at' in 'where clause'"}
```

## 问题分析

1. **模型定义**：`AutoSettlementConfig`模型继承了`BaseModel`，其中包含了`DeletedAt`字段用于软删除功能
2. **表结构不一致**：在数据库迁移文件`20250129000001_create_t2_settlement_tables.sql`中创建的`auto_settlement_configs`表没有包含`deleted_at`字段
3. **查询冲突**：GORM自动生成的查询语句包含了`deleted_at IS NULL`条件，但数据库表中不存在该字段

## 解决方案

### 1. 创建迁移文件

创建了以下两个迁移文件：
- `20250809000001_add_deleted_at_to_auto_settlement_configs.sql` - 添加`deleted_at`字段
- `20250809000001_add_deleted_at_to_auto_settlement_configs_rollback.sql` - 回滚迁移

### 2. 迁移内容

```sql
-- 添加 deleted_at 字段
ALTER TABLE `auto_settlement_configs` 
ADD COLUMN `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间';

-- 添加索引以提高软删除查询性能
ALTER TABLE `auto_settlement_configs` 
ADD KEY `idx_auto_settlement_configs_deleted_at` (`deleted_at`);
```

## 验证结果

执行迁移后，以下查询均能正常工作：
- `SELECT * FROM auto_settlement_configs WHERE config_key = 'auto_settlement_enabled' AND deleted_at IS NULL`
- `SELECT * FROM auto_settlement_configs WHERE deleted_at IS NULL ORDER BY created_at DESC`
- `SELECT * FROM auto_settlement_configs WHERE config_key = ? AND is_enabled = ? AND deleted_at IS NULL`

## 后续步骤

1. 将迁移文件提交到版本控制系统
2. 在生产环境中执行相同的迁移
3. 监控系统日志确保问题已解决

## 根本原因

该问题的根本原因是数据库表结构与GORM模型定义不一致。`AutoSettlementConfig`模型继承了包含软删除功能的`BaseModel`，但对应的数据库表没有创建相应的`deleted_at`字段。

## 预防措施

1. 在创建新表时，确保模型定义与表结构保持一致
2. 建立模型与表结构的自动同步机制
3. 定期检查模型与表结构的一致性
