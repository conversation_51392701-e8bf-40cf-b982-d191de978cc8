# 微信支付模式修复部署检查清单

## 修复内容
- [x] 代码支持公钥模式和自动证书模式切换
- [x] 根据环境变量 `APP_WECHAT_USE_PUBLIC_KEY_MODE` 选择模式
- [x] 修复了公钥加载逻辑
- [x] 修复了代码语法问题

## 部署前检查
- [ ] 确认环境变量 `APP_WECHAT_USE_PUBLIC_KEY_MODE=true`
- [ ] 确认公钥文件存在且格式正确
- [ ] 确认公钥文件权限正确（建议 600 或 644）
- [ ] 确认代码编译通过

## 部署步骤
1. 停止当前服务
2. 更新代码文件
3. 更新环境变量配置
4. 启动服务
5. 验证转账功能

## 验证步骤
1. 检查服务启动日志，确认无错误
2. 尝试发起一笔小额转账测试
3. 检查转账日志，确认使用了正确的认证模式
4. 监控后续转账是否正常

## 回滚方案
如果出现问题，可以：
1. 恢复备份的代码文件
2. 恢复备份的环境变量配置
3. 重启服务
4. 联系技术支持

## 技术说明
- 公钥模式：使用 `WithWechatPayPublicKeyAuthCipher`，不需要下载平台证书权限
- 自动证书模式：使用 `WithWechatPayAutoAuthCipher`，需要下载平台证书权限
- 当前配置优先使用公钥模式，解决"无可用的平台证书"错误

## 联系信息
如有问题，请联系技术团队。
