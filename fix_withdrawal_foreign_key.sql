-- 修复提现转账表的外键约束问题
-- 问题：外键约束仍然指向旧表 withdrawals_old，需要指向新表 withdrawals

-- 1. 检查当前外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'carego_prod' 
AND TABLE_NAME = 'withdrawal_transfers' 
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 2. 删除旧的外键约束
ALTER TABLE withdrawal_transfers 
DROP FOREIGN KEY fk_withdrawal_transfers_withdrawal_id;

-- 3. 创建新的外键约束，指向正确的表
ALTER TABLE withdrawal_transfers 
ADD CONSTRAINT fk_withdrawal_transfers_withdrawal_id 
FOREIGN KEY (withdrawal_id) REFERENCES withdrawals(id) ON DELETE CASCADE;

-- 4. 验证修复结果
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'carego_prod' 
AND TABLE_NAME = 'withdrawal_transfers' 
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 5. 检查是否还有其他表引用 withdrawals_old
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'carego_prod' 
AND REFERENCED_TABLE_NAME = 'withdrawals_old';