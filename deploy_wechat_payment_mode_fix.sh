#!/bin/bash

# 部署微信支付模式修复
# 确保公钥模式正确配置和部署

echo "🚀 部署微信支付模式修复"
echo "=================================="

# 1. 检查当前环境
echo -e "\n1. 检查当前环境："
echo "   当前目录: $(pwd)"
echo "   用户: $(whoami)"
echo "   时间: $(date)"

# 2. 备份原始文件
echo -e "\n2. 备份原始文件："
backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"

if [ -f "backend/pkg/wechat/official_transfer_client_v2.go" ]; then
    cp "backend/pkg/wechat/official_transfer_client_v2.go" "$backup_dir/"
    echo "   ✅ 已备份 official_transfer_client_v2.go"
fi

if [ -f "production.env" ]; then
    cp "production.env" "$backup_dir/"
    echo "   ✅ 已备份 production.env"
fi

echo "   备份目录: $backup_dir"

# 3. 验证代码修复
echo -e "\n3. 验证代码修复："
if grep -q "APP_WECHAT_USE_PUBLIC_KEY_MODE" backend/pkg/wechat/official_transfer_client_v2.go; then
    echo "   ✅ 代码已支持公钥模式切换"
    
    # 检查具体的修复内容
    if grep -q "WithWechatPayPublicKeyAuthCipher" backend/pkg/wechat/official_transfer_client_v2.go; then
        echo "   ✅ 公钥认证模式已配置"
    fi
    
    if grep -q "WithWechatPayAutoAuthCipher" backend/pkg/wechat/official_transfer_client_v2.go; then
        echo "   ✅ 自动证书模式已配置"
    fi
else
    echo "   ❌ 代码修复不完整，请检查"
    exit 1
fi

# 4. 检查环境变量配置
echo -e "\n4. 检查环境变量配置："
if [ -f "production.env" ]; then
    public_key_mode=$(grep "APP_WECHAT_USE_PUBLIC_KEY_MODE" production.env | cut -d'=' -f2)
    public_key_path=$(grep "WECHAT_PUBLIC_KEY_PATH" production.env | cut -d'=' -f2)
    public_key_id=$(grep "WECHAT_PUBLIC_KEY_ID" production.env | cut -d'=' -f2)
    
    echo "   公钥模式: $public_key_mode"
    echo "   公钥路径: $public_key_path"
    echo "   公钥ID: $public_key_id"
    
    if [ "$public_key_mode" = "true" ]; then
        echo "   ✅ 公钥模式已启用"
        
        # 检查公钥文件
        if [ -n "$public_key_path" ] && [ -f "$public_key_path" ]; then
            echo "   ✅ 公钥文件存在"
            
            # 检查文件权限
            file_perm=$(stat -c "%a" "$public_key_path" 2>/dev/null || stat -f "%A" "$public_key_path" 2>/dev/null)
            echo "   文件权限: $file_perm"
            
            # 检查文件内容格式
            if head -1 "$public_key_path" | grep -q "BEGIN"; then
                echo "   ✅ 公钥文件格式正确"
            else
                echo "   ⚠️  公钥文件格式可能有问题"
            fi
        else
            echo "   ❌ 公钥文件不存在或路径未配置"
            echo "   请确保公钥文件存在: $public_key_path"
        fi
    else
        echo "   ⚠️  公钥模式未启用，将使用自动证书模式"
    fi
else
    echo "   ❌ production.env 文件不存在"
    exit 1
fi

# 5. 编译检查
echo -e "\n5. 编译检查："
echo "   开始编译后端服务..."

cd backend
if go build -o /tmp/peizhen_test main.go; then
    echo "   ✅ 编译成功"
    rm -f /tmp/peizhen_test
else
    echo "   ❌ 编译失败，请检查代码"
    cd ..
    exit 1
fi
cd ..

# 6. 创建部署检查清单
echo -e "\n6. 创建部署检查清单："
cat > WECHAT_PAYMENT_MODE_DEPLOYMENT_CHECKLIST.md << 'EOF'
# 微信支付模式修复部署检查清单

## 修复内容
- [x] 代码支持公钥模式和自动证书模式切换
- [x] 根据环境变量 `APP_WECHAT_USE_PUBLIC_KEY_MODE` 选择模式
- [x] 修复了公钥加载逻辑
- [x] 修复了代码语法问题

## 部署前检查
- [ ] 确认环境变量 `APP_WECHAT_USE_PUBLIC_KEY_MODE=true`
- [ ] 确认公钥文件存在且格式正确
- [ ] 确认公钥文件权限正确（建议 600 或 644）
- [ ] 确认代码编译通过

## 部署步骤
1. 停止当前服务
2. 更新代码文件
3. 更新环境变量配置
4. 启动服务
5. 验证转账功能

## 验证步骤
1. 检查服务启动日志，确认无错误
2. 尝试发起一笔小额转账测试
3. 检查转账日志，确认使用了正确的认证模式
4. 监控后续转账是否正常

## 回滚方案
如果出现问题，可以：
1. 恢复备份的代码文件
2. 恢复备份的环境变量配置
3. 重启服务
4. 联系技术支持

## 技术说明
- 公钥模式：使用 `WithWechatPayPublicKeyAuthCipher`，不需要下载平台证书权限
- 自动证书模式：使用 `WithWechatPayAutoAuthCipher`，需要下载平台证书权限
- 当前配置优先使用公钥模式，解决"无可用的平台证书"错误

## 联系信息
如有问题，请联系技术团队。
EOF

echo "   ✅ 已创建部署检查清单: WECHAT_PAYMENT_MODE_DEPLOYMENT_CHECKLIST.md"

# 7. 总结
echo -e "\n🎉 部署准备完成！"
echo "=================================="
echo "✅ 完成的工作："
echo "1. 备份了原始文件到 $backup_dir"
echo "2. 验证了代码修复完整性"
echo "3. 检查了环境变量配置"
echo "4. 验证了代码编译通过"
echo "5. 创建了部署检查清单"
echo ""
echo "📋 下一步操作："
echo "1. 查看部署检查清单: WECHAT_PAYMENT_MODE_DEPLOYMENT_CHECKLIST.md"
echo "2. 按照清单执行部署步骤"
echo "3. 验证转账功能是否正常"
echo ""
echo "🔧 关键配置："
echo "- APP_WECHAT_USE_PUBLIC_KEY_MODE=true (使用公钥模式)"
echo "- 确保公钥文件存在且格式正确"
echo "- 重启服务使修改生效"

echo -e "\n✨ 准备就绪，可以开始部署！"