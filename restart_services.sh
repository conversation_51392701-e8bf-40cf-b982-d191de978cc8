#!/bin/bash

# 重启服务脚本
# 修复配置后重启后端和管理后台服务

echo "🚀 重启服务"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 检查当前服务状态
echo -e "${BLUE}步骤1: 检查当前服务状态${NC}"

# 检查 systemd 服务
if systemctl is-active --quiet peizhen-gin-app.service; then
    echo -e "${YELLOW}检测到 systemd 服务正在运行${NC}"
    USE_SYSTEMD=true
else
    echo -e "${YELLOW}systemd 服务未运行，检查手动启动的进程${NC}"
    USE_SYSTEMD=false
fi

# 检查手动启动的进程
if pgrep -f "peizhen" > /dev/null; then
    echo -e "${YELLOW}检测到手动启动的后端进程${NC}"
    MANUAL_BACKEND=true
else
    MANUAL_BACKEND=false
fi

if pgrep -f "admin.*server" > /dev/null; then
    echo -e "${YELLOW}检测到手动启动的管理后台进程${NC}"
    MANUAL_ADMIN=true
else
    MANUAL_ADMIN=false
fi

# 2. 重启后端服务
echo ""
echo -e "${BLUE}步骤2: 重启后端服务${NC}"

if [ "$USE_SYSTEMD" = true ]; then
    echo "使用 systemd 重启后端服务..."
    sudo systemctl restart peizhen-gin-app.service
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet peizhen-gin-app.service; then
        echo -e "${GREEN}✅ systemd 服务重启成功${NC}"
    else
        echo -e "${RED}❌ systemd 服务重启失败${NC}"
        echo "查看错误日志:"
        sudo journalctl -u peizhen-gin-app.service --no-pager -n 10
        exit 1
    fi
else
    if [ "$MANUAL_BACKEND" = true ]; then
        echo "停止手动启动的后端进程..."
        pkill -f "peizhen"
        sleep 2
    fi
    
    echo "手动启动后端服务..."
    echo "请在新的终端窗口中执行:"
    echo "  source production.env"
    echo "  cd backend && ./bin/peizhen"
fi

# 3. 重启管理后台服务（如果需要）
if [ "$MANUAL_ADMIN" = true ]; then
    echo ""
    echo -e "${BLUE}步骤3: 重启管理后台服务${NC}"
    
    echo "停止手动启动的管理后台进程..."
    pkill -f "admin.*server"
    sleep 2
    
    echo "请在新的终端窗口中执行:"
    echo "  source admin.production.env"
    echo "  cd admin/server && APP_ENV=prod go run main.go"
fi

# 4. 验证服务状态
echo ""
echo -e "${BLUE}步骤4: 验证服务状态${NC}"

sleep 5  # 等待服务完全启动

# 检查端口
echo "检查端口占用:"
if netstat -an 2>/dev/null | grep -q ":8080.*LISTEN" || ss -an 2>/dev/null | grep -q ":8080.*LISTEN"; then
    echo -e "${GREEN}✅ 后端端口 8080 正在监听${NC}"
else
    echo -e "${RED}❌ 后端端口 8080 未监听${NC}"
fi

if netstat -an 2>/dev/null | grep -q ":8081.*LISTEN" || ss -an 2>/dev/null | grep -q ":8081.*LISTEN"; then
    echo -e "${GREEN}✅ 管理后台端口 8081 正在监听${NC}"
else
    echo -e "${YELLOW}⚠️ 管理后台端口 8081 未监听（可能需要手动启动）${NC}"
fi

# 5. 测试API连接
echo ""
echo -e "${BLUE}步骤5: 测试API连接${NC}"

# 测试后端健康检查
if curl -s -f http://localhost:8080/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端服务健康检查通过${NC}"
else
    echo -e "${YELLOW}⚠️ 后端服务健康检查失败（可能服务还在启动中）${NC}"
fi

# 6. 显示日志查看命令
echo ""
echo -e "${BLUE}步骤6: 日志查看命令${NC}"

if [ "$USE_SYSTEMD" = true ]; then
    echo "查看后端服务日志:"
    echo "  sudo journalctl -u peizhen-gin-app.service -f"
    echo ""
    echo "查看最近的错误:"
    echo "  sudo journalctl -u peizhen-gin-app.service --no-pager -n 20"
fi

echo ""
echo -e "${GREEN}🎉 服务重启完成！${NC}"
echo ""
echo -e "${YELLOW}接下来的操作:${NC}"
echo "1. 检查服务日志确认启动成功"
echo "2. 在管理后台测试转账功能"
echo "3. 观察是否还有 'ttl' 类型错误"
echo ""
echo -e "${BLUE}如果仍有问题，请检查:${NC}"
echo "- 环境变量是否正确加载"
echo "- 配置文件语法是否正确"
echo "- 数据库连接是否正常"