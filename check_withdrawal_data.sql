-- 检查线上提现申请数据的详细情况
-- 用于确定 "收款用户姓名不能为空" 错误的真实原因

-- 1. 检查提现申请 ID 1 的详细信息
SELECT 
    id,
    withdrawal_no,
    user_id,
    amount,
    actual_amount,
    status,
    real_name,
    openid,
    created_at,
    updated_at
FROM withdrawals 
WHERE id = 1;

-- 2. 检查所有提现申请的 real_name 字段情况
SELECT 
    COUNT(*) as total_count,
    COUNT(real_name) as real_name_not_null_count,
    COUNT(CASE WHEN real_name = '' THEN 1 END) as real_name_empty_count,
    COUNT(CASE WHEN real_name IS NULL THEN 1 END) as real_name_null_count
FROM withdrawals;

-- 3. 查看 real_name 为空或 NULL 的记录
SELECT 
    id,
    withdrawal_no,
    user_id,
    real_name,
    openid,
    status
FROM withdrawals 
WHERE real_name IS NULL OR real_name = ''
ORDER BY id;

-- 4. 检查提现申请与陪诊师的关联情况
SELECT 
    w.id as withdrawal_id,
    w.withdrawal_no,
    w.user_id,
    w.real_name as withdrawal_real_name,
    w.openid as withdrawal_openid,
    a.id as attendant_id,
    a.name as attendant_name,
    a.phone as attendant_phone
FROM withdrawals w
LEFT JOIN attendants a ON w.user_id = a.user_id
WHERE w.id = 1;

-- 5. 检查最近的提现申请记录
SELECT 
    id,
    withdrawal_no,
    user_id,
    amount,
    status,
    real_name,
    openid,
    created_at
FROM withdrawals 
ORDER BY created_at DESC 
LIMIT 10;

-- 6. 检查陪诊师表中的姓名情况
SELECT 
    COUNT(*) as total_attendants,
    COUNT(name) as name_not_null_count,
    COUNT(CASE WHEN name = '' THEN 1 END) as name_empty_count,
    COUNT(CASE WHEN name IS NULL THEN 1 END) as name_null_count
FROM attendants;

-- 7. 检查用户表中的相关信息（如果有的话）
SELECT 
    u.id as user_id,
    u.openid as user_openid,
    w.id as withdrawal_id,
    w.real_name as withdrawal_real_name,
    w.openid as withdrawal_openid,
    a.name as attendant_name
FROM users u
LEFT JOIN withdrawals w ON u.id = w.user_id
LEFT JOIN attendants a ON u.id = a.user_id
WHERE w.id = 1;

-- 8. 检查转账记录表（如果存在）
SELECT 
    id,
    withdrawal_id,
    transfer_no,
    amount,
    openid,
    real_name,
    status,
    fail_reason,
    created_at
FROM withdrawal_transfers 
WHERE withdrawal_id = 1
ORDER BY created_at DESC;

-- 9. 检查数据表结构
DESCRIBE withdrawals;
DESCRIBE attendants;
DESCRIBE withdrawal_transfers;