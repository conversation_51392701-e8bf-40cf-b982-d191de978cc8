#!/bin/bash

# 修复转账场景ID为1005（佣金报酬）
# 根据微信支付商户平台的要求，陪诊师提现应使用佣金报酬场景

echo "🔧 修复转账场景ID为1005（佣金报酬）"
echo "=================================="

# 检查是否在项目根目录
if [ ! -f "go.work" ] && [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

echo "📋 转账场景ID说明:"
echo "   1000 - 普通转账"
echo "   1001 - 营销活动"
echo "   1005 - 佣金报酬 ✅ (陪诊师提现应使用此场景)"

echo ""
echo "🔍 检查当前代码中的场景ID设置..."

# 检查主要的转账客户端文件
if [ -f "backend/pkg/wechat/official_transfer_client_v2.go" ]; then
    current_scene_id=$(grep -n "TransferSceneId.*core.String" backend/pkg/wechat/official_transfer_client_v2.go | head -1)
    if [[ $current_scene_id == *"1005"* ]]; then
        echo "✅ 主转账客户端已使用场景ID 1005"
    else
        echo "⚠️  主转账客户端场景ID需要更新"
        echo "   当前: $current_scene_id"
    fi
else
    echo "❌ 未找到主转账客户端文件"
fi

# 检查其他相关文件
files_to_check=(
    "wechat_transfer_api_enhanced.go"
    "check_wechat_merchant_permissions.go"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        if grep -q "1005" "$file"; then
            echo "✅ $file 已使用场景ID 1005"
        else
            echo "⚠️  $file 可能需要更新场景ID"
        fi
    fi
done

echo ""
echo "🧪 测试场景ID 1005 的转账权限..."

# 如果环境变量完整，可以测试
if [ -n "$WECHAT_MCH_ID" ] && [ -n "$WECHAT_APP_ID" ] && [ -n "$WECHAT_API_V3_KEY" ]; then
    echo "✅ 环境变量完整，可以进行测试"
    
    # 运行增强API测试（现在会优先测试1005场景）
    if [ -f "run_enhanced_api_test.sh" ]; then
        echo "🚀 运行增强API测试（优先测试场景ID 1005）..."
        chmod +x run_enhanced_api_test.sh
        if ./run_enhanced_api_test.sh; then
            echo "✅ 场景ID 1005 测试完成"
        else
            echo "❌ 场景ID 1005 测试发现问题"
        fi
    else
        echo "⚠️  增强API测试脚本不存在"
    fi
else
    echo "⚠️  环境变量不完整，跳过API测试"
    echo "   需要设置: WECHAT_MCH_ID, WECHAT_APP_ID, WECHAT_API_V3_KEY"
fi

echo ""
echo "📋 场景ID 1005 的优势:"
echo "   ✅ 符合陪诊师提现的业务场景"
echo "   ✅ 明确标识为佣金报酬类型"
echo "   ✅ 可能有更高的转账限额"
echo "   ✅ 更符合微信支付的业务分类"

echo ""
echo "💡 如果仍然出现 NO_AUTH 错误:"
echo "   1. 场景ID 1005 需要在商户平台单独开通"
echo "   2. 登录微信支付商户平台检查佣金报酬功能"
echo "   3. 确认该场景的API权限已配置"
echo "   4. 可能需要提供相关的业务资质"

echo ""
echo "📞 商户平台检查路径:"
echo "   网址: https://pay.weixin.qq.com/"
echo "   路径: 产品中心 → 商家转账 → 转账场景管理"
echo "   确认: 佣金报酬场景(1005)是否已开通"

echo ""
echo "🎯 场景ID修复完成！"
echo "   主要修改: 转账场景ID从1000改为1005"
echo "   业务匹配: 佣金报酬更符合陪诊师提现场景"
echo "   下一步: 在商户平台确认场景1005的权限配置"