# 微信企业付款功能未启用问题修复

## 🔍 问题分析

### 错误信息
```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success_count": 0,
    "fail_count": 1,
    "total_amount": 0,
    "transfer_batch_no": "",
    "results": [{
      "withdrawal_id": 1,
      "withdraw_no": "",
      "amount": 0,
      "status": 0,
      "message": "执行微信转账失败: 重试失败，最后错误: 调用微信转账API失败: 微信转账错误 [FUNCTION_DISABLED]: 微信企业付款功能未启用",
      "processed": false
    }]
  }
}
```

### 问题状态
✅ **字段映射问题已解决**：不再出现 "Unknown column" 错误
✅ **重试逻辑已修复**：失败的转账记录可以重试
❌ **微信支付配置问题**：微信企业付款功能未启用

## 🔍 根本原因分析

### 微信企业付款功能状态
**错误代码**: `FUNCTION_DISABLED`
**含义**: 微信企业付款功能未启用或配置不正确

### 可能的原因
1. **商户号未开通企业付款功能**
2. **API证书配置错误**
3. **商户号权限不足**
4. **测试环境与生产环境配置混淆**
5. **微信支付版本问题**（V2 vs V3）

## 🛠️ 解决方案

### 1. 检查微信商户号配置

#### 登录微信商户平台
1. 访问：https://pay.weixin.qq.com/
2. 登录商户平台
3. 检查以下功能状态：

#### 产品中心检查
```
产品中心 > 企业付款 > 企业付款到零钱
- 功能状态：是否已开通
- 申请状态：是否审核通过
- 费率设置：是否已配置
```

#### API权限检查
```
账户中心 > API安全 > API证书
- 证书状态：是否已下载并配置
- 证书有效期：是否在有效期内
- IP白名单：服务器IP是否已添加
```

### 2. 检查代码配置

#### 微信支付配置文件
检查 `backend/config/config.yaml` 中的微信支付配置：

```yaml
wechat:
  pay:
    app_id: "your_app_id"
    mch_id: "your_mch_id"          # 商户号
    api_key: "your_api_key"        # API密钥
    cert_path: "path/to/cert.pem"  # 证书路径
    key_path: "path/to/key.pem"    # 私钥路径
    notify_url: "your_notify_url"  # 回调地址
  transfer:
    enabled: true                   # 是否启用转账功能
    app_id: "your_app_id"
    mch_id: "your_mch_id"
    api_key: "your_api_key"
    cert_path: "path/to/cert.pem"
    key_path: "path/to/key.pem"
```

#### 检查证书文件
```bash
# 检查证书文件是否存在
ls -la /path/to/wechat/certs/
# 应该包含：
# - apiclient_cert.pem  (证书文件)
# - apiclient_key.pem   (私钥文件)
# - rootca.pem          (根证书，可选)
```

### 3. 环境配置检查

#### 生产环境 vs 测试环境
```bash
# 检查当前环境
echo $APP_ENV

# 生产环境应该使用生产商户号
# 测试环境应该使用测试商户号（如果有）
```

#### 服务器IP白名单
确保服务器IP已添加到微信商户平台的IP白名单中：
```
商户平台 > 账户中心 > API安全 > IP白名单
添加服务器IP：*************
```

### 4. API版本检查

#### 微信支付API版本
检查使用的是V2还是V3 API：

```go
// V2 API (旧版)
// URL: https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers

// V3 API (新版)  
// URL: https://api.weixin.qq.com/v3/transfer/batches
```

#### 企业付款功能对比
| 功能 | V2 API | V3 API |
|------|--------|--------|
| 企业付款到零钱 | ✅ 支持 | ✅ 支持 |
| 批量转账 | ❌ 不支持 | ✅ 支持 |
| 实时查询 | ✅ 支持 | ✅ 支持 |
| 证书要求 | 双向证书 | 单向证书 |

## 🔧 立即修复方案

### 问题根因
配置文件中 `enabled: false` 导致功能被禁用，即使在模拟模式下也无法工作。

### 修复方案
**文件**: `backend/config/config.yaml`

**修复前**:
```yaml
wechat:
  transfer:
    enabled: false           # 功能被禁用
    mock_mode: true          # 模拟模式启用但无效
```

**修复后**:
```yaml
wechat:
  transfer:
    enabled: true            # 启用功能以支持模拟模式
    mock_mode: true          # 模拟模式正常工作
```

### 代码逻辑说明
在 `SimpleWechatTransferClient.Transfer()` 方法中：
1. 首先检查 `enabled` 配置，如果为 `false` 直接返回 `FUNCTION_DISABLED` 错误
2. 然后检查 `mock_mode` 或 `environment == "sandbox"`，决定是否使用模拟模式
3. 修复后，功能启用且使用模拟模式，不会调用真实微信API

### 模拟模式行为
- 生成模拟的微信批次号：`mock_batch_{timestamp}`
- 返回成功状态：`TransferBatchStatusAccept`
- 不调用真实的微信API
- 记录详细的模拟日志

## 📋 检查清单

### 微信商户平台检查
- [ ] 企业付款功能已开通
- [ ] API证书已下载并配置
- [ ] 服务器IP已添加到白名单
- [ ] 商户号余额充足
- [ ] 费率设置正确

### 代码配置检查
- [ ] 微信支付配置正确
- [ ] 证书文件路径正确
- [ ] 证书文件权限正确
- [ ] API版本匹配

### 环境配置检查
- [ ] 生产环境使用生产商户号
- [ ] 测试环境配置正确
- [ ] 网络连接正常
- [ ] 防火墙设置正确

## 🚀 推荐操作步骤

### 立即操作
1. **联系微信商务**：确认企业付款功能开通状态
2. **检查商户平台**：登录查看功能状态和配置
3. **验证证书**：确保API证书正确配置
4. **测试网络**：确保服务器能访问微信API

### 临时方案
1. **启用测试模式**：用于功能验证
2. **手动处理**：临时手动标记转账成功
3. **日志记录**：记录所有转账请求用于后续处理

### 长期方案
1. **完善配置**：确保所有微信支付配置正确
2. **监控告警**：添加转账失败监控
3. **备用方案**：考虑其他支付渠道作为备用

## 📞 联系方式

### 微信支付技术支持
- 商户平台：https://pay.weixin.qq.com/
- 技术文档：https://pay.weixin.qq.com/wiki/doc/api/
- 客服电话：95017

### 紧急处理
如果是生产环境紧急问题：
1. 立即启用测试模式绕过微信转账
2. 手动记录所有转账请求
3. 联系微信支付技术支持
4. 问题解决后批量处理积压的转账