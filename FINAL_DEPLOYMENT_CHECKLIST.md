# 微信转账平台证书问题修复 - 最终部署清单

## 问题回顾
**原始错误**: `无可用的平台证书，请在商户平台-API安全申请使用微信支付公钥`
**错误代码**: `RESOURCE_NOT_EXISTS (404)`

## 修复内容总结

### ✅ 已完成的修复

#### 1. 代码层面修复
- **文件**: `backend/pkg/wechat/official_transfer_client_v2.go`
- **修复**: 优化微信支付客户端初始化，添加详细日志
- **状态**: ✅ 已完成

#### 2. 配置文件修复
- **文件**: `backend/config/conf/config.prod.yaml`
- **修复**: 启用 `use_auto_cert_mode: true`
- **状态**: ✅ 已完成

#### 3. 环境变量配置
- **文件**: `production.env`
- **修复**: 补充所有必要的环境变量
- **状态**: ✅ 已完成

#### 4. 环境变量绑定
- **文件**: `backend/config/loader.go`
- **修复**: 添加缺失的环境变量绑定
- **状态**: ✅ 已完成

## 生产环境部署步骤

### 第一步：上传修复后的文件 ✅

需要上传到生产服务器的文件：
```bash
# 主要修复文件
backend/pkg/wechat/official_transfer_client_v2.go
backend/config/conf/config.prod.yaml
backend/config/loader.go
production.env

# 验证工具（可选）
verify_production_env.sh
```

### 第二步：验证证书文件 ✅

在生产服务器上确认证书文件存在：
```bash
# 你已确认这些文件存在
ls -la /etc/peizhen/certs/wechat/
# apiclient_cert.pem  ✅
# apiclient_key.pem   ✅  
# wechat_public_key.pem ✅
```

### 第三步：应用环境变量 🔄

在生产服务器上应用新的环境变量：

```bash
# 1. 备份现有环境变量文件
cp production.env production.env.backup.$(date +%Y%m%d_%H%M%S)

# 2. 应用新的环境变量文件
# (上传新的 production.env 文件)

# 3. 验证关键环境变量
source production.env
echo "WECHAT_CERT_SERIAL_NUMBER: $WECHAT_CERT_SERIAL_NUMBER"
echo "APP_WECHAT_USE_AUTO_CERT_MODE: $APP_WECHAT_USE_AUTO_CERT_MODE"
echo "WECHAT_TRANSFER_ENABLED: $WECHAT_TRANSFER_ENABLED"
```

### 第四步：重启服务 🔄

```bash
# 重启后端服务
sudo systemctl restart peizhen-backend

# 检查服务状态
sudo systemctl status peizhen-backend

# 查看启动日志
tail -f backend/logs/app.prod.log | grep -E "(证书|transfer|微信|启动)"
```

### 第五步：功能测试 🔄

```bash
# 测试转账接口
curl -X POST https://admin.kanghuxing.cn/api/admin/withdrawals/pay \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_admin_token" \
  -d '{"withdrawal_ids": [1]}'
```

## 关键环境变量清单

### ✅ 已在 production.env 中配置的变量

```bash
# 微信基础配置
WECHAT_APP_ID=wx2ce88b500238c799
WECHAT_MCH_ID=1717184423
WECHAT_API_V3_KEY=0C9821B2517645438B93B1B21CC62901
WECHAT_CERT_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247

# 证书配置（关键修复）
APP_WECHAT_USE_AUTO_CERT_MODE=true  # 🔧 修复平台证书问题
WECHAT_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem

# 转账配置
WECHAT_TRANSFER_CLIENT_TYPE=official
WECHAT_TRANSFER_ENABLED=true
WECHAT_TRANSFER_MOCK_MODE=false
WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247
WECHAT_TRANSFER_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem

# 内部API配置
INTERNAL_API_KEY=s137xrkVgGxJTX6gitt1DZ29j9tEZRF5iCOsPKIE
BACKEND_API_KEY_ID=admin-key-prod-001
BACKEND_API_SECRET_KEY=peizhen-backend-api-secret-key-2024-prod-32chars
```

## 验证清单

### ✅ 代码修复验证
- [x] 微信转账客户端初始化逻辑已优化
- [x] 自动证书下载模式已启用
- [x] 详细错误日志已添加

### ✅ 配置验证
- [x] 生产配置文件已更新
- [x] 环境变量绑定已完善
- [x] 所有必要环境变量已配置

### 🔄 部署验证（需在生产环境完成）
- [ ] 文件已上传到生产服务器
- [ ] 环境变量已应用
- [ ] 服务已重启
- [ ] 转账功能测试通过

## 预期结果

修复完成后，转账接口应该返回成功响应：

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success_count": 1,
    "fail_count": 0,
    "total_amount": 1,
    "transfer_batch_no": "BATCH_xxx",
    "results": [{
      "withdrawal_id": 1,
      "withdraw_no": "WD20250803000001",
      "amount": 0.01,
      "status": 1,
      "message": "转账成功",
      "processed": true
    }]
  }
}
```

## 故障排除

### 如果仍然出现平台证书错误

1. **检查微信商户平台**
   - 登录 https://pay.weixin.qq.com
   - 进入 账户中心 -> API安全
   - 确认已申请微信支付公钥
   - 验证证书序列号正确

2. **检查服务器配置**
   ```bash
   # 验证环境变量
   ./verify_production_env.sh
   
   # 检查证书文件
   ls -la /etc/peizhen/certs/wechat/
   
   # 查看详细日志
   tail -f backend/logs/app.prod.log | grep -E "(ERROR|证书|certificate)"
   ```

3. **网络连接测试**
   ```bash
   # 测试微信API连接
   curl -I https://api.mch.weixin.qq.com/v3/certificates
   ```

## 联系支持

如果按照以上步骤操作后仍然遇到问题，请提供：

1. 服务重启后的完整日志
2. 转账接口的错误响应
3. 环境变量验证结果
4. 微信商户平台证书状态截图

## 相关文档

- [微信支付API v3证书指引](https://pay.weixin.qq.com/doc/v3/merchant/4012153196)
- [微信支付Go SDK](https://github.com/wechatpay-apiv3/wechatpay-go)
- [商户平台API安全](https://pay.weixin.qq.com/index.php/core/cert/api_cert)