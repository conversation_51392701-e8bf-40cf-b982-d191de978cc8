#!/bin/bash

# 修复配置类型问题的脚本
# 解决 ttl 字段类型错误导致的服务启动失败

echo "🔧 修复配置类型问题"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 1. 检查问题
echo "步骤1: 检查配置问题"

# 检查后端配置文件
if grep -q 'ttl: "${BACKEND_API_TTL:300}"' backend/config/conf/config.prod.yaml; then
    echo -e "${RED}❌ 后端配置文件存在类型问题${NC}"
    echo "修复中..."
    sed -i 's/ttl: "${BACKEND_API_TTL:300}"/ttl: 300/' backend/config/conf/config.prod.yaml
    echo -e "${GREEN}✅ 后端配置文件已修复${NC}"
else
    echo -e "${GREEN}✅ 后端配置文件正常${NC}"
fi

# 检查管理后台配置文件
if grep -q 'ttl: "${BACKEND_API_TTL:300}"' admin/server/config/config.prod.yaml; then
    echo -e "${RED}❌ 管理后台配置文件存在类型问题${NC}"
    echo "修复中..."
    sed -i 's/ttl: "${BACKEND_API_TTL:300}"/ttl: 300/' admin/server/config/config.prod.yaml
    echo -e "${GREEN}✅ 管理后台配置文件已修复${NC}"
else
    echo -e "${GREEN}✅ 管理后台配置文件正常${NC}"
fi

# 2. 验证修复结果
echo ""
echo "步骤2: 验证修复结果"

# 检查后端配置
if grep -q 'ttl: 300' backend/config/conf/config.prod.yaml; then
    echo -e "${GREEN}✅ 后端配置 ttl 字段类型正确${NC}"
else
    echo -e "${RED}❌ 后端配置 ttl 字段仍有问题${NC}"
    exit 1
fi

# 检查管理后台配置
if grep -q 'ttl: 300' admin/server/config/config.prod.yaml; then
    echo -e "${GREEN}✅ 管理后台配置 ttl 字段类型正确${NC}"
else
    echo -e "${RED}❌ 管理后台配置 ttl 字段仍有问题${NC}"
    exit 1
fi

# 3. 显示修复后的配置
echo ""
echo "步骤3: 显示修复后的配置"

echo -e "${YELLOW}后端配置 (backend/config/conf/config.prod.yaml):${NC}"
grep -A 5 "internal_api:" backend/config/conf/config.prod.yaml

echo ""
echo -e "${YELLOW}管理后台配置 (admin/server/config/config.prod.yaml):${NC}"
grep -A 5 "api_key:" admin/server/config/config.prod.yaml

# 4. 测试配置加载
echo ""
echo "步骤4: 测试配置加载"

# 加载环境变量
echo "加载环境变量..."
set -a
source production.env
set +a

echo "关键环境变量:"
echo "  BACKEND_API_KEY_ID: $BACKEND_API_KEY_ID"
echo "  BACKEND_API_SECRET_KEY: ${BACKEND_API_SECRET_KEY:0:10}...${BACKEND_API_SECRET_KEY: -10}"

# 5. 重启建议
echo ""
echo "步骤5: 重启建议"
echo -e "${YELLOW}配置修复完成，请重启服务:${NC}"
echo ""
echo "1. 如果使用 systemd 服务:"
echo "   sudo systemctl restart peizhen-gin-app.service"
echo "   sudo systemctl status peizhen-gin-app.service"
echo ""
echo "2. 如果手动启动:"
echo "   # 停止现有服务"
echo "   pkill -f peizhen"
echo "   "
echo "   # 重新启动"
echo "   source production.env"
echo "   cd backend && ./bin/peizhen"
echo ""
echo "3. 检查服务状态:"
echo "   journalctl -u peizhen-gin-app.service -f"
echo ""

echo -e "${GREEN}✅ 配置类型问题修复完成${NC}"
echo -e "${GREEN}✅ ttl 字段现在使用正确的整数类型${NC}"