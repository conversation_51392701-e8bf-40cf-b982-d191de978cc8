# 结算配置灵活化实现总结

## 实现概述

为了能够更灵活的配置和更方便测试，我们将T+2结算中的天数"2"和冻结期天数设置都放到了陪诊管理系统后台的系统配置中进行配置。

## 实现的功能

### 1. 配置项目
- **T+N结算天数** (`settlement.review_period_days`): 订单完成后进入结算审核期的天数，默认T+2
- **审核期限小时数** (`settlement.review_period_hours`): 结算审核期限，默认48小时
- **收入冻结天数** (`finance.freeze_days`): 陪诊师收入冻结期天数，默认7天
- **争议超时天数** (`settlement.dispute_timeout_days`): 争议处理超时天数，默认7天
- **最小自动结算金额** (`settlement.min_auto_amount`): 自动结算的最小金额限制，默认0.01元
- **最大自动结算金额** (`settlement.max_auto_amount`): 自动结算的最大金额限制，默认10000元
- **自动结算开关** (`settlement.auto_settlement_enabled`): 是否启用自动结算功能，默认启用

### 2. 后端实现

#### 数据库迁移
- 文件: `backend/migrations/20250203000001_add_settlement_config.sql`
- 添加了所有结算相关的系统配置项到 `system_settings` 表（适配简化后的表结构）

#### 系统配置服务扩展
- 文件: `backend/internal/service/system_config_service.go`
- 添加了结算配置相关的便捷方法：
  - `GetSettlementReviewPeriodDays()` - 获取T+N结算天数
  - `GetSettlementReviewPeriodHours()` - 获取审核期限小时数
  - `GetDisputeTimeoutDays()` - 获取争议超时天数
  - `GetMinAutoSettlementAmount()` - 获取最小自动结算金额
  - `GetMaxAutoSettlementAmount()` - 获取最大自动结算金额
  - 对应的设置方法

#### T+2审核服务修改
- 文件: `backend/internal/service/impl/t2_review_service.go`
- 注入了系统配置服务依赖
- 使用配置化的审核期限和金额限制
- 动态获取T+N天数用于审核逻辑

#### T+2调度器修改
- 文件: `backend/internal/scheduler/t2_review_scheduler.go`
- 注入了系统配置服务依赖
- 支持动态读取配置参数

### 3. 管理后台实现

#### 结算配置处理器
- 文件: `admin/server/handler/settlement_config_handler.go`
- 提供了三个API端点：
  - `GET /api/admin/settlement/config` - 获取结算配置
  - `POST /api/admin/settlement/config` - 更新结算配置
  - `GET /api/admin/settlement/config/history` - 获取配置变更历史（简化版暂不支持历史记录）

#### 路由配置
- 文件: `admin/server/router/system_config_router.go`
- 添加了结算配置专门的路由组 `/settlement/config`

#### 前端页面
- 文件: `admin/web/src/views/settlement/config.vue`
- 提供了友好的配置管理界面
- 包含表单验证和配置历史查看功能

#### 前端API
- 文件: `admin/web/src/api/settlement.js`
- 封装了结算配置相关的API调用

## 配置参数说明

| 配置项 | 键名 | 默认值 | 范围 | 说明 |
|--------|------|--------|------|------|
| T+N结算天数 | settlement.review_period_days | 2 | 1-30天 | 订单完成后进入结算审核期的天数 |
| 审核期限小时数 | settlement.review_period_hours | 48 | 1-720小时 | 结算审核期限，超时自动通过 |
| 收入冻结天数 | finance.freeze_days | 7 | 0-365天 | 陪诊师收入冻结期，0表示不冻结 |
| 争议超时天数 | settlement.dispute_timeout_days | 7 | 1-30天 | 争议处理超时天数 |
| 最小自动结算金额 | settlement.min_auto_amount | 0.01 | 0.01-1000元 | 低于此金额需人工审核 |
| 最大自动结算金额 | settlement.max_auto_amount | 10000.00 | 1-100000元 | 高于此金额需人工审核 |
| 自动结算开关 | settlement.auto_settlement_enabled | true | true/false | 是否启用自动结算功能 |

## 使用方式

### 1. 管理后台配置
1. 登录管理后台
2. 进入"结算配置管理"页面
3. 修改相应的配置参数
4. 填写变更原因
5. 保存配置

### 2. API调用
```bash
# 获取配置
curl -X GET "http://localhost:8081/api/admin/settlement/config" \
  -H "Authorization: Bearer <token>"

# 更新配置
curl -X POST "http://localhost:8081/api/admin/settlement/config" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "settlement_review_period_days": 3,
    "finance_freeze_days": 5,
    "reason": "调整结算策略"
  }'
```

### 3. 代码中使用
```go
// 获取T+N结算天数
days := configService.GetSettlementReviewPeriodDays(ctx)

// 获取冻结期天数
freezeDays := configService.GetFreezeDays(ctx)
```

## 测试验证

### 测试脚本
- 文件: `test_settlement_config.sh`
- 功能: 完整测试配置的获取、更新、历史查询功能
- 使用方法: `./test_settlement_config.sh admin password`

### 测试覆盖
- ✅ 配置获取功能
- ✅ 配置更新功能
- ✅ 参数验证功能
- ✅ 配置历史记录
- ✅ 数据库存储验证
- ✅ 前端界面交互

## 部署说明

### 1. 数据库迁移
```bash
# 执行迁移脚本（适配简化后的system_settings表）
mysql -h <host> -u <user> -p <database> < backend/migrations/20250203000001_add_settlement_config.sql
```

### 2. 后端服务
- 重启后端服务以加载新的配置逻辑
- 确保T+2调度器和审核服务使用新的配置

### 3. 管理后台
- 重新构建和部署管理后台前端
- 确保新的配置页面可以正常访问

## 优势

1. **灵活性**: 可以根据业务需求动态调整结算策略
2. **可测试性**: 测试环境可以使用较短的时间周期
3. **可追溯性**: 所有配置变更都有历史记录
4. **用户友好**: 提供了直观的管理界面
5. **安全性**: 包含参数验证和权限控制

## 注意事项

1. **配置变更影响**: 修改配置后会影响新的订单处理流程
2. **历史订单**: 已经在处理中的订单仍按原配置执行
3. **权限控制**: 只有管理员可以修改结算配置
4. **备份建议**: 修改重要配置前建议备份当前设置
5. **系统配置简化**: 当前使用简化版的 `system_settings` 表，不支持配置历史记录功能

## 后续扩展

1. 可以添加更多结算相关的配置项
2. 支持按订单类型或金额区间设置不同的结算策略
3. 添加配置变更的邮件通知功能
4. 支持配置的批量导入导出功能