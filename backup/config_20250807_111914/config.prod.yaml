# ===========================================
# 生产环境特定配置
# ===========================================
# 此文件只包含生产环境特定的配置
# 会覆盖 config.yaml 中的对应配置项
# 使用环境变量管理敏感信息
# ===========================================

# 生产环境应用配置
app:
  mode: "release"

# 生产环境服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s

# 生产环境域名配置
domains:
  main: "kanghuxing.cn"
  api: "www.kanghuxing.cn"
  admin: "admin.kanghuxing.cn"
  miniapp: "www.kanghuxing.cn"
  
# 生产环境URL配置
urls:
  api_base: "https://www.kanghuxing.cn"
  admin_base: "https://admin.kanghuxing.cn"
  frontend_base: "https://www.kanghuxing.cn"
  upload_domain: "https://www.kanghuxing.cn"
  
# 生产环境微信回调地址
callbacks:
  wechat_pay_notify: "https://www.kanghuxing.cn/api/v1/payment/notify/wechat"
  wechat_refund_notify: "https://www.kanghuxing.cn/api/v1/refund/notify/wechat"

# 生产环境邮箱配置
emails:
  support: "<EMAIL>"
  business: "<EMAIL>"
  feedback: "<EMAIL>"
  noreply: "<EMAIL>"

# 生产环境端口配置
ports:
  backend: 8080
  admin_server: 8081
  admin_web: 3001
  mysql: 3306
  redis: 6379
  rabbitmq: 5672

# 生产环境数据库配置（通过环境变量配置）
database:
  host: "${DB_HOST}"
  port: 3306
  database: "${DB_NAME}"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  max_idle_conns: 20
  max_open_conns: 200
  conn_max_lifetime: 1800
  show_sql: false

# 生产环境Redis配置（通过环境变量配置）
redis:
  host: "${REDIS_HOST}"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0

# 生产环境JWT配置（通过环境变量配置）
jwt:
  secret: "${JWT_SECRET}"
  expire_time: "12h"

# 生产环境日志配置
log:
  level: "info"
  filename: "logs/app.prod.log"
  max_age: 90

# 生产环境上传配置
upload:
  save_path: "/data/uploads"
  allowed_domain: "https://www.kanghuxing.cn"
  image_compression:
    max_size: 102400        # 100KB
    jpeg_quality: 85        # JPEG质量
    png_compression: 6      # PNG压缩级别
    max_width: 1920        # 最大宽度
    max_height: 1920       # 最大高度
    min_quality: 30        # 最低质量阈值

# 生产环境微信配置（通过环境变量绑定）
wechat:
  # 小程序基础配置
  miniapp:
    app_id: "${WECHAT_APP_ID}"          # 微信小程序AppID
    app_secret: "${WECHAT_APP_SECRET}"   # 微信小程序AppSecret
    
  # 微信支付配置
  payment:
    # 商户基础信息（通过环境变量配置）
    mch_id: "${WECHAT_MCH_ID}"          # 微信支付商户号
    api_v3_key: "${WECHAT_API_V3_KEY}"  # 微信支付APIv3密钥
    serial_no: "${WECHAT_SERIAL_NO}"    # 商户证书序列号
    
    # 🔧 证书文件配置（推荐使用文件形式）
    private_key_file: "${WECHAT_PRIVATE_KEY_PATH:/etc/peizhen/certs/wechat/apiclient_key.pem}"               # 商户私钥文件路径
    public_key_file: "${WECHAT_PUBLIC_KEY_PATH:/etc/peizhen/certs/wechat/wechat_public_key.pem}"             # 微信支付公钥文件路径
    public_key_id: "${WECHAT_PUBLIC_KEY_ID:}"                                                                # 微信支付公钥ID
    
    # 🔧 兼容性配置（向后兼容，优先使用文件形式）
    private_key: "${WECHAT_PRIVATE_KEY:}"        # 商户私钥（PEM格式）- 兼容旧配置
    public_key: "${WECHAT_PUBLIC_KEY:}"          # 微信支付公钥（PEM格式）- 兼容旧配置
    
    # 回调地址配置
    pay_notify_url: "https://www.kanghuxing.cn/api/v1/payment/notify/wechat"
    refund_notify_url: "https://www.kanghuxing.cn/api/v1/refund/notify/wechat"
    
    # 支付模式配置
    payment_mode: "${PAYMENT_MODE:real}"  # 支付模式：real=真实支付，mock=模拟支付
    mock_auto_success: false              # 模拟支付时是否自动成功
    mock_success_delay: 0                 # 模拟支付成功延迟（秒）
    
    # 环境配置
    environment: "production"             # 环境标识：production=生产环境
    dev_amount: 0                         # 开发环境固定金额（分）
    
    # 测试配置（可选）
    test:
      openid: "${WECHAT_TEST_OPENID:}"          # 测试用户OpenID
      paid_order_no: "${WECHAT_TEST_ORDER_NO:}" # 测试订单号
      
    # 🔧 证书配置（wechatpay-go SDK配置）
    cert_dir: "/etc/peizhen/certs/wechat"                                                                    # 证书存放目录
    cert_file: "${WECHAT_CERT_FILE_PATH:/etc/peizhen/certs/wechat/apiclient_cert.pem}"                       # 商户证书文件路径（标准文件名）
    platform_cert_path: "${WECHAT_PLATFORM_CERT_PATH:/etc/peizhen/certs/wechat/platform_cert.pem}"         # 平台证书路径（备用）
    
    # 🔧 SDK配置模式选择
    use_public_key_mode: true             # 使用公钥验证模式（推荐）
    use_auto_cert_mode: false            # 使用自动证书下载模式（备用）
    
    # 安全配置
    sandbox_mode: false                   # 是否使用沙箱环境
    auto_report_user: true               # 是否自动上报用户信息
    sign_type: "HMAC-SHA256"             # 签名类型
    
    # 日志配置
    log_level: "info"                    # 支付日志级别
    log_file: "logs/wechat_pay.log"      # 支付日志文件
    
    # 高级配置
    http_timeout: 30                      # HTTP请求超时时间（秒）
    retry_count: 3                        # 请求重试次数
    auto_retry_on_error: true            # 是否在错误时自动重试

  # 生产环境微信企业付款配置
  transfer:
    # 客户端类型：official=官方SDK, simple=简化客户端
    client_type: "official"  # 可通过WECHAT_TRANSFER_CLIENT_TYPE环境变量覆盖
    
    # 基础配置（与支付配置共享）
    app_id: "${WECHAT_APP_ID}"                    # 小程序AppID（与支付共享）
    mch_id: "${WECHAT_MCH_ID}"                    # 商户号（与支付共享）
    api_v3_key: "${WECHAT_API_V3_KEY}"            # APIv3密钥（与支付共享）
    
    # 证书配置（官方SDK需要）
    certificate_serial_number: "${WECHAT_CERT_SERIAL_NUMBER}" # 商户证书序列号
    private_key_path: "${WECHAT_TRANSFER_PRIVATE_KEY_PATH:/etc/peizhen/certs/wechat/apiclient_key.pem}" # 私钥文件路径
    
    # 兼容性配置（向后兼容）
    serial_no: "${WECHAT_TRANSFER_SERIAL_NO}"              # 证书序列号（兼容旧配置）
    private_key_file: "${WECHAT_TRANSFER_PRIVATE_KEY_FILE:/etc/peizhen/certs/wechat/apiclient_key.pem}" # 私钥文件路径（兼容旧配置）
    
    # 环境配置
    environment: "production"                                                                           # 环境：production/sandbox
    notify_url: "https://www.kanghuxing.cn/api/v1/wechat/transfer/notify"                             # 回调通知地址
    
    # 转账限制配置
    single_limit: 20000000        # 单笔转账限额（分）= 200,000元 - 可通过WECHAT_TRANSFER_SINGLE_LIMIT环境变量覆盖
    daily_limit: 100000000        # 日累计限额（分）= 1,000,000元 - 可通过WECHAT_TRANSFER_DAILY_LIMIT环境变量覆盖
    min_amount: 1                 # 最小转账金额（分）= 0.01元 - 可通过WECHAT_TRANSFER_MIN_AMOUNT环境变量覆盖
    
    # 重试配置
    max_retries: 3                # 最大重试次数 - 可通过WECHAT_TRANSFER_MAX_RETRIES环境变量覆盖
    retry_interval: "30s"         # 重试间隔 - 可通过WECHAT_TRANSFER_RETRY_INTERVAL环境变量覆盖
    
    # 功能开关
    enabled: true                 # 启用企业付款功能 - 可通过WECHAT_TRANSFER_ENABLED环境变量覆盖
    mock_mode: false              # 生产环境禁用模拟模式，使用真实API

# 生产环境RabbitMQ配置（通过环境变量配置）
rabbitmq:
  url: "${RABBITMQ_URL}"

# 生产环境CORS配置
cors:
  enabled: true
  allowed_origins:
    - "https://www.kanghuxing.cn"
    - "https://admin.kanghuxing.cn"
  
# 生产环境API配置
api:
  rate_limit:
    enabled: true
    requests_per_minute: 60
    burst_size: 120
  
# 生产环境安全配置
security:
  api_key: "${API_KEY}"
  internal_api_key: "${INTERNAL_API_KEY}"  # 用于内部系统通信
  enable_https_redirect: true
  session_timeout: 3600
  max_request_size: 10485760  # 10MB
  
  # 内部API配置（生产环境）
  internal_api:
    key_id: "${BACKEND_API_KEY_ID:admin-key-prod-001}"
    secret_key: "${BACKEND_API_SECRET_KEY:prod-32-char-secret-key-here-456}"
    algorithm: "HMAC-SHA256"
    ttl: 300
  
  # IP白名单配置（可选）
  ip_whitelist:
    enabled: false
    allowed_ips: "${ALLOWED_IPS:}"  # 逗号分隔的IP列表
    
  # 调试接口保护
  debug_protection:
    enabled: true
    require_admin_role: true
    require_ip_whitelist: false

# 生产环境SMS配置
sms:
  provider: "aliyun"
  aliyun:
    access_key_id: "${SMS_ACCESS_KEY_ID}"
    access_key_secret: "${SMS_ACCESS_KEY_SECRET}"
    endpoint: "dysmsapi.aliyuncs.com"
    region: "cn-hangzhou"
    sign_name: "${SMS_SIGN_NAME}"

# 生产环境监控配置
monitoring:
  enabled: true
  metrics_path: "/metrics"
  health_path: "/health"
  
# 生产环境性能配置
performance:
  max_request_size: 10485760
  request_timeout: "30s"
  enable_gzip: true 

# 生产环境订单超时配置
order_timeout:
  enable_checker: true
  check_interval: "1m"                     # 生产环境检查间隔
  unpaid_timeout_minutes: 30               # 未支付订单超时 - 30分钟
  paid_timeout_minutes: 300                # 已支付订单超时 - 5小时
  matching_timeout_minutes: 5              # 匹配超时时间 - 5分钟
  protection_window_minutes: 30            # 保护窗口时间 - 30分钟
  manual_operation_timeout_minutes: 30     # 手动操作超时时间 - 30分钟
  enabled: true
