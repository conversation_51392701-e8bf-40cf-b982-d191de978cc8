#!/bin/bash

# 微信支付平台证书问题修复脚本
# 解决 "无可用的平台证书" 错误

echo "=== 微信支付平台证书问题修复 ==="

# 1. 问题分析
echo "1. 问题分析..."
echo "错误信息: 无可用的平台证书，请在商户平台-API安全申请使用微信支付公钥"
echo "错误代码: RESOURCE_NOT_EXISTS (404)"
echo "问题原因: 微信支付平台证书配置不正确或缺失"

# 2. 检查当前配置
echo ""
echo "2. 检查当前配置..."

config_file="backend/config/conf/config.prod.yaml"
if [ -f "$config_file" ]; then
    echo "✅ 找到生产配置文件: $config_file"
    
    # 检查平台证书相关配置
    echo "检查平台证书配置..."
    if grep -q "use_auto_cert_mode.*true" "$config_file"; then
        echo "✅ 已启用自动证书下载模式"
    else
        echo "⚠️  自动证书下载模式未启用"
    fi
    
    if grep -q "platform_cert_path" "$config_file"; then
        echo "✅ 找到平台证书路径配置"
    else
        echo "⚠️  未找到平台证书路径配置"
    fi
else
    echo "❌ 配置文件不存在: $config_file"
    exit 1
fi

# 3. 修复配置文件
echo ""
echo "3. 修复配置文件..."

# 确保启用自动证书下载模式
if ! grep -q "use_auto_cert_mode.*true" "$config_file"; then
    echo "启用自动证书下载模式..."
    
    # 备份配置文件
    cp "$config_file" "${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 修改配置
    sed -i.tmp 's/use_auto_cert_mode: false/use_auto_cert_mode: true/g' "$config_file"
    rm -f "${config_file}.tmp"
    
    echo "✅ 已启用自动证书下载模式"
else
    echo "✅ 自动证书下载模式已启用"
fi

# 4. 检查证书目录
echo ""
echo "4. 检查证书目录..."

cert_dir="/etc/peizhen/certs/wechat"
echo "证书目录: $cert_dir"

if [ -d "$cert_dir" ]; then
    echo "✅ 证书目录存在"
    
    # 检查目录权限
    dir_perms=$(stat -c "%a" "$cert_dir" 2>/dev/null || stat -f "%A" "$cert_dir" 2>/dev/null)
    echo "目录权限: $dir_perms"
    
    # 确保目录权限正确
    if [ "$dir_perms" != "755" ] && [ "$dir_perms" != "700" ]; then
        echo "修复目录权限..."
        chmod 755 "$cert_dir"
        echo "✅ 已修复目录权限"
    fi
    
    # 列出证书文件
    echo "证书文件列表:"
    ls -la "$cert_dir" 2>/dev/null || echo "目录为空或无法访问"
    
else
    echo "⚠️  证书目录不存在，创建目录..."
    mkdir -p "$cert_dir"
    chmod 755 "$cert_dir"
    echo "✅ 已创建证书目录"
fi

# 5. 验证环境变量
echo ""
echo "5. 验证环境变量..."

required_vars=(
    "WECHAT_APP_ID"
    "WECHAT_MCH_ID"
    "WECHAT_API_V3_KEY"
    "WECHAT_CERT_SERIAL_NUMBER"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
        echo "❌ $var: 未设置"
    else
        case $var in
            *KEY*|*SECRET*)
                echo "✅ $var: ${!var:0:8}****"
                ;;
            *)
                echo "✅ $var: ${!var}"
                ;;
        esac
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  以下环境变量需要设置："
    for var in "${missing_vars[@]}"; do
        echo "   export $var=\"your_value_here\""
    done
fi

# 6. 创建测试程序
echo ""
echo "6. 创建平台证书测试程序..."

cd backend 2>/dev/null || {
    echo "❌ 无法切换到backend目录"
    exit 1
}

cat > test_platform_cert.go << 'EOF'
package main

import (
    "context"
    "fmt"
    "log"
    "os"
    
    "github.com/gemeijie/peizhen/backend/config"
    "github.com/gemeijie/peizhen/backend/pkg/wechat"
)

func main() {
    fmt.Println("=== 微信支付平台证书测试 ===")
    
    // 设置生产环境
    os.Setenv("APP_ENV", "prod")
    
    // 初始化配置
    if err := config.InitConfig(); err != nil {
        log.Fatalf("配置初始化失败: %v", err)
    }
    
    cfg := config.GetGlobalConfig()
    if cfg == nil {
        log.Fatal("获取配置失败")
    }
    
    fmt.Printf("微信转账配置:\n")
    fmt.Printf("- AppID: %s\n", cfg.WeChat.Transfer.AppID)
    fmt.Printf("- MchID: %s\n", cfg.WeChat.Transfer.MchID)
    fmt.Printf("- CertificateSerialNumber: %s\n", cfg.WeChat.Transfer.CertificateSerialNumber)
    fmt.Printf("- PrivateKeyPath: %s\n", cfg.WeChat.Transfer.PrivateKeyPath)
    fmt.Printf("- ClientType: %s\n", cfg.WeChat.Transfer.ClientType)
    fmt.Printf("- Environment: %s\n", cfg.WeChat.Transfer.Environment)
    fmt.Printf("- Enabled: %t\n", cfg.WeChat.Transfer.Enabled)
    fmt.Printf("- MockMode: %t\n", cfg.WeChat.Transfer.MockMode)
    
    // 尝试创建转账客户端
    fmt.Println("\n=== 创建转账客户端 ===")
    client, err := wechat.GetGlobalTransferClient()
    if err != nil {
        fmt.Printf("❌ 创建转账客户端失败: %v\n", err)
        
        // 分析错误原因
        if contains(err.Error(), "platform") || contains(err.Error(), "证书") {
            fmt.Println("\n💡 解决方案:")
            fmt.Println("1. 确保在微信商户平台-API安全中申请了微信支付公钥")
            fmt.Println("2. 检查证书序列号是否正确")
            fmt.Println("3. 确保私钥文件存在且可读")
            fmt.Println("4. 启用自动证书下载模式")
        }
        
        os.Exit(1)
    }
    
    fmt.Println("✅ 转账客户端创建成功")
    
    // 测试获取APIv3密钥
    fmt.Println("\n=== 测试APIv3密钥 ===")
    apiKey, err := client.GetAPIv3Key()
    if err != nil {
        fmt.Printf("❌ 获取APIv3密钥失败: %v\n", err)
    } else {
        fmt.Printf("✅ APIv3密钥获取成功，长度: %d\n", len(apiKey))
    }
    
    fmt.Println("\n🎉 平台证书测试完成")
}

func contains(s, substr string) bool {
    return len(s) >= len(substr) && (s == substr || 
        (len(s) > len(substr) && 
         (s[:len(substr)] == substr || 
          s[len(s)-len(substr):] == substr ||
          findInString(s, substr))))
}

func findInString(s, substr string) bool {
    for i := 0; i <= len(s)-len(substr); i++ {
        if s[i:i+len(substr)] == substr {
            return true
        }
    }
    return false
}
EOF

echo "✅ 已创建平台证书测试程序"

# 7. 运行测试
echo ""
echo "7. 运行平台证书测试..."

if go run test_platform_cert.go; then
    echo "✅ 平台证书测试通过"
else
    echo "❌ 平台证书测试失败"
    echo ""
    echo "🔧 故障排除步骤:"
    echo "1. 检查微信商户平台-API安全设置"
    echo "2. 确认证书序列号正确"
    echo "3. 验证私钥文件路径和权限"
    echo "4. 检查网络连接"
    echo "5. 查看详细错误日志"
fi

# 清理测试文件
rm -f test_platform_cert.go

# 8. 提供解决方案
echo ""
echo "=== 解决方案总结 ==="
echo ""
echo "🔧 如果仍然遇到 '无可用的平台证书' 错误，请按以下步骤操作:"
echo ""
echo "1. 登录微信商户平台 (pay.weixin.qq.com)"
echo "2. 进入 账户中心 -> API安全"
echo "3. 在 '微信支付公钥' 部分，点击 '申请使用微信支付公钥'"
echo "4. 按照指引完成申请流程"
echo "5. 确保证书序列号与平台显示的一致"
echo ""
echo "📋 配置检查清单:"
echo "- ✅ WECHAT_CERT_SERIAL_NUMBER 环境变量已设置"
echo "- ✅ 私钥文件存在且权限正确 (600)"
echo "- ✅ 启用了自动证书下载模式"
echo "- ✅ 网络可以访问微信支付API"
echo ""
echo "🔄 完成配置后，重启后端服务:"
echo "   sudo systemctl restart peizhen-backend"
echo ""
echo "📝 查看日志:"
echo "   tail -f backend/logs/app.prod.log | grep -i cert"