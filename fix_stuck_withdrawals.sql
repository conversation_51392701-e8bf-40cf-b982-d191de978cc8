-- 修复卡在Processing状态的提现转账记录
-- 执行前请先备份相关表

-- 1. 查看当前卡在Processing状态的记录
SELECT 
    wt.id,
    wt.withdrawal_id,
    wt.transfer_no,
    wt.status,
    wt.transfer_time,
    wt.retry_count,
    wt.wechat_batch_no,
    w.withdraw_no,
    w.amount,
    TIMESTAMPDIFF(MINUTE, wt.transfer_time, NOW()) as minutes_stuck
FROM withdrawal_transfers wt 
LEFT JOIN withdrawals w ON wt.withdrawal_id = w.id
WHERE wt.status = 1  -- Processing状态
ORDER BY wt.transfer_time ASC;

-- 2. 标记超过30分钟的Processing记录为失败（安全起见，先不执行）
/*
UPDATE withdrawal_transfers 
SET 
    status = 3,  -- Failed状态
    fail_reason = '转账超时，系统自动标记为失败',
    updated_at = NOW()
WHERE status = 1 
    AND transfer_time IS NOT NULL 
    AND transfer_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE);
*/

-- 3. 手动修复特定的卡住记录（替换为实际的ID）
-- UPDATE withdrawal_transfers SET status = 3, fail_reason = '手动修复：转账超时' WHERE id = YOUR_SPECIFIC_ID;

-- 4. 检查修复后的状态
SELECT 
    status,
    COUNT(*) as count,
    AVG(retry_count) as avg_retry
FROM withdrawal_transfers 
GROUP BY status;

-- 5. 清理建议：删除超过3次重试失败的记录（可选，谨慎执行）
/*
SELECT 
    id,
    withdrawal_id,
    retry_count,
    fail_reason
FROM withdrawal_transfers 
WHERE status = 3 
    AND retry_count >= 3 
    AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
*/