-- 综合修复提现申请中收款人姓名为空的问题
-- 这个脚本会检查并修复所有相关的数据问题

-- 1. 检查当前数据状态
SELECT '=== 当前数据状态检查 ===' as section;

-- 检查提现申请中姓名为空的记录
SELECT 
    '提现申请中姓名为空的记录' as description,
    COUNT(*) as count
FROM withdrawals 
WHERE real_name IS NULL OR real_name = '';

-- 检查转账记录中姓名为空的记录
SELECT 
    '转账记录中姓名为空的记录' as description,
    COUNT(*) as count
FROM withdrawal_transfers 
WHERE real_name IS NULL OR real_name = '';

-- 检查数据不一致的记录
SELECT 
    '提现申请与转账记录姓名不一致的记录' as description,
    COUNT(*) as count
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.real_name != wt.real_name 
   OR (w.real_name IS NULL AND wt.real_name IS NOT NULL)
   OR (w.real_name IS NOT NULL AND wt.real_name IS NULL);

-- 2. 显示具体的问题记录
SELECT '=== 具体问题记录 ===' as section;

-- 显示姓名为空的提现申请
SELECT 
    '姓名为空的提现申请' as type,
    w.id as withdrawal_id,
    w.user_id,
    w.real_name as withdrawal_real_name,
    a.name as attendant_name,
    w.status,
    w.created_at
FROM withdrawals w
LEFT JOIN attendants a ON w.user_id = a.user_id
WHERE w.real_name IS NULL OR w.real_name = ''
ORDER BY w.id;

-- 显示姓名为空的转账记录
SELECT 
    '姓名为空的转账记录' as type,
    wt.id as transfer_id,
    wt.withdrawal_id,
    w.user_id,
    wt.real_name as transfer_real_name,
    w.real_name as withdrawal_real_name,
    a.name as attendant_name,
    wt.status,
    wt.created_at
FROM withdrawal_transfers wt
LEFT JOIN withdrawals w ON wt.withdrawal_id = w.id
LEFT JOIN attendants a ON w.user_id = a.user_id
WHERE wt.real_name IS NULL OR wt.real_name = ''
ORDER BY wt.id;

-- 3. 执行修复操作
SELECT '=== 开始修复操作 ===' as section;

-- 修复提现申请中的空姓名（从陪诊师表中获取）
UPDATE withdrawals w
JOIN attendants a ON w.user_id = a.user_id
SET w.real_name = a.name,
    w.updated_at = NOW()
WHERE (w.real_name IS NULL OR w.real_name = '')
  AND a.name IS NOT NULL 
  AND a.name != '';

-- 显示修复结果
SELECT 
    '修复提现申请姓名' as operation,
    ROW_COUNT() as affected_rows;

-- 修复转账记录中的空姓名（从对应的提现申请中获取）
UPDATE withdrawal_transfers wt
JOIN withdrawals w ON wt.withdrawal_id = w.id
SET wt.real_name = w.real_name,
    wt.updated_at = NOW()
WHERE (wt.real_name IS NULL OR wt.real_name = '')
  AND w.real_name IS NOT NULL 
  AND w.real_name != '';

-- 显示修复结果
SELECT 
    '修复转账记录姓名（从提现申请）' as operation,
    ROW_COUNT() as affected_rows;

-- 修复转账记录中仍然为空的姓名（直接从陪诊师表中获取）
UPDATE withdrawal_transfers wt
JOIN withdrawals w ON wt.withdrawal_id = w.id
JOIN attendants a ON w.user_id = a.user_id
SET wt.real_name = a.name,
    wt.updated_at = NOW()
WHERE (wt.real_name IS NULL OR wt.real_name = '')
  AND a.name IS NOT NULL 
  AND a.name != '';

-- 显示修复结果
SELECT 
    '修复转账记录姓名（从陪诊师表）' as operation,
    ROW_COUNT() as affected_rows;

-- 4. 修复数据不一致问题
SELECT '=== 修复数据一致性 ===' as section;

-- 将转账记录的姓名同步为提现申请中的姓名
UPDATE withdrawal_transfers wt
JOIN withdrawals w ON wt.withdrawal_id = w.id
SET wt.real_name = w.real_name,
    wt.updated_at = NOW()
WHERE w.real_name IS NOT NULL 
  AND w.real_name != ''
  AND (wt.real_name IS NULL OR wt.real_name = '' OR wt.real_name != w.real_name);

-- 显示修复结果
SELECT 
    '同步转账记录姓名' as operation,
    ROW_COUNT() as affected_rows;

-- 5. 验证修复结果
SELECT '=== 修复结果验证 ===' as section;

-- 检查修复后的状态
SELECT 
    '修复后提现申请中姓名为空的记录' as description,
    COUNT(*) as count
FROM withdrawals 
WHERE real_name IS NULL OR real_name = '';

SELECT 
    '修复后转账记录中姓名为空的记录' as description,
    COUNT(*) as count
FROM withdrawal_transfers 
WHERE real_name IS NULL OR real_name = '';

SELECT 
    '修复后数据不一致的记录' as description,
    COUNT(*) as count
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.real_name != wt.real_name 
   OR (w.real_name IS NULL AND wt.real_name IS NOT NULL)
   OR (w.real_name IS NOT NULL AND wt.real_name IS NULL);

-- 6. 显示修复后的具体数据
SELECT '=== 修复后的数据状态 ===' as section;

-- 显示所有提现申请的姓名状态
SELECT 
    w.id as withdrawal_id,
    w.user_id,
    w.real_name,
    w.status,
    CASE 
        WHEN w.real_name IS NULL OR w.real_name = '' THEN '❌ 姓名为空'
        ELSE '✅ 姓名正常'
    END as name_status
FROM withdrawals w
ORDER BY w.id;

-- 显示所有转账记录的姓名状态
SELECT 
    wt.id as transfer_id,
    wt.withdrawal_id,
    wt.real_name,
    wt.status,
    CASE 
        WHEN wt.real_name IS NULL OR wt.real_name = '' THEN '❌ 姓名为空'
        ELSE '✅ 姓名正常'
    END as name_status
FROM withdrawal_transfers wt
ORDER BY wt.id;

-- 7. 数据一致性检查
SELECT '=== 最终数据一致性检查 ===' as section;

SELECT 
    w.id as withdrawal_id,
    w.real_name as withdrawal_name,
    wt.id as transfer_id,
    wt.real_name as transfer_name,
    CASE 
        WHEN w.real_name = wt.real_name THEN '✅ 一致'
        WHEN w.real_name IS NULL AND wt.real_name IS NULL THEN '⚠️ 都为空'
        WHEN w.real_name IS NULL OR w.real_name = '' THEN '❌ 提现申请姓名为空'
        WHEN wt.real_name IS NULL OR wt.real_name = '' THEN '❌ 转账记录姓名为空'
        ELSE '❌ 不一致'
    END as consistency_status
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
ORDER BY w.id;

-- 8. 最终统计
SELECT '=== 最终统计 ===' as section;

SELECT 
    '总提现申请数' as metric,
    COUNT(*) as value
FROM withdrawals
UNION ALL
SELECT 
    '姓名正常的提现申请数' as metric,
    COUNT(*) as value
FROM withdrawals 
WHERE real_name IS NOT NULL AND real_name != ''
UNION ALL
SELECT 
    '总转账记录数' as metric,
    COUNT(*) as value
FROM withdrawal_transfers
UNION ALL
SELECT 
    '姓名正常的转账记录数' as metric,
    COUNT(*) as value
FROM withdrawal_transfers 
WHERE real_name IS NOT NULL AND real_name != '';