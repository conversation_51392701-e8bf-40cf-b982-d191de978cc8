# 数据库迁移服务实现文档

## 概述

本文档记录了订单状态流转优化项目中数据库迁移服务的实现，主要用于将历史的 `status = 4` 订单迁移到新的 T+2 审核流程（`status = 10`）。

## 实现内容

### 1. 核心服务实现

#### 1.1 迁移服务接口 (`backend/internal/service/order_status_migration.go`)

定义了完整的迁移服务接口，包括：

- `MigrateStatus4ToStatus10`: 批量迁移 status=4 订单到 status=10
- `RollbackMigration`: 回滚迁移操作
- `GetMigrationProgress`: 获取迁移进度
- `ValidateMigration`: 验证迁移结果
- `GetOrdersWithStatus4`: 获取待迁移订单列表

#### 1.2 迁移服务实现 (`backend/internal/service/impl/order_status_migration_service_impl.go`)

实现了所有迁移相关功能：

**核心功能：**
- 批量迁移处理，支持事务安全
- 完整的回滚数据存储，包括 ServicePhotos 字段处理
- 自动创建 T+2 审核记录
- 迁移进度跟踪和统计
- 数据一致性验证

**关键技术点：**
- 解决了 `model.JSON` 与 `json.RawMessage` 类型兼容性问题
- 实现了事务安全的批量操作
- 支持分批处理大量数据

### 2. 数据模型扩展

#### 2.1 迁移日志模型 (`backend/internal/model/order_status_migration_log.go`)

```go
type OrderStatusMigrationLog struct {
    BaseModel
    OrderID         uint            `json:"order_id"`
    OldStatus       int             `json:"old_status"`
    NewStatus       int             `json:"new_status"`
    MigrationType   string          `json:"migration_type"`
    MigrationReason string          `json:"migration_reason"`
    ReviewRecordID  *uint           `json:"review_record_id"`
    MigrationBatch  string          `json:"migration_batch"`
    MigratedBy      uint            `json:"migrated_by"`
    MigratedAt      time.Time       `json:"migrated_at"`
    RollbackData    json.RawMessage `json:"rollback_data"`
}
```

#### 2.2 回滚数据结构

```go
type RollbackDataStatus4To10 struct {
    OriginalStatus         int             `json:"original_status"`
    OriginalUpdatedAt      time.Time       `json:"original_updated_at"`
    OriginalServiceSummary string          `json:"original_service_summary"`
    OriginalServicePhotos  json.RawMessage `json:"original_service_photos"`
    OriginalActualEndTime  *time.Time      `json:"original_actual_end_time"`
    CreatedReviewRecordID  *uint           `json:"created_review_record_id"`
    CreatedStatusLogID     *uint           `json:"created_status_log_id"`
}
```

### 3. Repository 层实现

#### 3.1 迁移日志 Repository (`backend/internal/repository/impl/order_status_migration_log.go`)

实现了完整的迁移日志数据访问功能：

- `Create`: 创建迁移日志
- `FindByOrderID`: 根据订单ID查找迁移历史
- `FindByBatch`: 根据批次查找迁移记录
- `FindByType`: 根据迁移类型查找记录
- `FindByTimeRange`: 根据时间范围查找记录
- `GetMigrationStats`: 获取迁移统计信息
- `FindOrdersWithStatus4`: 查找待迁移订单

### 4. 数据库迁移

#### 4.1 迁移日志表创建 (`backend/migrations/20250731000002_create_order_status_migration_log.sql`)

```sql
CREATE TABLE order_status_migration_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNSIGNED NOT NULL,
    old_status INT NOT NULL,
    new_status INT NOT NULL,
    migration_type VARCHAR(50) NOT NULL,
    migration_reason TEXT,
    review_record_id BIGINT UNSIGNED,
    migration_batch VARCHAR(100),
    migrated_by BIGINT UNSIGNED DEFAULT 0,
    migrated_at TIMESTAMP NOT NULL,
    rollback_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_order_id (order_id),
    INDEX idx_migration_type (migration_type),
    INDEX idx_migration_batch (migration_batch),
    INDEX idx_migrated_at (migrated_at),
    INDEX idx_review_record_id (review_record_id)
);
```

## 技术难点解决

### 1. ServicePhotos 字段类型兼容性

**问题：** `model.JSON` 类型与 `json.RawMessage` 类型不兼容

**解决方案：**
```go
// 存储时转换
if order.ServicePhotos != nil {
    rollbackData.OriginalServicePhotos = json.RawMessage(order.ServicePhotos)
}

// 恢复时转换
if len(rollbackData.OriginalServicePhotos) > 0 {
    order.ServicePhotos = model.JSON(rollbackData.OriginalServicePhotos)
}
```

### 2. 结构体命名冲突

**问题：** `ValidationResult` 与现有结构体冲突

**解决方案：** 重命名为 `MigrationValidationResult`

### 3. 事务安全

**实现：** 使用 GORM 事务确保数据一致性
```go
return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
    // 迁移操作
    // 创建审核记录
    // 记录迁移日志
    return nil
})
```

## 测试验证

### 1. 单元测试 (`backend/internal/service/impl/order_status_migration_service_simple_test.go`)

实现了以下测试用例：

- `TestOrderStatusMigrationService_ServicePhotosHandling`: 测试 ServicePhotos 字段处理
- `TestMigrationProgress_Calculation`: 测试迁移进度计算
- `TestMigrationValidation_DataConsistency`: 测试数据一致性验证
- `TestMigrationTypeConstants`: 测试迁移类型常量
- `TestRollbackDataSerialization`: 测试回滚数据序列化

### 2. 测试结果

所有测试用例均通过，验证了：
- JSON 类型转换正确性
- 迁移进度计算准确性
- 数据结构完整性
- 序列化/反序列化功能

## 使用示例

### 1. 执行迁移

```go
// 创建迁移请求
req := &service.MigrateStatus4ToStatus10Request{
    BatchSize:   100,
    BatchName:   "migration-batch-001",
    Reason:      "订单状态流转优化",
    OperatorID:  1,
}

// 执行迁移
resp, err := migrationService.MigrateStatus4ToStatus10(ctx, req)
if err != nil {
    log.Error("迁移失败", zap.Error(err))
    return
}

log.Info("迁移完成", 
    zap.Int("processed", resp.ProcessedCount),
    zap.Int("success", resp.SuccessCount),
    zap.Int("failed", resp.FailedCount))
```

### 2. 查看进度

```go
progress, err := migrationService.GetMigrationProgress(ctx, "migration-batch-001")
if err != nil {
    log.Error("获取进度失败", zap.Error(err))
    return
}

log.Info("迁移进度",
    zap.Float64("progress", progress.Progress),
    zap.Int("success", progress.SuccessCount),
    zap.Int("failure", progress.FailureCount))
```

### 3. 验证结果

```go
result, err := migrationService.ValidateMigration(ctx, "migration-batch-001")
if err != nil {
    log.Error("验证失败", zap.Error(err))
    return
}

if result.DataConsistency.TotalInconsistencies > 0 {
    log.Warn("发现数据不一致", 
        zap.Int("inconsistencies", result.DataConsistency.TotalInconsistencies))
}
```

## 下一步计划

1. **API 接口实现**: 创建 HTTP 接口供管理后台调用
2. **监控告警**: 添加迁移过程监控和异常告警
3. **性能优化**: 针对大批量数据的性能优化
4. **回滚测试**: 完善回滚功能的测试用例

## 总结

数据库迁移服务已成功实现，解决了以下关键问题：

1. ✅ **类型兼容性**: 解决了 `model.JSON` 与 `json.RawMessage` 的兼容性问题
2. ✅ **事务安全**: 实现了事务安全的批量迁移操作
3. ✅ **数据完整性**: 完整保存回滚所需的所有数据
4. ✅ **进度跟踪**: 实现了迁移进度跟踪和统计功能
5. ✅ **验证机制**: 实现了数据一致性验证功能
6. ✅ **测试覆盖**: 完成了核心功能的单元测试

该服务为订单状态流转优化项目的数据迁移提供了可靠的技术支撑。
