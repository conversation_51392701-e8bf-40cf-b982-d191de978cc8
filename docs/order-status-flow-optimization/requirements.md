# 订单状态流转优化需求文档

## 项目背景

当前订单系统中 `status = 4` (OrderStatusCompleted) 存在多种产生路径，导致业务逻辑不一致和状态流转混乱：

### 现有问题分析

1. **接口不一致**：
   - 新接口：`POST /api/v1/order-actions/:order_id/complete` → status = 8 (T+2审核)
   - 旧接口：`POST /api/v1/attendants/end-service` → status = 4 (直接完成)

2. **业务逻辑混乱**：
   - 同样是陪诊师完成服务，却有两种不同的状态流转
   - status = 4 跳过了T+2审核机制，存在风险

3. **多种产生路径**：
   - 路径1：陪诊师手动结束服务（旧接口）- 直接跳过T+2审核
   - 路径2：管理员强制完成异常订单 - 跳过T+2审核
   - 路径3：异常处理服务自动完成 - 跳过T+2审核
   - 路径4：订单模型的EndService方法 - 跳过T+2审核

## 需求概述

优化订单状态流转逻辑，统一订单完成流程，确保状态的唯一性和业务逻辑的一致性，防止歧义性导致的各种问题。

## 用户故事和需求

### 需求1：统一订单完成流程

**用户故事：** 作为系统架构师，我希望所有订单完成操作都遵循统一的状态流转规则，避免业务逻辑不一致。

#### 验收标准
1. WHEN 陪诊师完成服务时 THEN 订单应进入T+2审核流程 (status = 8)
2. WHEN 管理员强制完成异常订单时 THEN 订单应直接完成 (status = 4) 但需记录特殊原因
3. WHEN 异常处理系统自动完成时 THEN 订单应直接完成 (status = 4) 但需记录处理日志
4. 正常的陪诊师完成服务不应直接跳到 status = 4
5. 所有状态变更都应有明确的触发条件和业务含义

### 需求2：废弃旧的结束服务接口

**用户故事：** 作为系统维护者，我希望移除导致状态不一致的旧接口，确保状态流转的唯一性。

#### 验收标准
1. WHEN 调用旧的 `/api/v1/attendants/end-service` 接口时 THEN 应返回废弃提示
2. 旧接口应重定向到新的订单完成流程
3. 现有使用旧接口的前端代码应迁移到新接口
4. 旧接口的功能应完全由新接口替代
5. 迁移过程中不应影响现有业务功能

### 需求3：完全移除status=4的使用

**用户故事：** 作为系统架构师，我希望完全移除 status = 4 的使用，统一使用T+2审核流程，确保状态流转的唯一性。

#### 验收标准
1. 所有原本产生 status = 4 的路径都应改为走T+2审核流程
2. 管理员强制完成异常订单应使用 status = 8 → status = 9 → status = 10 流程
3. 异常处理系统自动完成应使用 status = 8 → status = 9 → status = 10 流程
4. 订单模型的EndService方法应移除直接设置 status = 4 的逻辑
5. 所有相关代码中的 status = 4 判断逻辑应更新为 status = 10

### 需求4：统一结算服务状态处理

**用户故事：** 作为财务系统，我希望结算服务只处理经过完整审核流程的订单，确保分账的合规性。

#### 验收标准
1. 结算服务应只处理 status = 10 (已结算审核通过) 的订单
2. 移除结算服务中对 status = 4 的支持
3. 所有订单必须经过T+2审核才能进入结算流程
4. 结算逻辑应基于统一的订单完成状态
5. 历史 status = 4 的订单应迁移到新的状态体系

### 需求5：完善状态流转监控和日志

**用户故事：** 作为系统运维人员，我希望能够清楚地追踪每个订单的状态变更历史和原因。

#### 验收标准
1. 所有状态变更都应记录详细的操作日志
2. 日志应包含操作人、操作时间、变更原因、前后状态
3. 特殊状态变更（如直接到status=4）应有特别标记
4. 应提供状态流转的统计和监控面板
5. 异常状态流转应触发告警机制

## 技术约束

1. 必须保持现有订单数据的完整性
2. 状态变更必须支持事务处理
3. 旧接口的废弃应采用渐进式方式
4. 新的状态流转逻辑不应影响现有正常订单
5. 所有变更应支持回滚机制

## 验收标准

1. 订单完成流程统一，状态流转逻辑清晰
2. 旧接口成功废弃，新接口功能完整
3. status = 4 完全移除，状态流转统一
4. 结算服务只处理审核通过的订单
5. 状态变更日志完整，监控告警有效
6. 现有业务功能无回归问题
7. 系统性能无明显下降

## 优先级

- P0: 废弃旧的结束服务接口，统一完成流程
- P0: 修改结算服务支持多种状态
- P1: 完善状态流转监控和日志
- P1: 完全移除status=4的使用，统一状态流转
- P2: 前端代码迁移和兼容性处理