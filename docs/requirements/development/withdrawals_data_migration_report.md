# 提现数据迁移报告

## 迁移概述

**迁移时间**: 2024-12-20  
**迁移类型**: withdrawals_old → withdrawals  
**迁移环境**: 生产数据库  
**迁移状态**: ✅ 成功完成  

## 迁移背景

由于提现系统功能升级，需要将旧版 `withdrawals_old` 表的数据迁移到新版 `withdrawals` 表中。新表结构增加了微信支付相关字段，优化了数据结构和业务流程。

## 表结构对比

### withdrawals_old 表字段
- `id` - 主键
- `user_id` - 用户ID
- `withdraw_no` - 提现单号
- `amount` - 提现金额
- `method` - 提现方式 (1:微信 2:支付宝 3:银行卡)
- `account` - 账户信息
- `account_name` - 账户名称
- `bank_name` - 银行名称
- `status` - 状态
- `reject_reason` - 拒绝原因
- `audit_time` - 审核时间
- `auditor_id` - 审核员ID
- `pay_time` - 打款时间
- `payer_id` - 打款员ID
- `created_at`, `updated_at`, `deleted_at` - 时间戳

### withdrawals 表字段
- `id` - 主键
- `withdrawal_no` - 提现单号 (字段名变更)
- `user_id` - 用户ID
- `amount` - 提现金额
- `fee` - 手续费 (新增)
- `actual_amount` - 实际到账金额 (新增)
- `payment_method` - 支付方式 (字符串类型，新增)
- `openid` - 微信OpenID (新增)
- `real_name` - 真实姓名 (新增)
- `status` - 状态
- `apply_time` - 申请时间 (新增)
- `audit_time` - 审核时间
- `audit_admin_id` - 审核管理员ID (字段名变更)
- `audit_remark` - 审核备注 (新增)
- `pay_time` - 打款时间
- `pay_admin_id` - 打款管理员ID (字段名变更)
- `pay_remark` - 打款备注 (新增)
- `transfer_no` - 转账单号 (新增)
- `wechat_batch_no` - 微信批次号 (新增)
- `transfer_fail_reason` - 转账失败原因 (新增)
- `created_at`, `updated_at`, `deleted_at` - 时间戳

## 字段映射规则

| 源字段 (withdrawals_old) | 目标字段 (withdrawals) | 映射规则 |
|-------------------------|----------------------|----------|
| `withdraw_no` | `withdrawal_no` | 直接映射 |
| `user_id` | `user_id` | 直接映射 |
| `amount` | `amount` | 直接映射 |
| - | `fee` | 默认值 0.00 |
| `amount` | `actual_amount` | 等于申请金额 |
| `method` | `payment_method` | 1→'wechat', 2→'alipay', 3→'bank' |
| `account` | `openid` | 仅当method=1时映射 |
| `account_name` | `real_name` | 直接映射 |
| `status` | `status` | 直接映射 |
| `created_at` | `apply_time` | 直接映射 |
| `audit_time` | `audit_time` | 直接映射 |
| `auditor_id` | `audit_admin_id` | 直接映射 |
| `reject_reason` | `audit_remark` | 直接映射 |
| `pay_time` | `pay_time` | 直接映射 |
| `payer_id` | `pay_admin_id` | 直接映射 |
| - | `pay_remark` | NULL |
| - | `transfer_no` | NULL |
| - | `wechat_batch_no` | NULL |
| - | `transfer_fail_reason` | NULL |
| `created_at` | `created_at` | 直接映射 |
| `updated_at` | `updated_at` | 直接映射 |
| `deleted_at` | `deleted_at` | 直接映射 |

## 迁移执行过程

### 1. 数据统计
- **迁移前记录数**: 14条
- **迁移后记录数**: 14条
- **迁移成功率**: 100%

### 2. 迁移脚本
位置: `/scripts/data-migration/migrate_withdrawals_old_to_new.sql`

### 3. 执行结果
```sql
INSERT INTO withdrawals (...) SELECT ... FROM withdrawals_old
-- 结果: Records: 14  Duplicates: 0  Warnings: 0
```

## 数据验证

### 样本数据对比

**迁移前 (withdrawals_old)**:
```
ID: 1, withdraw_no: WD20240620001, amount: 100.00, method: 1, account: wx_123456
ID: 2, withdraw_no: WD20240620002, amount: 150.00, method: 2, account: ***********
```

**迁移后 (withdrawals)**:
```
ID: 1, withdrawal_no: WD20240620001, amount: 100.00, payment_method: wechat, openid: wx_123456
ID: 2, withdrawal_no: WD20240620002, amount: 150.00, payment_method: alipay, openid: null
```

### 数据完整性检查
- ✅ 所有记录成功迁移
- ✅ 字段映射正确
- ✅ 数据类型转换正确
- ✅ 业务逻辑保持一致

## 迁移后处理

### 已完成
1. ✅ 数据迁移执行
2. ✅ 数据验证
3. ✅ 迁移报告生成

### 待处理 (可选)
1. 🔄 备份 withdrawals_old 表
2. 🔄 删除 withdrawals_old 表 (需要业务确认)

## 风险评估

### 低风险
- 数据量小 (14条记录)
- 字段映射简单
- 有完整的回滚方案

### 注意事项
- 新增字段使用默认值或NULL
- 支付方式从数字转换为字符串
- OpenID仅对微信支付方式有效

## 回滚方案

如需回滚，可执行以下操作：

```sql
-- 删除迁移的数据
DELETE FROM withdrawals 
WHERE withdrawal_no IN (
    SELECT withdraw_no FROM withdrawals_old WHERE deleted_at IS NULL
);
```

## 总结

✅ **迁移成功完成**
- 所有14条记录成功从 `withdrawals_old` 迁移到 `withdrawals` 表
- 字段映射和数据转换正确
- 业务逻辑保持一致
- 数据完整性验证通过

迁移过程顺利，新的提现系统可以正常使用历史数据。