# 线上编译说明

## 背景
为了确保测试代码不会被部署到生产环境，我们使用 Go 的构建标签来隔离测试相关代码。

## 构建标签使用方式

测试相关的文件（如 `test_payment_handler.go` 和 `test_payment_router.go`）添加了以下构建标签：
```go
//go:build !production
// +build !production
```

这表示这些文件只在非 production 构建标签下才会被编译。

同时，我们在项目中采用了条件编译的方式实现函数：

1. `main.go` - 包含函数声明和默认空实现
2. `test_handler_dev.go` - 开发环境的完整实现（使用 `//go:build !production` 标签）
3. `main_prod.go` - 生产环境的空实现（使用 `//go:build production` 标签）

## 线上编译命令

在生产环境编译时，请使用以下命令：

```bash
cd backend
go build -tags 'production' -o backend main.go
```

## 开发环境编译命令

在开发环境中，可以正常使用以下命令进行编译：

```bash
cd backend
go build -o backend main.go
```

## CI/CD 集成建议

在 CI/CD 流程中，建议在生产环境部署步骤中明确使用生产构建标签：

```yaml
# 示例（以 GitHub Actions 为例）
- name: Build for production
  run: |
    cd backend
    go build -tags 'production' -o backend main.go
```

这样可以确保生产环境中不会包含任何测试代码。

## 文件结构说明

项目中与构建标签相关的文件：
- `internal/handler/test_payment_handler.go` - 测试支付处理器（非生产环境）
- `internal/router/test_payment_router.go` - 测试路由注册（非生产环境）
- `test_handler_dev.go` - 开发环境测试处理器初始化和路由注册
- `main_prod.go` - 生产环境的函数实现
- `main.go` - 默认实现和主程序入口