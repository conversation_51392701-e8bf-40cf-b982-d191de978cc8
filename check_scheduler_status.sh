#!/bin/bash

# 检查T+2调度器和相关API状态

BASE_URL="https://www.kanghuxing.cn"

echo "=== 检查T+2调度器状态 ==="

echo "1. 检查服务健康状态..."
curl -s "${BASE_URL}/health" | jq '.' 2>/dev/null || echo "健康检查失败"

echo -e "\n2. 检查API端点可用性..."

echo "2.1 管理员登录端点:"
curl -s -X POST "${BASE_URL}/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}' | head -1

echo -e "\n2.2 T+2审核触发端点:"
curl -s -X POST "${BASE_URL}/api/admin/reviews/trigger-settlement" \
  -H "Content-Type: application/json" | head -1

echo -e "\n2.3 待审核记录查询端点:"
curl -s "${BASE_URL}/api/admin/reviews/pending" | head -1

echo -e "\n2.4 过期审核记录查询端点:"
curl -s "${BASE_URL}/api/admin/reviews/expired" | head -1

echo -e "\n3. 检查数据库中的配置状态..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '自动分账配置' as config_type,
    config_key,
    config_value,
    is_enabled,
    CASE 
        WHEN config_key = 'auto_settlement_enabled' AND config_value = 'true' THEN '✅ 已开启'
        WHEN config_key = 'auto_settlement_enabled' AND config_value = 'false' THEN '❌ 已关闭'
        WHEN config_key = 'review_period_hours' THEN CONCAT('⏰ ', config_value, '小时')
        ELSE config_value
    END as status_text
FROM auto_settlement_configs 
WHERE config_key IN ('auto_settlement_enabled', 'review_period_hours', 'auto_review_threshold')
ORDER BY config_key;
"

echo -e "\n4. 检查待处理的审核记录..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '待处理审核记录' as record_type,
    osr.id,
    osr.order_id,
    osr.review_status,
    osr.review_deadline,
    TIMESTAMPDIFF(HOUR, osr.created_at, NOW()) as hours_since_created,
    CASE 
        WHEN osr.review_deadline < NOW() THEN '✅ 已过期，可触发结算'
        ELSE '⏳ 未过期'
    END as eligibility_status
FROM order_settlement_reviews osr
WHERE osr.review_status = 1
ORDER BY osr.created_at DESC
LIMIT 5;
"

echo -e "\n5. 检查目标订单的完整状态..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '订单ORD202507311203108006状态' as check_item,
    o.id as order_id,
    o.status as order_status,
    o.settlement_status,
    COALESCE(osr.review_status, 0) as t2_review_status,
    COALESCE(ai.status, 0) as income_status,
    CASE 
        WHEN o.status = 10 THEN '审核通过'
        WHEN o.status = 13 THEN '已结算'
        ELSE CONCAT('状态:', o.status)
    END as order_status_text,
    CASE 
        WHEN COALESCE(osr.review_status, 0) = 0 THEN '❌ 无T+2审核记录'
        WHEN osr.review_status = 1 THEN '⏳ 待审核'
        WHEN osr.review_status = 3 THEN '✅ 审核通过'
        ELSE CONCAT('状态:', osr.review_status)
    END as t2_review_text,
    CASE 
        WHEN COALESCE(ai.status, 0) = 1 THEN '⏳ 待结算'
        WHEN ai.status = 2 THEN '✅ 已结算'
        ELSE CONCAT('状态:', COALESCE(ai.status, 0))
    END as income_status_text
FROM orders o 
LEFT JOIN order_settlement_reviews osr ON o.id = osr.order_id
LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = 13
WHERE o.order_no = 'ORD202507311203108006';
"

echo -e "\n=== 状态检查完成 ==="
echo "如果看到 '401' 或 'Unauthorized'，说明端点存在但需要认证"
echo "如果看到 '404' 或 'page not found'，说明端点不存在或路径错误"
echo "如果T+2审核记录存在且已过期，可以尝试手动触发结算"