-- 修复生产环境自动结算问题
-- 问题：订单ORD202507311203108006审核已过期但未自动结算
-- 根因：自动分账开关被关闭 (is_enabled = 0)
-- 连接命令: mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod

-- 1. 检查问题订单的完整状态
SELECT '=== 问题订单状态 ===' as info;
SELECT
    o.id,
    o.order_no,
    o.status,
    o.settlement_status,
    o.amount,
    o.attendant_amount,
    o.actual_service_end_time,
    o.settlement_time
FROM orders o
WHERE o.order_no = 'ORD202507311203108006';

-- 2. 检查审核记录状态
SELECT '=== 审核记录状态 ===' as info;
SELECT
    osr.id,
    osr.order_id,
    osr.review_status,
    osr.review_deadline,
    osr.auto_review_eligible,
    TIMESTAMPDIFF(HOUR, osr.review_deadline, NOW()) as hours_overdue
FROM order_settlement_reviews osr
WHERE osr.order_id = 10017;

-- 3. 检查陪诊师收入记录
SELECT '=== 陪诊师收入记录 ===' as info;
SELECT
    ai.id,
    ai.attendant_id,
    ai.order_id,
    ai.amount,
    ai.status,
    ai.settlement_batch_id,
    ai.settle_time
FROM attendant_income ai
WHERE ai.order_id = 10017;

-- 4. 检查当前自动分账配置
SELECT '=== 自动分账配置 ===' as info;
SELECT
    id,
    config_key,
    config_value,
    is_enabled,
    update_reason,
    updated_at
FROM auto_settlement_configs
ORDER BY id;

-- 5. 修复：启用自动分账开关
SELECT '=== 启用自动分账开关 ===' as info;
UPDATE auto_settlement_configs
SET
    is_enabled = 1,
    update_reason = '修复订单ORD202507311203108006结算问题，启用自动分账',
    updated_at = NOW()
WHERE config_key = 'auto_settlement_enabled';

-- 6. 验证配置修改结果
SELECT '=== 配置修改结果 ===' as info;
SELECT
    id,
    config_key,
    config_value,
    is_enabled,
    update_reason,
    updated_at
FROM auto_settlement_configs
WHERE config_key = 'auto_settlement_enabled';

-- 7. 手动处理过期的审核记录
-- 将过期且符合自动审核条件的记录标记为已审核通过
SELECT '=== 处理过期审核记录 ===' as info;
UPDATE order_settlement_reviews
SET
    review_status = 2,  -- 审核通过
    reviewed_at = NOW(),
    updated_at = NOW()
WHERE order_id = 10017
  AND review_status = 1  -- 待审核
  AND review_deadline < NOW()  -- 已过期
  AND auto_review_eligible = 1;  -- 符合自动审核条件

-- 8. 验证审核记录更新结果
SELECT '=== 审核记录更新结果 ===' as info;
SELECT
    osr.id,
    osr.order_id,
    osr.review_status,
    osr.review_deadline,
    osr.reviewed_at,
    osr.auto_review_eligible
FROM order_settlement_reviews osr
WHERE osr.order_id = 10017;

-- 9. 创建结算批次并执行分账
-- 注意：这需要根据实际的结算逻辑来实现
SELECT '=== 创建结算批次 ===' as info;
SET @batch_id = CONCAT('BATCH_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'));

INSERT INTO settlement_batches (
    batch_id,
    batch_date,
    total_amount,
    total_orders,
    status,
    created_at,
    updated_at
) VALUES (
    @batch_id,
    CURDATE(),
    0.01,  -- 订单金额
    1,     -- 订单数量
    1,     -- 处理中状态
    NOW(),
    NOW()
);

-- 10. 更新陪诊师收入记录
SELECT '=== 更新陪诊师收入记录 ===' as info;
UPDATE attendant_income
SET
    settlement_batch_id = @batch_id,
    status = 2,  -- 已结算
    settle_time = NOW(),
    updated_at = NOW()
WHERE order_id = 10017;

-- 11. 更新订单结算状态
SELECT '=== 更新订单结算状态 ===' as info;
UPDATE orders
SET
    settlement_status = 1,  -- 已结算
    settlement_time = NOW(),
    updated_at = NOW()
WHERE id = 10017;

-- 12. 验证最终结果
SELECT '=== 最终验证结果 ===' as info;
SELECT
    o.order_no,
    o.status,
    o.settlement_status,
    o.settlement_time,
    ai.status as income_status,
    ai.settlement_batch_id,
    ai.settle_time,
    osr.review_status,
    osr.reviewed_at
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id
LEFT JOIN order_settlement_reviews osr ON o.id = osr.order_id
WHERE o.order_no = 'ORD202507311203108006';
