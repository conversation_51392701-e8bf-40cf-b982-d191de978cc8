-- 修复mock批次号数据的SQL脚本
-- 清理测试数据并重置转账状态，允许重新发起正确的转账

-- 1. 查看当前的mock数据情况
SELECT 
    '=== 当前mock数据情况 ===' as section,
    id,
    withdrawal_id,
    transfer_no,
    wechat_batch_no,
    status,
    fail_reason,
    retry_count,
    created_at
FROM withdrawal_transfers 
WHERE wechat_batch_no LIKE 'mock_batch_%';

-- 2. 重置mock批次号的转账记录状态
-- 将状态改为失败，清除mock批次号，允许重新发起转账
UPDATE withdrawal_transfers 
SET 
    status = 3,  -- 失败状态
    wechat_batch_no = NULL,  -- 清除mock批次号
    wechat_detail_id = NULL,  -- 清除mock详情ID
    wechat_status = NULL,  -- 清除微信状态
    fail_reason = '清理测试数据，允许重新发起转账',
    retry_count = LEAST(retry_count, 2),  -- 重置重试次数，确保可以重试
    updated_at = NOW()
WHERE wechat_batch_no LIKE 'mock_batch_%';

-- 3. 同时重置对应的提现申请状态
-- 将提现状态从转账失败改为审核通过，允许重新发起转账
UPDATE withdrawals w
INNER JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
SET 
    w.status = 2,  -- 审核通过状态
    w.transfer_no = NULL,  -- 清除转账单号
    w.transfer_time = NULL,  -- 清除转账时间
    w.operator_id = NULL,  -- 清除操作员ID
    w.updated_at = NOW()
WHERE wt.wechat_batch_no IS NULL 
  AND wt.fail_reason = '清理测试数据，允许重新发起转账';

-- 4. 验证修复结果
SELECT 
    '=== 修复后的数据状态 ===' as section,
    w.id as withdrawal_id,
    w.status as withdrawal_status,
    w.real_name,
    w.openid,
    wt.id as transfer_id,
    wt.status as transfer_status,
    wt.wechat_batch_no,
    wt.fail_reason,
    wt.retry_count
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.id = 1;

-- 5. 检查是否还有其他mock数据
SELECT 
    '=== 检查其他mock数据 ===' as section,
    COUNT(*) as remaining_mock_count
FROM withdrawal_transfers 
WHERE wechat_batch_no LIKE 'mock_batch_%';