#!/bin/bash

# 更新生产环境变量脚本
# 补充微信转账所需的缺失环境变量

echo "=== 更新生产环境变量 ==="

ENV_FILE="production.env"

# 检查文件是否存在
if [ ! -f "$ENV_FILE" ]; then
    echo "❌ 文件不存在: $ENV_FILE"
    exit 1
fi

echo "✅ 找到环境变量文件: $ENV_FILE"

# 备份原文件
backup_file="${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
cp "$ENV_FILE" "$backup_file"
echo "✅ 已备份原文件到: $backup_file"

# 1. 修改现有变量：启用自动证书下载模式
echo ""
echo "1. 修改自动证书下载模式..."
if grep -q "APP_WECHAT_USE_AUTO_CERT_MODE=false" "$ENV_FILE"; then
    sed -i 's/APP_WECHAT_USE_AUTO_CERT_MODE=false/APP_WECHAT_USE_AUTO_CERT_MODE=true/' "$ENV_FILE"
    echo "✅ 已启用自动证书下载模式"
else
    echo "⚠️  自动证书下载模式已启用或不存在"
fi

# 2. 检查并添加缺失的环境变量
echo ""
echo "2. 检查并添加缺失的环境变量..."

# 需要添加的变量列表
declare -A missing_vars=(
    ["WECHAT_CERT_SERIAL_NUMBER"]="3B2F1BB6FBF9CD4D2448AB6310720C15CD668247"
    ["INTERNAL_API_KEY"]="s137xrkVgGxJTX6gitt1DZ29j9tEZRF5iCOsPKIE"
    ["BACKEND_API_KEY_ID"]="admin-key-prod-001"
    ["BACKEND_API_SECRET_KEY"]="peizhen-backend-api-secret-key-2024-prod-32chars"
)

# 检查并添加缺失的变量
for var_name in "${!missing_vars[@]}"; do
    if grep -q "^${var_name}=" "$ENV_FILE"; then
        echo "✅ $var_name 已存在"
    else
        echo "${var_name}=${missing_vars[$var_name]}" >> "$ENV_FILE"
        echo "✅ 已添加 $var_name"
    fi
done

# 3. 添加转账配置补充（如果不存在）
echo ""
echo "3. 检查转账配置..."

# 转账相关变量
declare -A transfer_vars=(
    ["WECHAT_TRANSFER_APIV3_KEY"]="0C9821B2517645438B93B1B21CC62901"
    ["WECHAT_TRANSFER_APP_ID"]="wx2ce88b500238c799"
    ["WECHAT_TRANSFER_MCH_ID"]="1717184423"
)

for var_name in "${!transfer_vars[@]}"; do
    if grep -q "^${var_name}=" "$ENV_FILE"; then
        echo "✅ $var_name 已存在"
    else
        echo "${var_name}=${transfer_vars[$var_name]}" >> "$ENV_FILE"
        echo "✅ 已添加 $var_name"
    fi
done

# 4. 添加注释分隔符
echo ""
echo "4. 整理文件格式..."

# 检查是否已有补充标记
if ! grep -q "=== 补充的环境变量 ===" "$ENV_FILE"; then
    # 在文件末尾添加分隔符和说明
    cat >> "$ENV_FILE" << 'EOF'

# === 补充的环境变量（微信转账修复）===
# 以上变量由 update_production_env.sh 脚本自动添加
# 用于修复微信支付平台证书问题
EOF
    echo "✅ 已添加说明注释"
fi

# 5. 验证关键变量
echo ""
echo "5. 验证关键环境变量..."

required_vars=(
    "WECHAT_APP_ID"
    "WECHAT_MCH_ID"
    "WECHAT_API_V3_KEY"
    "WECHAT_CERT_SERIAL_NUMBER"
    "WECHAT_TRANSFER_PRIVATE_KEY_PATH"
    "WECHAT_TRANSFER_ENABLED"
    "APP_WECHAT_USE_AUTO_CERT_MODE"
)

all_present=true
for var in "${required_vars[@]}"; do
    if grep -q "^${var}=" "$ENV_FILE"; then
        value=$(grep "^${var}=" "$ENV_FILE" | cut -d'=' -f2-)
        case $var in
            *KEY*|*SECRET*)
                echo "✅ $var: ${value:0:8}****"
                ;;
            *)
                echo "✅ $var: $value"
                ;;
        esac
    else
        echo "❌ $var: 缺失"
        all_present=false
    fi
done

# 6. 显示更新摘要
echo ""
echo "=== 更新摘要 ==="
if [ "$all_present" = true ]; then
    echo "🎉 所有必要的环境变量都已配置"
else
    echo "⚠️  仍有部分环境变量缺失，请手动检查"
fi

echo ""
echo "📋 下一步操作："
echo "1. 检查更新后的环境变量文件"
echo "2. 重启后端服务以应用新配置"
echo "3. 测试微信转账功能"
echo ""
echo "🔄 重启命令："
echo "   sudo systemctl restart peizhen-backend"
echo ""
echo "📝 查看日志："
echo "   tail -f backend/logs/app.prod.log | grep -E '(证书|transfer|微信)'"

# 7. 显示文件差异
echo ""
echo "=== 文件变更对比 ==="
echo "原文件: $backup_file"
echo "新文件: $ENV_FILE"
echo ""
echo "主要变更："
diff "$backup_file" "$ENV_FILE" | grep '^>' | head -10