---
description: 
globs: admin/server/**/*.go,admin/web/**/*.js,admin/web/**/*.vue,admin/web/**/*.json,admin/web/**/*.scss,admin/web/**/*.html
alwaysApply: false
---
<!--
File pattern matches:
admin/server/**/*.go
admin/server/**/*.md
admin/web/**/*.js
admin/web/**/*.vue
admin/web/**/*.jsx
admin/web/**/*.ts
admin/web/**/*.tsx
admin/web/**/*.json
admin/web/**/*.css
admin/web/**/*.scss
admin/web/**/*.html
-->

# 管理后台开发规则

## 目录结构
### 后端目录结构
- /admin/server/model - 管理后台数据模型
- /admin/server/repository - 管理后台数据访问层
- /admin/server/service - 管理后台业务逻辑层
- /admin/server/handler - 管理后台API处理器
- /admin/server/middleware - 中间件
- /admin/server/utils - 工具函数
- /admin/server/router - 路由定义

### 前端目录结构
- /admin/web/src/api - 前端API调用
- /admin/web/src/views - 前端页面组件
- /admin/web/src/components - 可复用组件
- /admin/web/src/utils - 工具函数
- /admin/web/src/router - 路由定义
- /admin/web/src/store - 状态管理

## 后端开发规则
- 遵循与主后端相同的文件命名规范和架构设计
- 模型定义必须与主后端保持一致性，避免重复定义
- 接口定义文件必须使用`{entity}_repository.go`和`{entity}_service.go`格式命名
- 所有错误必须被显式处理，不允许使用`_`忽略错误返回值
- 控制器层必须对所有用户输入进行验证，防止非法数据进入业务逻辑

## 前端开发规则（Vue.js）

### 组件编写规则
- 组件名称必须使用PascalCase命名法（如UserProfile.vue）
- 使用v-for指令时必须提供:key属性，且key值应唯一
- 组件props必须明确定义类型和默认值/是否必须
- 避免在template中编写复杂条件表达式，应移至计算属性
- 不要在计算属性中进行异步操作，应使用methods或actions

### API调用规则
- API参数命名必须与后端DTO保持一致，避免混淆
- 所有API响应必须统一处理响应格式和错误情况
- API错误处理必须标准化，包括网络错误、业务错误等
- 修改API接口后必须测试所有相关功能点
- 所有API请求必须使用拦截器处理通用逻辑（如认证、刷新令牌）

### 组件设计规则
- 每个组件文件必须包含注释说明组件的功能和使用场景
- 复杂组件应拆分为多个子组件，保持单一职责
- props验证必须使用type属性指定类型，并设置default或required
- 自定义事件名称必须使用kebab-case命名法（如data-updated）
- 可复用组件应考虑使用插槽设计，提高灵活性

## 安全性规则
- 所有管理后台页面必须进行登录验证和权限控制
- 所有表单提交必须进行CSRF防护
- 密码必须使用安全算法加密存储
- 敏感操作必须有操作日志记录
- 用户操作权限必须在前后端都进行验证
