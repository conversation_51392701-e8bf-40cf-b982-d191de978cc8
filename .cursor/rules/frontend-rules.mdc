---
description: 
globs: 
alwaysApply: true
---
<!--
File pattern matches:
frontend/**/*.js
frontend/**/*.wxss
frontend/**/*.wxml
frontend/**/*.json
frontend/**/*.wxs
frontend/pages/**/*.js
frontend/components/**/*.js
frontend/utils/**/*.js
frontend/services/**/*.js
frontend/assets/**/*.wxss
-->

# 前端开发规则

## 目录结构
- /frontend/assets - 静态资源文件（图片、样式等）
- /frontend/components - 可复用组件
- /frontend/pages - 页面组件
- /frontend/services - API调用服务
- /frontend/utils - 工具函数

## 微信小程序规则

### WXML模板规则
- 列表渲染时必须使用`wx:key`属性提高渲染性能
- 所有`image`标签必须添加`lazy-load="true"`属性优化加载性能
- 模板文件中避免复杂的表达式逻辑，复杂逻辑应在JS中处理
- 内容较多时使用`wx:if`代替`hidden`属性提高性能
- 长列表应考虑实现虚拟列表或分页加载优化性能

### WXSS样式规则
- 布局尺寸必须使用`rpx`单位确保不同屏幕设备适配
- 避免使用`!important`强制覆盖样式
- 颜色值应使用项目预定义的变量，保持一致性
- 布局优先使用flex布局，避免使用float
- 遵循项目已有的样式命名规范

### JS逻辑规则
- 调用`Page()`前必须检查所有依赖是否正确引入
- 每个页面必须在`onUnload`中清理事件监听和定时器
- 所有API请求必须处理加载状态、成功状态和错误情况
- 数据请求必须使用`services/`目录下的统一方法
- 避免在`onShow`中执行重量级操作

### 性能优化规则
- 减少`setData`调用频率，避免频繁更新引起的性能问题
- 利用分包加载机制优化小程序首屏加载体验
- 所有图片资源必须经过压缩优化后再使用
- 避免过度使用计算属性或复杂的数据处理逻辑
- 长列表数据应实现懒加载或虚拟列表技术

## API调用规则
- API参数命名必须与后端DTO保持一致，避免混淆
- 所有API响应必须统一处理响应格式和错误情况
- API错误处理必须标准化，包括网络错误、业务错误等
- 修改API接口后必须测试所有相关功能点
- 所有API请求必须使用拦截器处理通用逻辑（如认证、刷新令牌）

## 组件开发规则
- 组件必须使用自定义组件优化性能
- 组件复用优先考虑已有组件，避免重复实现
- 每个组件必须有明确的职责和功能说明
- 复杂组件应拆分为多个子组件，保持单一职责
- 组件props必须明确定义类型和默认值/是否必须

## 安全性规则
- 所有用户输入必须经过验证，防止恶意攻击
- 敏感信息（如用户令牌）不应明文存储在本地
- 所有敏感操作必须进行适当的用户身份验证
- 敏感数据传输必须使用HTTPS协议
- 不要在小程序中存储敏感业务逻辑，应放在服务端处理
