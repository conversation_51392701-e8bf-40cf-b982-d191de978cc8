# 项目配置规则

## 配置文件规范

项目使用YAML格式的配置文件，而非.env文件。所有配置应遵循以下规范：

### 配置文件层次结构

```
/config
  /conf
    config.yaml         - 基础配置文件
    config.dev.yaml     - 开发环境特定配置
    config.test.yaml    - 测试环境特定配置
    config.prod.yaml    - 生产环境特定配置
```

### 配置加载优先级

1. 命令行参数（最高优先级）
2. 环境变量（APP_前缀）
3. 环境特定配置文件（如config.dev.yaml）
4. 基础配置文件（config.yaml）

## 关键配置项

### JWT配置
JWT密钥必须在YAML配置文件中设置，不应使用.env文件：

```yaml
# JWT配置
jwt:
  secret: peizhen-jwt-secret-key-2024-secure-and-long-enough-for-hmac-sha256-signing-32bytes
  expire: 168h
```

注意：
- `secret`必须是长度足够的安全字符串（至少32字节）
- `expire`是token的有效期，使用时间字符串格式（如72h, 7d）

### 数据库配置

```yaml
# 数据库配置
database:
  driver: "mysql"
  host: 127.0.0.1
  port: 3306
  database: carego
  username: care_go_dev
  password: 4F7ADD93-52BE-48D1-9E0E-0494020FE220
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s
  show_sql: true
```

### 微信配置

```yaml
# 微信配置
wechat:
  app_id: wxb2aad241de262891
  app_secret: 2d11ca7286ff31be9c252576f59c997a
```

## 配置修改规则

1. 新增配置项时，必须先在config.yaml中添加默认值
2. 修改配置项时，应确保所有环境的配置文件都得到相应更新
3. 敏感信息（如密钥、密码）不应提交到版本控制系统
4. 配置结构更改需同步更新config/config.go中的相应结构体

## 配置加载流程

项目使用Viper库加载配置，遵循以下流程：
1. 加载基础配置文件（config.yaml）
2. 根据APP_ENV环境变量加载环境特定配置文件
3. 从环境变量中加载配置（覆盖已有配置）
4. 应用命令行参数（如果有）

此流程在config/loader.go中实现，不应随意修改。

# 配置管理规则

## 配置文件规则
- 配置文件必须放在`config/conf`目录下
- 不同环境的配置必须使用不同的配置文件（如config.dev.yaml、config.prod.yaml）
- 配置文件中敏感信息（如密码、API密钥）应使用环境变量或加密存储
- 配置文件必须使用YAML格式，保持一致性
- 所有配置项必须有注释说明其用途和可能的取值范围

## 环境变量规则
- 环境变量名称必须使用大写字母和下划线，如`APP_ENV`、`DB_PASSWORD`
- 敏感信息优先使用环境变量而非配置文件存储
- 环境变量的使用必须有默认值处理，避免空值导致的问题
- 环境变量的使用必须在启动文档中明确说明
- 生产环境的环境变量设置应有明确的文档说明

## 配置加载规则
- 配置加载必须使用`backend/pkg/config`包提供的功能
- 配置加载失败必须提供明确的错误信息，方便排查
- 配置变更应支持热加载，无需重启应用
- 配置加载时必须验证必要配置项是否存在
- 应用启动时必须打印当前使用的配置环境（如dev、prod）

## 密钥管理规则
- 所有密钥和证书必须存放在`config/cert`目录下
- 密钥文件必须设置适当的访问权限，避免非授权访问
- 生产环境的密钥不允许提交到代码仓库
- 密钥更新必须有明确的流程和文档
- 测试环境和生产环境必须使用不同的密钥

## 最佳实践
- 所有配置项必须有合理的默认值
- 配置项变更必须在变更日志中记录
- 配置访问应封装在专门的服务中，避免直接访问配置对象
- 定期审计配置项，移除不再使用的配置
- 新增配置项必须更新相关文档 