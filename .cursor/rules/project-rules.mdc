---
description: 
globs: 
alwaysApply: true
---
<!--
File pattern matches:
**/*.go
**/*.js
**/*.vue
**/*.wxss
**/*.wxml
**/*.json
**/*.md
**/*.yaml
**/*.yml
**/*.sh
-->

# 项目通用规则

## 项目结构
- /frontend - 微信小程序前端 (遵循 [frontend-rules.mdc](mdc:frontend-rules.mdc) 和 [frontend-specific-rules.mdc](mdc:frontend/.cursor/rules/frontend-specific-rules.mdc))
- /backend - Go后端服务 (遵循 [backend-rules.mdc](mdc:backend-rules.mdc) 和 [backend-specific-rules.mdc](mdc:backend/.cursor/rules/backend-specific-rules.mdc))
- /admin/web - 管理后台前端(Vue.js) (遵循 [admin-rules.mdc](mdc:admin-rules.mdc) 和 [admin-web-specific-rules.mdc](mdc:admin/web/.cursor/rules/admin-web-specific-rules.mdc))
- /admin/server - 管理后台后端(Go) (遵循 [admin-rules.mdc](mdc:admin-rules.mdc) 和 [admin-server-specific-rules.mdc](mdc:admin/server/.cursor/rules/admin-server-specific-rules.mdc))

## 通用规则
- 文件名必须使用小写字母和下划线命名法（snake_case）
- 代码中不允许提交包含未解决的TODO或FIXME标记
- 源代码中不允许包含任何API密钥、密码等敏感凭证
- 提交代码前必须移除临时调试打印语句
- 所有提交必须经过相关测试验证通过

## 安全性规则
- 所有用户输入必须经过安全过滤和验证，防止注入攻击
- 密码必须使用bcrypt算法加密存储，禁止明文或简单加密
- 所有API请求必须通过权限验证中间件进行身份认证和授权
- 所有敏感操作（登录、修改权限等）必须添加详细日志记录
- 禁止使用全局变量存储敏感信息

## 开发流程规范
### 1. 需求分析与设计
- 明确功能需求和接口定义
- 设计数据模型和业务逻辑

### 2. API文档编写
- 使用Swagger/OpenAPI定义接口
- 包括请求参数、响应格式和错误码

### 3. 后端开发
- 遵循 [backend-rules.mdc](mdc:backend-rules.mdc) 和项目特定规则
- 模型定义(model) → DTO定义(dto) → 仓库接口(repository) → 仓库实现(repository/impl) → 服务接口(service) → 服务实现(service/impl) → 控制器(handler) → 路由注册(router)

### 4. 前端开发
- 遵循前端或管理后台前端规则
- API服务定义 → 页面组件开发 → 路由注册

### 5. 联调测试
- 先进行前端mock测试
- 后端单元测试
- 前后端联调验证功能完整性

## 代码修改验证机制
### 1. 模型修改规则
- 修改/删除模型字段前，使用grep搜索相关引用
- 关联关系修改需同步更新repository层代码
- 修改DTO字段时，确保前端调用参数一致

### 2. API接口修改规则
- 修改API路径或参数需同步更新swagger文档
- 修改请求/响应格式需通知前端开发人员
- 避免直接删除已发布的API，建议使用版本控制

### 3. 代码提交前检查
- 运行单元测试确保功能正常
- 手动验证修改的功能点
- 确保不破坏现有功能
