---
description: 
globs: 
alwaysApply: true
---
<!--
File pattern matches:
backend/**/*.go
backend/**/*.md
backend/api/**/*.json
backend/api/**/*.yaml
backend/cmd/**/*.go
backend/config/**/*.yaml
backend/config/**/*.json
backend/internal/**/*.go
backend/pkg/**/*.go
backend/test/**/*.go
-->

# 后端开发规则

## 目录结构
- /backend/internal/model - 数据模型定义
- /backend/internal/dto - 数据传输对象定义
- /backend/internal/repository - 数据访问层接口定义
- /backend/internal/repository/impl - 数据访问层实现
- /backend/internal/service - 业务逻辑层接口定义
- /backend/internal/service/impl - 业务逻辑层实现
- /backend/internal/handler - API处理器，处理HTTP请求
- /backend/internal/router - 路由定义
- /backend/internal/middleware - 中间件
- /backend/internal/utils - 工具函数

## 文件命名规范
- 接口定义文件必须使用`{entity}_repository.go`和`{entity}_service.go`格式命名
- 接口实现文件必须使用`{entity}_repository_impl.go`和`{entity}_service_impl.go`格式命名
- 接口命名必须为`I{Entity}Repository`和`I{Entity}Service`格式
- 实现结构体命名必须为小写开头的`{entity}Repository`和`{entity}Service`格式

## 错误处理和日志规则
- 所有错误必须被显式处理，不允许使用`_`忽略错误返回值
- 控制器层必须对所有用户输入进行验证，防止非法数据进入业务逻辑
- 所有数据库操作必须添加适当的超时控制，防止长时间阻塞
- 每个HTTP处理器必须记录请求信息、处理时间和响应状态
- 错误日志必须包含足够上下文信息，方便问题排查

## 模型定义规则
- 所有模型字段必须添加注释说明其用途和业务含义
- 模型字段名称必须使用驼峰命名法(CamelCase)，保持一致性
- 所有敏感字段（如密码）必须使用`json:"-"`标签防止序列化
- 模型之间的关联关系必须在结构体中明确定义（使用GORM标签）
- 修改现有模型前必须使用grep工具检查所有依赖该模型的代码
- 时间日期类型字段（如pay_time、payment_time等）在模型中必须定义为指针类型（*time.Time）而非直接类型（time.Time）

## 数据访问层规则
- 所有SQL查询必须使用参数化查询，禁止字符串拼接SQL语句
- 涉及多个表操作的复杂查询必须使用事务包装，确保数据一致性
- 每个实体必须实现标准的CRUD接口，保持代码结构统一
- 避免在循环中执行数据库操作，应使用批量操作提高性能
- 需要关联数据时优先使用Preload预加载，减少数据库查询次数

## API设计规则
- 所有API端点必须有详细的文档注释，说明功能和参数
- 请求参数结构体必须使用binding标签进行参数验证
- API响应必须使用统一的格式封装，包含状态码和数据/错误信息
- API路径必须使用资源名词而非动词，遵循RESTful设计理念
- 已发布的API不应直接删除，应标记为废弃并提供替代方案
- API文档必须包含详细的请求参数、响应格式和状态码说明

## 安全性规则
- 所有用户输入必须经过安全过滤和验证，防止注入攻击
- 密码必须使用bcrypt算法加密存储，禁止明文或简单加密
- 所有API请求必须通过权限验证中间件进行身份认证和授权
- 所有敏感操作（登录、修改权限等）必须添加详细日志记录
- 禁止使用全局变量存储敏感信息
