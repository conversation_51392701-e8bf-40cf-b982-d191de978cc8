# 添加详细的微信转账日志来确认API调用

## 🔍 问题分析

当前错误信息 "微信企业付款功能未启用" 可能出现在以下几个位置：

1. **简化客户端的 enabled 检查** (最可能)
2. **官方客户端的配置验证失败**
3. **微信API的真实返回**

## 📝 需要添加的日志点

### 1. 工厂类日志
在 `backend/pkg/wechat/transfer_factory.go` 中添加：

```go
// 在 GetClient 方法中添加
f.logger.Info("开始创建微信转账客户端",
    zap.String("client_type", transferConfig.ClientType),
    zap.Bool("enabled", transferConfig.Enabled),
    zap.Bool("mock_mode", transferConfig.MockMode))

switch transferConfig.ClientType {
case "official":
    f.logger.Info("尝试创建官方SDK客户端")
    var err error
    client, err = NewOfficialWechatTransferClientV2(transferConfig, f.logger)
    if err != nil {
        f.logger.Error("创建官方微信转账客户端失败，回退到简化客户端", 
            zap.Error(err),
            zap.String("error_detail", err.Error()))
        client = NewSimpleWechatTransferClient(transferConfig, f.logger)
    } else {
        f.logger.Info("官方SDK客户端创建成功")
    }
```

### 2. 简化客户端日志
在 `backend/pkg/wechat/simple_transfer_client.go` 的 Transfer 方法中：

```go
func (c *SimpleWechatTransferClient) Transfer(ctx context.Context, req *TransferRequest) (*TransferResponse, error) {
    c.logger.Info("简化客户端开始处理转账请求",
        zap.String("out_batch_no", req.OutBatchNo),
        zap.Int64("total_amount", req.TotalAmount),
        zap.Bool("enabled", c.config.Enabled),
        zap.Bool("mock_mode", c.config.MockMode),
        zap.String("environment", c.config.Environment))

    // 验证配置
    if !c.config.Enabled {
        c.logger.Error("微信企业付款功能被禁用",
            zap.Bool("enabled", c.config.Enabled),
            zap.String("client_type", "simple"))
        return nil, &TransferError{
            Code:    "FUNCTION_DISABLED",
            Message: "微信企业付款功能未启用",
            Detail:  "请在配置中启用企业付款功能",
        }
    }
    
    c.logger.Info("配置验证通过，继续处理转账")
    // ... 后续逻辑
}
```

### 3. 官方客户端日志
在 `backend/pkg/wechat/official_transfer_client_v2.go` 中：

```go
func NewOfficialWechatTransferClientV2(config *config.WechatTransferConfig, logger *zap.Logger) (IWechatTransferClient, error) {
    logger.Info("开始创建官方微信转账客户端V2",
        zap.String("mch_id", config.MchID),
        zap.String("app_id", config.AppID),
        zap.String("environment", config.Environment),
        zap.Bool("mock_mode", config.MockMode),
        zap.Bool("enabled", config.Enabled))

    // 如果是模拟模式，返回简化客户端
    if config.MockMode {
        logger.Info("检测到模拟模式，使用简化客户端")
        return NewSimpleWechatTransferClient(config, logger), nil
    }

    // 验证必要的配置
    logger.Info("开始验证官方客户端配置")
    if err := validateOfficialConfigV2(config); err != nil {
        logger.Error("官方客户端配置验证失败", zap.Error(err))
        return nil, fmt.Errorf("配置验证失败: %w", err)
    }
    logger.Info("配置验证通过")

    // ... 后续逻辑
}
```

## 🚀 临时调试方案

如果不想修改代码，可以通过以下方式确认：

### 方案1: 检查当前使用的客户端类型
```bash
# 在服务器上临时添加环境变量来启用详细日志
echo "LOG_LEVEL=debug" >> /etc/peizhen/production.env
sudo systemctl restart your-service

# 然后测试转账并查看日志
tail -f /var/www/html/peizhen/logs/app.prod.log | grep -E "(微信|wechat|transfer)" -i
```

### 方案2: 临时修改为模拟模式测试
```bash
# 备份当前配置
cp /etc/peizhen/production.env /etc/peizhen/production.env.backup

# 临时启用模拟模式
echo "WECHAT_TRANSFER_MOCK_MODE=true" >> /etc/peizhen/production.env
sudo systemctl restart your-service

# 测试转账，看是否能正常工作
# 如果模拟模式正常，说明问题在于真实API调用
```

### 方案3: 检查配置加载情况
```bash
# 检查进程的环境变量
ps aux | grep peizhen
# 假设进程ID是1234
cat /proc/1234/environ | tr '\0' '\n' | grep WECHAT_TRANSFER
```

## 📊 预期的调用流程

正常情况下应该看到以下日志序列：

1. **工厂类**: "开始创建微信转账客户端"
2. **官方客户端**: "开始创建官方微信转账客户端V2"
3. **配置验证**: "开始验证官方客户端配置"
4. **API调用**: "发起微信企业付款"
5. **API响应**: "微信企业付款API调用成功/失败"

如果在第2步就失败了，会看到：
- "创建官方微信转账客户端失败，回退到简化客户端"
- "简化客户端开始处理转账请求"
- "微信企业付款功能被禁用" (如果 enabled=false)

## 🎯 立即行动

请先运行以下命令确认当前的调用情况：

```bash
# 1. 重启服务并实时监控日志
sudo systemctl restart your-service
tail -f /var/www/html/peizhen/logs/app.prod.log &

# 2. 触发转账操作
# (在管理后台点击确认打款)

# 3. 查看日志中是否有客户端创建相关的信息
```

这样我们就能确认到底是在哪个环节失败的，以及是否真的调用了微信API。