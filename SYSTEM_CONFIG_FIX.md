# 系统配置管理页面数据不显示问题分析与修复方案

## 问题描述
系统配置管理页面无法显示配置数据，虽然后台数据库中存在64条配置记录，但前端页面显示为空。

## 问题分析

### 1. 数据模型不匹配
- **数据库表结构** (`system_configs`表):
  - 字段名: `key`, `value`, `value_type`, `category`, `name`, `description`, `is_system`, `status`

- **后台管理系统模型** (`admin/server/model/system_config.go`):
  - 字段名: `config_key`, `config_value`, `value_type`, `category`, `description`, `is_editable`, `is_visible`, `status`, `sort_order`

- **前端页面期望字段** (`admin/web/src/views/system/config.vue`):
  - 需要字段: `id`, `key`, `value`, `value_type`, `category`, `name`, `description`, `is_system`, `status`

### 2. 根本原因
后台管理系统使用了与主系统不同的系统配置模型，导致无法正确从数据库读取和展示数据。

## 修复方案

### 方案一：修改后台管理系统的Repository实现（推荐）

修改 `/admin/server/repository/impl/system_config_repository_impl.go` 文件，使其能够正确映射数据库字段到后台管理系统的模型字段。

需要修改以下方法：
1. `GetList` - 正确映射数据库字段到模型字段
2. `GetByID` - 正确映射数据库字段到模型字段
3. `GetByKey` - 正确映射数据库字段到模型字段

### 方案二：统一数据模型
修改后台管理系统的模型，使其与主系统保持一致，但这需要修改大量相关代码。

## 实施步骤

1. 修改后台管理系统的Repository实现，添加字段映射逻辑
2. 测试API接口，确保返回数据格式与前端期望一致
3. 验证前端页面能够正确显示配置数据
4. 测试配置的增删改查功能

## 验证方法

1. 使用curl或Postman测试API接口：
   ```bash
   curl -X GET "http://localhost:8080/api/admin/system/configs" -H "Authorization: Bearer [token]"
   ```

2. 检查返回数据格式是否包含以下字段：
   - `id`
   - `key`
   - `value`
   - `value_type`
   - `category`
   - `name`
   - `description`
   - `is_system`
   - `status`

3. 验证前端页面是否能正确显示配置数据