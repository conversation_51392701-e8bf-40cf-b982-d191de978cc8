#!/bin/bash

# 诊断微信证书访问问题
# 分析为什么证书文件存在但应用无法访问

echo "🔍 诊断微信证书访问问题"
echo "=========================="

# 1. 检查证书文件详细信息
echo -e "\n1. 检查证书文件详细信息："

cert_files=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem"
    "/etc/peizhen/certs/wechat/apiclient_cert.pem"
)

for cert_file in "${cert_files[@]}"; do
    echo -e "\n   📄 $cert_file:"
    if [ -f "$cert_file" ]; then
        echo "      存在: ✅"
        echo "      详细信息: $(ls -la "$cert_file")"
        echo "      文件大小: $(du -h "$cert_file" | cut -f1)"
        echo "      文件类型: $(file "$cert_file")"
        
        # 检查文件内容格式
        if head -1 "$cert_file" | grep -q "BEGIN"; then
            echo "      格式检查: ✅ PEM格式"
            echo "      开始行: $(head -1 "$cert_file")"
        else
            echo "      格式检查: ❌ 非标准PEM格式"
        fi
        
        # 检查文件权限
        perm=$(stat -c "%a" "$cert_file")
        owner=$(stat -c "%U:%G" "$cert_file")
        echo "      权限: $perm ($owner)"
        
        # 检查当前用户是否可读
        if [ -r "$cert_file" ]; then
            echo "      当前用户可读: ✅"
        else
            echo "      当前用户可读: ❌"
        fi
    else
        echo "      存在: ❌"
    fi
done

# 2. 检查应用运行用户
echo -e "\n2. 检查应用运行用户："

# 检查systemd服务配置
services=("peizhen-backend" "peizhen-admin")
for service in "${services[@]}"; do
    echo -e "\n   🔧 $service 服务:"
    if systemctl is-active --quiet "$service"; then
        echo "      状态: 运行中 ✅"
        
        # 获取进程信息
        pid=$(systemctl show --property MainPID --value "$service")
        if [ "$pid" != "0" ] && [ -n "$pid" ]; then
            user=$(ps -o user= -p "$pid" 2>/dev/null)
            echo "      运行用户: $user"
            echo "      进程ID: $pid"
            
            # 检查进程对证书文件的访问权限
            echo "      证书文件访问权限:"
            for cert_file in "${cert_files[@]}"; do
                if [ -f "$cert_file" ]; then
                    if sudo -u "$user" test -r "$cert_file" 2>/dev/null; then
                        echo "        $cert_file: ✅ 可读"
                    else
                        echo "        $cert_file: ❌ 不可读"
                    fi
                fi
            done
        else
            echo "      运行用户: 无法获取"
        fi
    else
        echo "      状态: 未运行 ❌"
    fi
done

# 3. 检查环境变量配置
echo -e "\n3. 检查环境变量配置："

env_files=("production.env" "admin.production.env")
for env_file in "${env_files[@]}"; do
    if [ -f "$env_file" ]; then
        echo -e "\n   📋 $env_file:"
        echo "      WECHAT_TRANSFER_CLIENT_TYPE: $(grep WECHAT_TRANSFER_CLIENT_TYPE "$env_file" | cut -d'=' -f2)"
        echo "      WECHAT_PRIVATE_KEY_PATH: $(grep WECHAT_PRIVATE_KEY_PATH "$env_file" | cut -d'=' -f2)"
        echo "      WECHAT_PUBLIC_KEY_PATH: $(grep WECHAT_PUBLIC_KEY_PATH "$env_file" | cut -d'=' -f2)"
        echo "      WECHAT_CERT_FILE_PATH: $(grep WECHAT_CERT_FILE_PATH "$env_file" | cut -d'=' -f2)"
    fi
done

# 4. 检查目录权限
echo -e "\n4. 检查目录权限："

dirs=("/etc/peizhen" "/etc/peizhen/certs" "/etc/peizhen/certs/wechat")
for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "   📁 $dir: $(ls -ld "$dir")"
    else
        echo "   📁 $dir: 不存在 ❌"
    fi
done

# 5. 生成修复建议
echo -e "\n5. 修复建议："
echo "=================================="

# 检查是否需要修复权限
need_fix_permission=false
for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        owner=$(stat -c "%U" "$cert_file")
        if [ "$owner" != "www-data" ]; then
            need_fix_permission=true
            break
        fi
    fi
done

if [ "$need_fix_permission" = true ]; then
    echo "🔧 权限修复命令："
    echo "   # 修复证书文件所有者为www-data（与应用进程用户一致）"
    for cert_file in "${cert_files[@]}"; do
        if [ -f "$cert_file" ]; then
            echo "   sudo chown www-data:www-data $cert_file"
        fi
    done
    echo ""
    echo "   # 设置适当的权限"
    for cert_file in "${cert_files[@]}"; do
        if [ -f "$cert_file" ]; then
            echo "   sudo chmod 600 $cert_file"
        fi
    done
fi

echo ""
echo "🔧 环境变量修复："
echo "   确保 production.env 中的配置正确："
echo "   WECHAT_TRANSFER_CLIENT_TYPE=official"
echo "   WECHAT_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem"
echo "   WECHAT_PUBLIC_KEY_PATH=/etc/peizhen/certs/wechat/wechat_public_key.pem"

echo ""
echo "🔧 服务重启："
echo "   sudo systemctl restart peizhen-backend"
echo "   sudo systemctl restart peizhen-admin"

echo ""
echo "🧪 测试验证："
echo "   运行测试脚本验证修复结果："
echo "   go run test_wechat_transfer_client_type.go"

echo -e "\n✅ 诊断完成！"