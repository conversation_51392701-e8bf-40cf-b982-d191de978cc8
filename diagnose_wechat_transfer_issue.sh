#!/bin/bash

echo "=== 微信企业付款功能诊断 ==="

echo "1. 检查当前环境变量状态："
echo "   WECHAT_TRANSFER_CLIENT_TYPE: ${WECHAT_TRANSFER_CLIENT_TYPE:-未设置}"
echo "   WECHAT_TRANSFER_APP_ID: ${WECHAT_TRANSFER_APP_ID:-未设置}"
echo "   WECHAT_TRANSFER_MCH_ID: ${WECHAT_TRANSFER_MCH_ID:-未设置}"
echo "   WECHAT_TRANSFER_ENVIRONMENT: ${WECHAT_TRANSFER_ENVIRONMENT:-未设置}"

echo -e "\n2. 检查production.env文件："
if [ -f "production.env" ]; then
    echo "   ✅ production.env 文件存在"
    echo "   微信转账相关配置："
    grep "WECHAT_TRANSFER" production.env | head -5
else
    echo "   ❌ production.env 文件不存在"
fi

echo -e "\n3. 检查应用配置文件："
if [ -f "backend/config/config.yaml" ]; then
    echo "   ✅ config.yaml 文件存在"
    echo "   转账配置片段："
    grep -A 5 "transfer:" backend/config/config.yaml
else
    echo "   ❌ config.yaml 文件不存在"
fi

echo -e "\n4. 可能的问题原因："
echo "   ❌ 服务器环境变量未加载 production.env"
echo "   ❌ 应用服务未重启"
echo "   ❌ 环境变量配置路径错误"
echo "   ❌ 配置文件格式问题"

echo -e "\n5. 解决方案："
echo "   1️⃣ 确保服务器加载 production.env："
echo "      source production.env"
echo "      export \$(cat production.env | xargs)"
echo ""
echo "   2️⃣ 重启应用服务："
echo "      sudo systemctl restart your-app-service"
echo "      # 或者"
echo "      docker restart your-app-container"
echo ""
echo "   3️⃣ 验证环境变量："
echo "      env | grep WECHAT_TRANSFER"
echo ""
echo "   4️⃣ 检查应用日志："
echo "      tail -f logs/app.log | grep -E '(官方SDK|简化客户端|FUNCTION_DISABLED)'"

echo -e "\n=== 诊断完成 ==="