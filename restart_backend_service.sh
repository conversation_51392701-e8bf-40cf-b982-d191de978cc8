#!/bin/bash

echo "=== 重启后端服务脚本 ==="

# 环境变量文件路径
ENV_FILE="/etc/peizhen/production.env"

echo "1. 加载环境变量"
if [ -f "$ENV_FILE" ]; then
    echo "   正在加载: $ENV_FILE"
    set -a
    source $ENV_FILE
    set +a
    echo "   ✅ 环境变量已加载"
else
    echo "   ❌ 环境变量文件不存在: $ENV_FILE"
    exit 1
fi

echo -e "\n2. 查找后端服务"
# 可能的服务名称
service_names=("peizhen-backend" "backend" "peizhen" "go-app")
service_found=""

for service in "${service_names[@]}"; do
    if systemctl list-units --full -all | grep -Fq "$service.service"; then
        echo "   ✅ 找到服务: $service"
        service_found="$service"
        break
    fi
done

if [ -z "$service_found" ]; then
    echo "   ⚠️  未找到标准systemd服务"
    echo "   请手动重启服务或提供正确的服务名称"
    
    # 尝试查找Go进程
    go_pids=$(pgrep -f "go.*main" 2>/dev/null || pgrep -f "peizhen" 2>/dev/null)
    if [ -n "$go_pids" ]; then
        echo "   找到Go进程: $go_pids"
        echo "   可以手动重启这些进程"
    fi
    exit 1
fi

echo -e "\n3. 重启服务"
echo "   正在重启服务: $service_found"

# 停止服务
sudo systemctl stop "$service_found"
echo "   ✅ 服务已停止"

# 等待一下确保完全停止
sleep 2

# 启动服务
sudo systemctl start "$service_found"
echo "   ✅ 服务已启动"

# 检查服务状态
sleep 3
if systemctl is-active --quiet "$service_found"; then
    echo "   ✅ 服务运行正常"
    echo "   状态: $(systemctl is-active $service_found)"
else
    echo "   ❌ 服务启动失败"
    echo "   状态: $(systemctl is-active $service_found)"
    echo "   请检查服务日志: sudo journalctl -u $service_found -f"
    exit 1
fi

echo -e "\n4. 验证环境变量加载"
# 等待服务完全启动
sleep 5

# 查找新的Go进程
new_go_pids=$(pgrep -f "go.*main" 2>/dev/null || pgrep -f "peizhen" 2>/dev/null)
if [ -n "$new_go_pids" ]; then
    first_pid=$(echo $new_go_pids | cut -d' ' -f1)
    if [ -f "/proc/$first_pid/environ" ]; then
        wechat_env_count=$(tr '\0' '\n' < /proc/$first_pid/environ | grep -c WECHAT_TRANSFER 2>/dev/null || echo "0")
        if [ "$wechat_env_count" -gt 0 ]; then
            echo "   ✅ 新进程已加载微信转账环境变量: $wechat_env_count 个"
        else
            echo "   ❌ 新进程未加载微信转账环境变量"
            echo "   可能需要检查服务启动脚本"
        fi
    fi
else
    echo "   ⚠️  未找到新的Go进程"
fi

echo -e "\n5. 测试建议"
echo "   1️⃣ 检查应用日志:"
echo "      tail -f /var/log/peizhen/app.log | grep -E '(官方SDK|微信企业付款)'"
echo ""
echo "   2️⃣ 测试确认打款功能:"
echo "      在管理后台点击'确认打款'按钮"
echo ""
echo "   3️⃣ 预期看到的日志:"
echo "      - '使用官方SDK客户端 V2'"
echo "      - '发起微信企业付款'"
echo "      - '微信企业付款API调用成功'"

echo -e "\n=== 重启完成 ==="