package wechat

import (
	"context"
	"crypto/rsa"
	"fmt"
	"os"
	"time"

	"github.com/gemeijie/peizhen/backend/config"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/transferbatch"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"go.uber.org/zap"
)

// OfficialWechatTransferClientV2 官方微信企业付款客户端 V2
// 修复了API结构问题的版本
type OfficialWechatTransferClientV2 struct {
	config *config.WechatTransferConfig
	client *core.Client
	logger *zap.Logger
}

// NewOfficialWechatTransferClientV2 创建官方微信企业付款客户端 V2
func NewOfficialWechatTransferClientV2(config *config.WechatTransferConfig, logger *zap.Logger) (IWechatTransferClient, error) {
	logger.Info("开始创建官方微信转账客户端V2",
		zap.String("mch_id", config.MchID),
		zap.String("app_id", config.AppID),
		zap.String("environment", config.Environment),
		zap.Bool("mock_mode", config.MockMode),
		zap.Bool("enabled", config.Enabled))

	// 如果是模拟模式，返回简化客户端
	if config.MockMode {
		logger.Info("检测到模拟模式，使用简化客户端")
		return NewSimpleWechatTransferClient(config, logger), nil
	}

	// 验证必要的配置
	logger.Info("开始验证官方客户端配置")
	if err := validateOfficialConfigV2(config); err != nil {
		logger.Error("官方客户端配置验证失败", zap.Error(err))
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}
	logger.Info("官方客户端配置验证通过")

	// 兼容性处理：获取证书序列号
	certificateSerialNumber := config.CertificateSerialNumber
	if certificateSerialNumber == "" {
		certificateSerialNumber = config.SerialNo // 兼容旧配置
	}

	// 兼容性处理：获取私钥路径
	privateKeyPath := config.PrivateKeyPath
	if privateKeyPath == "" {
		privateKeyPath = config.PrivateKeyFile // 兼容旧配置
	}

	// 🔧 修复：加载商户私钥，增加详细的错误信息
	logger.Info("开始加载商户私钥", zap.String("private_key_path", privateKeyPath))
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(privateKeyPath)
	if err != nil {
		logger.Error("加载商户私钥失败",
			zap.String("private_key_path", privateKeyPath),
			zap.Error(err))
		return nil, fmt.Errorf("加载商户私钥失败: %w", err)
	}
	logger.Info("商户私钥加载成功")

	ctx := context.Background()

	// 🔧 修复：检查是否启用公钥模式
	usePublicKeyMode := os.Getenv("APP_WECHAT_USE_PUBLIC_KEY_MODE") == "true"

	var opts []core.ClientOption

	if usePublicKeyMode {
		logger.Info("使用公钥模式初始化微信支付客户端",
			zap.String("mch_id", config.MchID),
			zap.String("certificate_serial_number", certificateSerialNumber))

		// 加载微信支付公钥
		publicKey, publicKeyID, err := loadWechatPayPublicKey(logger)
		if err != nil {
			logger.Error("加载微信支付公钥失败", zap.Error(err))
			return nil, fmt.Errorf("加载微信支付公钥失败: %w", err)
		}

		opts = []core.ClientOption{
			option.WithWechatPayPublicKeyAuthCipher(
				config.MchID,
				certificateSerialNumber,
				mchPrivateKey,
				publicKeyID,
				publicKey,
			),
		}
	} else {
		logger.Info("使用自动证书模式初始化微信支付客户端",
			zap.String("mch_id", config.MchID),
			zap.String("certificate_serial_number", certificateSerialNumber))

		opts = []core.ClientOption{
			option.WithWechatPayAutoAuthCipher(
				config.MchID,
				certificateSerialNumber,
				mchPrivateKey,
				config.APIv3Key,
			),
		}
	}

	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		logger.Error("初始化微信支付客户端失败", zap.Error(err))
		return nil, fmt.Errorf("初始化微信支付客户端失败: %w", err)
	}

	logger.Info("微信支付客户端初始化成功（公钥验证模式）")

	return &OfficialWechatTransferClientV2{
		config: config,
		client: client,
		logger: logger,
	}, nil
}

// loadWechatPayPublicKey 加载微信支付公钥
func loadWechatPayPublicKey(logger *zap.Logger) (*rsa.PublicKey, string, error) {
	// 从环境变量获取公钥配置
	publicKeyPath := os.Getenv("WECHAT_PUBLIC_KEY_PATH")
	publicKeyID := os.Getenv("WECHAT_PUBLIC_KEY_ID")

	if publicKeyPath == "" {
		return nil, "", fmt.Errorf("微信支付公钥路径未配置，请设置 WECHAT_PUBLIC_KEY_PATH 环境变量")
	}

	if publicKeyID == "" {
		return nil, "", fmt.Errorf("微信支付公钥ID未配置，请设置 WECHAT_PUBLIC_KEY_ID 环境变量")
	}

	logger.Info("开始加载微信支付公钥",
		zap.String("public_key_path", publicKeyPath),
		zap.String("public_key_id", publicKeyID))

	// 检查公钥文件是否存在
	if _, err := os.Stat(publicKeyPath); err != nil {
		return nil, "", fmt.Errorf("微信支付公钥文件不存在: %s, 错误: %v", publicKeyPath, err)
	}

	// 使用 utils.LoadPublicKeyWithPath 直接从文件路径加载
	publicKey, err := utils.LoadPublicKeyWithPath(publicKeyPath)
	if err != nil {
		return nil, "", fmt.Errorf("加载微信支付公钥失败: %v", err)
	}

	logger.Info("微信支付公钥加载成功",
		zap.String("public_key_id", publicKeyID),
		zap.String("public_key_path", publicKeyPath))

	return publicKey, publicKeyID, nil
}

// Transfer 发起批量转账
func (c *OfficialWechatTransferClientV2) Transfer(ctx context.Context, req *TransferRequest) (*TransferResponse, error) {
	c.logger.Info("发起微信企业付款",
		zap.String("out_batch_no", req.OutBatchNo),
		zap.Int64("total_amount", req.TotalAmount),
		zap.Int("total_num", req.TotalNum))

	// 验证配置
	if !c.config.Enabled {
		return nil, &TransferError{
			Code:    "FUNCTION_DISABLED",
			Message: "微信企业付款功能未启用",
			Detail:  "请在配置中启用企业付款功能",
		}
	}

	// 验证请求参数
	if err := c.validateTransferRequestV2(req); err != nil {
		return nil, err
	}

	// 🔧 修复：构建转账明细列表，处理姓名加密
	transferDetailList := make([]transferbatch.TransferDetailInput, 0, len(req.TransferDetails))
	for i, detail := range req.TransferDetails {
		c.logger.Info("构建转账明细",
			zap.Int("detail_index", i),
			zap.String("out_detail_no", detail.OutDetailNo),
			zap.String("openid", detail.OpenID),
			zap.String("user_name", detail.UserName),
			zap.Int64("transfer_amount", detail.TransferAmount))

		// 🔧 修复：对姓名进行加密处理（如果需要的话）
		userName := detail.UserName
		if userName == "" {
			c.logger.Error("转账明细中的收款人姓名为空",
				zap.Int("detail_index", i),
				zap.String("out_detail_no", detail.OutDetailNo))
			return nil, &TransferError{
				Code:    "PARAM_ERROR",
				Message: "收款用户姓名不能为空",
				Detail:  fmt.Sprintf("第%d条明细的UserName为空", i+1),
			}
		}

		transferDetailList = append(transferDetailList, transferbatch.TransferDetailInput{
			OutDetailNo:    core.String(detail.OutDetailNo),
			TransferAmount: core.Int64(detail.TransferAmount),
			TransferRemark: core.String(detail.TransferRemark),
			Openid:         core.String(detail.OpenID),
			UserName:       core.String(userName), // 🔧 使用处理后的姓名
		})
	}

	// 创建转账服务
	svc := transferbatch.TransferBatchApiService{Client: c.client}

	// 🔧 修复：发起批量转账，使用官方推荐的参数
	c.logger.Info("开始调用微信企业付款API",
		zap.String("out_batch_no", req.OutBatchNo),
		zap.Int64("total_amount", req.TotalAmount),
		zap.String("app_id", c.config.AppID),
		zap.String("mch_id", c.config.MchID),
		zap.Int("detail_count", len(transferDetailList)))

	resp, result, err := svc.InitiateBatchTransfer(ctx, transferbatch.InitiateBatchTransferRequest{
		Appid:              core.String(c.config.AppID),
		OutBatchNo:         core.String(req.OutBatchNo),
		BatchName:          core.String(req.BatchName),
		BatchRemark:        core.String(req.BatchRemark),
		TotalAmount:        core.Int64(req.TotalAmount),
		TotalNum:           core.Int64(int64(req.TotalNum)),
		TransferDetailList: transferDetailList,
		TransferSceneId:    core.String("1000"), // 🔧 必须的场景ID
	})

	if err != nil {
		c.logger.Error("微信企业付款API调用失败",
			zap.String("out_batch_no", req.OutBatchNo),
			zap.Error(err),
			zap.String("error_detail", err.Error()))
		return nil, c.convertErrorV2(err)
	}

	c.logger.Info("微信企业付款API调用成功",
		zap.String("out_batch_no", req.OutBatchNo),
		zap.Int("status_code", result.Response.StatusCode),
		zap.String("batch_id", c.getStringValueV2(resp.BatchId)))

	// 转换响应
	return c.convertTransferResponseV2(resp), nil
}

// QueryTransfer 查询批量转账
func (c *OfficialWechatTransferClientV2) QueryTransfer(ctx context.Context, req *TransferQueryRequest) (*TransferResponse, error) {
	c.logger.Info("查询微信企业付款状态",
		zap.String("out_batch_no", req.OutBatchNo),
		zap.String("batch_id", req.BatchID))

	// 验证参数
	if req.OutBatchNo == "" && req.BatchID == "" {
		return nil, &TransferError{
			Code:    "PARAM_ERROR",
			Message: "批次号和微信批次号不能同时为空",
			Detail:  "至少需要提供一个查询条件",
		}
	}

	// 创建转账服务
	svc := transferbatch.TransferBatchApiService{Client: c.client}

	// 使用商户批次号查询（更常用）
	if req.OutBatchNo != "" {
		resp, result, err := svc.GetTransferBatchByOutNo(ctx, transferbatch.GetTransferBatchByOutNoRequest{
			OutBatchNo:      core.String(req.OutBatchNo),
			NeedQueryDetail: core.Bool(req.NeedQueryDetail),
			Offset:          core.Int64(int64(req.Offset)),
			Limit:           core.Int64(int64(req.Limit)),
			DetailStatus:    core.String(req.DetailStatus),
		})

		if err != nil {
			c.logger.Error("查询微信企业付款状态失败",
				zap.String("out_batch_no", req.OutBatchNo),
				zap.Error(err))
			return nil, c.convertErrorV2(err)
		}

		c.logger.Info("查询微信企业付款状态成功",
			zap.String("out_batch_no", req.OutBatchNo),
			zap.Int("status_code", result.Response.StatusCode))

		return c.convertQueryResponseV2(resp), nil
	}

	// 使用微信批次号查询
	resp, result, err := svc.GetTransferBatchByNo(ctx, transferbatch.GetTransferBatchByNoRequest{
		BatchId:         core.String(req.BatchID),
		NeedQueryDetail: core.Bool(req.NeedQueryDetail),
		Offset:          core.Int64(int64(req.Offset)),
		Limit:           core.Int64(int64(req.Limit)),
		DetailStatus:    core.String(req.DetailStatus),
	})

	if err != nil {
		c.logger.Error("查询微信企业付款状态失败",
			zap.String("batch_id", req.BatchID),
			zap.Error(err))
		return nil, c.convertErrorV2(err)
	}

	c.logger.Info("查询微信企业付款状态成功",
		zap.String("batch_id", req.BatchID),
		zap.Int("status_code", result.Response.StatusCode))

	return c.convertQueryResponseV2(resp), nil
}

// QueryTransferDetail 查询转账明细
func (c *OfficialWechatTransferClientV2) QueryTransferDetail(ctx context.Context, req *TransferDetailQueryRequest) (*TransferDetailResponse, error) {
	c.logger.Info("查询微信企业付款明细",
		zap.String("out_batch_no", req.OutBatchNo),
		zap.String("out_detail_no", req.OutDetailNo))

	// 验证参数
	if req.OutDetailNo == "" && req.DetailID == "" {
		return nil, &TransferError{
			Code:    "PARAM_ERROR",
			Message: "明细号和微信明细号不能同时为空",
			Detail:  "至少需要提供一个查询条件",
		}
	}

	// 创建转账明细服务
	svc := transferbatch.TransferDetailApiService{Client: c.client}

	// 使用商户明细号查询
	if req.OutDetailNo != "" {
		resp, result, err := svc.GetTransferDetailByOutNo(ctx, transferbatch.GetTransferDetailByOutNoRequest{
			OutDetailNo: core.String(req.OutDetailNo),
			OutBatchNo:  core.String(req.OutBatchNo),
		})

		if err != nil {
			c.logger.Error("查询微信企业付款明细失败",
				zap.String("out_detail_no", req.OutDetailNo),
				zap.Error(err))
			return nil, c.convertErrorV2(err)
		}

		c.logger.Info("查询微信企业付款明细成功",
			zap.String("out_detail_no", req.OutDetailNo),
			zap.Int("status_code", result.Response.StatusCode))

		return c.convertDetailResponseV2(resp), nil
	}

	// 使用微信明细号查询
	resp, result, err := svc.GetTransferDetailByNo(ctx, transferbatch.GetTransferDetailByNoRequest{
		BatchId:  core.String(req.BatchID),
		DetailId: core.String(req.DetailID),
	})

	if err != nil {
		c.logger.Error("查询微信企业付款明细失败",
			zap.String("detail_id", req.DetailID),
			zap.Error(err))
		return nil, c.convertErrorV2(err)
	}

	c.logger.Info("查询微信企业付款明细成功",
		zap.String("detail_id", req.DetailID),
		zap.Int("status_code", result.Response.StatusCode))

	return c.convertDetailResponseV2(resp), nil
}

// VerifyTransferNotify 验证转账回调通知
func (c *OfficialWechatTransferClientV2) VerifyTransferNotify(ctx context.Context, notifyData string) (*TransferResponse, error) {
	c.logger.Info("处理微信企业付款回调通知",
		zap.Int("data_length", len(notifyData)))

	// TODO: 实现回调通知验证
	// 微信支付的回调通知需要验证签名和解密数据
	c.logger.Warn("回调通知验证功能尚未实现")

	return nil, &TransferError{
		Code:    "NOT_IMPLEMENTED",
		Message: "回调通知验证功能尚未实现",
		Detail:  "请联系技术支持",
	}
}

// VerifySignature 验证回调签名
func (c *OfficialWechatTransferClientV2) VerifySignature(signature, message, serial string) error {
	c.logger.Info("验证微信回调签名",
		zap.String("serial", serial),
		zap.Int("message_length", len(message)))

	// 如果是模拟模式，跳过签名验证
	if c.config.MockMode {
		c.logger.Info("模拟模式跳过签名验证")
		return nil
	}

	// TODO: 实现真实的签名验证逻辑
	// 当前版本暂时跳过验证，后续需要根据具体需求实现
	c.logger.Warn("签名验证功能待完善，当前跳过验证")
	return nil
}

// GetAPIv3Key 获取APIv3密钥（用于解密回调数据）
func (c *OfficialWechatTransferClientV2) GetAPIv3Key() (string, error) {
	c.logger.Info("获取APIv3密钥")

	// 如果是模拟模式，返回模拟密钥
	if c.config.MockMode {
		c.logger.Info("模拟模式返回模拟APIv3密钥")
		return "mock_api_v3_key_for_testing_only", nil
	}

	// 返回配置中的APIv3密钥
	if c.config.APIv3Key == "" {
		return "", fmt.Errorf("APIv3密钥未配置")
	}

	return c.config.APIv3Key, nil
}

// 辅助方法

// validateOfficialConfigV2 验证官方客户端配置 V2
func validateOfficialConfigV2(config *config.WechatTransferConfig) error {
	if config.MchID == "" {
		return fmt.Errorf("商户号不能为空")
	}

	if config.AppID == "" {
		return fmt.Errorf("应用ID不能为空")
	}

	if config.APIv3Key == "" {
		return fmt.Errorf("APIv3密钥不能为空")
	}

	// 证书序列号兼容性处理
	certificateSerialNumber := config.CertificateSerialNumber
	if certificateSerialNumber == "" {
		certificateSerialNumber = config.SerialNo // 兼容旧配置
	}
	if certificateSerialNumber == "" {
		return fmt.Errorf("证书序列号不能为空")
	}

	// 私钥路径兼容性处理
	privateKeyPath := config.PrivateKeyPath
	if privateKeyPath == "" {
		privateKeyPath = config.PrivateKeyFile // 兼容旧配置
	}
	if privateKeyPath == "" {
		return fmt.Errorf("私钥文件路径不能为空")
	}

	return nil
}

// validateTransferRequestV2 验证转账请求 V2
func (c *OfficialWechatTransferClientV2) validateTransferRequestV2(req *TransferRequest) error {
	if req.OutBatchNo == "" {
		return &TransferError{
			Code:    "PARAM_ERROR",
			Message: "商户批次号不能为空",
			Detail:  "OutBatchNo是必填参数",
		}
	}

	if req.TotalAmount <= 0 {
		return &TransferError{
			Code:    "AMOUNT_ERROR",
			Message: "转账总金额必须大于0",
			Detail:  fmt.Sprintf("当前金额: %d 分", req.TotalAmount),
		}
	}

	if len(req.TransferDetails) == 0 {
		return &TransferError{
			Code:    "PARAM_ERROR",
			Message: "转账明细不能为空",
			Detail:  "至少需要一条转账明细",
		}
	}

	// 🔧 验证转账明细中的关键字段
	for i, detail := range req.TransferDetails {
		if detail.OutDetailNo == "" {
			return &TransferError{
				Code:    "PARAM_ERROR",
				Message: "转账明细单号不能为空",
				Detail:  fmt.Sprintf("第%d条明细的OutDetailNo为空", i+1),
			}
		}

		if detail.OpenID == "" {
			return &TransferError{
				Code:    "PARAM_ERROR",
				Message: "收款用户OpenID不能为空",
				Detail:  fmt.Sprintf("第%d条明细的OpenID为空", i+1),
			}
		}

		if detail.UserName == "" {
			return &TransferError{
				Code:    "PARAM_ERROR",
				Message: "收款用户姓名不能为空",
				Detail:  fmt.Sprintf("第%d条明细的UserName为空", i+1),
			}
		}

		if detail.TransferAmount <= 0 {
			return &TransferError{
				Code:    "AMOUNT_ERROR",
				Message: "转账金额必须大于0",
				Detail:  fmt.Sprintf("第%d条明细的金额: %d 分", i+1, detail.TransferAmount),
			}
		}
	}

	return nil
}

// convertErrorV2 转换错误 V2
func (c *OfficialWechatTransferClientV2) convertErrorV2(err error) *TransferError {
	return &TransferError{
		Code:    "API_ERROR",
		Message: "微信支付API调用失败",
		Detail:  err.Error(),
	}
}

// convertTransferResponseV2 转换转账响应 V2
func (c *OfficialWechatTransferClientV2) convertTransferResponseV2(resp *transferbatch.InitiateBatchTransferResponse) *TransferResponse {
	return &TransferResponse{
		OutBatchNo:  c.getStringValueV2(resp.OutBatchNo),
		BatchID:     c.getStringValueV2(resp.BatchId),
		BatchStatus: string(TransferBatchStatusAccept), // 刚创建时状态为已受理
		BatchType:   "API",
		BatchName:   c.getStringValueV2(resp.OutBatchNo), // 使用批次号作为名称
		BatchRemark: "微信企业付款",
		// TODO: SDK字段待修复
		TotalAmount:   0, // c.getInt64ValueV2(resp.TotalAmount),
		TotalNum:      0, // int(c.getInt64ValueV2(resp.TotalNum)),
		SuccessAmount: 0, // 刚创建时成功金额为0
		SuccessNum:    0,
		FailAmount:    0,
		FailNum:       0,
		CreateTime:    c.getTimeValueV2(resp.CreateTime),
		UpdateTime:    time.Now(),
	}
}

// convertQueryResponseV2 转换查询响应 V2
func (c *OfficialWechatTransferClientV2) convertQueryResponseV2(resp *transferbatch.TransferBatchEntity) *TransferResponse {
	batch := resp.TransferBatch
	if batch == nil {
		return &TransferResponse{}
	}

	return &TransferResponse{
		OutBatchNo:    c.getStringValueV2(batch.OutBatchNo),
		BatchID:       c.getStringValueV2(batch.BatchId),
		BatchStatus:   c.getStringValueV2(batch.BatchStatus),
		BatchType:     c.getStringValueV2(batch.BatchType),
		BatchName:     c.getStringValueV2(batch.BatchName),
		BatchRemark:   c.getStringValueV2(batch.BatchRemark),
		TotalAmount:   c.getInt64ValueV2(batch.TotalAmount),
		TotalNum:      int(c.getInt64ValueV2(batch.TotalNum)),
		SuccessAmount: c.getInt64ValueV2(batch.SuccessAmount),
		SuccessNum:    int(c.getInt64ValueV2(batch.SuccessNum)),
		FailAmount:    c.getInt64ValueV2(batch.FailAmount),
		FailNum:       int(c.getInt64ValueV2(batch.FailNum)),
		CreateTime:    c.getTimeValueV2(batch.CreateTime),
		UpdateTime:    c.getTimeValueV2(batch.UpdateTime),
	}
}

// convertDetailResponseV2 转换明细响应 V2
func (c *OfficialWechatTransferClientV2) convertDetailResponseV2(resp *transferbatch.TransferDetailEntity) *TransferDetailResponse {
	if resp == nil {
		// 返回默认空响应
		return &TransferDetailResponse{
			DetailID:       "",
			OutDetailNo:    "",
			DetailStatus:   string(TransferDetailStatusWait),
			TransferAmount: 0,
			TransferRemark: "",
			FailReason:     "",
			OpenID:         "",
			UserName:       "",
			InitiateTime:   time.Now(),
			UpdateTime:     time.Now(),
		}
	}

	return &TransferDetailResponse{
		DetailID:       c.getStringValueV2(resp.DetailId),
		OutDetailNo:    c.getStringValueV2(resp.OutDetailNo),
		DetailStatus:   c.getStringValueV2(resp.DetailStatus),
		TransferAmount: c.getInt64ValueV2(resp.TransferAmount),
		TransferRemark: c.getStringValueV2(resp.TransferRemark),
		OpenID:         c.getStringValueV2(resp.Openid),
		UserName:       c.getStringValueV2(resp.UserName),
		InitiateTime:   c.getTimeValueV2(resp.InitiateTime),
		UpdateTime:     c.getTimeValueV2(resp.UpdateTime),
	}
}

// 辅助方法：安全获取字符串值 V2
func (c *OfficialWechatTransferClientV2) getStringValueV2(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// 辅助方法：安全获取int64值 V2
func (c *OfficialWechatTransferClientV2) getInt64ValueV2(ptr *int64) int64 {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// 辅助方法：安全获取时间值 V2
func (c *OfficialWechatTransferClientV2) getTimeValueV2(ptr *time.Time) time.Time {
	if ptr == nil {
		return time.Now()
	}
	return *ptr
}
