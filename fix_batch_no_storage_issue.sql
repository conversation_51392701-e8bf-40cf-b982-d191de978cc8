-- 修复批次号存储问题
-- 问题：out_batch_no 和 transfer_no 不匹配，导致回调处理失败

-- 1. 检查当前数据
SELECT 
    id,
    transfer_no,
    wechat_batch_no,
    status,
    fail_reason,
    created_at
FROM withdrawal_transfers 
ORDER BY created_at DESC;

-- 2. 由于当前只有一条失败的记录，且没有成功的微信批次号
-- 我们需要修改代码逻辑，而不是修复数据

-- 当前数据分析：
-- - transfer_no: TF1754460143573082747 (数据库中的转账单号)
-- - wechat_batch_no: null (微信返回的批次号，因为转账失败所以为空)
-- - 问题：代码中 generateBatchNo() 生成的是 BATCH_xxx 格式
-- - 但回调时用这个值查找 transfer_no，会找不到

-- 解决方案：
-- 方案1：修改代码，让 out_batch_no 直接使用 transfer_no
-- 方案2：在数据库中添加 out_batch_no 字段存储发送给微信的批次号
-- 方案3：修改回调查找逻辑，通过 wechat_batch_no 查找

-- 推荐方案1：让 out_batch_no 直接使用 transfer_no
-- 这样可以保持数据一致性，避免存储重复信息