import request from '@/utils/request'

/**
 * 订单审核API服务
 */

// 获取待审核订单列表
export function getPendingReviews(params) {
  return request({
    url: '/api/admin/reviews/pending',
    method: 'get',
    params: {
      page: params.page || 1,
      limit: params.limit || 20,
      completion_type: params.completion_type,
      ...params
    }
  })
}

// 审核订单
export function reviewOrder(orderId, data) {
  return request({
    url: `/api/admin/reviews/${orderId}/review`,
    method: 'post',
    data
  })
}

// 获取审核详情
export function getReviewDetail(orderId) {
  return request({
    url: `/api/admin/reviews/${orderId}/detail`,
    method: 'get'
  })
}

// 获取审核统计信息
export function getReviewStats() {
  return request({
    url: '/api/admin/reviews/stats',
    method: 'get'
  })
}
