<template>
  <div class="review-management">
    <!-- 页面标题和统计信息 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">订单审核管理</h2>
        <div class="stats-cards">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.pending_count || 0 }}</div>
                <div class="stat-label">待审核</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card approved">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.approved_today || 0 }}</div>
                <div class="stat-label">今日通过</div>
              </div>
            </div>
          </el-card>
          
          <el-card class="stat-card rejected">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statsData.rejected_today || 0 }}</div>
                <div class="stat-label">今日拒绝</div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :model="filterForm" inline>
          <el-form-item label="完成类型">
            <el-select v-model="filterForm.completion_type" placeholder="全部类型" clearable>
              <el-option label="陪诊师完成" :value="1" />
              <el-option label="管理员强制完成" :value="2" />
              <el-option label="异常处理完成" :value="3" />
              <el-option label="系统自动完成" :value="4" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="提交时间">
            <el-date-picker
              v-model="filterForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleFilter" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleFilterReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 审核列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span class="table-title">待审核订单列表</span>
          <div class="header-actions">
            <el-button @click="loadReviewData" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="tableData"
        stripe
        v-loading="loading"
        empty-text="暂无待审核订单"
        class="review-table"
      >
        <el-table-column prop="order_no" label="订单号" width="180">
          <template #default="{ row }">
            <el-link @click="handleReviewDetail(row)" type="primary">
              {{ row.order_no }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="completion_type_text" label="完成类型" width="130">
          <template #default="{ row }">
            <el-tag :type="getCompletionTypeTagType(row.completion_type)">
              {{ row.completion_type_text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="service_summary" label="服务总结" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="waiting_hours" label="等待时间" width="120">
          <template #default="{ row }">
            <span :class="getWaitingTimeClass(row.waiting_hours)">
              {{ formatWaitingTime(row.waiting_hours) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="提交时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <el-button
                type="success"
                size="small"
                @click="handleApprove(row)"
                :loading="processing && selectedReview?.order_id === row.order_id"
              >
                通过
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleReject(row)"
                :loading="processing && selectedReview?.order_id === row.order_id"
              >
                拒绝
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleReviewDetail(row)"
                link
              >
                详情
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="审核详情"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div v-if="selectedReview" class="review-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4>订单信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">{{ selectedReview.order_no }}</el-descriptions-item>
            <el-descriptions-item label="完成类型">
              <el-tag :type="getCompletionTypeTagType(selectedReview.completion_type)">
                {{ selectedReview.completion_type_text }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="提交时间">{{ formatDateTime(selectedReview.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="等待时间">
              <span :class="getWaitingTimeClass(selectedReview.waiting_hours)">
                {{ formatWaitingTime(selectedReview.waiting_hours) }}
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 服务总结 -->
        <div class="detail-section">
          <h4>服务总结</h4>
          <div class="service-summary">
            <p>{{ selectedReview.service_summary }}</p>
          </div>
        </div>

        <!-- 服务照片 -->
        <div v-if="selectedReview.service_photos && selectedReview.service_photos.length > 0" class="detail-section">
          <h4>服务照片</h4>
          <div class="service-photos">
            <el-image
              v-for="(photo, index) in selectedReview.service_photos"
              :key="index"
              :src="photo"
              :preview-src-list="selectedReview.service_photos"
              :initial-index="index"
              fit="cover"
              class="photo-item"
              lazy
              preview-teleported
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            type="success"
            @click="handleApprove(selectedReview)"
            :loading="processing"
          >
            通过
          </el-button>
          <el-button
            type="danger"
            @click="handleReject(selectedReview)"
            :loading="processing"
          >
            拒绝
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Clock,
  CircleCheck,
  CircleClose,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import {
  getPendingReviews,
  reviewOrder,
  getReviewStats
} from '@/api/review'

// 响应式数据
const loading = ref(false)
const processing = ref(false)
const tableData = ref([])
const selectedReview = ref(null)
const detailDialogVisible = ref(false)

const statsData = ref({
  pending_count: 0,
  approved_today: 0,
  rejected_today: 0
})

const filterForm = reactive({
  completion_type: null,
  date_range: null
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 方法
const loadReviewData = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      limit: pagination.pageSize,
      ...filterForm
    }
    
    // 处理日期范围
    if (filterForm.date_range && filterForm.date_range.length === 2) {
      params.date_start = filterForm.date_range[0]
      params.date_end = filterForm.date_range[1]
    }
    
    const response = await getPendingReviews(params)
    
    if (response.code === 0) {
      tableData.value = response.data.reviews || []
      pagination.total = response.data.total || 0
      
      // 更新统计数据
      if (response.data.stats) {
        statsData.value = response.data.stats
      }
    } else {
      ElMessage.error(response.message || '获取待审核订单失败')
    }
  } catch (error) {
    console.error('获取待审核订单失败:', error)
    ElMessage.error('获取待审核订单失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getReviewStats()
    if (response.code === 0) {
      statsData.value = response.data
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleFilter = () => {
  pagination.page = 1
  loadReviewData()
}

const handleFilterReset = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = null
  })
  pagination.page = 1
  loadReviewData()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadReviewData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadReviewData()
}

const handleReviewDetail = (row) => {
  selectedReview.value = row
  detailDialogVisible.value = true
}

const handleApprove = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认通过审核？订单号：${row.order_no}`,
      '审核确认',
      {
        type: 'warning',
        confirmButtonText: '确认通过',
        cancelButtonText: '取消'
      }
    )

    processing.value = true
    selectedReview.value = row

    const response = await reviewOrder(row.order_id, {
      review_result: 1, // 通过
      review_notes: '管理员审核通过'
    })

    if (response.code === 0) {
      ElMessage.success('审核通过成功')
      detailDialogVisible.value = false
      loadReviewData()
      loadStats()
    } else {
      ElMessage.error(response.message || '审核失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败：' + (error.message || '未知错误'))
    }
  } finally {
    processing.value = false
    selectedReview.value = null
  }
}

const handleReject = async (row) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确认拒绝审核？订单号：${row.order_no}`,
      '审核确认',
      {
        confirmButtonText: '确认拒绝',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入拒绝原因',
        inputValidator: (value) => {
          if (!value) {
            return '请输入拒绝原因'
          }
          return true
        }
      }
    )

    processing.value = true
    selectedReview.value = row

    const response = await reviewOrder(row.order_id, {
      review_result: 2, // 拒绝
      review_notes: reason
    })

    if (response.code === 0) {
      ElMessage.success('审核拒绝成功')
      detailDialogVisible.value = false
      loadReviewData()
      loadStats()
    } else {
      ElMessage.error(response.message || '审核失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error('审核失败：' + (error.message || '未知错误'))
    }
  } finally {
    processing.value = false
    selectedReview.value = null
  }
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatWaitingTime = (hours) => {
  if (hours < 1) {
    return Math.round(hours * 60) + '分钟'
  }
  return hours.toFixed(1) + '小时'
}

const getWaitingTimeClass = (hours) => {
  if (hours > 24) {
    return 'time-critical'
  } else if (hours > 12) {
    return 'time-warning'
  }
  return 'time-normal'
}

const getCompletionTypeTagType = (type) => {
  const types = {
    1: 'primary',   // 陪诊师完成
    2: 'success',   // 管理员强制完成
    3: 'warning',   // 异常处理完成
    4: 'info'       // 系统自动完成
  }
  return types[type] || 'info'
}

// 生命周期
onMounted(() => {
  loadReviewData()
  loadStats()
})
</script>

<style lang="scss" scoped>
.review-management {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .page-title {
        font-size: 24px;
        color: #303133;
        margin: 0 0 20px 0;
      }

      .stats-cards {
        display: flex;
        gap: 16px;

        .stat-card {
          min-width: 160px;
          
          :deep(.el-card__body) {
            padding: 16px;
          }

          .stat-content {
            display: flex;
            align-items: center;
            gap: 12px;

            .stat-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
            }

            .stat-info {
              .stat-value {
                font-size: 24px;
                font-weight: bold;
                line-height: 1;
                margin-bottom: 4px;
              }

              .stat-label {
                font-size: 12px;
                color: #909399;
              }
            }
          }

          &.pending {
            .stat-icon {
              background: #fef0e6;
              color: #e6a23c;
            }
            .stat-value {
              color: #e6a23c;
            }
          }

          &.approved {
            .stat-icon {
              background: #f0f9ff;
              color: #67c23a;
            }
            .stat-value {
              color: #67c23a;
            }
          }

          &.rejected {
            .stat-icon {
              background: #fef0f0;
              color: #f56c6c;
            }
            .stat-value {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;

    .filter-content {
      :deep(.el-form--inline .el-form-item) {
        margin-right: 24px;
        margin-bottom: 16px;
      }
    }
  }

  .table-card {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .table-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .review-table {
      margin-top: 16px;

      .time-critical {
        color: #f56c6c;
        font-weight: bold;
      }

      .time-warning {
        color: #e6a23c;
        font-weight: bold;
      }

      .time-normal {
        color: #67c23a;
      }

      .operation-buttons {
        display: flex;
        gap: 8px;
        
        .el-button {
          padding: 4px 8px;
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }

  .review-detail {
    .detail-section {
      margin-bottom: 24px;

      h4 {
        font-size: 16px;
        color: #303133;
        margin: 0 0 12px 0;
        border-left: 4px solid #409eff;
        padding-left: 12px;
      }

      .service-summary {
        background: #f5f7fa;
        padding: 12px;
        border-radius: 4px;
        color: #606266;
        line-height: 1.6;
        margin: 0;
        min-height: 80px;
      }

      .service-photos {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;

        .photo-item {
          width: 120px;
          height: 120px;
          border-radius: 4px;
          overflow: hidden;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
