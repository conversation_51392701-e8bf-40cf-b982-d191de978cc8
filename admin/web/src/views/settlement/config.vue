<template>
  <div class="settlement-config">
    <div class="page-header">
      <h2>结算配置管理</h2>
      <p>配置T+N结算天数、收入冻结期等结算相关参数</p>
    </div>

    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>结算配置</span>
          <el-button type="primary" @click="handleSave" :loading="saving">
            保存配置
          </el-button>
        </div>
      </template>

      <el-form
        ref="configForm"
        :model="configData"
        :rules="configRules"
        label-width="200px"
        class="config-form"
      >
        <!-- T+N结算配置 -->
        <el-divider content-position="left">T+N结算配置</el-divider>
        
        <el-form-item label="T+N结算天数" prop="settlement_review_period_days">
          <el-input-number
            v-model="configData.settlement_review_period_days"
            :min="1"
            :max="30"
            :precision="0"
            placeholder="默认2天"
          />
          <div class="form-help">
            订单完成后进入结算审核期的天数，如T+2表示订单完成2天后自动结算
          </div>
        </el-form-item>

        <el-form-item label="审核期限(小时)" prop="settlement_review_period_hours">
          <el-input-number
            v-model="configData.settlement_review_period_hours"
            :min="1"
            :max="720"
            :precision="0"
            placeholder="默认48小时"
          />
          <div class="form-help">
            结算审核期限，超过此时间未处理将自动通过审核
          </div>
        </el-form-item>

        <el-form-item label="自动结算开关" prop="auto_settlement_enabled">
          <el-switch
            v-model="configData.auto_settlement_enabled"
            active-text="启用"
            inactive-text="禁用"
          />
          <div class="form-help">
            是否启用自动结算功能，关闭后需要人工处理所有结算
          </div>
        </el-form-item>

        <!-- 财务配置 -->
        <el-divider content-position="left">财务配置</el-divider>

        <el-form-item label="收入冻结天数" prop="finance_freeze_days">
          <el-input-number
            v-model="configData.finance_freeze_days"
            :min="0"
            :max="365"
            :precision="0"
            placeholder="默认7天"
          />
          <div class="form-help">
            陪诊师收入冻结期天数，用于风险控制，0表示不冻结
          </div>
        </el-form-item>

        <!-- 争议处理配置 -->
        <el-divider content-position="left">争议处理配置</el-divider>

        <el-form-item label="争议超时天数" prop="dispute_timeout_days">
          <el-input-number
            v-model="configData.dispute_timeout_days"
            :min="1"
            :max="30"
            :precision="0"
            placeholder="默认7天"
          />
          <div class="form-help">
            争议处理超时天数，超过此时间未处理的争议将升级处理
          </div>
        </el-form-item>

        <!-- 自动结算金额限制 -->
        <el-divider content-position="left">自动结算金额限制</el-divider>

        <el-form-item label="最小自动结算金额" prop="min_auto_settlement_amount">
          <el-input-number
            v-model="configData.min_auto_settlement_amount"
            :min="0.01"
            :max="1000"
            :precision="2"
            placeholder="默认0.01元"
          />
          <div class="form-help">
            低于此金额的订单不会自动结算，需要人工审核
          </div>
        </el-form-item>

        <el-form-item label="最大自动结算金额" prop="max_auto_settlement_amount">
          <el-input-number
            v-model="configData.max_auto_settlement_amount"
            :min="1"
            :max="100000"
            :precision="2"
            placeholder="默认10000元"
          />
          <div class="form-help">
            高于此金额的订单不会自动结算，需要人工审核
          </div>
        </el-form-item>

        <!-- 变更原因 -->
        <el-divider content-position="left">变更信息</el-divider>

        <el-form-item label="变更原因" prop="reason" required>
          <el-input
            v-model="configData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入配置变更的原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 配置历史 -->
    <el-card class="history-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>配置变更历史</span>
          <el-button @click="loadHistory" :loading="loadingHistory">
            刷新
          </el-button>
        </div>
      </template>

      <el-table
        :data="historyData"
        v-loading="loadingHistory"
        style="width: 100%"
      >
        <el-table-column prop="config_key" label="配置项" width="200" />
        <el-table-column prop="old_value" label="原值" width="120" />
        <el-table-column prop="new_value" label="新值" width="120" />
        <el-table-column prop="operator_name" label="操作人" width="120" />
        <el-table-column prop="reason" label="变更原因" />
        <el-table-column prop="created_at" label="变更时间" width="180" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getSettlementConfig, updateSettlementConfig, getSettlementConfigHistory } from '@/api/settlement'

export default {
  name: 'SettlementConfig',
  data() {
    return {
      configData: {
        settlement_review_period_days: 2,
        settlement_review_period_hours: 48,
        finance_freeze_days: 7,
        dispute_timeout_days: 7,
        min_auto_settlement_amount: 0.01,
        max_auto_settlement_amount: 10000.00,
        auto_settlement_enabled: true,
        reason: ''
      },
      configRules: {
        settlement_review_period_days: [
          { required: true, message: '请输入T+N结算天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 30, message: '天数必须在1-30之间', trigger: 'blur' }
        ],
        settlement_review_period_hours: [
          { required: true, message: '请输入审核期限', trigger: 'blur' },
          { type: 'number', min: 1, max: 720, message: '小时数必须在1-720之间', trigger: 'blur' }
        ],
        finance_freeze_days: [
          { required: true, message: '请输入冻结天数', trigger: 'blur' },
          { type: 'number', min: 0, max: 365, message: '天数必须在0-365之间', trigger: 'blur' }
        ],
        dispute_timeout_days: [
          { required: true, message: '请输入争议超时天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 30, message: '天数必须在1-30之间', trigger: 'blur' }
        ],
        min_auto_settlement_amount: [
          { required: true, message: '请输入最小自动结算金额', trigger: 'blur' },
          { type: 'number', min: 0.01, max: 1000, message: '金额必须在0.01-1000之间', trigger: 'blur' }
        ],
        max_auto_settlement_amount: [
          { required: true, message: '请输入最大自动结算金额', trigger: 'blur' },
          { type: 'number', min: 1, max: 100000, message: '金额必须在1-100000之间', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '请输入变更原因', trigger: 'blur' },
          { min: 5, max: 200, message: '变更原因长度在5-200个字符之间', trigger: 'blur' }
        ]
      },
      historyData: [],
      saving: false,
      loadingHistory: false
    }
  },
  created() {
    this.loadConfig()
    this.loadHistory()
  },
  methods: {
    async loadConfig() {
      try {
        const response = await getSettlementConfig()
        if (response.code === 0) {
          // 转换数据类型
          const data = response.data
          this.configData = {
            settlement_review_period_days: parseInt(data.settlement_review_period_days) || 2,
            settlement_review_period_hours: parseInt(data.settlement_review_period_hours) || 48,
            finance_freeze_days: parseInt(data.finance_freeze_days) || 7,
            dispute_timeout_days: parseInt(data.dispute_timeout_days) || 7,
            min_auto_settlement_amount: parseFloat(data.min_auto_settlement_amount) || 0.01,
            max_auto_settlement_amount: parseFloat(data.max_auto_settlement_amount) || 10000.00,
            auto_settlement_enabled: data.auto_settlement_enabled === 'true',
            reason: ''
          }
        }
      } catch (error) {
        this.$message.error('加载配置失败: ' + error.message)
      }
    },
    async handleSave() {
      try {
        await this.$refs.configForm.validate()
        
        this.saving = true
        const response = await updateSettlementConfig(this.configData)
        
        if (response.code === 0) {
          this.$message.success('配置保存成功')
          this.configData.reason = '' // 清空变更原因
          this.loadHistory() // 刷新历史记录
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        if (error.message) {
          this.$message.error('保存失败: ' + error.message)
        }
      } finally {
        this.saving = false
      }
    },
    async loadHistory() {
      try {
        this.loadingHistory = true
        const response = await getSettlementConfigHistory({ limit: 20 })
        
        if (response.code === 0) {
          this.historyData = response.data.history || []
        }
      } catch (error) {
        this.$message.error('加载历史记录失败: ' + error.message)
      } finally {
        this.loadingHistory = false
      }
    }
  }
}
</script>

<style scoped>
.settlement-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-card, .history-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  max-width: 800px;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.el-divider {
  margin: 30px 0 20px 0;
}

.el-divider:first-of-type {
  margin-top: 0;
}

.el-input-number {
  width: 200px;
}

.el-switch {
  margin-right: 10px;
}
</style>