package client

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"go.uber.org/zap"
)

// BackendAPIClient Backend API客户端
type BackendAPIClient struct {
	baseURL   string
	keyID     string
	secretKey string
	algorithm string
	ttl       int
	client    *http.Client
	logger    *zap.SugaredLogger
}

// APIKeyConfig API密钥配置
type APIKeyConfig struct {
	KeyID     string `yaml:"key_id"`     // 密钥ID
	SecretKey string `yaml:"secret_key"` // 密钥内容
	Algorithm string `yaml:"algorithm"`  // 签名算法
	TTL       int    `yaml:"ttl"`        // 请求有效期(秒)
}

// BackendAPIConfig Backend API配置
type BackendAPIConfig struct {
	BaseURL string       `yaml:"base_url"` // Backend API基础URL
	APIKey  APIKeyConfig `yaml:"api_key"`  // API密钥配置
	Timeout int          `yaml:"timeout"`  // 请求超时时间(秒)
}

// NewBackendAPIClient 创建Backend API客户端
func NewBackendAPIClient(config BackendAPIConfig, log *zap.SugaredLogger) *BackendAPIClient {
	timeout := time.Duration(config.Timeout) * time.Second
	if timeout == 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	return &BackendAPIClient{
		baseURL:   config.BaseURL,
		keyID:     config.APIKey.KeyID,
		secretKey: config.APIKey.SecretKey,
		algorithm: config.APIKey.Algorithm,
		ttl:       config.APIKey.TTL,
		client: &http.Client{
			Timeout: timeout,
		},
		logger: log,
	}
}

// TransferRequest 转账请求
type TransferRequest struct {
	WithdrawalIDs []uint `json:"withdrawal_ids"`
	OperatorID    uint   `json:"operator_id"`
	Remark        string `json:"remark"`
}

// TransferResponse 转账响应
type TransferResponse struct {
	SuccessCount int                    `json:"success_count"`
	FailCount    int                    `json:"fail_count"`
	Results      []TransferResultDetail `json:"results"`
}

// TransferResultDetail 转账结果详情
type TransferResultDetail struct {
	WithdrawalID uint   `json:"withdrawal_id"`
	TransferNo   string `json:"transfer_no,omitempty"`
	Status       int    `json:"status"`
	Message      string `json:"message"`
	Processed    bool   `json:"processed"`
}

// TransferStatusResponse 转账状态响应
type TransferStatusResponse struct {
	WithdrawalID  uint   `json:"withdrawal_id"`
	TransferNo    string `json:"transfer_no,omitempty"`
	WechatBatchNo string `json:"wechat_batch_no,omitempty"`
	Amount        int64  `json:"amount"`
	Status        int    `json:"status"`
	WechatStatus  string `json:"wechat_status,omitempty"`
	TransferTime  string `json:"transfer_time,omitempty"`
	SuccessTime   string `json:"success_time,omitempty"`
	RetryCount    int    `json:"retry_count"`
	FailReason    string `json:"fail_reason,omitempty"`
}

// RetryTransferRequest 重试转账请求
type RetryTransferRequest struct {
	OperatorID uint   `json:"operator_id"`
	Reason     string `json:"reason"`
}

// BatchTransferQuery 批量查询请求
type BatchTransferQuery struct {
	WithdrawalIDs []uint `json:"withdrawal_ids"`
}

// APIResponse API响应结构
type APIResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

// InitiateTransfer 发起转账
func (c *BackendAPIClient) InitiateTransfer(req TransferRequest) (*TransferResponse, error) {
	c.logger.Infow("发起Backend转账API调用",
		"withdrawal_ids", req.WithdrawalIDs,
		"operator_id", req.OperatorID,
		"remark", req.Remark,
	)

	// 发送POST请求
	var response APIResponse
	err := c.sendRequest("POST", "/api/v1/internal/wechat/transfer", req, &response)
	if err != nil {
		c.logger.Errorw("发起转账API调用失败",
			"error", err.Error(),
			"withdrawal_ids", req.WithdrawalIDs,
		)
		return nil, fmt.Errorf("发起转账失败: %w", err)
	}

	if response.Code != 0 {
		c.logger.Errorw("发起转账API返回错误",
			"code", response.Code,
			"msg", response.Msg,
			"withdrawal_ids", req.WithdrawalIDs,
		)
		return nil, fmt.Errorf("发起转账失败: %s", response.Msg)
	}

	// 解析响应数据
	var transferResp TransferResponse
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &transferResp)
	if err != nil {
		return nil, fmt.Errorf("解析转账响应失败: %w", err)
	}

	c.logger.Infow("发起转账API调用成功",
		"success_count", transferResp.SuccessCount,
		"fail_count", transferResp.FailCount,
		"total_count", len(req.WithdrawalIDs),
	)

	return &transferResp, nil
}

// QueryTransferStatus 查询转账状态
func (c *BackendAPIClient) QueryTransferStatus(withdrawalID uint) (*TransferStatusResponse, error) {
	c.logger.Infow("查询转账状态API调用",
		"withdrawal_id", withdrawalID,
	)

	// 发送GET请求
	path := fmt.Sprintf("/api/v1/internal/wechat/transfer/%d", withdrawalID)
	var response APIResponse
	err := c.sendRequest("GET", path, nil, &response)
	if err != nil {
		c.logger.Errorw("查询转账状态API调用失败",
			"error", err.Error(),
			"withdrawal_id", withdrawalID,
		)
		return nil, fmt.Errorf("查询转账状态失败: %w", err)
	}

	if response.Code != 0 {
		c.logger.Errorw("查询转账状态API返回错误",
			"code", response.Code,
			"msg", response.Msg,
			"withdrawal_id", withdrawalID,
		)
		return nil, fmt.Errorf("查询转账状态失败: %s", response.Msg)
	}

	// 解析响应数据
	var statusResp TransferStatusResponse
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &statusResp)
	if err != nil {
		return nil, fmt.Errorf("解析转账状态响应失败: %w", err)
	}

	c.logger.Infow("查询转账状态API调用成功",
		"withdrawal_id", withdrawalID,
		"transfer_no", statusResp.TransferNo,
		"status", statusResp.Status,
	)

	return &statusResp, nil
}

// RetryTransfer 重试转账
func (c *BackendAPIClient) RetryTransfer(withdrawalID uint, req RetryTransferRequest) (*map[string]interface{}, error) {
	c.logger.Infow("重试转账API调用",
		"withdrawal_id", withdrawalID,
		"operator_id", req.OperatorID,
		"reason", req.Reason,
	)

	// 发送POST请求
	path := fmt.Sprintf("/api/v1/internal/wechat/transfer/%d/retry", withdrawalID)
	var response APIResponse
	err := c.sendRequest("POST", path, req, &response)
	if err != nil {
		c.logger.Errorw("重试转账API调用失败",
			"error", err.Error(),
			"withdrawal_id", withdrawalID,
		)
		return nil, fmt.Errorf("重试转账失败: %w", err)
	}

	if response.Code != 0 {
		c.logger.Errorw("重试转账API返回错误",
			"code", response.Code,
			"msg", response.Msg,
			"withdrawal_id", withdrawalID,
		)
		return nil, fmt.Errorf("重试转账失败: %s", response.Msg)
	}

	// 解析响应数据
	var retryResp map[string]interface{}
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &retryResp)
	if err != nil {
		return nil, fmt.Errorf("解析重试转账响应失败: %w", err)
	}

	c.logger.Infow("重试转账API调用成功",
		"withdrawal_id", withdrawalID,
		"transfer_no", retryResp["transfer_no"],
		"status", retryResp["status"],
	)

	return &retryResp, nil
}

// BatchQueryTransferStatus 批量查询转账状态
func (c *BackendAPIClient) BatchQueryTransferStatus(withdrawalIDs []uint) ([]TransferStatusResponse, error) {
	c.logger.Infow("批量查询转账状态API调用",
		"withdrawal_ids", withdrawalIDs,
		"count", len(withdrawalIDs),
	)

	// 构建请求
	req := BatchTransferQuery{
		WithdrawalIDs: withdrawalIDs,
	}

	// 发送POST请求
	var response APIResponse
	err := c.sendRequest("POST", "/api/v1/internal/wechat/transfer/batch-query", req, &response)
	if err != nil {
		c.logger.Errorw("批量查询转账状态API调用失败",
			"error", err.Error(),
			"withdrawal_ids", withdrawalIDs,
		)
		return nil, fmt.Errorf("批量查询转账状态失败: %w", err)
	}

	if response.Code != 0 {
		c.logger.Errorw("批量查询转账状态API返回错误",
			"code", response.Code,
			"msg", response.Msg,
			"withdrawal_ids", withdrawalIDs,
		)
		return nil, fmt.Errorf("批量查询转账状态失败: %s", response.Msg)
	}

	// 解析响应数据
	var statusList []TransferStatusResponse
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	err = json.Unmarshal(dataBytes, &statusList)
	if err != nil {
		return nil, fmt.Errorf("解析批量查询响应失败: %w", err)
	}

	c.logger.Infow("批量查询转账状态API调用成功",
		"requested_count", len(withdrawalIDs),
		"found_count", len(statusList),
	)

	return statusList, nil
}

// sendRequest 发送HTTP请求
func (c *BackendAPIClient) sendRequest(method, path string, body interface{}, response interface{}) error {
	// 构建完整URL
	url := c.baseURL + path

	// 序列化请求体
	var bodyBytes []byte
	var err error
	if body != nil {
		bodyBytes, err = json.Marshal(body)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %w", err)
		}
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 生成API密钥认证头
	authHeader, err := c.generateAuthHeader(method, path, bodyBytes)
	if err != nil {
		return fmt.Errorf("生成认证头失败: %w", err)
	}
	req.Header.Set("Authorization", authHeader)

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	err = json.Unmarshal(respBody, response)
	if err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	return nil
}

// generateAuthHeader 生成API密钥认证头
func (c *BackendAPIClient) generateAuthHeader(method, path string, body []byte) (string, error) {
	// 生成时间戳
	timestamp := time.Now().Unix()
	timestampStr := strconv.FormatInt(timestamp, 10)

	// 构建签名字符串
	signString := fmt.Sprintf("%s\n%s\n%s\n%s", method, path, timestampStr, string(body))

	// 计算HMAC-SHA256签名
	h := hmac.New(sha256.New, []byte(c.secretKey))
	h.Write([]byte(signString))
	signature := hex.EncodeToString(h.Sum(nil))

	// 构建Authorization头
	authHeader := fmt.Sprintf("APIKey keyId=%s,signature=%s,timestamp=%s",
		c.keyID, signature, timestampStr)

	return authHeader, nil
}

// IsHealthy 检查Backend API健康状态
func (c *BackendAPIClient) IsHealthy() bool {
	// 发送健康检查请求
	req, err := http.NewRequest("GET", c.baseURL+"/health", nil)
	if err != nil {
		c.logger.Errorw("创建健康检查请求失败",
			"error", err.Error(),
		)
		return false
	}

	// 设置较短的超时时间
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		c.logger.Errorw("Backend API健康检查失败",
			"error", err.Error(),
		)
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode == http.StatusOK
}
