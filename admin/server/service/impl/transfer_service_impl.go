package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/gemeijie/peizhen/admin/server/config"
	"github.com/gemeijie/peizhen/admin/server/pkg/wechat"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/logger"
)

// transferService 转账服务实现
type transferService struct {
	wechatClient *wechat.Client
	config       *config.Config
}

// NewTransferService 创建转账服务
func NewTransferService(wechatClient *wechat.Client, config *config.Config) service.ITransferService {
	return &transferService{
		wechatClient: wechatClient,
		config:       config,
	}
}

// Transfer 发起转账
func (s *transferService) Transfer(ctx context.Context, req *service.TransferRequest) (*service.TransferResult, error) {
	logger.Info("开始发起转账", "withdrawal_id", req.WithdrawalID, "amount", req.Amount)

	// 生成批次单号
	outBatchNo := s.GenerateBatchNo(req.WithdrawalID)

	// 构建微信转账请求
	wechatReq := &wechat.TransferRequest{
		OutBatchNo:   outBatchNo,
		BatchName:    "提现转账",
		BatchRemark:  fmt.Sprintf("提现ID: %d", req.WithdrawalID),
		TotalAmount:  req.Amount,
		TotalNum:     1,
		OpenID:       req.OpenID,
		UserName:     req.UserName,
		Amount:       req.Amount,
		Remark:       req.Remark,
	}

	// 调用微信转账API
	resp, err := s.wechatClient.Transfer(ctx, wechatReq)
	if err != nil {
		logger.Error("微信转账失败", "error", err, "withdrawal_id", req.WithdrawalID)
		return &service.TransferResult{
			OutBatchNo: outBatchNo,
			Amount:     req.Amount,
			Success:    false,
			ErrorMsg:   err.Error(),
		}, err
	}

	logger.Info("微信转账成功", "withdrawal_id", req.WithdrawalID, "batch_id", resp.BatchID)

	return &service.TransferResult{
		OutBatchNo:  resp.OutBatchNo,
		BatchID:     resp.BatchID,
		BatchStatus: resp.BatchStatus,
		Amount:      resp.TotalAmount,
		CreateTime:  resp.CreateTime,
		Success:     true,
	}, nil
}

// QueryTransfer 查询转账状态
func (s *transferService) QueryTransfer(ctx context.Context, outBatchNo string) (*service.TransferResult, error) {
	logger.Info("开始查询转账状态", "out_batch_no", outBatchNo)

	// 调用微信查询API
	resp, err := s.wechatClient.QueryTransfer(ctx, outBatchNo)
	if err != nil {
		logger.Error("查询微信转账状态失败", "error", err, "out_batch_no", outBatchNo)
		return &service.TransferResult{
			OutBatchNo: outBatchNo,
			Success:    false,
			ErrorMsg:   err.Error(),
		}, err
	}

	logger.Info("查询微信转账状态成功", "out_batch_no", outBatchNo, "status", resp.BatchStatus)

	return &service.TransferResult{
		OutBatchNo:  resp.OutBatchNo,
		BatchID:     resp.BatchID,
		BatchStatus: resp.BatchStatus,
		Amount:      resp.TotalAmount,
		CreateTime:  resp.CreateTime,
		Success:     true,
	}, nil
}

// GenerateBatchNo 生成批次单号
func (s *transferService) GenerateBatchNo(withdrawalID uint) string {
	// 格式: WD{提现ID}_{时间戳}
	return fmt.Sprintf("WD%d_%d", withdrawalID, time.Now().Unix())
}