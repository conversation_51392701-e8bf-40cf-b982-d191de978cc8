package impl

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/model"
	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/service"
	"go.uber.org/zap"
)

// operationLogService 操作日志服务实现
type operationLogService struct {
	repo   repository.IOperationLogRepository
	logger *zap.Logger
}

// NewOperationLogService 创建操作日志服务实例
func NewOperationLogService(repo repository.IOperationLogRepository, logger *zap.Logger) service.IOperationLogService {
	return &operationLogService{
		repo:   repo,
		logger: logger,
	}
}

// CreateLog 创建操作日志
func (s *operationLogService) CreateLog(ctx context.Context, req *dto.CreateLogRequest) error {
	log := &model.OperationLog{
		OperatorID:      req.OperatorID,
		OperatorName:    "", // TODO: 从上下文或数据库获取操作员姓名
		OperationType:   req.OperationType,
		ResourceType:    req.ResourceType,
		ResourceID:      req.ResourceID,
		OperationDetail: req.OperationDetail,
		OperationResult: req.OperationResult,
		ErrorMessage:    req.ErrorMessage,
		IPAddress:       req.IPAddress,
		UserAgent:       req.UserAgent,
		RequestData:     req.RequestData,
		ResponseData:    req.ResponseData,
		CreatedAt:       time.Now(),
	}

	return s.repo.Create(ctx, log)
}

// BatchCreateLogs 批量创建操作日志
func (s *operationLogService) BatchCreateLogs(ctx context.Context, logs []*dto.CreateLogRequest) error {
	var operationLogs []*model.OperationLog
	for _, req := range logs {
		log := &model.OperationLog{
			OperatorID:      req.OperatorID,
			OperatorName:    "", // TODO: 从上下文或数据库获取操作员姓名
			OperationType:   req.OperationType,
			ResourceType:    req.ResourceType,
			ResourceID:      req.ResourceID,
			OperationDetail: req.OperationDetail,
			OperationResult: req.OperationResult,
			ErrorMessage:    req.ErrorMessage,
			IPAddress:       req.IPAddress,
			UserAgent:       req.UserAgent,
			RequestData:     req.RequestData,
			ResponseData:    req.ResponseData,
			CreatedAt:       time.Now(),
		}
		operationLogs = append(operationLogs, log)
	}

	// TODO: 批量创建逻辑
	return nil
}

// LogOperation 记录操作
func (s *operationLogService) LogOperation(ctx context.Context, operatorID int64, operationType, resourceType string, resourceID int64, detail string) error {
	req := &dto.CreateLogRequest{
		OperatorID:      operatorID,
		OperationType:   operationType,
		ResourceType:    resourceType,
		ResourceID:      resourceID,
		OperationDetail: detail,
		OperationResult: 1, // 默认成功
	}
	return s.CreateLog(ctx, req)
}

// LogSuccess 记录成功操作
func (s *operationLogService) LogSuccess(ctx context.Context, operatorID int64, operationType, resourceType string, resourceID int64, detail string) error {
	return s.LogOperation(ctx, operatorID, operationType, resourceType, resourceID, detail)
}

// LogFailure 记录失败操作
func (s *operationLogService) LogFailure(ctx context.Context, operatorID int64, operationType, resourceType string, resourceID int64, detail, errorMessage string) error {
	req := &dto.CreateLogRequest{
		OperatorID:      operatorID,
		OperationType:   operationType,
		ResourceType:    resourceType,
		ResourceID:      resourceID,
		OperationDetail: detail,
		OperationResult: 0, // 失败
		ErrorMessage:    errorMessage,
	}
	return s.CreateLog(ctx, req)
}

// GetLog 获取单个日志
func (s *operationLogService) GetLog(ctx context.Context, logID int64) (interface{}, error) {
	log, err := s.repo.GetByID(ctx, logID)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	return s.convertToOperationLogInfo(log), nil
}

// GetLogList 获取日志列表
func (s *operationLogService) GetLogList(ctx context.Context, req *dto.LogListRequest) (*dto.LogListResponse, error) {
	// 构建筛选条件
	filters := make(map[string]interface{})

	if req.OperatorID != nil {
		filters["operator_id"] = *req.OperatorID
	}
	if req.OperationType != "" {
		filters["operation_type"] = req.OperationType
	}
	if req.ResourceType != "" {
		filters["resource_type"] = req.ResourceType
	}
	if req.ResourceID != nil {
		filters["resource_id"] = *req.ResourceID
	}
	if req.Result != nil {
		filters["operation_result"] = *req.Result
	}
	if req.IPAddress != "" {
		filters["ip_address"] = req.IPAddress
	}

	// 处理日期范围
	if req.DateStart != "" {
		filters["date_start"] = req.DateStart
	}
	if req.DateEnd != "" {
		filters["date_end"] = req.DateEnd
	}

	// 如果只查询失败记录
	if req.OnlyFailures {
		filters["operation_result"] = 0
	}

	// 调用repository获取数据
	logs, total, err := s.repo.List(ctx, req.Page, req.PageSize, filters)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfo := s.convertToOperationLogInfo(log)
		if frontendLog, ok := logInfo.(*dto.FrontendOperationLogInfo); ok {
			// 类型断言成功，但需要转换为 OperationLogInfo
			logInfos = append(logInfos, &dto.OperationLogInfo{
				ID:              frontendLog.ID,
				OperatorName:    frontendLog.AdminName,
				OperationType:   "", // TODO: 从映射中获取
				OperationDetail: frontendLog.Action,
				OperationResult: frontendLog.Status,
				ResultText:      "", // TODO: 从状态码映射
				CreatedAt:       frontendLog.CreatedAt,
			})
		}
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &dto.LogListResponse{
		List:       logInfos,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// SearchLogs 搜索日志
func (s *operationLogService) SearchLogs(ctx context.Context, req *dto.LogSearchRequest) (*dto.LogListResponse, error) {
	// TODO: 实现搜索逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetLogsByOperator 获取指定操作员的日志
func (s *operationLogService) GetLogsByOperator(ctx context.Context, operatorID int64, req *dto.LogListRequest) (*dto.LogListResponse, error) {
	// TODO: 实现按操作员查询逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetLogsByResource 获取指定资源的日志
func (s *operationLogService) GetLogsByResource(ctx context.Context, resourceType string, resourceID int64) ([]*dto.OperationLogInfo, error) {
	// TODO: 实现按资源查询逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetLogsByOperationType 获取指定操作类型的日志
func (s *operationLogService) GetLogsByOperationType(ctx context.Context, operationType string, limit int) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetByOperationType(ctx, operationType, limit)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetLogsByResourceType 获取指定资源类型的日志
func (s *operationLogService) GetLogsByResourceType(ctx context.Context, resourceType string, limit int) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetByResourceType(ctx, resourceType, limit)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetRecentLogs 获取最近几小时的日志
func (s *operationLogService) GetRecentLogs(ctx context.Context, hours int) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetRecentLogs(ctx, hours)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetTodayLogs 获取今日日志
func (s *operationLogService) GetTodayLogs(ctx context.Context, operatorID int64) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetTodayLogs(ctx, operatorID)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetOperatorActivity 获取操作员活动日志
func (s *operationLogService) GetOperatorActivity(ctx context.Context, operatorID int64, startDate, endDate string) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetOperatorActivity(ctx, operatorID, startDate, endDate)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetFailedOperations 获取失败的操作日志
func (s *operationLogService) GetFailedOperations(ctx context.Context, limit int) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetFailedOperations(ctx, limit)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetSuspiciousOperations 获取可疑操作日志
func (s *operationLogService) GetSuspiciousOperations(ctx context.Context, limit int) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetSuspiciousOperations(ctx, limit)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetResourceOperationHistory 获取资源操作历史
func (s *operationLogService) GetResourceOperationHistory(ctx context.Context, resourceType string, resourceID int64) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetByResourceID(ctx, resourceType, resourceID)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// GetOperationChain 获取操作链
func (s *operationLogService) GetOperationChain(ctx context.Context, resourceType string, resourceID int64) ([]*dto.OperationChainResponse, error) {
	// TODO: 实现操作链逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetLogStats 获取日志统计
func (s *operationLogService) GetLogStats(ctx context.Context, startDate, endDate string) (*dto.LogStatsResponse, error) {
	stats, err := s.repo.GetOperationStats(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &dto.LogStatsResponse{
		Period:          fmt.Sprintf("%s to %s", startDate, endDate),
		TotalOperations: s.toInt64(stats["total_operations"]),
		SuccessCount:    s.toInt64(stats["success_operations"]),
		FailureCount:    s.toInt64(stats["failed_operations"]),
		SuccessRate:     s.toFloat64(stats["success_rate"]),
	}, nil
}

// toInt64 转换interface{}为int64
func (s *operationLogService) toInt64(value interface{}) int64 {
	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	default:
		return 0
	}
}

// toFloat64 转换interface{}为float64
func (s *operationLogService) toFloat64(value interface{}) float64 {
	switch v := value.(type) {
	case float64:
		return v
	case int64:
		return float64(v)
	case int:
		return float64(v)
	default:
		return 0.0
	}
}

// convertToFrontendLog 转换为前端操作日志信息
func (s *operationLogService) convertToFrontendLog(log interface{}) *dto.OperationLogInfo {
	logInfo := s.convertToOperationLogInfo(log)
	if frontendLog, ok := logInfo.(*dto.FrontendOperationLogInfo); ok {
		// 将前端日志转换为标准操作日志信息
		return &dto.OperationLogInfo{
			ID:              frontendLog.ID,
			OperatorName:    frontendLog.AdminName,
			OperationType:   "", // TODO: 从映射中获取
			OperationDetail: frontendLog.Action,
			OperationResult: frontendLog.Status,
			ResultText:      s.getStatusText(frontendLog.Status),
			CreatedAt:       frontendLog.CreatedAt,
		}
	}
	return &dto.OperationLogInfo{}
}

// getStatusText 根据状态码获取状态文本
func (s *operationLogService) getStatusText(status int) string {
	switch status {
	case 200:
		return "成功"
	case 500:
		return "失败"
	default:
		return "未知"
	}
}

// GetOperatorStats 获取操作员统计
func (s *operationLogService) GetOperatorStats(ctx context.Context, operatorID int64, startDate, endDate string) (*dto.OperatorLogStatsResponse, error) {
	// TODO: 实现操作员统计逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetOperationTypeStats 获取操作类型统计
func (s *operationLogService) GetOperationTypeStats(ctx context.Context, operationType string, startDate, endDate string) (*dto.OperationTypeStatsResponse, error) {
	// TODO: 实现操作类型统计逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetErrorRate 获取错误率
func (s *operationLogService) GetErrorRate(ctx context.Context, startDate, endDate string) (float64, error) {
	return s.repo.GetErrorRate(ctx, startDate, endDate)
}

// GetHourlyStats 获取小时统计
func (s *operationLogService) GetHourlyStats(ctx context.Context, date string) ([]*dto.HourlyStatsResponse, error) {
	// TODO: 实现小时统计逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetDailyStats 获取日统计
func (s *operationLogService) GetDailyStats(ctx context.Context, days int) ([]*dto.DailyStatsResponse, error) {
	// TODO: 实现日统计逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetActiveOperators 获取活跃操作员
func (s *operationLogService) GetActiveOperators(ctx context.Context, minutes int) ([]int64, error) {
	return s.repo.GetActiveOperators(ctx, minutes)
}

// GetRecentErrors 获取最近错误
func (s *operationLogService) GetRecentErrors(ctx context.Context, minutes int) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetRecentErrors(ctx, minutes)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// MonitorOperationHealth 监控操作健康
func (s *operationLogService) MonitorOperationHealth(ctx context.Context) (*dto.OperationHealthResponse, error) {
	// TODO: 实现健康监控逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetTopOperators 获取顶级操作员
func (s *operationLogService) GetTopOperators(ctx context.Context, limit int, startDate, endDate string) ([]*dto.TopOperatorResponse, error) {
	// TODO: 实现顶级操作员逻辑
	return nil, fmt.Errorf("not implemented")
}

// AnalyzeOperationPatterns 分析操作模式
func (s *operationLogService) AnalyzeOperationPatterns(ctx context.Context, operatorID int64, days int) (*dto.PatternAnalysisResponse, error) {
	// TODO: 实现模式分析逻辑
	return nil, fmt.Errorf("not implemented")
}

// DetectAnomalies 检测异常
func (s *operationLogService) DetectAnomalies(ctx context.Context, operatorID int64, days int) ([]*dto.AnomalyResponse, error) {
	// TODO: 实现异常检测逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetConcurrentOperations 获取并发操作
func (s *operationLogService) GetConcurrentOperations(ctx context.Context, timeWindow int) ([]*dto.ConcurrentOperationResponse, error) {
	// TODO: 实现并发操作逻辑
	return nil, fmt.Errorf("not implemented")
}

// AnalyzeFailureReasons 分析失败原因
func (s *operationLogService) AnalyzeFailureReasons(ctx context.Context, days int) (*dto.FailureAnalysisResponse, error) {
	// TODO: 实现失败原因分析逻辑
	return nil, fmt.Errorf("not implemented")
}

// SearchByKeyword 按关键词搜索
func (s *operationLogService) SearchByKeyword(ctx context.Context, keyword string, limit int) ([]*dto.OperationLogInfo, error) {
	// TODO: 实现关键词搜索逻辑
	return nil, fmt.Errorf("not implemented")
}

// SearchByIPAddress 按IP地址搜索
func (s *operationLogService) SearchByIPAddress(ctx context.Context, ipAddress string) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetLogsByIPAddress(ctx, ipAddress)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// SearchByDateRange 按日期范围搜索
func (s *operationLogService) SearchByDateRange(ctx context.Context, startDate, endDate string) ([]*dto.OperationLogInfo, error) {
	logs, err := s.repo.GetByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	var logInfos []*dto.OperationLogInfo
	for _, log := range logs {
		logInfos = append(logInfos, s.convertToFrontendLog(log))
	}
	return logInfos, nil
}

// SearchByPattern 按模式搜索
func (s *operationLogService) SearchByPattern(ctx context.Context, pattern string) ([]*dto.OperationLogInfo, error) {
	// TODO: 实现模式搜索逻辑
	return nil, fmt.Errorf("not implemented")
}

// ExportLogs 导出日志
func (s *operationLogService) ExportLogs(ctx context.Context, req *dto.LogExportRequest) (string, error) {
	// TODO: 实现日志导出逻辑
	return "", fmt.Errorf("not implemented")
}

// GenerateAuditReport 生成审计报告
func (s *operationLogService) GenerateAuditReport(ctx context.Context, req *dto.AuditReportRequest) (*dto.AuditReportResponse, error) {
	// TODO: 实现审计报告逻辑
	return nil, fmt.Errorf("not implemented")
}

// ExportOperatorActivity 导出操作员活动
func (s *operationLogService) ExportOperatorActivity(ctx context.Context, operatorID int64, startDate, endDate string) (string, error) {
	// TODO: 实现操作员活动导出逻辑
	return "", fmt.Errorf("not implemented")
}

// CleanupOldLogs 清理旧日志
func (s *operationLogService) CleanupOldLogs(ctx context.Context, retentionDays int) (int64, error) {
	return s.repo.CleanupLogs(ctx, retentionDays)
}

// ArchiveLogs 归档日志
func (s *operationLogService) ArchiveLogs(ctx context.Context, beforeDate string) error {
	return s.repo.ArchiveLogs(ctx, beforeDate)
}

// DeleteLogsByDate 按日期删除日志
func (s *operationLogService) DeleteLogsByDate(ctx context.Context, beforeDate string) (int64, error) {
	return s.repo.DeleteOldLogs(ctx, beforeDate)
}

// GetLogStorageStats 获取日志存储统计
func (s *operationLogService) GetLogStorageStats(ctx context.Context) (*dto.LogStorageStatsResponse, error) {
	// TODO: 实现存储统计逻辑
	return nil, fmt.Errorf("not implemented")
}

// GetLogRetentionPolicy 获取日志保留策略
func (s *operationLogService) GetLogRetentionPolicy(ctx context.Context) (*dto.LogRetentionPolicyResponse, error) {
	// TODO: 实现保留策略逻辑
	return nil, fmt.Errorf("not implemented")
}

// UpdateLogRetentionPolicy 更新日志保留策略
func (s *operationLogService) UpdateLogRetentionPolicy(ctx context.Context, policy *dto.LogRetentionPolicyRequest) error {
	// TODO: 实现更新保留策略逻辑
	return fmt.Errorf("not implemented")
}

// GetLogCategories 获取日志分类
func (s *operationLogService) GetLogCategories(ctx context.Context) ([]*dto.LogCategoryResponse, error) {
	// TODO: 实现日志分类逻辑
	return nil, fmt.Errorf("not implemented")
}

// ConfigureLogLevel 配置日志级别
func (s *operationLogService) ConfigureLogLevel(ctx context.Context, category string, level int) error {
	// TODO: 实现日志级别配置逻辑
	return fmt.Errorf("not implemented")
}

// ValidateLogIntegrity 验证日志完整性
func (s *operationLogService) ValidateLogIntegrity(ctx context.Context, logID int64) (*dto.IntegrityCheckResponse, error) {
	// TODO: 实现完整性验证逻辑
	return nil, fmt.Errorf("not implemented")
}

// DetectLogTampering 检测日志篡改
func (s *operationLogService) DetectLogTampering(ctx context.Context, startDate, endDate string) ([]*dto.TamperingDetectionResponse, error) {
	// TODO: 实现篡改检测逻辑
	return nil, fmt.Errorf("not implemented")
}

// EncryptSensitiveLogs 加密敏感日志
func (s *operationLogService) EncryptSensitiveLogs(ctx context.Context, logIDs []int64) error {
	// TODO: 实现敏感日志加密逻辑
	return fmt.Errorf("not implemented")
}

// SetupLogAlerts 设置日志告警
func (s *operationLogService) SetupLogAlerts(ctx context.Context, alertConfig *dto.LogAlertConfig) error {
	// TODO: 实现日志告警逻辑
	return fmt.Errorf("not implemented")
}

// TriggerSecurityAlert 触发安全告警
func (s *operationLogService) TriggerSecurityAlert(ctx context.Context, logID int64, alertType string) error {
	// TODO: 实现安全告警逻辑
	return fmt.Errorf("not implemented")
}

// NotifyAbnormalActivity 通知异常活动
func (s *operationLogService) NotifyAbnormalActivity(ctx context.Context, operatorID int64, activityType string) error {
	// TODO: 实现异常活动通知逻辑
	return fmt.Errorf("not implemented")
}

// convertToOperationLogInfo 转换为操作日志信息DTO（适配前端页面期望的字段格式）
func (s *operationLogService) convertToOperationLogInfo(log interface{}) interface{} {
	operationLog, ok := log.(*model.OperationLog)
	if !ok {
		return &dto.FrontendOperationLogInfo{
			ID:        0,
			AdminName: "",
			IP:        "",
			Method:    "",
			Path:      "",
			Action:    "",
			Params:    "",
			Status:    0,
			Duration:  0,
			CreatedAt: time.Now(),
		}
	}

	// 将业务操作日志字段映射到前端期望的HTTP请求日志字段
	return &dto.FrontendOperationLogInfo{
		ID:        operationLog.ID,
		AdminName: operationLog.OperatorName,                                  // 操作人
		IP:        operationLog.IPAddress,                                     // IP地址
		Method:    s.mapOperationTypeToMethod(operationLog.OperationType),     // 操作类型映射为HTTP方法
		Path:      s.mapResourceTypeToPath(operationLog.ResourceType),         // 资源类型映射为路径
		Action:    operationLog.OperationDetail,                               // 操作详情
		Params:    operationLog.RequestData,                                   // 请求数据
		Status:    s.mapOperationResultToStatus(operationLog.OperationResult), // 操作结果映射为HTTP状态码
		Duration:  0,                                                          // 暂时设置为0，因为模型中没有耗时字段
		CreatedAt: operationLog.CreatedAt,                                     // 创建时间
	}
}

// mapOperationTypeToMethod 将操作类型映射为HTTP方法
func (s *operationLogService) mapOperationTypeToMethod(operationType string) string {
	switch operationType {
	case model.OperationTypeCreate:
		return "POST"
	case model.OperationTypeUpdate:
		return "PUT"
	case model.OperationTypeDelete:
		return "DELETE"
	case model.OperationTypeQuery:
		return "GET"
	case model.OperationTypeLogin:
		return "POST"
	case model.OperationTypeLogout:
		return "POST"
	default:
		return "POST"
	}
}

// mapResourceTypeToPath 将资源类型映射为路径
func (s *operationLogService) mapResourceTypeToPath(resourceType string) string {
	switch resourceType {
	case model.ResourceTypeOrder:
		return "/api/admin/orders"
	case model.ResourceTypeUser:
		return "/api/admin/users"
	case model.ResourceTypeAttendant:
		return "/api/admin/attendants"
	case model.ResourceTypeTask:
		return "/api/admin/tasks"
	case model.ResourceTypeAssignment:
		return "/api/admin/assignments"
	case model.ResourceTypeRefund:
		return "/api/admin/refunds"
	default:
		return "/api/admin/" + strings.ToLower(resourceType)
	}
}

// mapOperationResultToStatus 将操作结果映射为HTTP状态码
func (s *operationLogService) mapOperationResultToStatus(operationResult int) int {
	switch operationResult {
	case model.OperationResultSuccess:
		return 200 // 成功
	case model.OperationResultFailed:
		return 500 // 失败
	default:
		return 200 // 默认成功
	}
}
