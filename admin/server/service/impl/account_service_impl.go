package impl

import (
	"context"
	"fmt"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/service"
	"gorm.io/gorm"
)

// AccountServiceImpl 账户服务实现
type AccountServiceImpl struct {
	accountRepo repository.IAccountRepository
	userRepo    repository.IUserRepository
}

// NewAccountService 创建账户服务实例
func NewAccountService(accountRepo repository.IAccountRepository, userRepo repository.IUserRepository) service.IAccountService {
	return &AccountServiceImpl{
		accountRepo: accountRepo,
		userRepo:    userRepo,
	}
}

// GetAccountInfo 获取账户信息
func (s *AccountServiceImpl) GetAccountInfo(ctx context.Context, userID uint) (*dto.AccountInfoResponse, error) {
	// 获取用户信息
	user, err := s.userRepo.GetUserByID(userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 获取账户信息
	account, err := s.accountRepo.GetByUserID(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户还没有账户，返回默认值
			return &dto.AccountInfoResponse{
				UserID:           userID,
				UserName:         user.Nickname,
				Phone:            user.Phone,
				Balance:          0,
				Frozen:           0,
				AvailableBalance: 0,
				TotalIncome:      0,
				TotalWithdrawal:  0,
			}, nil
		}
		return nil, fmt.Errorf("获取账户信息失败: %w", err)
	}

	return &dto.AccountInfoResponse{
		UserID:           userID,
		UserName:         user.Nickname,
		Phone:            user.Phone,
		Balance:          account.Balance,
		Frozen:           account.FrozenBalance,
		AvailableBalance: account.GetAvailableBalance(),
		TotalIncome:      account.TotalIncome,
		TotalWithdrawal:  account.TotalWithdrawal,
	}, nil
}

// GetTransactionHistory 获取账户变动历史
func (s *AccountServiceImpl) GetTransactionHistory(ctx context.Context, req *dto.AccountTransactionRequest) (*dto.AccountTransactionResponse, error) {
	// 获取账户变动记录
	transactions, total, err := s.accountRepo.GetTransactionsByUserID(ctx, req.UserID, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("获取账户变动记录失败: %w", err)
	}

	// 转换为DTO
	items := make([]*dto.AccountTransactionItem, len(transactions))
	for i, tx := range transactions {
		items[i] = &dto.AccountTransactionItem{
			ID:               tx.ID,
			Type:             tx.Type,
			TypeText:         tx.GetTypeText(),
			Amount:           tx.Amount,
			Balance:          tx.Balance,
			FrozenBalance:    tx.FrozenBalance,
			RelatedType:      tx.RelatedType,
			RelatedID:        tx.RelatedID,
			Description:      tx.Description,
			OperatorID:       tx.OperatorID,
			OperatorType:     tx.OperatorType,
			OperatorTypeText: tx.GetOperatorTypeText(),
			OperatedAt:       tx.OperatedAt,
			CreatedAt:        tx.CreatedAt,
		}
	}

	return &dto.AccountTransactionResponse{
		Total:        total,
		Page:         req.Page,
		PageSize:     req.PageSize,
		Transactions: items,
	}, nil
}

// AdjustBalance 人工调整余额
func (s *AccountServiceImpl) AdjustBalance(ctx context.Context, req *dto.BalanceAdjustmentRequest, operatorID uint) (*dto.BalanceAdjustmentResponse, error) {
	// 验证调整金额
	if req.Amount == 0 {
		return nil, fmt.Errorf("调整金额不能为0")
	}

	// 获取用户信息
	user, err := s.userRepo.GetUserByID(req.UserID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("用户不存在")
		}
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 获取调整前的账户信息
	beforeAccount, err := s.accountRepo.GetByUserID(ctx, req.UserID)
	var beforeBalance float64
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("获取账户信息失败: %w", err)
		}
		// 账户不存在，使用默认值
		beforeBalance = 0
	} else {
		beforeBalance = beforeAccount.Balance
	}

	// 执行余额调整
	var adjustErr error
	if req.Amount > 0 {
		// 增加余额
		adjustErr = s.accountRepo.AddIncome(ctx, req.UserID, req.Amount, "adjustment", 0, fmt.Sprintf("人工调整余额: +%.2f，原因: %s", req.Amount, req.Reason))
	} else {
		// 减少余额（需要先检查余额是否足够）
		if beforeAccount != nil && beforeAccount.GetAvailableBalance() < -req.Amount {
			return nil, fmt.Errorf("可用余额不足，当前可用余额: %.2f", beforeAccount.GetAvailableBalance())
		}
		
		// 减少余额
		adjustErr = s.accountRepo.DeductAmount(ctx, req.UserID, -req.Amount, "adjustment", 0, fmt.Sprintf("人工调整余额: %.2f，原因: %s", req.Amount, req.Reason), &operatorID)
	}

	if adjustErr != nil {
		return nil, fmt.Errorf("调整余额失败: %w", adjustErr)
	}

	// 获取调整后的账户信息
	afterAccount, err := s.accountRepo.GetByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("获取调整后账户信息失败: %w", err)
	}

	return &dto.BalanceAdjustmentResponse{
		UserID:        req.UserID,
		UserName:      user.Nickname,
		AdjustAmount:  req.Amount,
		BeforeBalance: beforeBalance,
		AfterBalance:  afterAccount.Balance,
		Reason:        req.Reason,
		OperatorID:    operatorID,
	}, nil
}

// ProcessWithdrawal 处理提现申请
func (s *AccountServiceImpl) ProcessWithdrawal(ctx context.Context, userID uint, amount float64, withdrawalID uint) error {
	// 冻结提现金额
	return s.accountRepo.FreezeAmount(ctx, userID, amount, "withdrawal", withdrawalID, "提现冻结", nil)
}