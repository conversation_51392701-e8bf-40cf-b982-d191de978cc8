package impl

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/logger"
	"go.uber.org/zap"
)

// orderCompletionService 订单完成服务实现（管理员版本）
type orderCompletionService struct {
	db        *gorm.DB
	orderRepo repository.IOrderRepository
	logger    *zap.Logger
}

// NewOrderCompletionService 创建订单完成服务
func NewOrderCompletionService(
	db *gorm.DB,
	orderRepo repository.IOrderRepository,
) service.IOrderCompletionService {
	return &orderCompletionService{
		db:        db,
		orderRepo: orderRepo,
		logger:    logger.GetLogger(),
	}
}

// CompleteOrder 统一的订单完成入口
func (s *orderCompletionService) CompleteOrder(ctx context.Context, req *service.OrderCompletionRequest) (*service.OrderCompletionResponse, error) {
	s.logger.Info("开始处理订单完成请求",
		zap.Uint("order_id", req.OrderID),
		zap.Int("completion_type", int(req.CompletionType)),
		zap.String("reason", req.Reason),
	)

	switch req.CompletionType {
	case service.CompletionTypeAttendant:
		return s.CompleteByAttendant(ctx, req.OrderID, req.Evidence)
	case service.CompletionTypeAdminForce:
		return s.ForceCompleteByAdmin(ctx, req.OrderID, req.OperatorID, req.Reason)
	case service.CompletionTypeExceptionHandler:
		return s.CompleteByExceptionHandler(ctx, req.OrderID, fmt.Sprintf("%d", req.OperatorID), req.Reason)
	case service.CompletionTypeAutoSystem:
		return s.AutoComplete(ctx, req.OrderID, req.Reason)
	default:
		return nil, fmt.Errorf("不支持的完成类型: %d", req.CompletionType)
	}
}

// CompleteByAttendant 陪诊师完成订单
func (s *orderCompletionService) CompleteByAttendant(ctx context.Context, orderID uint, evidence service.ServiceEvidence) (*service.OrderCompletionResponse, error) {
	s.logger.Info("陪诊师完成订单", zap.Uint("order_id", orderID))

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 获取订单信息
	order, err := s.orderRepo.GetOrderByID(orderID)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	// 2. 检查订单状态
	if order.Status != 3 { // 假设3是服务中状态
		tx.Rollback()
		return nil, fmt.Errorf("订单状态不允许完成，当前状态: %d", order.Status)
	}

	// 3. 更新订单状态为待审核 (status = 8)
	now := time.Now()
	order.Status = 8 // 待审核状态
	order.ActualServiceEndTime = &now
	order.UpdatedAt = now

	if err := tx.Save(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 4. 创建审核记录（简化版本，实际应该调用后端的ReviewService）
	// 这里暂时只更新订单状态，审核记录的创建需要集成后端服务

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("陪诊师完成订单成功", zap.Uint("order_id", orderID))

	return &service.OrderCompletionResponse{
		OrderID:      orderID,
		Status:       8, // 待审核
		ReviewStatus: 1, // 待审核
		CompletedAt:  now,
		Message:      "订单已完成，等待审核",
	}, nil
}

// ForceCompleteByAdmin 管理员强制完成订单
func (s *orderCompletionService) ForceCompleteByAdmin(ctx context.Context, orderID uint, adminID uint, reason string) (*service.OrderCompletionResponse, error) {
	s.logger.Info("管理员强制完成订单",
		zap.Uint("order_id", orderID),
		zap.Uint("admin_id", adminID),
		zap.String("reason", reason),
	)

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 获取订单信息
	order, err := s.orderRepo.GetOrderByID(orderID)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	// 2. 检查订单状态（管理员可以强制完成多种状态的订单）
	if order.Status == 10 || order.Status == 13 { // 已完成或已结算
		tx.Rollback()
		return nil, fmt.Errorf("订单已完成，无需重复操作")
	}

	// 3. 直接更新订单状态为已完成 (status = 10)，因为管理员强制完成自动审核通过
	now := time.Now()
	order.Status = 10 // 已完成状态（审核通过）
	if order.ActualServiceEndTime == nil {
		order.ActualServiceEndTime = &now
	}
	order.UpdatedAt = now

	if err := tx.Save(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 4. 创建审核记录（自动审核通过）
	// 这里暂时只更新订单状态，审核记录的创建需要集成后端服务

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("管理员强制完成订单成功", zap.Uint("order_id", orderID))

	return &service.OrderCompletionResponse{
		OrderID:      orderID,
		Status:       10, // 已完成
		ReviewStatus: 3,  // 审核通过
		CompletedAt:  now,
		Message:      "订单已强制完成，自动审核通过",
	}, nil
}

// CompleteByExceptionHandler 异常处理完成订单
func (s *orderCompletionService) CompleteByExceptionHandler(ctx context.Context, orderID uint, handlerID string, reason string) (*service.OrderCompletionResponse, error) {
	s.logger.Info("异常处理完成订单",
		zap.Uint("order_id", orderID),
		zap.String("handler_id", handlerID),
		zap.String("reason", reason),
	)

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 获取订单信息
	order, err := s.orderRepo.GetOrderByID(orderID)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	// 2. 检查订单状态
	if order.Status == 10 || order.Status == 13 { // 已完成或已结算
		tx.Rollback()
		return nil, fmt.Errorf("订单已完成，无需重复操作")
	}

	// 3. 直接更新订单状态为已完成 (status = 10)，因为异常处理完成自动审核通过
	now := time.Now()
	order.Status = 10 // 已完成状态（审核通过）
	if order.ActualServiceEndTime == nil {
		order.ActualServiceEndTime = &now
	}
	order.UpdatedAt = now

	if err := tx.Save(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 4. 创建审核记录（自动审核通过）
	// 这里暂时只更新订单状态，审核记录的创建需要集成后端服务

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("异常处理完成订单成功", zap.Uint("order_id", orderID))

	return &service.OrderCompletionResponse{
		OrderID:      orderID,
		Status:       10, // 已完成
		ReviewStatus: 3,  // 审核通过
		CompletedAt:  now,
		Message:      "订单异常处理完成，自动审核通过",
	}, nil
}

// AutoComplete 系统自动完成订单
func (s *orderCompletionService) AutoComplete(ctx context.Context, orderID uint, reason string) (*service.OrderCompletionResponse, error) {
	s.logger.Info("系统自动完成订单",
		zap.Uint("order_id", orderID),
		zap.String("reason", reason),
	)

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 获取订单信息
	order, err := s.orderRepo.GetOrderByID(orderID)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	// 2. 检查订单状态
	if order.Status == 10 || order.Status == 13 { // 已完成或已结算
		tx.Rollback()
		return nil, fmt.Errorf("订单已完成，无需重复操作")
	}

	// 3. 直接更新订单状态为已完成 (status = 10)，因为系统自动完成自动审核通过
	now := time.Now()
	order.Status = 10 // 已完成状态（审核通过）
	if order.ActualServiceEndTime == nil {
		order.ActualServiceEndTime = &now
	}
	order.UpdatedAt = now

	if err := tx.Save(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 4. 创建审核记录（自动审核通过）
	// 这里暂时只更新订单状态，审核记录的创建需要集成后端服务

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("系统自动完成订单成功", zap.Uint("order_id", orderID))

	return &service.OrderCompletionResponse{
		OrderID:      orderID,
		Status:       10, // 已完成
		ReviewStatus: 3,  // 审核通过
		CompletedAt:  now,
		Message:      "订单系统自动完成，自动审核通过",
	}, nil
}

// GetOrderCompletion 获取订单完成记录
func (s *orderCompletionService) GetOrderCompletion(ctx context.Context, orderID uint) (*service.OrderCompletionResponse, error) {
	s.logger.Info("获取订单完成记录", zap.Uint("order_id", orderID))

	// 获取订单信息
	order, err := s.orderRepo.GetOrderByID(orderID)
	if err != nil {
		return nil, fmt.Errorf("获取订单失败: %w", err)
	}

	// 构造响应
	response := &service.OrderCompletionResponse{
		OrderID: orderID,
		Status:  order.Status,
	}

	// 根据状态设置审核状态和消息
	switch order.Status {
	case 8: // 待审核
		response.ReviewStatus = 1
		response.Message = "订单已完成，等待审核"
	case 9: // 审核中
		response.ReviewStatus = 2
		response.Message = "订单审核中"
	case 10: // 已完成（审核通过）
		response.ReviewStatus = 3
		response.Message = "订单已完成，审核通过"
	case 13: // 已结算
		response.ReviewStatus = 3
		response.Message = "订单已结算"
	default:
		response.Message = "订单未完成"
	}

	if order.ActualServiceEndTime != nil {
		response.CompletedAt = *order.ActualServiceEndTime
	}

	return response, nil
}

// GetPendingCompletionOrders 获取待完成订单列表
func (s *orderCompletionService) GetPendingCompletionOrders(ctx context.Context, limit int) ([]*service.OrderCompletionResponse, error) {
	s.logger.Info("获取待完成订单列表", zap.Int("limit", limit))

	// 这里需要查询状态为3（服务中）的订单
	// 暂时返回空列表，实际实现需要根据具体的查询需求来实现
	return []*service.OrderCompletionResponse{}, nil
}
