package impl

import (
	"context"
	"fmt"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/model"
	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type systemConfigService struct {
	configRepo repository.ISystemConfigRepository
	logger     *zap.Logger
}

func NewSystemConfigService(
	configRepo repository.ISystemConfigRepository,
	logger *zap.Logger,
) service.ISystemConfigService {
	return &systemConfigService{
		configRepo: configRepo,
		logger:     logger,
	}
}

func (s *systemConfigService) GetConfigList(ctx context.Context, req *dto.SystemConfigListRequest) (*dto.SystemConfigListResponse, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	configs, total, err := s.configRepo.GetList(ctx, req.Category, req.Status, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("获取配置列表失败: %w", err)
	}

	items := make([]*dto.SystemConfigItem, len(configs))
	for i, config := range configs {
		items[i] = s.convertToDTO(config)
	}

	return &dto.SystemConfigListResponse{
		List:  items,
		Total: total,
		Page:  req.Page,
	}, nil
}

func (s *systemConfigService) GetConfigDetail(ctx context.Context, configID uint) (*dto.SystemConfigDetailResponse, error) {
	config, err := s.configRepo.GetByID(ctx, configID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置不存在")
		}
		return nil, fmt.Errorf("获取配置详情失败: %w", err)
	}

	return &dto.SystemConfigDetailResponse{
		SystemConfigItem: s.convertToDTO(config),
	}, nil
}

func (s *systemConfigService) CreateConfig(ctx context.Context, req *dto.CreateSystemConfigRequest) (*dto.SystemConfigResponse, error) {
	exists, err := s.configRepo.ExistsByKey(ctx, req.Key)
	if err != nil {
		return nil, fmt.Errorf("检查配置键失败: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("配置键已存在: %s", req.Key)
	}

	config := &model.SystemConfig{
		Key:         req.Key,
		Value:       req.Value,
		Description: req.Description,
		Status:      model.SystemConfigStatusEnabled,
	}

	err = s.configRepo.Create(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("创建配置失败: %w", err)
	}

	return &dto.SystemConfigResponse{
		SystemConfigItem: s.convertToDTO(config),
		Message:          "配置创建成功",
	}, nil
}

func (s *systemConfigService) UpdateConfig(ctx context.Context, configID uint, req *dto.UpdateSystemConfigRequest) (*dto.SystemConfigResponse, error) {
	config, err := s.configRepo.GetByID(ctx, configID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("配置不存在")
		}
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}

	if req.Value != "" {
		config.Value = req.Value
	}
	if req.Description != "" {
		config.Description = req.Description
	}
	if req.Status != nil {
		config.Status = *req.Status
	}

	err = s.configRepo.Update(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("更新配置失败: %w", err)
	}

	return &dto.SystemConfigResponse{
		SystemConfigItem: s.convertToDTO(config),
		Message:          "配置更新成功",
	}, nil
}

func (s *systemConfigService) DeleteConfig(ctx context.Context, configID uint) error {
	_, err := s.configRepo.GetByID(ctx, configID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("配置不存在")
		}
		return fmt.Errorf("获取配置失败: %w", err)
	}

	err = s.configRepo.Delete(ctx, configID)
	if err != nil {
		return fmt.Errorf("删除配置失败: %w", err)
	}

	return nil
}

func (s *systemConfigService) BatchUpdateConfigs(ctx context.Context, req *dto.BatchUpdateSystemConfigRequest) (*dto.BatchUpdateSystemConfigResponse, error) {
	var successKeys []string
	var failedKeys []string
	errorMessages := make(map[string]string)

	for configKey, configValue := range req.Configs {
		config, err := s.configRepo.GetByKey(ctx, configKey)
		if err != nil {
			failedKeys = append(failedKeys, configKey)
			errorMessages[configKey] = "配置不存在"
			continue
		}

		config.Value = configValue

		err = s.configRepo.Update(ctx, config)
		if err != nil {
			failedKeys = append(failedKeys, configKey)
			errorMessages[configKey] = err.Error()
			continue
		}

		successKeys = append(successKeys, configKey)
	}

	return &dto.BatchUpdateSystemConfigResponse{
		UpdatedCount:  len(successKeys),
		SuccessKeys:   successKeys,
		FailedKeys:    failedKeys,
		ErrorMessages: errorMessages,
	}, nil
}

func (s *systemConfigService) TestConfig(ctx context.Context, req *dto.TestSystemConfigRequest) (*dto.TestSystemConfigResponse, error) {
	testResult := true
	testMessage := "配置测试通过"

	if req.Key == "" {
		testResult = false
		testMessage = "配置键不能为空"
	}

	if req.Value == "" {
		testResult = false
		testMessage = "配置值不能为空"
	}

	return &dto.TestSystemConfigResponse{
		Key:         req.Key,
		TestResult:  testResult,
		TestMessage: testMessage,
	}, nil
}

func (s *systemConfigService) GetConfigValue(ctx context.Context, configKey string) (interface{}, error) {
	config, err := s.configRepo.GetByKey(ctx, configKey)
	if err != nil {
		return nil, err
	}
	return config.Value, nil
}

func (s *systemConfigService) SetConfigValue(ctx context.Context, configKey string, value interface{}, operatorID uint) error {
	config, err := s.configRepo.GetByKey(ctx, configKey)
	if err != nil {
		return err
	}

	config.Value = fmt.Sprintf("%v", value)

	err = s.configRepo.Update(ctx, config)
	if err != nil {
		return err
	}

	return nil
}

func (s *systemConfigService) convertToDTO(config *model.SystemConfig) *dto.SystemConfigItem {
	return &dto.SystemConfigItem{
		ID:          config.ID,
		Key:         config.Key,
		Value:       config.Value,
		Description: config.Description,
		Status:      config.Status,
		CreatedAt:   config.CreatedAt,
		UpdatedAt:   config.UpdatedAt,
	}
}
