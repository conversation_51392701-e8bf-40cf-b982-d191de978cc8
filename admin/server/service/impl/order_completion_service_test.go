package impl

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/model"
	"github.com/gemeijie/peizhen/admin/server/service"
)

// MockOrderRepository 模拟订单仓库
type MockOrderRepository struct {
	mock.Mock
}

func (m *MockOrderRepository) GetOrderByID(orderID uint) (*model.Order, error) {
	args := m.Called(orderID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Order), args.Error(1)
}

func (m *MockOrderRepository) CreateOrder(order *model.Order) error {
	args := m.Called(order)
	return args.Error(0)
}

func (m *MockOrderRepository) UpdateOrder(order *model.Order) error {
	args := m.Called(order)
	return args.Error(0)
}

func (m *MockOrderRepository) GetOrderList(req *dto.OrderListRequest) ([]*model.Order, int64, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*model.Order), args.Get(1).(int64), args.Error(2)
}

func (m *MockOrderRepository) GetOrderDetailList(req *dto.OrderListRequest) ([]model.OrderDetail, int64, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]model.OrderDetail), args.Get(1).(int64), args.Error(2)
}

func (m *MockOrderRepository) GetOrderDetail(id uint) (*model.OrderDetail, error) {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.OrderDetail), args.Error(1)
}

func (m *MockOrderRepository) GetOrderStatistics() (*dto.OrderStatistics, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dto.OrderStatistics), args.Error(1)
}

func (m *MockOrderRepository) GetHospitalList() ([]string, error) {
	args := m.Called()
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]string), args.Error(1)
}

// MockDB 模拟数据库
type MockDB struct {
	mock.Mock
}

func (m *MockDB) Begin() *gorm.DB {
	args := m.Called()
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Save(value interface{}) *gorm.DB {
	args := m.Called(value)
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Commit() *gorm.DB {
	args := m.Called()
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Rollback() *gorm.DB {
	args := m.Called()
	return args.Get(0).(*gorm.DB)
}

func (m *MockDB) Error() error {
	args := m.Called()
	return args.Error(0)
}

// TestOrderCompletionService_ForceCompleteByAdmin 测试管理员强制完成订单
func TestOrderCompletionService_ForceCompleteByAdmin(t *testing.T) {
	// 准备测试数据
	orderID := uint(123)
	adminID := uint(456)
	reason := "测试强制完成"

	// 创建模拟对象
	mockOrderRepo := new(MockOrderRepository)
	mockDB := &gorm.DB{} // 简化的DB对象

	// 创建测试订单
	testOrder := &model.Order{
		ID:     orderID,
		Status: 3, // 服务中状态
	}

	// 设置期望调用
	mockOrderRepo.On("GetOrderByID", orderID).Return(testOrder, nil)

	// 创建服务实例
	service := NewOrderCompletionService(mockDB, mockOrderRepo)

	// 执行测试
	ctx := context.Background()
	response, err := service.ForceCompleteByAdmin(ctx, orderID, adminID, reason)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, orderID, response.OrderID)
	assert.Equal(t, 10, response.Status)      // 已完成状态
	assert.Equal(t, 3, response.ReviewStatus) // 审核通过
	assert.Contains(t, response.Message, "强制完成")

	// 验证模拟对象的调用
	mockOrderRepo.AssertExpectations(t)
}

// TestOrderCompletionService_CompleteByExceptionHandler 测试异常处理完成订单
func TestOrderCompletionService_CompleteByExceptionHandler(t *testing.T) {
	// 准备测试数据
	orderID := uint(123)
	handlerID := "handler_001"
	reason := "测试异常处理完成"

	// 创建模拟对象
	mockOrderRepo := new(MockOrderRepository)
	mockDB := &gorm.DB{} // 简化的DB对象

	// 创建测试订单
	testOrder := &model.Order{
		ID:     orderID,
		Status: 3, // 服务中状态
	}

	// 设置期望调用
	mockOrderRepo.On("GetOrderByID", orderID).Return(testOrder, nil)

	// 创建服务实例
	service := NewOrderCompletionService(mockDB, mockOrderRepo)

	// 执行测试
	ctx := context.Background()
	response, err := service.CompleteByExceptionHandler(ctx, orderID, handlerID, reason)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, orderID, response.OrderID)
	assert.Equal(t, 10, response.Status)      // 已完成状态
	assert.Equal(t, 3, response.ReviewStatus) // 审核通过
	assert.Contains(t, response.Message, "异常处理完成")

	// 验证模拟对象的调用
	mockOrderRepo.AssertExpectations(t)
}

// TestOrderCompletionService_AutoComplete 测试系统自动完成订单
func TestOrderCompletionService_AutoComplete(t *testing.T) {
	// 准备测试数据
	orderID := uint(123)
	reason := "测试系统自动完成"

	// 创建模拟对象
	mockOrderRepo := new(MockOrderRepository)
	mockDB := &gorm.DB{} // 简化的DB对象

	// 创建测试订单
	testOrder := &model.Order{
		ID:     orderID,
		Status: 3, // 服务中状态
	}

	// 设置期望调用
	mockOrderRepo.On("GetOrderByID", orderID).Return(testOrder, nil)

	// 创建服务实例
	service := NewOrderCompletionService(mockDB, mockOrderRepo)

	// 执行测试
	ctx := context.Background()
	response, err := service.AutoComplete(ctx, orderID, reason)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, orderID, response.OrderID)
	assert.Equal(t, 10, response.Status)      // 已完成状态
	assert.Equal(t, 3, response.ReviewStatus) // 审核通过
	assert.Contains(t, response.Message, "自动完成")

	// 验证模拟对象的调用
	mockOrderRepo.AssertExpectations(t)
}

// TestCompletionTypeHelpers 测试完成类型辅助函数
func TestCompletionTypeHelpers(t *testing.T) {
	// 测试GetCompletionTypeText
	assert.Equal(t, "陪诊师完成", service.GetCompletionTypeText(service.CompletionTypeAttendant))
	assert.Equal(t, "管理员强制完成", service.GetCompletionTypeText(service.CompletionTypeAdminForce))
	assert.Equal(t, "异常处理完成", service.GetCompletionTypeText(service.CompletionTypeExceptionHandler))
	assert.Equal(t, "系统自动完成", service.GetCompletionTypeText(service.CompletionTypeAutoSystem))
	assert.Equal(t, "未知类型", service.GetCompletionTypeText(service.CompletionType(999)))

	// 测试ShouldAutoApprove
	assert.False(t, service.ShouldAutoApprove(service.CompletionTypeAttendant))
	assert.True(t, service.ShouldAutoApprove(service.CompletionTypeAdminForce))
	assert.True(t, service.ShouldAutoApprove(service.CompletionTypeExceptionHandler))
	assert.True(t, service.ShouldAutoApprove(service.CompletionTypeAutoSystem))
	assert.False(t, service.ShouldAutoApprove(service.CompletionType(999)))
}

// TestOrderCompletionService_GetOrderCompletion 测试获取订单完成记录
func TestOrderCompletionService_GetOrderCompletion(t *testing.T) {
	// 准备测试数据
	orderID := uint(123)

	// 创建模拟对象
	mockOrderRepo := new(MockOrderRepository)
	mockDB := &gorm.DB{} // 简化的DB对象

	// 创建测试订单（已完成状态）
	now := time.Now()
	testOrder := &model.Order{
		ID:                   orderID,
		Status:               10, // 已完成状态
		ActualServiceEndTime: &now,
	}

	// 设置期望调用
	mockOrderRepo.On("GetOrderByID", orderID).Return(testOrder, nil)

	// 创建服务实例
	service := NewOrderCompletionService(mockDB, mockOrderRepo)

	// 执行测试
	ctx := context.Background()
	response, err := service.GetOrderCompletion(ctx, orderID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, orderID, response.OrderID)
	assert.Equal(t, 10, response.Status)      // 已完成状态
	assert.Equal(t, 3, response.ReviewStatus) // 审核通过
	assert.Contains(t, response.Message, "审核通过")
	assert.Equal(t, now.Unix(), response.CompletedAt.Unix())

	// 验证模拟对象的调用
	mockOrderRepo.AssertExpectations(t)
}
