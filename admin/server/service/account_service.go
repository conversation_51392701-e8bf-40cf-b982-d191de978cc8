package service

import (
	"context"

	"github.com/gemeijie/peizhen/admin/server/dto"
)

// IAccountService 账户服务接口
type IAccountService interface {
	// GetAccountInfo 获取账户信息
	GetAccountInfo(ctx context.Context, userID uint) (*dto.AccountInfoResponse, error)
	// GetTransactionHistory 获取账户变动历史
	GetTransactionHistory(ctx context.Context, req *dto.AccountTransactionRequest) (*dto.AccountTransactionResponse, error)
	// AdjustBalance 人工调整余额
	AdjustBalance(ctx context.Context, req *dto.BalanceAdjustmentRequest, operatorID uint) (*dto.BalanceAdjustmentResponse, error)
}