package service

import (
	"context"
	"time"
)

// CompletionType 订单完成类型
type CompletionType int

const (
	CompletionTypeAttendant        CompletionType = 1 // 陪诊师完成
	CompletionTypeAdminForce       CompletionType = 2 // 管理员强制完成
	CompletionTypeExceptionHandler CompletionType = 3 // 异常处理完成
	CompletionTypeAutoSystem       CompletionType = 4 // 系统自动完成
)

// GetCompletionTypeText 获取完成类型文本
func GetCompletionTypeText(completionType CompletionType) string {
	switch completionType {
	case CompletionTypeAttendant:
		return "陪诊师完成"
	case CompletionTypeAdminForce:
		return "管理员强制完成"
	case CompletionTypeExceptionHandler:
		return "异常处理完成"
	case CompletionTypeAutoSystem:
		return "系统自动完成"
	default:
		return "未知类型"
	}
}

// ShouldAutoApprove 判断是否应该自动审核
func ShouldAutoApprove(completionType CompletionType) bool {
	switch completionType {
	case CompletionTypeAdminForce, CompletionTypeExceptionHandler, CompletionTypeAutoSystem:
		return true
	case CompletionTypeAttendant:
		return false
	default:
		return false
	}
}

// ServiceEvidence 服务凭证
type ServiceEvidence struct {
	Photos      []string `json:"photos"`       // 服务照片
	Description string   `json:"description"`  // 服务描述
	Location    string   `json:"location"`     // 服务地点
	Duration    int      `json:"duration"`     // 服务时长（分钟）
}

// OrderCompletionRequest 订单完成请求
type OrderCompletionRequest struct {
	OrderID        uint            `json:"order_id" binding:"required"`
	CompletionType CompletionType  `json:"completion_type" binding:"required"`
	Evidence       ServiceEvidence `json:"evidence"`
	Reason         string          `json:"reason"`
	OperatorID     uint            `json:"operator_id"`
	OperatorName   string          `json:"operator_name"`
}

// ForceCompleteByAdminRequest 管理员强制完成请求
type ForceCompleteByAdminRequest struct {
	Reason string `json:"reason" binding:"required,max=500"`
}

// CompleteByExceptionHandlerRequest 异常处理完成请求
type CompleteByExceptionHandlerRequest struct {
	HandlerID string `json:"handler_id" binding:"required"`
	Reason    string `json:"reason" binding:"required,max=500"`
}

// AutoCompleteRequest 自动完成请求
type AutoCompleteRequest struct {
	Reason string `json:"reason" binding:"required,max=500"`
}

// OrderCompletionResponse 订单完成响应
type OrderCompletionResponse struct {
	OrderID      uint      `json:"order_id"`
	ReviewID     uint      `json:"review_id"`
	Status       int       `json:"status"`
	ReviewStatus int       `json:"review_status"`
	CompletedAt  time.Time `json:"completed_at"`
	Message      string    `json:"message"`
}

// IOrderCompletionService 订单完成服务接口（管理员版本）
type IOrderCompletionService interface {
	// CompleteOrder 统一的订单完成入口
	CompleteOrder(ctx context.Context, req *OrderCompletionRequest) (*OrderCompletionResponse, error)

	// CompleteByAttendant 陪诊师完成订单
	CompleteByAttendant(ctx context.Context, orderID uint, evidence ServiceEvidence) (*OrderCompletionResponse, error)

	// ForceCompleteByAdmin 管理员强制完成订单
	ForceCompleteByAdmin(ctx context.Context, orderID uint, adminID uint, reason string) (*OrderCompletionResponse, error)

	// CompleteByExceptionHandler 异常处理完成订单
	CompleteByExceptionHandler(ctx context.Context, orderID uint, handlerID string, reason string) (*OrderCompletionResponse, error)

	// AutoComplete 系统自动完成订单
	AutoComplete(ctx context.Context, orderID uint, reason string) (*OrderCompletionResponse, error)

	// GetOrderCompletion 获取订单完成记录
	GetOrderCompletion(ctx context.Context, orderID uint) (*OrderCompletionResponse, error)

	// GetPendingCompletionOrders 获取待完成订单列表
	GetPendingCompletionOrders(ctx context.Context, limit int) ([]*OrderCompletionResponse, error)
}
