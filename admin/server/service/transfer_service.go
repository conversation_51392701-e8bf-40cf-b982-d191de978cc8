package service

import (
	"context"
	"time"
)

// TransferRequest 转账请求
type TransferRequest struct {
	WithdrawalID uint   `json:"withdrawal_id"` // 提现ID
	Amount       int64  `json:"amount"`        // 转账金额（分）
	OpenID       string `json:"openid"`        // 用户OpenID
	UserName     string `json:"user_name"`     // 收款用户姓名
	Remark       string `json:"remark"`        // 转账备注
	OperatorID   uint   `json:"operator_id"`   // 操作人ID
}

// TransferResult 转账结果
type TransferResult struct {
	OutBatchNo  string     `json:"out_batch_no"`  // 商户批次单号
	BatchID     string     `json:"batch_id"`      // 微信批次单号
	BatchStatus string     `json:"batch_status"`  // 批次状态
	Amount      int64      `json:"amount"`        // 转账金额（分）
	CreateTime  *time.Time `json:"create_time"`   // 创建时间
	Success     bool       `json:"success"`       // 是否成功
	ErrorMsg    string     `json:"error_msg"`     // 错误信息
}

// ITransferService 转账服务接口
type ITransferService interface {
	// Transfer 发起转账
	Transfer(ctx context.Context, req *TransferRequest) (*TransferResult, error)
	
	// QueryTransfer 查询转账状态
	QueryTransfer(ctx context.Context, outBatchNo string) (*TransferResult, error)
	
	// GenerateBatchNo 生成批次单号
	GenerateBatchNo(withdrawalID uint) string
}