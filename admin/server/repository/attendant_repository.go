package repository

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gemeijie/peizhen/admin/server/model"
	"gorm.io/gorm"
)

// AttendantRepository 陪诊师数据操作接口
type AttendantRepository struct {
	db *gorm.DB
}

// NewAttendantRepository 创建AttendantRepository实例
func NewAttendantRepository(db *gorm.DB) *AttendantRepository {
	return &AttendantRepository{db: db}
}

// GetAttendantByID 根据ID获取陪诊师信息
func (r *AttendantRepository) GetAttendantByID(id uint) (*model.AttendantWithStats, error) {
	var attendant model.AttendantWithStats

	// 查询基本信息
	if err := r.db.First(&attendant, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("陪诊师不存在")
		}
		return nil, err
	}

	// 查询统计信息
	var stats struct {
		PendingOrders int     `json:"pending_orders"`
		TotalIncome   float64 `json:"total_income"`
		MonthlyOrders int     `json:"monthly_orders"`
	}

	if err := r.db.Raw(`
		SELECT 
			COUNT(CASE WHEN status = 0 THEN 1 END) as pending_orders,
			IFNULL(SUM(amount), 0) as total_income,
			COUNT(CASE WHEN created_at >= DATE_FORMAT(NOW() ,'%Y-%m-01') THEN 1 END) as monthly_orders
		FROM orders
		WHERE attendant_id = ?
	`, id).Scan(&stats).Error; err != nil {
		return nil, err
	}

	attendant.PendingOrders = stats.PendingOrders
	attendant.TotalIncome = stats.TotalIncome
	attendant.MonthlyOrders = stats.MonthlyOrders

	// 获取陪诊师标签
	var tags []model.Tag
	if err := r.db.Table("tags").
		Joins("JOIN attendant_tags ON tags.id = attendant_tags.tag_id").
		Where("attendant_tags.attendant_id = ?", id).
		Find(&tags).Error; err != nil {
		// 只记录错误，不中断流程
	} else {
		attendant.Tags = tags
	}

	return &attendant, nil
}

// ListAttendants 获取陪诊师列表
func (r *AttendantRepository) ListAttendants(params map[string]interface{}) ([]model.AttendantWithStats, int64, error) {
	var attendants []model.AttendantWithStats
	var total int64

	// 打印一下查询参数方便调试
	r.db.Logger.Info(r.db.Statement.Context, "查询参数: %v", params)

	// 构建查询条件 - 使用Unscoped()忽略软删除条件
	query := r.db.Unscoped().Model(&model.AttendantWithStats{}).Where("deleted_at IS NULL")

	// 根据筛选条件过滤
	if status, ok := params["status"].(int); ok && status >= 0 {
		query = query.Where("status = ?", status)
	} else {
		// 当status为-1或不存在时，获取所有状态的记录
		statusFromStr, ok := params["status"].(string)
		if ok && statusFromStr == "-1" {
			// 不添加status过滤条件
		} else if ok && statusFromStr != "" {
			// 尝试将字符串转换为int
			if statusInt, err := strconv.Atoi(statusFromStr); err == nil && statusInt >= 0 {
				query = query.Where("status = ?", statusInt)
			}
		}
	}

	if name, ok := params["name"].(string); ok && name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	if phone, ok := params["phone"].(string); ok && phone != "" {
		query = query.Where("phone LIKE ?", "%"+phone+"%")
	}

	// 执行计数查询并打印SQL
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 打印一下查到的总数
	r.db.Logger.Info(r.db.Statement.Context, "查询到的总数: %d", total)

	// 分页参数
	page, _ := params["page"].(int)
	pageSize, _ := params["pageSize"].(int)
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 排序方式
	orderBy, _ := params["orderBy"].(string)
	if orderBy == "" {
		orderBy = "id DESC"
	}

	// 执行查询
	if err := query.Limit(pageSize).Offset(offset).Order(orderBy).Find(&attendants).Error; err != nil {
		return nil, 0, err
	}

	// 打印一下查询结果数量
	r.db.Logger.Info(r.db.Statement.Context, "查询结果数量: %d", len(attendants))

	// 查询每个陪诊师的统计信息
	for i := range attendants {
		// 查询待处理订单数、总收入和月订单数
		var stats struct {
			PendingOrders int     `json:"pending_orders"`
			TotalIncome   float64 `json:"total_income"`
			MonthlyOrders int     `json:"monthly_orders"`
		}

		err := r.db.Raw(`
			SELECT 
				COUNT(CASE WHEN status = 0 THEN 1 END) as pending_orders,
				IFNULL(SUM(amount), 0) as total_income,
				COUNT(CASE WHEN created_at >= DATE_FORMAT(NOW() ,'%Y-%m-01') THEN 1 END) as monthly_orders
			FROM orders
			WHERE attendant_id = ?
		`, attendants[i].ID).Scan(&stats).Error

		if err != nil {
			// 只记录错误，不中断流程
			r.db.Logger.Error(r.db.Statement.Context, "获取统计信息失败: %v", err)
			continue
		}

		attendants[i].PendingOrders = stats.PendingOrders
		attendants[i].TotalIncome = stats.TotalIncome
		attendants[i].MonthlyOrders = stats.MonthlyOrders

		// 查询标签
		var tags []model.Tag
		if err := r.db.Table("tags").
			Joins("JOIN attendant_tags ON tags.id = attendant_tags.tag_id").
			Where("attendant_tags.attendant_id = ?", attendants[i].ID).
			Find(&tags).Error; err != nil {
			// 只记录错误，不中断流程
			r.db.Logger.Error(r.db.Statement.Context, "获取标签失败: %v", err)
			continue
		}

		attendants[i].Tags = tags
	}

	return attendants, total, nil
}

// UpdateAttendantStatus 更新陪诊师状态
func (r *AttendantRepository) UpdateAttendantStatus(id uint, status int) error {
	return r.db.Model(&model.AttendantWithStats{}).Where("id = ?", id).Update("status", status).Error
}

// UpdateAttendant 更新陪诊师信息
func (r *AttendantRepository) UpdateAttendant(id uint, data map[string]interface{}) error {
	// 记录日志
	r.db.Logger.Info(r.db.Statement.Context, "更新陪诊师信息，ID: %d, 数据: %v", id, data)

	// 过滤一些不允许直接更新的字段
	protectedFields := []string{"id", "user_id", "created_at", "deleted_at"}
	for _, field := range protectedFields {
		delete(data, field)
	}

	// 过滤非数据库字段（gorm:"-"标记的字段）
	nonDBFields := []string{"pending_orders", "total_income", "monthly_orders", "tags"}
	for _, field := range nonDBFields {
		delete(data, field)
	}

	// 添加更新时间
	data["updated_at"] = time.Now()

	// 执行更新
	result := r.db.Model(&model.AttendantWithStats{}).Where("id = ?", id).Updates(data)
	if result.Error != nil {
		r.db.Logger.Error(r.db.Statement.Context, "更新陪诊师信息失败: %v", result.Error)
		return result.Error
	}

	// 检查是否找到记录
	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到ID为%d的陪诊师记录", id)
	}

	return nil
}

// ListVerifications 获取认证申请列表
func (r *AttendantRepository) ListVerifications(params map[string]interface{}) ([]model.AttendantVerification, int64, error) {
	var verifications []model.AttendantVerification
	var total int64

	// 构建查询条件
	query := r.db.Model(&model.AttendantVerification{})

	// 根据筛选条件过滤
	if status, ok := params["status"].(int); ok {
		query = query.Where("status = ?", status)
	}

	if name, ok := params["name"].(string); ok && name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	if phone, ok := params["phone"].(string); ok && phone != "" {
		query = query.Where("phone LIKE ?", "%"+phone+"%")
	}

	// 日期范围筛选
	if startDate, ok := params["startDate"].(string); ok && startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}

	if endDate, ok := params["endDate"].(string); ok && endDate != "" {
		query = query.Where("created_at <= ?", endDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页参数
	page, _ := params["page"].(int)
	pageSize, _ := params["pageSize"].(int)
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize

	// 执行查询 - 移除Preload调用
	err := query.
		Limit(pageSize).Offset(offset).Order("created_at DESC").
		Find(&verifications).Error

	if err != nil {
		return nil, 0, err
	}

	// 手动关联陪诊师信息
	for i := range verifications {
		if verifications[i].AttendantID > 0 {
			attendant, err := r.GetAttendantByID(verifications[i].AttendantID)
			if err == nil {
				verifications[i].Attendant = attendant
			}
		}
	}

	return verifications, total, nil
}

// VerifyAttendant 审核陪诊师
func (r *AttendantRepository) VerifyAttendant(id uint, status int, remark string, adminUserId uint) error {
	// 添加开始操作日志
	r.db.Logger.Info(r.db.Statement.Context, "开始审核认证申请 ID: %d, 状态: %d", id, status)

	// 开启事务
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 查询认证记录
		var verification model.AttendantVerification
		if err := tx.First(&verification, id).Error; err != nil {
			r.db.Logger.Error(r.db.Statement.Context, "查询认证记录失败 ID: %d, 错误: %v", id, err)
			return fmt.Errorf("查询认证记录失败: %w", err)
		}

		// 检查状态是否允许修改
		if verification.Status != 1 { // 只允许审核"待审核"状态的申请
			r.db.Logger.Warn(r.db.Statement.Context, "认证申请当前状态不允许修改 ID: %d, 当前状态: %d", id, verification.Status)
			return fmt.Errorf("当前状态(%d)不允许修改为(%d)", verification.Status, status)
		}

		// 如果用户ID为0，通过手机号查找真实用户ID
		var userID uint = verification.UserID
		if userID == 0 && verification.Phone != "" {
			var user struct {
				ID uint
			}
			if err := tx.Table("users").Select("id").Where("phone = ?", verification.Phone).First(&user).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					r.db.Logger.Error(r.db.Statement.Context, "查询用户ID失败，手机号: %s, 错误: %v", verification.Phone, err)
					return fmt.Errorf("查询用户ID失败: %w", err)
				}
				r.db.Logger.Warn(r.db.Statement.Context, "未找到手机号对应的用户: %s", verification.Phone)
			} else {
				userID = user.ID
				r.db.Logger.Info(r.db.Statement.Context, "通过手机号: %s 找到用户ID: %d", verification.Phone, userID)

				// 更新认证记录的用户ID
				if err := tx.Model(&model.AttendantVerification{}).Where("id = ?", id).Update("user_id", userID).Error; err != nil {
					r.db.Logger.Error(r.db.Statement.Context, "更新认证记录的用户ID失败 ID: %d, 错误: %v", id, err)
					return fmt.Errorf("更新认证记录的用户ID失败: %w", err)
				}

				// 同时更新verification对象，确保后续使用的是更新后的数据
				verification.UserID = userID
			}
		}

		// 更新认证状态
		updates := map[string]interface{}{
			"status":     status,
			"remark":     remark,
			"updated_at": time.Now(),
		}
		if err := tx.Model(&model.AttendantVerification{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			r.db.Logger.Error(r.db.Statement.Context, "更新认证状态失败 ID: %d, 错误: %v", id, err)
			return fmt.Errorf("更新认证状态失败: %w", err)
		}

		r.db.Logger.Info(r.db.Statement.Context, "更新认证状态成功 ID: %d, 状态: %d", id, status)

		// 如果审核通过，创建或更新陪诊师信息
		if status == model.VerificationStatusApproved {
			// 再次确认用户ID是否有效
			if userID == 0 {
				r.db.Logger.Error(r.db.Statement.Context, "无法审核通过：未找到有效的用户ID，申请ID: %d, 手机号: %s", id, verification.Phone)
				return fmt.Errorf("审核失败: 无法确定用户身份，未找到对应用户")
			}

			// 检查是否已存在陪诊师记录
			var existingAttendant model.AttendantWithStats
			existingAttendantFound := false

			if verification.AttendantID > 0 {
				if err := tx.First(&existingAttendant, verification.AttendantID).Error; err != nil {
					if !errors.Is(err, gorm.ErrRecordNotFound) {
						r.db.Logger.Error(r.db.Statement.Context, "查询现有陪诊师记录失败 ID: %d, 错误: %v", verification.AttendantID, err)
						return fmt.Errorf("查询现有陪诊师记录失败: %w", err)
					}
					r.db.Logger.Info(r.db.Statement.Context, "未找到现有陪诊师记录 ID: %d, 将创建新记录", verification.AttendantID)
				} else {
					existingAttendantFound = true
					r.db.Logger.Info(r.db.Statement.Context, "找到现有陪诊师记录 ID: %d, 将更新", existingAttendant.ID)
				}
			}

			// 如果已存在陪诊师记录，则更新
			if existingAttendantFound {
				// 更新陪诊师信息
				updates := map[string]interface{}{
					"name":          verification.Name,
					"gender":        verification.Gender,
					"age":           verification.Age,
					"phone":         verification.Phone,
					"id_card":       verification.IDCard,
					"id_card_front": verification.IDCardFront,
					"id_card_back":  verification.IDCardBack,
					"health_cert":   verification.HealthCert,
					"experience":    verification.Experience,
					"status":        model.AttendantStatusNormal, // 设置为正常状态
					"updated_at":    time.Now(),
					"user_id":       userID, // 确保用户ID正确设置
				}

				// 如果有推荐人ID，则更新推荐人信息
				if verification.ReferrerID > 0 {
					updates["referrer_id"] = verification.ReferrerID
					r.db.Logger.Info(r.db.Statement.Context, "更新陪诊师推荐人ID: %d", verification.ReferrerID)
				}

				// 如果有头像，则更新
				if verification.Avatar != "" {
					updates["avatar"] = verification.Avatar
				}

				// 记录日志
				r.db.Logger.Info(r.db.Statement.Context, "开始更新陪诊师信息 ID: %d", existingAttendant.ID)

				if err := tx.Model(&existingAttendant).Updates(updates).Error; err != nil {
					r.db.Logger.Error(r.db.Statement.Context, "更新陪诊师信息失败 ID: %d, 错误: %v", existingAttendant.ID, err)
					return fmt.Errorf("更新陪诊师信息失败: %w", err)
				}

				r.db.Logger.Info(r.db.Statement.Context, "更新陪诊师信息成功 ID: %d", existingAttendant.ID)

				// 检查并初始化陪诊师服务能力（如果还没有的话）
				r.db.Logger.Info(r.db.Statement.Context, "检查陪诊师服务能力，陪诊师ID: %d", existingAttendant.ID)
				var capabilityCount int64
				if err := tx.Table("attendant_capabilities").Where("attendant_id = ? AND status = ?", existingAttendant.ID, 1).Count(&capabilityCount).Error; err != nil {
					r.db.Logger.Error(r.db.Statement.Context, "检查陪诊师服务能力失败: %v", err)
				} else if capabilityCount == 0 {
					// 如果没有服务能力，则初始化
					r.db.Logger.Info(r.db.Statement.Context, "陪诊师暂无服务能力，开始初始化，陪诊师ID: %d", existingAttendant.ID)
					if err := r.initAttendantCapabilities(tx, existingAttendant.ID, verification.Experience); err != nil {
						r.db.Logger.Error(r.db.Statement.Context, "初始化陪诊师服务能力失败: %v", err)
						return fmt.Errorf("初始化陪诊师服务能力失败: %w", err)
					}
					r.db.Logger.Info(r.db.Statement.Context, "陪诊师服务能力初始化成功，陪诊师ID: %d", existingAttendant.ID)
				} else {
					r.db.Logger.Info(r.db.Statement.Context, "陪诊师已有 %d 项服务能力，跳过初始化", capabilityCount)
				}
			} else {
				// 创建新的陪诊师记录
				newAttendant := model.AttendantWithStats{
					UserID:      userID, // 使用确认后的有效用户ID
					Name:        verification.Name,
					Gender:      verification.Gender,
					Age:         verification.Age,
					Phone:       verification.Phone,
					IDCard:      verification.IDCard,
					IDCardFront: verification.IDCardFront,
					IDCardBack:  verification.IDCardBack,
					HealthCert:  verification.HealthCert,
					Experience:  verification.Experience,
					Avatar:      verification.Avatar,
					Status:      model.AttendantStatusNormal, // 设置为正常状态
					Rating:      5.0,                         // 默认评分
					Price:       0.0,                         // 默认价格，后续可以更新
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
					LastActive:  time.Now(), // 设置最后活跃时间为当前时间
				}

				// 如果有推荐人ID，则设置推荐人信息
				if verification.ReferrerID > 0 {
					newAttendant.ReferrerID = &verification.ReferrerID
					r.db.Logger.Info(r.db.Statement.Context, "设置新陪诊师推荐人ID: %d", verification.ReferrerID)
				}

				// 设置服务区域
				if verification.ServiceArea != "" {
					newAttendant.Address = verification.ServiceArea
				}

				r.db.Logger.Info(r.db.Statement.Context, "开始创建新陪诊师记录，用户ID: %d", userID)

				// 创建陪诊师记录
				if err := tx.Create(&newAttendant).Error; err != nil {
					r.db.Logger.Error(r.db.Statement.Context, "创建陪诊师记录失败, 错误: %v", err)
					return fmt.Errorf("创建陪诊师记录失败: %w", err)
				}

				// 更新认证记录的AttendantID
				if err := tx.Model(&model.AttendantVerification{}).Where("id = ?", verification.ID).Update("attendant_id", newAttendant.ID).Error; err != nil {
					r.db.Logger.Error(r.db.Statement.Context, "更新认证记录的AttendantID失败 ID: %d, 错误: %v", verification.ID, err)
					return fmt.Errorf("更新认证记录的AttendantID失败: %w", err)
				}

				r.db.Logger.Info(r.db.Statement.Context, "创建陪诊师记录成功，ID: %d, 并已更新认证记录的AttendantID", newAttendant.ID)

				// 自动初始化陪诊师服务能力
				r.db.Logger.Info(r.db.Statement.Context, "开始为新陪诊师初始化服务能力，陪诊师ID: %d", newAttendant.ID)
				if err := r.initAttendantCapabilities(tx, newAttendant.ID, verification.Experience); err != nil {
					r.db.Logger.Error(r.db.Statement.Context, "初始化陪诊师服务能力失败: %v", err)
					return fmt.Errorf("初始化陪诊师服务能力失败: %w", err)
				}
				r.db.Logger.Info(r.db.Statement.Context, "陪诊师服务能力初始化成功，陪诊师ID: %d", newAttendant.ID)
			}

			// 记录审核日志
			auditLog := struct {
				ID          uint      `gorm:"primaryKey"`
				AdminUserID uint      `gorm:"not null"`
				TargetType  string    `gorm:"size:50;not null"`
				TargetID    uint      `gorm:"not null"`
				Action      string    `gorm:"size:50;not null"`
				Status      int       `gorm:"not null"`
				Remark      string    `gorm:"size:255"`
				CreatedAt   time.Time `gorm:"not null"`
			}{
				AdminUserID: adminUserId,
				TargetType:  "attendant_verification",
				TargetID:    verification.ID,
				Action:      "audit",
				Status:      status,
				Remark:      remark,
				CreatedAt:   time.Now(),
			}

			r.db.Logger.Info(r.db.Statement.Context, "开始记录审核通过日志")

			if err := tx.Table("admin_audit_logs").Create(&auditLog).Error; err != nil {
				// 只记录错误，不中断流程
				r.db.Logger.Error(r.db.Statement.Context, "记录审核通过日志失败: %v", err)
			} else {
				r.db.Logger.Info(r.db.Statement.Context, "记录审核通过日志成功")
			}

			// 更新用户角色为陪诊师（必须成功，否则事务回滚）
			r.db.Logger.Info(r.db.Statement.Context, "开始更新用户角色为陪诊师 UserID: %d", userID)
			if err := tx.Table("users").Where("id = ?", userID).Update("role", "attendant").Error; err != nil {
				r.db.Logger.Error(r.db.Statement.Context, "更新用户角色失败 UserID: %d, 错误: %v", userID, err)
				return fmt.Errorf("更新用户角色失败: %w", err)
			}
			r.db.Logger.Info(r.db.Statement.Context, "更新用户角色成功 UserID: %d", userID)
		} else if status == model.VerificationStatusRejected {
			// 记录拒绝审核日志
			auditLog := struct {
				ID          uint      `gorm:"primaryKey"`
				AdminUserID uint      `gorm:"not null"`
				TargetType  string    `gorm:"size:50;not null"`
				TargetID    uint      `gorm:"not null"`
				Action      string    `gorm:"size:50;not null"`
				Status      int       `gorm:"not null"`
				Remark      string    `gorm:"size:255"`
				CreatedAt   time.Time `gorm:"not null"`
			}{
				AdminUserID: adminUserId,
				TargetType:  "attendant_verification",
				TargetID:    verification.ID,
				Action:      "audit",
				Status:      status,
				Remark:      remark,
				CreatedAt:   time.Now(),
			}

			r.db.Logger.Info(r.db.Statement.Context, "开始记录审核拒绝日志 ID: %d, 原因: %s", verification.ID, remark)

			if err := tx.Table("admin_audit_logs").Create(&auditLog).Error; err != nil {
				// 只记录错误，不中断流程
				r.db.Logger.Error(r.db.Statement.Context, "记录审核拒绝日志失败: %v", err)
			} else {
				r.db.Logger.Info(r.db.Statement.Context, "记录审核拒绝日志成功")
			}
		}

		r.db.Logger.Info(r.db.Statement.Context, "审核认证申请完成 ID: %d, 状态: %d", id, status)
		return nil
	})
}

// AddTag 添加标签
func (r *AttendantRepository) AddTag(tag *model.Tag) error {
	return r.db.Create(tag).Error
}

// GetTags 获取标签列表
func (r *AttendantRepository) GetTags() ([]model.Tag, error) {
	var tags []model.Tag
	if err := r.db.Find(&tags).Error; err != nil {
		return nil, err
	}
	return tags, nil
}

// AddAttendantTag 为陪诊师添加标签
func (r *AttendantRepository) AddAttendantTag(attendantID, tagID uint) error {
	return r.db.Exec("INSERT INTO attendant_tags (attendant_id, tag_id) VALUES (?, ?)", attendantID, tagID).Error
}

// RemoveAttendantTag 移除陪诊师标签
func (r *AttendantRepository) RemoveAttendantTag(attendantID, tagID uint) error {
	return r.db.Exec("DELETE FROM attendant_tags WHERE attendant_id = ? AND tag_id = ?", attendantID, tagID).Error
}

// GetVerificationByID 根据ID获取认证申请记录
func (r *AttendantRepository) GetVerificationByID(id uint) (*model.AttendantVerification, error) {
	var verification model.AttendantVerification

	// 添加调试日志
	r.db.Logger.Info(r.db.Statement.Context, "获取认证申请详情，ID: %d", id)

	if err := r.db.First(&verification, id).Error; err != nil {
		r.db.Logger.Error(r.db.Statement.Context, "获取认证申请详情失败: %v", err)
		return nil, err
	}

	r.db.Logger.Info(r.db.Statement.Context, "获取认证申请详情成功，ID: %d, 状态: %d", verification.ID, verification.Status)

	return &verification, nil
}

// GetUserByPhone 根据手机号查询用户
func (r *AttendantRepository) GetUserByPhone(phone string) (*model.User, error) {
	if phone == "" {
		return nil, fmt.Errorf("手机号不能为空")
	}

	r.db.Logger.Info(r.db.Statement.Context, "根据手机号查询用户: %s", phone)

	var user model.User
	err := r.db.Where("phone = ?", phone).First(&user).Error
	if err != nil {
		r.db.Logger.Error(r.db.Statement.Context, "根据手机号查询用户失败: %v", err)
		return nil, err
	}

	r.db.Logger.Info(r.db.Statement.Context, "根据手机号查询用户成功: phone=%s, userID=%d", phone, user.ID)
	return &user, nil
}

// initAttendantCapabilities 自动初始化陪诊师服务能力
func (r *AttendantRepository) initAttendantCapabilities(tx *gorm.DB, attendantID uint, experience int) error {
	r.db.Logger.Info(r.db.Statement.Context, "开始初始化陪诊师服务能力，陪诊师ID: %d, 经验年限: %d", attendantID, experience)

	// 获取所有启用的服务
	var services []struct {
		ID uint `gorm:"column:id"`
	}

	if err := tx.Table("services").Select("id").Where("status = ?", 1).Find(&services).Error; err != nil {
		return fmt.Errorf("获取服务列表失败: %w", err)
	}

	if len(services) == 0 {
		r.db.Logger.Info(r.db.Statement.Context, "没有找到启用的服务，跳过能力初始化")
		return nil
	}

	// 根据经验年限确定熟练度和认证状态
	var proficiencyLevel int
	var isCertified bool
	var maxDailyOrders int

	if experience >= 5 {
		proficiencyLevel = 3 // 专业级
		isCertified = true
		maxDailyOrders = 5
	} else if experience >= 2 {
		proficiencyLevel = 2 // 熟练级
		isCertified = true
		maxDailyOrders = 3
	} else {
		proficiencyLevel = 1 // 基础级
		isCertified = false
		maxDailyOrders = 2
	}

	// 批量插入服务能力记录
	var values []string
	var args []interface{}

	for _, service := range services {
		values = append(values, "(?, ?, ?, ?, ?, ?, ?, ?)")
		args = append(args,
			attendantID,         // attendant_id
			service.ID,          // service_id
			proficiencyLevel,    // proficiency_level
			isCertified,         // is_certified
			float64(experience), // experience_years
			false,               // is_preferred (暂时都不设为首选)
			maxDailyOrders,      // max_daily_orders
			1,                   // status (启用)
		)
	}

	sql := fmt.Sprintf(`
		INSERT INTO attendant_capabilities 
		(attendant_id, service_id, proficiency_level, is_certified, experience_years, is_preferred, max_daily_orders, status) 
		VALUES %s`, strings.Join(values, ","))

	if err := tx.Exec(sql, args...).Error; err != nil {
		return fmt.Errorf("批量插入服务能力失败: %w", err)
	}

	r.db.Logger.Info(r.db.Statement.Context, "成功为陪诊师初始化 %d 项服务能力", len(services))
	return nil
}
