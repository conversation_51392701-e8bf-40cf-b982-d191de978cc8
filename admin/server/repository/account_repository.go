package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/gemeijie/peizhen/admin/server/model"
	"gorm.io/gorm"
)

// IAccountRepository 账户仓库接口
type IAccountRepository interface {
	// GetByUserID 根据用户ID获取账户
	GetByUserID(ctx context.Context, userID uint) (*model.Account, error)
	// CreateAccount 创建账户
	CreateAccount(ctx context.Context, account *model.Account) error
	// UpdateBalance 更新账户余额（带版本控制）
	UpdateBalance(ctx context.Context, accountID uint, balance, frozen float64, version int) error
	// FreezeAmount 冻结金额
	FreezeAmount(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string, operatorID *uint) error
	// UnfreezeAmount 解冻金额
	UnfreezeAmount(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string, operatorID *uint) error
	// DeductAmount 扣除金额（从冻结金额中扣除）
	DeductAmount(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string, operatorID *uint) error
	// AddIncome 增加收入
	AddIncome(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string) error
	// CreateTransaction 创建账户变动记录
	CreateTransaction(ctx context.Context, transaction *model.AccountTransaction) error
	// GetTransactionsByUserID 获取用户的账户变动记录
	GetTransactionsByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*model.AccountTransaction, int64, error)
}

// AccountRepository 账户仓库实现
type AccountRepository struct {
	db *gorm.DB
}

// NewAccountRepository 创建账户仓库实例
func NewAccountRepository(db *gorm.DB) IAccountRepository {
	return &AccountRepository{db: db}
}

// GetByUserID 根据用户ID获取账户
func (r *AccountRepository) GetByUserID(ctx context.Context, userID uint) (*model.Account, error) {
	var account model.Account
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&account).Error
	if err != nil {
		return nil, err
	}
	return &account, nil
}

// CreateAccount 创建账户
func (r *AccountRepository) CreateAccount(ctx context.Context, account *model.Account) error {
	return r.db.WithContext(ctx).Create(account).Error
}

// UpdateBalance 更新账户余额（不使用版本控制，因为原表没有version字段）
func (r *AccountRepository) UpdateBalance(ctx context.Context, accountID uint, balance, frozen float64, version int) error {
	result := r.db.WithContext(ctx).Model(&model.Account{}).
		Where("id = ?", accountID).
		Updates(map[string]interface{}{
			"balance":        balance,
			"frozen_balance": frozen,
		})
	
	if result.Error != nil {
		return result.Error
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("账户不存在")
	}
	
	return nil
}

// FreezeAmount 冻结金额
func (r *AccountRepository) FreezeAmount(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string, operatorID *uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取账户信息
		var account model.Account
		if err := tx.Where("user_id = ?", userID).First(&account).Error; err != nil {
			return err
		}
		
		// 检查可用余额
		if account.GetAvailableBalance() < amount {
			return fmt.Errorf("可用余额不足")
		}
		
		// 更新冻结金额
		newFrozen := account.FrozenBalance + amount
		result := tx.Model(&account).Updates(map[string]interface{}{
			"frozen_balance": newFrozen,
		})
		if result.Error != nil {
			return result.Error
		}
		
		// 创建变动记录
		var opID uint = 0
		if operatorID != nil {
			opID = *operatorID
		}
		
		var relType *string
		var relID *uint
		if relatedType != "" {
			relType = &relatedType
		}
		if relatedID > 0 {
			relID = &relatedID
		}
		
		transaction := &model.AccountTransaction{
			UserID:        userID,
			Type:          model.TransactionTypeWithdrawFreeze,
			Amount:        amount,
			Balance:       account.Balance,
			FrozenBalance: newFrozen,
			RelatedType:   relType,
			RelatedID:     relID,
			Description:   description,
			OperatorID:    opID,
		}
		
		return tx.Create(transaction).Error
	})
}

// UnfreezeAmount 解冻金额
func (r *AccountRepository) UnfreezeAmount(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string, operatorID *uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取账户信息
		var account model.Account
		if err := tx.Where("user_id = ?", userID).First(&account).Error; err != nil {
			return err
		}
		
		// 检查冻结金额
		if account.FrozenBalance < amount {
			return fmt.Errorf("冻结金额不足")
		}
		
		// 更新冻结金额
		newFrozen := account.FrozenBalance - amount
		result := tx.Model(&account).Updates(map[string]interface{}{
			"frozen_balance": newFrozen,
		})
		if result.Error != nil {
			return result.Error
		}
		
		// 创建变动记录
		var opID uint = 0
		if operatorID != nil {
			opID = *operatorID
		}
		
		var relType *string
		var relID *uint
		if relatedType != "" {
			relType = &relatedType
		}
		if relatedID > 0 {
			relID = &relatedID
		}
		
		transaction := &model.AccountTransaction{
			UserID:        userID,
			Type:          model.TransactionTypeWithdrawUnfreeze,
			Amount:        amount,
			Balance:       account.Balance,
			FrozenBalance: newFrozen,
			RelatedType:   relType,
			RelatedID:     relID,
			Description:   description,
			OperatorID:    opID,
		}
		
		return tx.Create(transaction).Error
	})
}

// DeductAmount 扣除金额（从冻结金额中扣除）
func (r *AccountRepository) DeductAmount(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string, operatorID *uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取账户信息
		var account model.Account
		if err := tx.Where("user_id = ?", userID).First(&account).Error; err != nil {
			return err
		}
		
		// 检查冻结金额
		if account.FrozenBalance < amount {
			return fmt.Errorf("冻结金额不足")
		}
		
		// 更新账户（从余额和冻结金额中同时扣除）
		newBalance := account.Balance - amount
		newFrozen := account.FrozenBalance - amount
		result := tx.Model(&account).Updates(map[string]interface{}{
			"balance":        newBalance,
			"frozen_balance": newFrozen,
		})
		if result.Error != nil {
			return result.Error
		}
		
		// 创建变动记录
		var opID uint = 0
		if operatorID != nil {
			opID = *operatorID
		}
		
		var relType *string
		var relID *uint
		if relatedType != "" {
			relType = &relatedType
		}
		if relatedID > 0 {
			relID = &relatedID
		}
		
		transaction := &model.AccountTransaction{
			UserID:        userID,
			Type:          model.TransactionTypeWithdrawDeduct,
			Amount:        amount,
			Balance:       newBalance,
			FrozenBalance: newFrozen,
			RelatedType:   relType,
			RelatedID:     relID,
			Description:   description,
			OperatorID:    opID,
		}
		
		return tx.Create(transaction).Error
	})
}

// AddIncome 增加收入
func (r *AccountRepository) AddIncome(ctx context.Context, userID uint, amount float64, relatedType string, relatedID uint, description string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取账户信息
		var account model.Account
		if err := tx.Where("user_id = ?", userID).First(&account).Error; err != nil {
			return err
		}
		
		// 更新余额
		newBalance := account.Balance + amount
		result := tx.Model(&account).Updates(map[string]interface{}{
			"balance":      newBalance,
			"total_income": account.TotalIncome + amount,
		})
		if result.Error != nil {
			return result.Error
		}
		
		// 创建变动记录
		var relType *string
		var relID *uint
		if relatedType != "" {
			relType = &relatedType
		}
		if relatedID > 0 {
			relID = &relatedID
		}
		
		transaction := &model.AccountTransaction{
			UserID:        userID,
			Type:          model.TransactionTypeIncome,
			Amount:        amount,
			Balance:       newBalance,
			FrozenBalance: account.FrozenBalance,
			RelatedType:   relType,
			RelatedID:     relID,
			Description:   description,
			OperatorID:    0,
			OperatorType:  model.OperatorTypeSystem,
			OperatedAt:    time.Now(),
		}
		
		return tx.Create(transaction).Error
	})
}

// CreateTransaction 创建账户变动记录
func (r *AccountRepository) CreateTransaction(ctx context.Context, transaction *model.AccountTransaction) error {
	return r.db.WithContext(ctx).Create(transaction).Error
}

// GetTransactionsByUserID 获取用户的账户变动记录
func (r *AccountRepository) GetTransactionsByUserID(ctx context.Context, userID uint, page, pageSize int) ([]*model.AccountTransaction, int64, error) {
	var transactions []*model.AccountTransaction
	var total int64
	
	query := r.db.WithContext(ctx).Model(&model.AccountTransaction{}).Where("user_id = ? AND deleted_at IS NULL", userID)
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	offset := (page - 1) * pageSize
	err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&transactions).Error
	if err != nil {
		return nil, 0, err
	}
	
	return transactions, total, nil
}