use carego;

-- 创建操作日志表
CREATE TABLE `operation_logs` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `operator_id` bigint NOT NULL COMMENT '操作员ID',
    `operator_name` varchar(100) NOT NULL COMMENT '操作员姓名',
    `operation_type` varchar(50) NOT NULL COMMENT '操作类型: CREATE/UPDATE/DELETE/QUERY/ASSIGN/REFUND/LOGIN/LOGOUT',
    `resource_type` varchar(50) NOT NULL COMMENT '资源类型: TASK/ASSIGNMENT/REFUND/ORDER/USER/ATTENDANT',
    `resource_id` bigint DEFAULT NULL COMMENT '资源ID',
    `operation_detail` text COMMENT '操作详情',
    `operation_result` tinyint NOT NULL DEFAULT '1' COMMENT '操作结果: 1=成功,2=失败',
    `error_message` text COMMENT '错误信息',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text COMMENT '用户代理',
    `request_data` longtext COMMENT '请求数据',
    `response_data` longtext COMMENT '响应数据',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`id`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_operation_type` (`operation_type`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_resource_id` (`resource_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_operation_result` (`operation_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志表';

-- 插入一些示例数据（可选）
INSERT INTO `operation_logs` (`operator_id`, `operator_name`, `operation_type`, `resource_type`, `resource_id`, `operation_detail`, `operation_result`, `created_at`) VALUES
(1, '系统管理员', 'LOGIN', 'USER', NULL, '管理员登录系统', 1, NOW()),
(1, '系统管理员', 'QUERY', 'ORDER', 1001, '查询订单详情', 1, NOW()),
(1, '系统管理员', 'UPDATE', 'ATTENDANT', 2001, '更新陪诊师信息', 1, NOW());
