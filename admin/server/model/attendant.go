package model

import (
	"time"

	"gorm.io/gorm"
)

// AttendantWithStats 陪诊师及统计信息
type AttendantWithStats struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UserID       uint           `json:"user_id" gorm:"uniqueIndex"`
	ReferrerID   *uint          `json:"referrer_id" gorm:"index"`
	Name         string         `json:"name" gorm:"type:varchar(32);not null"`
	Avatar       string         `json:"avatar" gorm:"type:varchar(255)"`
	Gender       int            `json:"gender" gorm:"type:tinyint;not null"`
	Age          int            `json:"age" gorm:"type:int;not null"`
	Phone        string         `json:"phone" gorm:"type:varchar(11);not null;uniqueIndex"`
	IDCard       string         `json:"id_card" gorm:"type:varchar(18);not null;uniqueIndex"`
	IDCardFront  string         `json:"id_card_front" gorm:"type:varchar(255)"`
	IDCardBack   string         `json:"id_card_back" gorm:"type:varchar(255)"`
	HealthCert   string         `json:"health_cert" gorm:"type:varchar(255)"`
	Address      string         `json:"address" gorm:"type:varchar(255);not null"`
	Experience   int            `json:"experience" gorm:"type:int;not null;default:0"`
	Status       int            `json:"status" gorm:"type:tinyint;not null;default:1;index"`
	Rating       float64        `json:"rating" gorm:"type:decimal(2,1);not null;default:5.0;index"`
	RatingCount  int            `json:"rating_count" gorm:"type:int;not null;default:0"`
	ServiceCount int            `json:"service_count" gorm:"type:bigint;not null;default:0"`
	Price        float64        `json:"price" gorm:"type:decimal(10,2);not null;index"`
	Description  string         `json:"description" gorm:"type:text"`
	LastActive   time.Time      `json:"last_active" gorm:"type:timestamp"`
	Version      int            `json:"version" gorm:"type:int;not null;default:1"`
	CreatedAt    time.Time      `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 统计信息
	PendingOrders int     `json:"pending_orders" gorm:"-"` // 待处理订单数
	TotalIncome   float64 `json:"total_income" gorm:"-"`   // 总收入
	MonthlyOrders int     `json:"monthly_orders" gorm:"-"` // 本月订单数
	Tags          []Tag   `json:"tags" gorm:"-"`           // 标签，使用gorm:"-"忽略该字段的数据库映射
}

// TableName 指定表名
func (AttendantWithStats) TableName() string {
	return "attendants"
}

// 陪诊师状态常量
const (
	AttendantStatusNormal   = 1 // 正常
	AttendantStatusRest     = 2 // 休息
	AttendantStatusDisabled = 3 // 已禁用
)

// Tag 陪诊师标签
type Tag struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"type:varchar(32);not null"`
	Type      string    `json:"type" gorm:"type:varchar(32);not null"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// AttendantVerification 陪诊师认证申请记录
type AttendantVerification struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	UserID        uint      `json:"user_id"`
	AttendantID   uint      `json:"attendant_id"`
	Name          string    `json:"name"`
	Gender        int       `json:"gender"`
	Age           int       `json:"age"`
	Phone         string    `json:"phone"`
	IDCard        string    `json:"id_card"`
	IDCardFront   string    `json:"id_card_front"`
	IDCardBack    string    `json:"id_card_back"`
	HealthCert    string    `json:"health_cert"`
	Experience    int       `json:"experience"`
	ServiceArea   string    `json:"service_area"`
	Introduction  string    `json:"introduction"`
	ReferrerPhone string    `json:"referrer_phone"`
	ReferrerID    uint      `json:"referrer_id"`
	Avatar        string    `json:"avatar"`
	Status        int       `json:"status"`
	Remark        string    `json:"remark"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联
	Attendant *AttendantWithStats `json:"attendant,omitempty" gorm:"-"` // 使用gorm:"-"忽略数据库映射
}

// 陪诊师认证状态常量
const (
	VerificationStatusPending  = 1 // 待审核
	VerificationStatusApproved = 2 // 已通过
	VerificationStatusRejected = 3 // 已拒绝
)
