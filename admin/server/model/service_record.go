package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// ServiceRecord 服务记录模型
type ServiceRecord struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	RecordNo      string     `json:"record_no" gorm:"size:32;not null;uniqueIndex"`
	OrderID       uint       `json:"order_id" gorm:"not null;index"`
	AttendantID   uint       `json:"attendant_id" gorm:"not null;index"`
	PatientID     uint       `json:"patient_id" gorm:"not null;index"`
	StartTime     *time.Time `json:"start_time"`
	EndTime       *time.Time `json:"end_time"`
	ServiceItems  JSON       `json:"service_items" gorm:"type:json"`
	ServiceNotes  string     `json:"service_notes" gorm:"type:text"`
	MedicalAdvice string     `json:"medical_advice" gorm:"type:text"`
	Prescription  string     `json:"prescription" gorm:"type:text"`
	Images        JSON       `json:"images" gorm:"type:json"`
	Status        int        `json:"status" gorm:"default:0"` // 0未开始，1进行中，2已完成，3异常
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// JSON 自定义JSON类型
type JSON []string

// Value 实现driver.Valuer接口
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return nil
	}

	return json.Unmarshal(bytes, j)
}

// TableName 指定表名
func (ServiceRecord) TableName() string {
	return "service_records"
}

// GetServiceSummary 获取服务总结
func (sr *ServiceRecord) GetServiceSummary() string {
	if sr.ServiceNotes != "" {
		return sr.ServiceNotes
	}
	return "暂无服务总结"
}

// GetServicePhotos 获取服务照片
func (sr *ServiceRecord) GetServicePhotos() []string {
	if sr.Images != nil {
		return []string(sr.Images)
	}
	return []string{}
}
