package model

import (
	"time"

	"gorm.io/gorm"
)

// Account 账户模型
type Account struct {
	ID              uint           `json:"id" gorm:"primaryKey"`
	UserID          uint           `json:"user_id" gorm:"not null;uniqueIndex"`
	Balance         float64        `json:"balance" gorm:"type:decimal(10,2);not null;default:0"`
	FrozenBalance   float64        `json:"frozen_balance" gorm:"type:decimal(10,2);not null;default:0"`
	TotalIncome     float64        `json:"total_income" gorm:"type:decimal(10,2);not null;default:0"`
	TotalWithdrawal float64        `json:"total_withdrawal" gorm:"type:decimal(10,2);not null;default:0"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联关系
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (Account) TableName() string {
	return "accounts"
}

// GetAvailableBalance 获取可用余额
func (a *Account) GetAvailableBalance() float64 {
	return a.Balance - a.FrozenBalance
}

// CanWithdraw 检查是否可以提现指定金额
func (a *Account) CanWithdraw(amount float64) bool {
	return a.GetAvailableBalance() >= amount
}

// AccountTransaction 账户变动记录（对应 account_logs 表）
type AccountTransaction struct {
	ID             uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID         uint           `gorm:"not null;index" json:"user_id"`        // 用户ID
	Type           int            `gorm:"not null;index" json:"type"`           // 变动类型（int类型）
	Amount         float64        `gorm:"not null;type:decimal(10,2)" json:"amount"`         // 变动金额
	Balance        float64        `gorm:"not null;type:decimal(10,2)" json:"balance"`        // 变动后余额
	FrozenBalance  float64        `gorm:"not null;type:decimal(10,2)" json:"frozen_balance"` // 变动后冻结金额
	RelatedType    *string        `gorm:"size:20" json:"related_type"`                      // 关联类型
	RelatedID      *uint          `gorm:"index" json:"related_id"`                         // 关联ID
	Description    string         `gorm:"size:255;not null" json:"description"`             // 描述
	OperatorID     uint           `gorm:"not null" json:"operator_id"`                     // 操作员ID
	OperatorType   int            `gorm:"not null" json:"operator_type"`                   // 操作员类型
	OperatedAt     time.Time      `gorm:"not null" json:"operated_at"`                     // 操作时间
	SettlementID   *uint          `json:"settlement_id"`                                   // 结算ID
	CommissionRate *float64       `gorm:"type:decimal(5,4)" json:"commission_rate"`        // 佣金比例
	CreatedAt      time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// TableName 指定表名
func (AccountTransaction) TableName() string {
	return "account_logs"
}

// 账户变动类型常量（int类型，匹配数据库）
const (
	TransactionTypeIncome         = 1  // 收入
	TransactionTypeWithdrawFreeze = 2  // 提现冻结
	TransactionTypeWithdrawUnfreeze = 3  // 提现解冻
	TransactionTypeWithdrawDeduct = 4  // 提现扣除
	TransactionTypeRefund         = 5  // 退款
	TransactionTypeAdjustment     = 6  // 人工调整
)

// 操作员类型常量
const (
	OperatorTypeSystem = 1 // 系统操作
	OperatorTypeAdmin  = 2 // 管理员操作
	OperatorTypeUser   = 3 // 用户操作
)

// GetTypeText 获取变动类型文本
func (t *AccountTransaction) GetTypeText() string {
	switch t.Type {
	case TransactionTypeIncome:
		return "收入"
	case TransactionTypeWithdrawFreeze:
		return "提现冻结"
	case TransactionTypeWithdrawUnfreeze:
		return "提现解冻"
	case TransactionTypeWithdrawDeduct:
		return "提现扣除"
	case TransactionTypeRefund:
		return "退款"
	case TransactionTypeAdjustment:
		return "人工调整"
	default:
		return "未知"
	}
}

// GetOperatorTypeText 获取操作员类型文本
func (t *AccountTransaction) GetOperatorTypeText() string {
	switch t.OperatorType {
	case OperatorTypeSystem:
		return "系统"
	case OperatorTypeAdmin:
		return "管理员"
	case OperatorTypeUser:
		return "用户"
	default:
		return "未知"
	}
}