package router

import (
	"github.com/gemeijie/peizhen/admin/server/handler"
	"github.com/gin-gonic/gin"
)

// SetupAdminRoutes 设置管理员管理路由
func SetupAdminRoutes(r *gin.RouterGroup, adminHandler *handler.AdminHandler) {
	admins := r.Group("/admins")
	{
		// 管理员列表
		admins.GET("", adminHandler.GetAdminList)

		// 管理员详情
		admins.GET("/:id", adminHandler.GetAdminDetail)

		// 创建管理员
		admins.POST("", adminHandler.CreateAdmin)

		// 更新管理员
		admins.PUT("/:id", adminHandler.UpdateAdmin)

		// 删除管理员
		admins.DELETE("/:id", adminHandler.DeleteAdmin)

		// 更新管理员状态
		admins.PUT("/:id/status", adminHandler.UpdateAdminStatus)

		// 分配角色
		admins.POST("/:id/roles", adminHandler.AssignRoles)
	}

	// 角色相关路由
	roles := r.Group("/roles")
	{
		// 角色列表
		roles.GET("", adminHandler.GetRoleList)

		// 角色详情
		roles.GET("/:id", adminHandler.GetRoleList) // TODO: 实现具体的角色详情接口

		// 创建角色
		roles.POST("", adminHandler.GetRoleList) // TODO: 实现具体的角色创建接口

		// 更新角色
		roles.PUT("/:id", adminHandler.GetRoleList) // TODO: 实现具体的角色更新接口

		// 删除角色
		roles.DELETE("/:id", adminHandler.GetRoleList) // TODO: 实现具体的角色删除接口

		// 更新角色状态
		roles.PUT("/:id/status", adminHandler.GetRoleList) // TODO: 实现具体的角色状态更新接口
	}
}
