package router

import (
	"github.com/gemeijie/peizhen/admin/server/handler"
	"github.com/gin-gonic/gin"
)

// SetupReviewRoutes 设置审核相关路由
func SetupReviewRoutes(r *gin.RouterGroup, reviewHandler *handler.ReviewHandler) {
	// 审核管理路由组 - 匹配前端API调用 /api/v1/admin/reviews/*
	// 这里的r是 /api/v1/admin，所以直接添加reviews路径
	reviews := r.Group("/reviews")
	{
		// 获取待审核订单列表
		reviews.GET("/pending", reviewHandler.GetPendingReviews)

		// 审核订单
		reviews.POST("/:orderId/review", reviewHandler.ReviewOrder)

		// 获取审核详情
		reviews.GET("/:orderId/detail", reviewHandler.GetReviewDetail)

		// 获取审核统计信息
		reviews.GET("/stats", reviewHandler.GetReviewStats)
	}
}
