package router

import (
	"github.com/gemeijie/peizhen/admin/server/controller"
	"github.com/gin-gonic/gin"
)

// SetupAccountRoutes 设置账户管理路由
func SetupAccountRoutes(r *gin.RouterGroup, accountController *controller.AccountController) {
	account := r.Group("/account")
	{
		// 获取账户信息
		account.GET("/:user_id", accountController.GetAccountInfo) // GET /api/admin/account/:user_id - 获取指定用户的账户信息
		
		// 获取账户变动历史
		account.POST("/transactions", accountController.GetTransactionHistory) // POST /api/admin/account/transactions - 获取账户变动历史
		
		// 人工调整余额
		account.POST("/adjust", accountController.AdjustBalance) // POST /api/admin/account/adjust - 人工调整用户余额
	}
}