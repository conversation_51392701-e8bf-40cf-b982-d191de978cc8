package router

import (
	"github.com/gin-gonic/gin"

	"github.com/gemeijie/peizhen/admin/server/handler"
)

// SetupOrderCompletionRoutes 设置订单完成路由
func SetupOrderCompletionRoutes(r *gin.RouterGroup, orderCompletionHandler *handler.OrderCompletionHandler) {
	// 订单完成管理路由
	orderCompletion := r.Group("/orders")
	{
		// 管理员强制完成订单
		orderCompletion.POST("/:id/force-complete", orderCompletionHandler.ForceCompleteOrderByIDWithAdmin)
		
		// 异常处理完成订单
		orderCompletion.POST("/:id/exception-complete", orderCompletionHandler.CompleteOrderByExceptionHandlerWithID)
		
		// 系统自动完成订单
		orderCompletion.POST("/:id/auto-complete", orderCompletionHandler.AutoCompleteOrderByID)
		
		// 获取订单完成记录
		orderCompletion.GET("/:id/completion", orderCompletionHandler.GetOrderCompletion)
		
		// 获取待完成订单列表
		orderCompletion.GET("/pending-completion", orderCompletionHandler.GetPendingCompletionOrders)
	}
}
