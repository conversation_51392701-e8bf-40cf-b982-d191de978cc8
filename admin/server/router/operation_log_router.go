package router

import (
	"github.com/gemeijie/peizhen/admin/server/handler"
	"github.com/gin-gonic/gin"
)

// SetupOperationLogRoutes 设置操作日志路由
func SetupOperationLogRoutes(r *gin.RouterGroup, operationLogHandler *handler.OperationLogHandler) {
	operationLogs := r.Group("/operation-logs")
	{
		// 获取操作日志列表
		operationLogs.GET("", operationLogHandler.GetOperationLogs)

		// 导出操作日志
		operationLogs.GET("/export", operationLogHandler.ExportOperationLogs)

		// 清理操作日志
		operationLogs.POST("/clear", operationLogHandler.CleanupOperationLogs)
	}

	// 操作日志详情路由
	operationLog := r.Group("/operation-log")
	{
		// 获取操作日志详情
		operationLog.GET("/detail/:id", operationLogHandler.GetOperationLogDetail)

		// 清理操作日志（另一种方式）
		operationLog.POST("/cleanup", operationLogHandler.CleanupOperationLogs)
	}
}
