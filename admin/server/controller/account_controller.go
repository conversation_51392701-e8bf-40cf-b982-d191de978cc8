package controller

import (
	"net/http"
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gin-gonic/gin"
)

// AccountController 账户控制器
type AccountController struct {
	accountService service.IAccountService
}

// NewAccountController 创建账户控制器
func NewAccountController(accountService service.IAccountService) *AccountController {
	return &AccountController{
		accountService: accountService,
	}
}

// GetAccountInfo 获取账户信息
// @Summary 获取账户信息
// @Description 根据用户ID获取账户信息
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param user_id path int true "用户ID"
// @Success 200 {object} dto.AccountInfoResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/admin/account/{user_id} [get]
func (c *AccountController) GetAccountInfo(ctx *gin.Context) {
	// 获取用户ID
	userIDStr := ctx.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID格式错误",
		})
		return
	}

	// 获取账户信息
	accountInfo, err := c.accountService.GetAccountInfo(ctx.Request.Context(), uint(userID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": accountInfo,
	})
}

// GetTransactionHistory 获取账户变动历史
// @Summary 获取账户变动历史
// @Description 获取指定用户的账户变动历史记录
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param request body dto.AccountTransactionRequest true "查询参数"
// @Success 200 {object} dto.AccountTransactionResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/admin/account/transactions [post]
func (c *AccountController) GetTransactionHistory(ctx *gin.Context) {
	var req dto.AccountTransactionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取变动历史
	transactionHistory, err := c.accountService.GetTransactionHistory(ctx.Request.Context(), &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": transactionHistory,
	})
}

// AdjustBalance 人工调整余额
// @Summary 人工调整余额
// @Description 管理员手动调整用户账户余额
// @Tags 账户管理
// @Accept json
// @Produce json
// @Param request body dto.BalanceAdjustmentRequest true "调整参数"
// @Success 200 {object} dto.BalanceAdjustmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/admin/account/adjust [post]
func (c *AccountController) AdjustBalance(ctx *gin.Context) {
	var req dto.BalanceAdjustmentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取操作员ID（从JWT token或session中获取）
	// 这里假设从context中获取，实际实现需要根据认证方式调整
	operatorID, exists := ctx.Get("admin_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未授权操作",
		})
		return
	}

	// 执行余额调整
	adjustmentResult, err := c.accountService.AdjustBalance(ctx.Request.Context(), &req, operatorID.(uint))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"data": adjustmentResult,
	})
}