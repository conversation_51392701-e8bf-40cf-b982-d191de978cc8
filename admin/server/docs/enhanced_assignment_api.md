# 增强的人工分配接口文档

## 概述

增强的人工分配接口集成了状态重置逻辑，解决了管理员手动分配订单后陪诊师无法接单的问题（"匹配状态不允许接受：4"）。

## 核心功能

### 1. 状态重置逻辑
- 自动重置订单匹配状态为"已推送"
- 确保陪诊师可以正常接单
- 记录状态变更历史

### 2. 详细反馈机制
- 提供操作结果的详细信息
- 包含下一步操作指导
- 错误时提供具体的解决建议

### 3. 撤销功能
- 支持取消已分配的订单
- 支持重新分配给其他陪诊师
- 保持操作的可逆性

## API 接口

### 1. 手动分配订单

**接口**: `POST /api/admin/orders/{id}/assign`

**功能**: 手动分配订单给指定陪诊师，集成状态重置逻辑

**请求参数**:
```json
{
  "attendant_id": 123,
  "remark": "管理员手动分配"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "订单分配成功",
    "order_id": 456,
    "attendant_id": 123,
    "status_reset": true,
    "timeout_minutes": 30,
    "next_steps": [
      "订单匹配状态已重置为'已推送'",
      "陪诊师将收到接单通知",
      "陪诊师需在30分钟内确认接单"
    ]
  }
}
```

### 2. 批量分配订单

**接口**: `POST /api/admin/orders/batch-assign`

**功能**: 批量分配多个订单，提高操作效率

**请求参数**:
```json
{
  "assignments": [
    {
      "order_id": 456,
      "attendant_id": 123,
      "remark": "批量分配1"
    },
    {
      "order_id": 457,
      "attendant_id": 124,
      "remark": "批量分配2"
    }
  ],
  "remark": "批量操作"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success_count": 2,
    "failure_count": 0,
    "total_count": 2,
    "results": [
      {
        "order_id": 456,
        "attendant_id": 123,
        "success": true
      },
      {
        "order_id": 457,
        "attendant_id": 124,
        "success": true
      }
    ],
    "summary": "成功分配 2 个订单，失败 0 个订单",
    "status_reset": true,
    "timeout_minutes": 30
  }
}
```

### 3. 取消分配（撤销功能）

**接口**: `POST /api/admin/orders/{id}/cancel-assignment`

**功能**: 取消订单的当前分配，支持撤销操作

**请求参数**:
```json
{
  "reason": "陪诊师临时有事，需要重新分配"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "分配已成功取消",
    "order_id": 456,
    "reason": "陪诊师临时有事，需要重新分配",
    "status_changes": [
      "订单匹配状态已重置",
      "陪诊师分配已清除",
      "订单重新进入待分配状态"
    ],
    "next_steps": "订单可重新进行人工分配或自动匹配"
  }
}
```

### 4. 重新分配订单

**接口**: `POST /api/admin/orders/{id}/reassign`

**功能**: 将订单重新分配给其他陪诊师

**请求参数**:
```json
{
  "new_attendant_id": 125,
  "reason": "原陪诊师无法服务，重新分配"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "重新分配成功",
    "order_id": 456,
    "new_attendant_id": 125,
    "reason": "原陪诊师无法服务，重新分配",
    "status_changes": [
      "原分配已取消",
      "订单匹配状态已重置为'已推送'",
      "新陪诊师已收到接单通知"
    ],
    "timeout_minutes": 30,
    "next_steps": "新陪诊师需在30分钟内确认接单"
  }
}
```

### 5. 获取分配历史

**接口**: `GET /api/admin/orders/{id}/assignment-history`

**功能**: 获取订单的分配历史记录

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "order_id": 456,
    "history": [
      {
        "id": 1,
        "order_id": 456,
        "attendant_id": 123,
        "attendant_name": "张三",
        "operator_id": 1,
        "operator_name": "管理员",
        "assignment_type": "manual",
        "reason": "管理员手动分配",
        "status": "assigned",
        "assigned_at": "2025-01-21T10:00:00Z",
        "timeout_at": "2025-01-21T10:30:00Z"
      }
    ],
    "count": 1
  }
}
```

### 6. 验证分配条件

**接口**: `POST /api/admin/orders/validate-assignment`

**功能**: 验证订单是否可以分配给指定陪诊师

**请求参数**:
```json
{
  "order_id": 456,
  "attendant_id": 123
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "分配条件验证通过",
    "order_id": 456,
    "attendant_id": 123,
    "can_assign": true
  }
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `404`: 订单或陪诊师不存在
- `409`: 分配条件不满足
- `500`: 服务器内部错误

### 错误响应示例

```json
{
  "code": 400,
  "message": "分配条件验证失败",
  "data": {
    "error": "订单状态不允许分配，当前状态: 5",
    "order_id": 456,
    "attendant_id": 123,
    "can_assign": false,
    "suggestion": "请检查订单状态和陪诊师可用性"
  }
}
```

## 技术实现

### 状态重置逻辑

1. **问题背景**: 管理员手动分配后，订单匹配状态可能为"已拒绝"或"已超时"，导致陪诊师无法接单
2. **解决方案**: 在分配前自动重置匹配状态为"已推送"
3. **实现方式**: 更新 `order_matching` 表的状态字段

### 事务保证

所有分配操作都在数据库事务中执行，确保数据一致性：

```sql
BEGIN;
-- 1. 验证分配条件
-- 2. 重置匹配状态
-- 3. 更新订单分配
-- 4. 记录操作日志
COMMIT;
```

### 日志记录

所有分配操作都会记录详细日志，包括：
- 操作时间
- 操作员信息
- 订单和陪诊师信息
- 操作原因
- 状态变更

## 使用建议

1. **分配前验证**: 使用验证接口确认分配条件
2. **批量操作**: 对于多个订单，使用批量分配接口提高效率
3. **错误处理**: 根据错误响应中的建议进行相应处理
4. **历史追踪**: 定期查看分配历史，了解操作效果
5. **撤销机制**: 遇到问题时及时使用撤销功能

## 测试

使用提供的测试脚本验证功能：

```bash
cd admin/server/scripts
./test_enhanced_assignment.sh
```

## 注意事项

1. 所有接口都需要管理员权限
2. 分配操作会触发通知发送（需要集成通知服务）
3. 状态重置操作不可逆，请谨慎使用
4. 建议在生产环境使用前进行充分测试