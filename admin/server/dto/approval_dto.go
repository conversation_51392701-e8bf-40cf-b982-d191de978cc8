package dto

import (
	"time"
)

// ApprovalDecisionRequest 审批决定请求
type ApprovalDecisionRequest struct {
	Approved *bool  `json:"approved" binding:"required" comment:"是否批准"`
	Reason   string `json:"reason" binding:"required,max=500" comment:"审批原因"`
	Remark   string `json:"remark" binding:"max=1000" comment:"审批备注"`
}

// ApprovalHistoryResponse 审批历史响应
type ApprovalHistoryResponse struct {
	ID           uint      `json:"id"`
	RefundID     uint      `json:"refund_id"`
	ApproverID   uint      `json:"approver_id"`
	ApproverName string    `json:"approver_name"`
	Level        int       `json:"level"`
	LevelText    string    `json:"level_text"`
	Status       string    `json:"status"`
	StatusText   string    `json:"status_text"`
	Reason       string    `json:"reason"`
	Remark       string    `json:"remark"`
	ProcessedAt  time.Time `json:"processed_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// PendingApprovalsRequest 待审批列表请求
type PendingApprovalsRequest struct {
	Page       int    `json:"page" form:"page" binding:"min=1"`
	Limit      int    `json:"limit" form:"limit" binding:"min=1,max=100"`
	RefundType *int   `json:"refund_type" form:"refund_type" comment:"退款类型"`
	Level      *int   `json:"level" form:"level" comment:"审批级别"`
	Priority   string `json:"priority" form:"priority" comment:"优先级排序:urgent,normal"`
	DateStart  string `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd    string `json:"date_end" form:"date_end" comment:"结束日期"`
}

// PendingApprovalsResponse 待审批列表响应
type PendingApprovalsResponse struct {
	List       []*PendingApprovalItem `json:"list"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
	Stats      *ApprovalStats         `json:"stats"`
}

// PendingApprovalItem 待审批项目
type PendingApprovalItem struct {
	ID             uint      `json:"id"`
	RefundID       uint      `json:"refund_id"`
	RefundNo       string    `json:"refund_no"`
	OrderID        uint      `json:"order_id"`
	RefundAmount   float64   `json:"refund_amount"`
	RefundType     int       `json:"refund_type"`
	RefundTypeText string    `json:"refund_type_text"`
	RefundReason   string    `json:"refund_reason"`
	RequiredLevel  int       `json:"required_level"`
	LevelText      string    `json:"level_text"`
	Priority       string    `json:"priority"`
	TimeLimit      time.Time `json:"time_limit"`
	IsUrgent       bool      `json:"is_urgent"`
	SubmittedAt    time.Time `json:"submitted_at"`
	SubmitterName  string    `json:"submitter_name"`
	CustomerName   string    `json:"customer_name"`
}

// BatchApprovalRequest 批量审批请求
type BatchApprovalRequest struct {
	ApprovalIDs []uint `json:"approval_ids" binding:"required,min=1,max=50" comment:"审批ID列表"`
	Approved    *bool  `json:"approved" binding:"required" comment:"是否批准"`
	Reason      string `json:"reason" binding:"required,max=500" comment:"审批原因"`
	Remark      string `json:"remark" binding:"max=1000" comment:"审批备注"`
}

// BatchApprovalResponse 批量审批响应
type BatchApprovalResponse struct {
	SuccessCount int                   `json:"success_count"`
	FailureCount int                   `json:"failure_count"`
	TotalCount   int                   `json:"total_count"`
	Results      []*ApprovalResult     `json:"results"`
	Summary      *BatchApprovalSummary `json:"summary"`
}

// ApprovalResult 审批结果
type ApprovalResult struct {
	ApprovalID uint   `json:"approval_id"`
	RefundID   uint   `json:"refund_id"`
	RefundNo   string `json:"refund_no"`
	Success    bool   `json:"success"`
	Message    string `json:"message"`
}

// BatchApprovalSummary 批量审批汇总
type BatchApprovalSummary struct {
	TotalAmount    float64 `json:"total_amount"`
	ApprovedAmount float64 `json:"approved_amount"`
	RejectedAmount float64 `json:"rejected_amount"`
	ApprovedCount  int     `json:"approved_count"`
	RejectedCount  int     `json:"rejected_count"`
}

// ApprovalRuleResponse 审批规则响应
type ApprovalRuleResponse struct {
	ID             uint      `json:"id"`
	Name           string    `json:"name"`
	RefundType     int       `json:"refund_type"`
	RefundTypeText string    `json:"refund_type_text"`
	MinAmount      float64   `json:"min_amount"`
	MaxAmount      float64   `json:"max_amount"`
	RequireLevel   int       `json:"require_level"`
	LevelText      string    `json:"level_text"`
	AutoApprove    bool      `json:"auto_approve"`
	TimeLimit      int       `json:"time_limit"`
	Description    string    `json:"description"`
	Enabled        bool      `json:"enabled"`
	Priority       int       `json:"priority"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// UpdateApprovalRuleRequest 更新审批规则请求
type UpdateApprovalRuleRequest struct {
	Name         *string  `json:"name" binding:"max=100" comment:"规则名称"`
	RefundType   *int     `json:"refund_type" binding:"min=1,max=4" comment:"退款类型"`
	MinAmount    *float64 `json:"min_amount" binding:"min=0" comment:"最小金额"`
	MaxAmount    *float64 `json:"max_amount" binding:"min=0" comment:"最大金额"`
	RequireLevel *int     `json:"require_level" binding:"min=0,max=3" comment:"所需审批级别"`
	AutoApprove  *bool    `json:"auto_approve" comment:"是否自动审批"`
	TimeLimit    *int     `json:"time_limit" binding:"min=1,max=168" comment:"审批时限(小时)"`
	Description  *string  `json:"description" binding:"max=500" comment:"规则描述"`
	Enabled      *bool    `json:"enabled" comment:"是否启用"`
	Priority     *int     `json:"priority" binding:"min=1,max=100" comment:"优先级"`
}

// ApprovalRequiredResponse 审批需求响应
type ApprovalRequiredResponse struct {
	Required     bool                    `json:"required"`
	Level        int                     `json:"level"`
	LevelText    string                  `json:"level_text"`
	AutoApprove  bool                    `json:"auto_approve"`
	TimeLimit    int                     `json:"time_limit"`
	Reason       string                  `json:"reason"`
	MatchedRules []*ApprovalRuleResponse `json:"matched_rules"`
	Requirements []string                `json:"requirements"`
	Restrictions []string                `json:"restrictions"`
}

// ApprovalStatsRequest 审批统计请求
type ApprovalStatsRequest struct {
	Period    string `json:"period" form:"period" comment:"统计周期:today,week,month,quarter,year"`
	DateStart string `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd   string `json:"date_end" form:"date_end" comment:"结束日期"`
	GroupBy   string `json:"group_by" form:"group_by" comment:"分组方式:level,type,approver"`
}

// ApprovalStatsResponse 审批统计响应
type ApprovalStatsResponse struct {
	Period         string                   `json:"period"`
	TotalCount     int64                    `json:"total_count"`
	ApprovedCount  int64                    `json:"approved_count"`
	RejectedCount  int64                    `json:"rejected_count"`
	PendingCount   int64                    `json:"pending_count"`
	ApprovalRate   float64                  `json:"approval_rate"`
	AvgProcessTime float64                  `json:"avg_process_time"`
	TotalAmount    float64                  `json:"total_amount"`
	ApprovedAmount float64                  `json:"approved_amount"`
	RejectedAmount float64                  `json:"rejected_amount"`
	ByLevel        []*ApprovalLevelStats    `json:"by_level"`
	ByType         []*ApprovalTypeStats     `json:"by_type"`
	ByApprover     []*ApprovalApproverStats `json:"by_approver"`
	Trends         []*ApprovalTrendPoint    `json:"trends"`
}

// ApprovalStats 审批统计
type ApprovalStats struct {
	PendingCount int64   `json:"pending_count"`
	UrgentCount  int64   `json:"urgent_count"`
	OverdueCount int64   `json:"overdue_count"`
	TodayCount   int64   `json:"today_count"`
	AvgWaitTime  float64 `json:"avg_wait_time"`
}

// ApprovalLevelStats 按级别统计
type ApprovalLevelStats struct {
	Level          int     `json:"level"`
	LevelText      string  `json:"level_text"`
	Count          int64   `json:"count"`
	ApprovedCount  int64   `json:"approved_count"`
	RejectedCount  int64   `json:"rejected_count"`
	ApprovalRate   float64 `json:"approval_rate"`
	AvgProcessTime float64 `json:"avg_process_time"`
}

// ApprovalTypeStats 按类型统计
type ApprovalTypeStats struct {
	RefundType    int     `json:"refund_type"`
	TypeText      string  `json:"type_text"`
	Count         int64   `json:"count"`
	ApprovedCount int64   `json:"approved_count"`
	RejectedCount int64   `json:"rejected_count"`
	ApprovalRate  float64 `json:"approval_rate"`
	TotalAmount   float64 `json:"total_amount"`
}

// ApprovalApproverStats 按审批人统计
type ApprovalApproverStats struct {
	ApproverID     uint    `json:"approver_id"`
	ApproverName   string  `json:"approver_name"`
	Count          int64   `json:"count"`
	ApprovedCount  int64   `json:"approved_count"`
	RejectedCount  int64   `json:"rejected_count"`
	ApprovalRate   float64 `json:"approval_rate"`
	AvgProcessTime float64 `json:"avg_process_time"`
}

// ApprovalTrendPoint 审批趋势点
type ApprovalTrendPoint struct {
	Date           string  `json:"date"`
	Count          int64   `json:"count"`
	ApprovedCount  int64   `json:"approved_count"`
	RejectedCount  int64   `json:"rejected_count"`
	ApprovalRate   float64 `json:"approval_rate"`
	AvgProcessTime float64 `json:"avg_process_time"`
}

// ApproverWorkload 审批人工作负载
type ApproverWorkload struct {
	ApproverID     uint    `json:"approver_id"`
	ApproverName   string  `json:"approver_name"`
	PendingCount   int64   `json:"pending_count"`
	ProcessedCount int64   `json:"processed_count"`
	ApprovedCount  int64   `json:"approved_count"`
	RejectedCount  int64   `json:"rejected_count"`
	TimeoutCount   int64   `json:"timeout_count"`
	AvgProcessTime float64 `json:"avg_process_time"`
	Workload       string  `json:"workload"` // light, normal, heavy
}

// CreateApprovalRuleRequest 创建审批规则请求
type CreateApprovalRuleRequest struct {
	Name         string  `json:"name" binding:"required,max=100" comment:"规则名称"`
	RefundType   int     `json:"refund_type" binding:"required,min=1,max=4" comment:"退款类型"`
	MinAmount    float64 `json:"min_amount" binding:"min=0" comment:"最小金额"`
	MaxAmount    float64 `json:"max_amount" binding:"min=0" comment:"最大金额"`
	RequireLevel int     `json:"require_level" binding:"min=0,max=3" comment:"所需审批级别"`
	AutoApprove  bool    `json:"auto_approve" comment:"是否自动审批"`
	TimeLimit    int     `json:"time_limit" binding:"required,min=1,max=168" comment:"审批时限(小时)"`
	Description  string  `json:"description" binding:"max=500" comment:"规则描述"`
	Enabled      bool    `json:"enabled" comment:"是否启用"`
	Priority     int     `json:"priority" binding:"required,min=1,max=100" comment:"优先级"`
}

// ApprovalWorkflowResponse 审批工作流响应
type ApprovalWorkflowResponse struct {
	RefundID     uint                       `json:"refund_id"`
	CurrentStep  int                        `json:"current_step"`
	TotalSteps   int                        `json:"total_steps"`
	Status       string                     `json:"status"`
	StatusText   string                     `json:"status_text"`
	Steps        []*ApprovalWorkflowStep    `json:"steps"`
	History      []*ApprovalHistoryResponse `json:"history"`
	NextApprover *ApproverInfo              `json:"next_approver"`
}

// ApprovalWorkflowStep 审批工作流步骤
type ApprovalWorkflowStep struct {
	Step        int           `json:"step"`
	Level       int           `json:"level"`
	LevelText   string        `json:"level_text"`
	Status      string        `json:"status"`
	StatusText  string        `json:"status_text"`
	Approver    *ApproverInfo `json:"approver"`
	TimeLimit   time.Time     `json:"time_limit"`
	ProcessedAt *time.Time    `json:"processed_at"`
}

// ApproverInfo 审批人信息
type ApproverInfo struct {
	ID    uint   `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
	Role  string `json:"role"`
	Level int    `json:"level"`
}
