package dto

import "time"

// WithdrawalListRequest 提现列表请求
type WithdrawalListRequest struct {
	Page      int    `form:"page,default=1" binding:"min=1"`
	PageSize  int    `form:"page_size,default=20" binding:"min=1,max=100"`
	Status    *int   `form:"status"`     // 状态筛选：1待审核 2审核通过 3已打款 4已驳回
	UserID    *uint  `form:"user_id"`    // 用户ID筛选
	Method    *int   `form:"method"`     // 提现方式筛选：1微信 2支付宝 3银行卡
	StartDate string `form:"start_date"` // 开始日期
	EndDate   string `form:"end_date"`   // 结束日期
}

// WithdrawalResponse 提现记录响应
type WithdrawalResponse struct {
	ID           uint       `json:"id"`
	UserID       uint       `json:"user_id"`
	WithdrawNo   string     `json:"withdraw_no"`           // 兼容旧字段名
	WithdrawalNo string     `json:"withdrawal_no"`         // 新字段名，与数据库一致
	Amount       float64    `json:"amount"`
	Fee          float64    `json:"fee"`                   // 手续费
	ActualAmount float64    `json:"actual_amount"`         // 实际到账金额
	Method       int        `json:"method"`
	MethodText   string     `json:"method_text"`
	PaymentMethod string    `json:"payment_method,omitempty"` // 支付方式：wechat/alipay/bank
	Account      string     `json:"account"`
	AccountName  string     `json:"account_name"`
	BankName     string     `json:"bank_name,omitempty"`
	OpenID       string     `json:"open_id,omitempty"`     // 微信OpenID
	RealName     string     `json:"real_name,omitempty"`   // 真实姓名
	Status       int        `json:"status"`
	StatusText   string     `json:"status_text"`
	RejectReason string     `json:"reject_reason,omitempty"`
	ApplyTime    *time.Time `json:"apply_time,omitempty"`  // 申请时间
	AuditTime    *time.Time `json:"audit_time,omitempty"`
	AuditorID    *uint      `json:"auditor_id,omitempty"`
	AuditAdminID *uint      `json:"audit_admin_id,omitempty"` // 审核管理员ID
	AuditRemark  string     `json:"audit_remark,omitempty"`   // 审核备注
	PayTime      *time.Time `json:"pay_time,omitempty"`
	PayerID      *uint      `json:"payer_id,omitempty"`
	PayAdminID   *uint      `json:"pay_admin_id,omitempty"`   // 支付管理员ID
	PayRemark    string     `json:"pay_remark,omitempty"`     // 支付备注
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`

	// 转账相关信息
	OutBatchNo         string     `json:"out_batch_no,omitempty"`         // 商户批次号
	BatchID            string     `json:"batch_id,omitempty"`             // 微信批次ID
	TransferNo         string     `json:"transfer_no,omitempty"`          // 微信转账单号
	TransferBatchNo    string     `json:"transfer_batch_no,omitempty"`    // 微信转账批次号
	WechatBatchNo      string     `json:"wechat_batch_no,omitempty"`      // 微信批次号
	TransferStatus     string     `json:"transfer_status,omitempty"`      // 转账状态
	TransferTime       *time.Time `json:"transfer_time,omitempty"`        // 转账时间
	FailReason         string     `json:"fail_reason,omitempty"`          // 失败原因
	TransferFailReason string     `json:"transfer_fail_reason,omitempty"` // 转账失败原因

	// 关联信息
	UserName    string `json:"user_name,omitempty"`
	UserPhone   string `json:"user_phone,omitempty"`
	AuditorName string `json:"auditor_name,omitempty"`
	PayerName   string `json:"payer_name,omitempty"`
}

// WithdrawalListResponse 提现列表响应
type WithdrawalListResponse struct {
	List  []*WithdrawalResponse `json:"list"`
	Total int64                 `json:"total"`
	Page  int                   `json:"page"`
	Size  int                   `json:"size"`
}

// WithdrawalDetailResponse 提现详情响应
type WithdrawalDetailResponse struct {
	*WithdrawalResponse
	OperationLogs []*WithdrawalOperationLogResponse `json:"operation_logs,omitempty"` // 操作日志
}

// WithdrawalOperationLogResponse 提现操作日志响应
type WithdrawalOperationLogResponse struct {
	ID            uint      `json:"id"`
	WithdrawalID  uint      `json:"withdrawal_id"`
	OperatorID    uint      `json:"operator_id"`
	OperatorName  string    `json:"operator_name"`
	OperationType string    `json:"operation_type"`
	OperationText string    `json:"operation_text"`
	BeforeStatus  int       `json:"before_status"`
	AfterStatus   int       `json:"after_status"`
	Description   string    `json:"description"`
	Remark        string    `json:"remark"`
	CreatedAt     time.Time `json:"created_at"`
}

// WithdrawalApprovalRequest 提现审批请求
type WithdrawalApprovalRequest struct {
	Approved bool   `json:"approved"` // 是否批准
	Reason   string `json:"reason"`   // 审批原因/拒绝原因
}

// WithdrawalApprovalResponse 提现审批响应
type WithdrawalApprovalResponse struct {
	WithdrawalID uint   `json:"withdrawal_id"`
	Status       int    `json:"status"`
	Message      string `json:"message"`
	Processed    bool   `json:"processed"`
}

// WithdrawalBatchApprovalRequest 批量提现审批请求
type WithdrawalBatchApprovalRequest struct {
	WithdrawalIDs []uint `json:"withdrawal_ids" binding:"required"` // 提现ID列表
	Approved      bool   `json:"approved" binding:"required"`       // 是否批准
	Reason        string `json:"reason"`                            // 审批原因/拒绝原因
}

// WithdrawalBatchApprovalResponse 批量提现审批响应
type WithdrawalBatchApprovalResponse struct {
	SuccessCount int                          `json:"success_count"` // 成功数量
	FailCount    int                          `json:"fail_count"`    // 失败数量
	Results      []WithdrawalApprovalResponse `json:"results"`       // 详细结果
}

// WithdrawalStatsResponse 提现统计响应
type WithdrawalStatsResponse struct {
	PendingCount     int64   `json:"pending_count"`     // 待审核数量
	ApprovedCount    int64   `json:"approved_count"`    // 审核通过数量
	PaidCount        int64   `json:"paid_count"`        // 已打款数量
	RejectedCount    int64   `json:"rejected_count"`    // 已驳回数量
	TodayAmount      float64 `json:"today_amount"`      // 今日提现金额
	TotalAmount      float64 `json:"total_amount"`      // 总提现金额
	PendingAmount    float64 `json:"pending_amount"`    // 待审核金额
	ProcessingAmount float64 `json:"processing_amount"` // 处理中金额
}

// WithdrawalPaymentRequest 提现打款请求
type WithdrawalPaymentRequest struct {
	WithdrawalIDs []uint `json:"withdrawal_ids" binding:"required"` // 提现ID列表
	PaymentMethod string `json:"payment_method" binding:"required"` // 打款方式：manual/batch
	Remark        string `json:"remark"`                            // 备注
}

// WithdrawalPaymentResult 单个提现打款结果
type WithdrawalPaymentResult struct {
	WithdrawalID    uint   `json:"withdrawal_id"`
	WithdrawNo      string `json:"withdraw_no"`
	Amount          float64 `json:"amount"`
	Status          int    `json:"status"`
	Message         string `json:"message"`
	Processed       bool   `json:"processed"`
	TransferBatchNo string `json:"transfer_batch_no,omitempty"` // 微信转账批次号
	TransferNo      string `json:"transfer_no,omitempty"`       // 微信转账单号
	TransferStatus  string `json:"transfer_status,omitempty"`   // 转账状态
	FailReason      string `json:"fail_reason,omitempty"`       // 失败原因
}

// WithdrawalPaymentResponse 提现打款响应
type WithdrawalPaymentResponse struct {
	SuccessCount    int                        `json:"success_count"`    // 成功数量
	FailCount       int                        `json:"fail_count"`       // 失败数量
	TotalAmount     float64                    `json:"total_amount"`     // 总金额
	TransferBatchNo string                     `json:"transfer_batch_no"` // 微信转账批次号
	Results         []WithdrawalPaymentResult  `json:"results"`          // 详细结果
}

// TransferStatusQueryRequest 转账状态查询请求
type TransferStatusQueryRequest struct {
	WithdrawalIDs   []uint `json:"withdrawal_ids,omitempty"`   // 提现ID列表
	TransferBatchNo string `json:"transfer_batch_no,omitempty"` // 微信转账批次号
	TransferNo      string `json:"transfer_no,omitempty"`      // 微信转账单号
}

// TransferStatusResponse 转账状态响应
type TransferStatusResponse struct {
	WithdrawalID    uint    `json:"withdrawal_id"`
	WithdrawNo      string  `json:"withdraw_no"`
	TransferBatchNo string  `json:"transfer_batch_no"`
	TransferNo      string  `json:"transfer_no"`
	TransferStatus  string  `json:"transfer_status"`
	Amount          float64 `json:"amount"`
	TransferTime    *time.Time `json:"transfer_time,omitempty"`
	FailReason      string  `json:"fail_reason,omitempty"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TransferRetryRequest 转账重试请求
type TransferRetryRequest struct {
	WithdrawalIDs []uint `json:"withdrawal_ids" binding:"required"` // 提现ID列表
	Remark        string `json:"remark"`                            // 重试备注
}

// TransferDetailRequest 转账记录详情请求
type TransferDetailRequest struct {
	WithdrawalID uint `json:"withdrawal_id" binding:"required"` // 提现ID
}

// TransferDetailResponse 转账记录详情响应
type TransferDetailResponse struct {
	WithdrawalID     uint       `json:"withdrawal_id"`
	WithdrawNo       string     `json:"withdraw_no"`
	TransferBatchNo  string     `json:"transfer_batch_no"`
	TransferNo       string     `json:"transfer_no"`
	Amount           float64    `json:"amount"`
	TransferStatus   string     `json:"transfer_status"`
	TransferTime     *time.Time `json:"transfer_time,omitempty"`
	FailReason       string     `json:"fail_reason,omitempty"`
	RetryCount       int        `json:"retry_count"`
	LastRetryTime    *time.Time `json:"last_retry_time,omitempty"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	
	// 关联的提现信息
	Withdrawal *WithdrawalResponse `json:"withdrawal,omitempty"`
}
