package dto

import (
	"time"
)

// CreateAssignmentRequest 创建分配请求
type CreateAssignmentRequest struct {
	TaskID           int64  `json:"task_id" binding:"required" comment:"任务ID"`
	OrderID          int64  `json:"order_id" binding:"required" comment:"订单ID"`
	AttendantID      int64  `json:"attendant_id" binding:"required" comment:"陪诊师ID"`
	OperatorID       int64  `json:"operator_id" binding:"required" comment:"操作员ID"`
	AssignmentReason string `json:"assignment_reason" binding:"max=500" comment:"分配原因"`
	AssignmentNote   string `json:"assignment_note" binding:"max=1000" comment:"分配备注"`
	ConfirmHours     int    `json:"confirm_hours" binding:"min=1,max=24" comment:"确认时限(小时)"`
}

// AssignmentListRequest 分配列表请求
type AssignmentListRequest struct {
	Page        int    `json:"page" form:"page" binding:"min=1" comment:"页码"`
	PageSize    int    `json:"page_size" form:"page_size" binding:"min=1,max=100" comment:"每页大小"`
	TaskID      *int64 `json:"task_id" form:"task_id" comment:"任务ID"`
	OrderID     *int64 `json:"order_id" form:"order_id" comment:"订单ID"`
	AttendantID *int64 `json:"attendant_id" form:"attendant_id" comment:"陪诊师ID"`
	OperatorID  *int64 `json:"operator_id" form:"operator_id" comment:"操作员ID"`
	Status      *int   `json:"status" form:"status" comment:"分配状态"`
	DateStart   string `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd     string `json:"date_end" form:"date_end" comment:"结束日期"`
	OnlyExpired bool   `json:"only_expired" form:"only_expired" comment:"仅显示过期分配"`
}

// AssignmentResponse 分配响应
type AssignmentResponse struct {
	ID                   int64      `json:"id"`
	TaskID               int64      `json:"task_id"`
	OrderID              int64      `json:"order_id"`
	AttendantID          int64      `json:"attendant_id"`
	AttendantName        string     `json:"attendant_name"`
	AttendantPhone       string     `json:"attendant_phone"`
	AttendantAvatar      string     `json:"attendant_avatar"`
	AttendantRating      float64    `json:"attendant_rating"`
	OperatorID           int64      `json:"operator_id"`
	OperatorName         string     `json:"operator_name"`
	AssignmentReason     string     `json:"assignment_reason"`
	AssignmentNote       string     `json:"assignment_note"`
	ConfirmationDeadline time.Time  `json:"confirmation_deadline"`
	Status               int        `json:"status"`
	StatusText           string     `json:"status_text"`
	AttendantResponse    string     `json:"attendant_response"`
	ResponseTime         *time.Time `json:"response_time"`
	IsExpired            bool       `json:"is_expired"`
	RemainingTime        string     `json:"remaining_time"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

// AssignmentListResponse 分配列表响应
type AssignmentListResponse struct {
	List       []*AssignmentResponse `json:"list"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}

// AssignmentStatusResponse 分配状态响应
type AssignmentStatusResponse struct {
	ID                   int64      `json:"id"`
	Status               int        `json:"status"`
	StatusText           string     `json:"status_text"`
	AttendantResponse    string     `json:"attendant_response"`
	ResponseTime         *time.Time `json:"response_time"`
	ConfirmationDeadline time.Time  `json:"confirmation_deadline"`
	IsExpired            bool       `json:"is_expired"`
	RemainingTime        string     `json:"remaining_time"`
}

// ConfirmAssignmentRequest 确认分配请求
type ConfirmAssignmentRequest struct {
	AssignmentID int64  `json:"assignment_id" binding:"required" comment:"分配ID"`
	AttendantID  int64  `json:"attendant_id" binding:"required" comment:"陪诊师ID"`
	Response     string `json:"response" binding:"max=500" comment:"响应内容"`
}

// RejectAssignmentRequest 拒绝分配请求
type RejectAssignmentRequest struct {
	AssignmentID int64  `json:"assignment_id" binding:"required" comment:"分配ID"`
	AttendantID  int64  `json:"attendant_id" binding:"required" comment:"陪诊师ID"`
	Reason       string `json:"reason" binding:"required,max=500" comment:"拒绝原因"`
}

// AttendantInfo 陪诊师信息
type AttendantInfo struct {
	ID           int64     `json:"id"`
	Name         string    `json:"name"`
	Phone        string    `json:"phone"`
	Avatar       string    `json:"avatar"`
	Gender       int       `json:"gender"`
	Age          int       `json:"age"`
	Rating       float64   `json:"rating"`
	ServiceCount int       `json:"service_count"`
	Experience   int       `json:"experience"`
	Specialties  string    `json:"specialties"`
	Address      string    `json:"address"`
	Distance     float64   `json:"distance"`
	IsOnline     bool      `json:"is_online"`
	IsBusy       bool      `json:"is_busy"`
	LastActive   time.Time `json:"last_active"`
}

// AttendantSearchRequest 陪诊师搜索请求
type AttendantSearchRequest struct {
	Keyword       string   `json:"keyword" form:"keyword" comment:"关键词"`
	ServiceType   string   `json:"service_type" form:"service_type" comment:"服务类型"`
	Gender        *int     `json:"gender" form:"gender" comment:"性别"`
	MinRating     *float64 `json:"min_rating" form:"min_rating" comment:"最低评分"`
	MaxDistance   *float64 `json:"max_distance" form:"max_distance" comment:"最大距离"`
	OnlineOnly    bool     `json:"online_only" form:"online_only" comment:"仅在线"`
	AvailableOnly bool     `json:"available_only" form:"available_only" comment:"仅可用"`
	SortBy        string   `json:"sort_by" form:"sort_by" comment:"排序字段"`
	SortOrder     string   `json:"sort_order" form:"sort_order" comment:"排序方向"`
	Page          int      `json:"page" form:"page" binding:"min=1" comment:"页码"`
	PageSize      int      `json:"page_size" form:"page_size" binding:"min=1,max=50" comment:"每页大小"`
}

// AttendantSearchResponse 陪诊师搜索响应
type AttendantSearchResponse struct {
	List       []*AttendantInfo `json:"list"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// AssignmentStatsResponse 分配统计响应
type AssignmentStatsResponse struct {
	TotalAssignments    int64 `json:"total_assignments"`    // 总分配数
	PendingAssignments  int64 `json:"pending_assignments"`  // 待确认分配
	AcceptedAssignments int64 `json:"accepted_assignments"` // 已确认分配
	RejectedAssignments int64 `json:"rejected_assignments"` // 已拒绝分配
	ExpiredAssignments  int64 `json:"expired_assignments"`  // 已过期分配

	// 今日统计
	TodayAssignments int64 `json:"today_assignments"` // 今日分配
	TodayAccepted    int64 `json:"today_accepted"`    // 今日确认
	TodayRejected    int64 `json:"today_rejected"`    // 今日拒绝
	TodayExpired     int64 `json:"today_expired"`     // 今日过期

	// 成功率
	AcceptanceRate      float64 `json:"acceptance_rate"`       // 接受率
	AverageResponseTime int     `json:"average_response_time"` // 平均响应时间(分钟)
}

// BatchAssignmentRequest 批量分配请求
type BatchAssignmentRequest struct {
	TaskIDs          []int64 `json:"task_ids" binding:"required,min=1,max=10" comment:"任务ID列表"`
	AttendantIDs     []int64 `json:"attendant_ids" binding:"required,min=1,max=3" comment:"陪诊师ID列表"`
	OperatorID       int64   `json:"operator_id" binding:"required" comment:"操作员ID"`
	AssignmentReason string  `json:"assignment_reason" binding:"max=500" comment:"分配原因"`
	AssignmentNote   string  `json:"assignment_note" binding:"max=1000" comment:"分配备注"`
	ConfirmHours     int     `json:"confirm_hours" binding:"min=1,max=24" comment:"确认时限(小时)"`
}

// BatchAssignmentResponse 批量分配响应
type BatchAssignmentResponse struct {
	SuccessCount int                 `json:"success_count"` // 成功数量
	FailureCount int                 `json:"failure_count"` // 失败数量
	Results      []*AssignmentResult `json:"results"`       // 详细结果
}

// AssignmentResult 分配结果
type AssignmentResult struct {
	TaskID       int64  `json:"task_id"`
	AttendantID  int64  `json:"attendant_id"`
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	AssignmentID *int64 `json:"assignment_id,omitempty"`
}

// UpdateAssignmentRequest 更新分配请求
type UpdateAssignmentRequest struct {
	AssignmentReason     *string `json:"assignment_reason,omitempty" binding:"max=500" comment:"分配原因"`
	AssignmentNote       *string `json:"assignment_note,omitempty" binding:"max=1000" comment:"分配备注"`
	ConfirmHours         *int    `json:"confirm_hours,omitempty" binding:"min=1,max=24" comment:"确认时限(小时)"`
	ConfirmationDeadline *string `json:"confirmation_deadline,omitempty" comment:"确认截止时间"`
}

// AttendantOption 陪诊师选项
type AttendantOption struct {
	AttendantInfo
	Score            float64                `json:"score"`              // 匹配分数
	Reason           string                 `json:"reason"`             // 推荐原因
	Availability     bool                   `json:"availability"`       // 可用性
	EstimatedArrival string                 `json:"estimated_arrival"`  // 预计到达时间
	ServiceFee       float64                `json:"service_fee"`        // 服务费用
	Pros             []string               `json:"pros"`               // 优势
	Cons             []string               `json:"cons"`               // 劣势
	Tags             []string               `json:"tags"`               // 标签
	Metadata         map[string]interface{} `json:"metadata,omitempty"` // 额外信息
}

// SelectionCriteria 选择标准
type SelectionCriteria struct {
	ServiceType     string             `json:"service_type"`     // 服务类型
	PreferredGender *int               `json:"preferred_gender"` // 偏好性别
	MaxDistance     *float64           `json:"max_distance"`     // 最大距离
	MinRating       *float64           `json:"min_rating"`       // 最低评分
	MaxPrice        *float64           `json:"max_price"`        // 最高价格
	RequiredSkills  []string           `json:"required_skills"`  // 必需技能
	PreferredSkills []string           `json:"preferred_skills"` // 偏好技能
	Weights         map[string]float64 `json:"weights"`          // 权重配置
}

// AttendantRanking 陪诊师排名
type AttendantRanking struct {
	AttendantID   int64              `json:"attendant_id"`
	AttendantName string             `json:"attendant_name"`
	Rank          int                `json:"rank"`
	Score         float64            `json:"score"`
	Details       map[string]float64 `json:"details"` // 各项得分详情
}

// AttendantPerformanceResponse 陪诊师绩效响应
type AttendantPerformanceResponse struct {
	AttendantID      int64   `json:"attendant_id"`
	AttendantName    string  `json:"attendant_name"`
	TotalAssignments int64   `json:"total_assignments"`
	AcceptedCount    int64   `json:"accepted_count"`
	RejectedCount    int64   `json:"rejected_count"`
	AcceptanceRate   float64 `json:"acceptance_rate"`
	AverageRating    float64 `json:"average_rating"`
	CompletionRate   float64 `json:"completion_rate"`
	ResponseTime     float64 `json:"response_time"` // 平均响应时间(分钟)
	Period           string  `json:"period"`
}

// ResponseTimeStatsResponse 响应时间统计响应
type ResponseTimeStatsResponse struct {
	AttendantID          int64            `json:"attendant_id"`
	AverageResponseTime  float64          `json:"average_response_time"` // 平均响应时间(分钟)
	MedianResponseTime   float64          `json:"median_response_time"`  // 中位数响应时间
	FastestResponse      float64          `json:"fastest_response"`      // 最快响应时间
	SlowestResponse      float64          `json:"slowest_response"`      // 最慢响应时间
	ResponseDistribution map[string]int64 `json:"response_distribution"` // 响应时间分布
}

// WorkloadResponse 工作负载响应
type WorkloadResponse struct {
	AttendantID        int64            `json:"attendant_id"`
	AttendantName      string           `json:"attendant_name"`
	TotalTasks         int64            `json:"total_tasks"`
	ActiveTasks        int64            `json:"active_tasks"`
	CompletedTasks     int64            `json:"completed_tasks"`
	WorkloadLevel      string           `json:"workload_level"` // low/medium/high
	DailyDistribution  map[string]int64 `json:"daily_distribution"`
	HourlyDistribution map[string]int64 `json:"hourly_distribution"`
	ServiceTypes       map[string]int64 `json:"service_types"`
	Period             string           `json:"period"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid          bool     `json:"valid"`
	Errors         []string `json:"errors,omitempty"`
	Warnings       []string `json:"warnings,omitempty"`
	Score          float64  `json:"score"`
	Recommendation string   `json:"recommendation"`
}

// ConflictInfo 冲突信息
type ConflictInfo struct {
	ConflictType string    `json:"conflict_type"` // time/location/capacity
	Description  string    `json:"description"`
	ConflictTime time.Time `json:"conflict_time"`
	OrderID      int64     `json:"order_id,omitempty"`
	TaskID       int64     `json:"task_id,omitempty"`
	Severity     string    `json:"severity"` // low/medium/high
}

// AssignmentHistoryResponse 分配历史响应
type AssignmentHistoryResponse struct {
	AssignmentResponse
	TaskType       string  `json:"task_type"`
	ServiceType    string  `json:"service_type"`
	ServiceFee     float64 `json:"service_fee"`
	Duration       int     `json:"duration"` // 服务时长(分钟)
	CustomerRating float64 `json:"customer_rating"`
	Feedback       string  `json:"feedback"`
}

// PatternAnalysisResponse 模式分析响应
type PatternAnalysisResponse struct {
	AttendantID           int64              `json:"attendant_id"`
	AnalysisPeriod        string             `json:"analysis_period"`
	PreferredServiceTypes []string           `json:"preferred_service_types"`
	PeakHours             []int              `json:"peak_hours"`
	SuccessfulPatterns    map[string]float64 `json:"successful_patterns"`
	AvoidancePatterns     []string           `json:"avoidance_patterns"`
	Recommendations       []string           `json:"recommendations"`
}

// AssignmentExportRequest 分配导出请求
type AssignmentExportRequest struct {
	Format      string `json:"format" binding:"required,oneof=excel csv" comment:"导出格式"`
	DateStart   string `json:"date_start" comment:"开始日期"`
	DateEnd     string `json:"date_end" comment:"结束日期"`
	Status      *int   `json:"status" comment:"分配状态"`
	AttendantID *int64 `json:"attendant_id" comment:"陪诊师ID"`
	OperatorID  *int64 `json:"operator_id" comment:"操作员ID"`
}
