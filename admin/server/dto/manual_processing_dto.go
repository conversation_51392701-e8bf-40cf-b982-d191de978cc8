package dto

import (
	"time"
)

// CreateTaskRequest 创建任务请求
type CreateTaskRequest struct {
	OrderID       int64  `json:"order_id" binding:"required" comment:"订单ID"`
	TaskType      int    `json:"task_type" binding:"required,min=1,max=3" comment:"任务类型:1=人工分配,2=服务失败,3=用户取消"`
	Priority      int    `json:"priority" binding:"min=1,max=4" comment:"优先级:1=低,2=中,3=高,4=紧急"`
	FailureReason string `json:"failure_reason" binding:"max=500" comment:"匹配失败原因"`
	CreatedBy     int64  `json:"created_by" binding:"required" comment:"创建人ID"`
}

// TaskListRequest 任务列表请求
type TaskListRequest struct {
	Page        int    `json:"page" form:"page" binding:"min=1" comment:"页码"`
	PageSize    int    `json:"page_size" form:"page_size" binding:"min=1,max=100" comment:"每页大小"`
	Status      *int   `json:"status" form:"status" comment:"处理状态"`
	Priority    *int   `json:"priority" form:"priority" comment:"优先级"`
	TaskType    *int   `json:"task_type" form:"task_type" comment:"任务类型"`
	Keyword     string `json:"keyword" form:"keyword" comment:"关键词搜索"`
	AssignedTo  *int64 `json:"assigned_to" form:"assigned_to" comment:"分配给谁"`
	DateStart   string `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd     string `json:"date_end" form:"date_end" comment:"结束日期"`
	OnlyOverdue bool   `json:"only_overdue" form:"only_overdue" comment:"仅显示超时任务"`
	OnlyUrgent  bool   `json:"only_urgent" form:"only_urgent" comment:"仅显示紧急任务"`
}

// TaskResponse 任务响应
type TaskResponse struct {
	ID             int64      `json:"id"`
	OrderID        int64      `json:"order_id"`
	TaskType       int        `json:"task_type"`
	TaskTypeText   string     `json:"task_type_text"`
	Status         int        `json:"status"`
	StatusText     string     `json:"status_text"`
	Priority       int        `json:"priority"`
	PriorityText   string     `json:"priority_text"`
	FailureReason  string     `json:"failure_reason"`
	CreatedBy      int64      `json:"created_by"`
	CreatedByName  string     `json:"created_by_name"`
	AssignedTo     int64      `json:"assigned_to"`
	AssignedToName string     `json:"assigned_to_name"`
	Deadline       *time.Time `json:"deadline"`
	IsOverdue      bool       `json:"is_overdue"`
	RemainingTime  string     `json:"remaining_time"`
	CompletedAt    *time.Time `json:"completed_at"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`

	// 关联订单信息
	OrderInfo *OrderInfo `json:"order_info,omitempty"`
}

// OrderInfo 订单信息
type OrderInfo struct {
	OrderNo        string    `json:"order_no"`
	UserID         int64     `json:"user_id"`
	UserName       string    `json:"user_name"`
	UserPhone      string    `json:"user_phone"`
	ServiceType    string    `json:"service_type"`
	AppointmentTime    time.Time `json:"appointment_time"`
	ServiceAddr    string    `json:"service_addr"`
	TotalAmount    float64   `json:"total_amount"`
	SpecialReq     string    `json:"special_req"`
	OrderStatus    int       `json:"order_status"`
	MatchingStatus int       `json:"matching_status"`
}

// TaskListResponse 任务列表响应
type TaskListResponse struct {
	List       []*TaskResponse `json:"list"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"page_size"`
	TotalPages int             `json:"total_pages"`
}

// TaskDetailResponse 任务详情响应
type TaskDetailResponse struct {
	*TaskResponse
	MatchingHistory   []*MatchingRecord   `json:"matching_history"`   // 匹配历史
	AssignmentHistory []*AssignmentInfo   `json:"assignment_history"` // 分配历史
	OperationLogs     []*OperationLogInfo `json:"operation_logs"`     // 操作日志
}

// MatchingRecord 匹配记录
type MatchingRecord struct {
	AttendantID   int64      `json:"attendant_id"`
	AttendantName string     `json:"attendant_name"`
	PushTime      time.Time  `json:"push_time"`
	RejectTime    *time.Time `json:"reject_time"`
	RejectReason  string     `json:"reject_reason"`
}

// AssignmentInfo 分配信息
type AssignmentInfo struct {
	ID                   int64      `json:"id"`
	AttendantID          int64      `json:"attendant_id"`
	AttendantName        string     `json:"attendant_name"`
	OperatorID           int64      `json:"operator_id"`
	OperatorName         string     `json:"operator_name"`
	AssignmentReason     string     `json:"assignment_reason"`
	AssignmentNote       string     `json:"assignment_note"`
	ConfirmationDeadline time.Time  `json:"confirmation_deadline"`
	Status               int        `json:"status"`
	StatusText           string     `json:"status_text"`
	AttendantResponse    string     `json:"attendant_response"`
	ResponseTime         *time.Time `json:"response_time"`
	CreatedAt            time.Time  `json:"created_at"`
}

// OperationLogInfo 操作日志信息
type OperationLogInfo struct {
	ID              int64     `json:"id"`
	OperatorName    string    `json:"operator_name"`
	OperationType   string    `json:"operation_type"`
	OperationDetail string    `json:"operation_detail"`
	OperationResult int       `json:"operation_result"`
	ResultText      string    `json:"result_text"`
	CreatedAt       time.Time `json:"created_at"`
}

// UpdateTaskStatusRequest 更新任务状态请求
type UpdateTaskStatusRequest struct {
	TaskID     int64  `json:"task_id" binding:"required"`
	Status     int    `json:"status" binding:"required,min=1,max=4"`
	OperatorID int64  `json:"operator_id" binding:"required"`
	Note       string `json:"note" binding:"max=500"`
}

// ClaimTaskRequest 领取任务请求
type ClaimTaskRequest struct {
	TaskID     int64 `json:"task_id" binding:"required"`
	OperatorID int64 `json:"operator_id" binding:"required"`
}

// TaskStatsResponse 任务统计响应
type TaskStatsResponse struct {
	TotalTasks      int64 `json:"total_tasks"`      // 总任务数
	PendingTasks    int64 `json:"pending_tasks"`    // 待处理任务数
	ProcessingTasks int64 `json:"processing_tasks"` // 处理中任务数
	CompletedTasks  int64 `json:"completed_tasks"`  // 已完成任务数
	OverdueTasks    int64 `json:"overdue_tasks"`    // 超时任务数
	UrgentTasks     int64 `json:"urgent_tasks"`     // 紧急任务数

	// 今日统计
	TodayCreated   int64 `json:"today_created"`   // 今日新增
	TodayCompleted int64 `json:"today_completed"` // 今日完成
	TodayOverdue   int64 `json:"today_overdue"`   // 今日超时

	// 按类型统计
	ManualAssignTasks  int64 `json:"manual_assign_tasks"`  // 人工分配任务
	ServiceFailedTasks int64 `json:"service_failed_tasks"` // 服务失败任务
	UserCancelTasks    int64 `json:"user_cancel_tasks"`    // 用户取消任务
}

// TaskPriorityUpdateRequest 任务优先级更新请求
type TaskPriorityUpdateRequest struct {
	TaskID     int64  `json:"task_id" binding:"required"`
	Priority   int    `json:"priority" binding:"required,min=1,max=4"`
	OperatorID int64  `json:"operator_id" binding:"required"`
	Reason     string `json:"reason" binding:"max=200"`
}

// UpdateTaskRequest 更新任务请求
type UpdateTaskRequest struct {
	Priority      *int    `json:"priority,omitempty" binding:"min=1,max=4" comment:"优先级"`
	FailureReason *string `json:"failure_reason,omitempty" binding:"max=500" comment:"失败原因"`
	AssignedTo    *int64  `json:"assigned_to,omitempty" comment:"分配给谁"`
	Deadline      *string `json:"deadline,omitempty" comment:"截止时间"`
	Note          *string `json:"note,omitempty" binding:"max=1000" comment:"备注"`
}

// TaskCompletionRequest 任务完成请求
type TaskCompletionRequest struct {
	TaskID         int64  `json:"task_id" binding:"required" comment:"任务ID"`
	OperatorID     int64  `json:"operator_id" binding:"required" comment:"操作员ID"`
	CompletionNote string `json:"completion_note" binding:"max=1000" comment:"完成备注"`
	ResultType     int    `json:"result_type" binding:"required,min=1,max=3" comment:"处理结果:1=成功,2=失败,3=转交"`
	NextSteps      string `json:"next_steps,omitempty" binding:"max=500" comment:"后续步骤"`
}

// OperatorWorkloadResponse 操作员工作负载响应
type OperatorWorkloadResponse struct {
	OperatorID       int64            `json:"operator_id"`
	OperatorName     string           `json:"operator_name"`
	AssignedTasks    int64            `json:"assigned_tasks"`    // 分配的任务数
	CompletedTasks   int64            `json:"completed_tasks"`   // 完成的任务数
	PendingTasks     int64            `json:"pending_tasks"`     // 待处理任务数
	OverdueTasks     int64            `json:"overdue_tasks"`     // 超期任务数
	WorkloadLevel    string           `json:"workload_level"`    // 工作负载等级 low/medium/high
	CompletionRate   float64          `json:"completion_rate"`   // 完成率
	AvgProcessTime   float64          `json:"avg_process_time"`  // 平均处理时间(小时)
	TaskDistribution map[string]int64 `json:"task_distribution"` // 任务类型分布
	Period           string           `json:"period"`            // 统计周期
}

// TodayStatsResponse 今日统计响应
type TodayStatsResponse struct {
	Date            string `json:"date"`             // 统计日期
	NewTasks        int64  `json:"new_tasks"`        // 新增任务
	CompletedTasks  int64  `json:"completed_tasks"`  // 完成任务
	AssignedTasks   int64  `json:"assigned_tasks"`   // 分配任务
	OverdueTasks    int64  `json:"overdue_tasks"`    // 超期任务
	PendingTasks    int64  `json:"pending_tasks"`    // 待处理任务
	ProcessingTasks int64  `json:"processing_tasks"` // 处理中任务

	// 按优先级统计
	UrgentTasks int64 `json:"urgent_tasks"` // 紧急任务
	HighTasks   int64 `json:"high_tasks"`   // 高优先级
	MediumTasks int64 `json:"medium_tasks"` // 中优先级
	LowTasks    int64 `json:"low_tasks"`    // 低优先级

	// 按类型统计
	ManualAssignTasks  int64 `json:"manual_assign_tasks"`  // 人工分配
	ServiceFailedTasks int64 `json:"service_failed_tasks"` // 服务失败
	UserCancelTasks    int64 `json:"user_cancel_tasks"`    // 用户取消
}

// TaskTrendsResponse 任务趋势响应
type TaskTrendsResponse struct {
	Period    string                 `json:"period"`     // 统计周期
	TrendData []*TaskTrendPoint      `json:"trend_data"` // 趋势数据点
	Summary   map[string]interface{} `json:"summary"`    // 汇总信息
}

// TaskTrendPoint 任务趋势数据点
type TaskTrendPoint struct {
	Date      string `json:"date"`      // 日期
	NewTasks  int64  `json:"new_tasks"` // 新增任务
	Completed int64  `json:"completed"` // 完成任务
	Pending   int64  `json:"pending"`   // 待处理任务
	Overdue   int64  `json:"overdue"`   // 超期任务
}

// TaskSearchRequest 任务搜索请求
type TaskSearchRequest struct {
	Keyword     string  `json:"keyword" form:"keyword" comment:"关键词"`
	TaskTypes   []int   `json:"task_types" form:"task_types" comment:"任务类型列表"`
	TaskType    *int    `json:"task_type" form:"task_type" comment:"任务类型"`
	Statuses    []int   `json:"statuses" form:"statuses" comment:"状态列表"`
	Status      *int    `json:"status" form:"status" comment:"状态"`
	Priorities  []int   `json:"priorities" form:"priorities" comment:"优先级列表"`
	Priority    *int    `json:"priority" form:"priority" comment:"优先级"`
	OperatorIDs []int64 `json:"operator_ids" form:"operator_ids" comment:"操作员ID列表"`
	DateStart   string  `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd     string  `json:"date_end" form:"date_end" comment:"结束日期"`
	OnlyOverdue bool    `json:"only_overdue" form:"only_overdue" comment:"仅超期任务"`
	OnlyUrgent  bool    `json:"only_urgent" form:"only_urgent" comment:"仅紧急任务"`
	SortBy      string  `json:"sort_by" form:"sort_by" comment:"排序字段"`
	SortOrder   string  `json:"sort_order" form:"sort_order" comment:"排序方向"`
	Page        int     `json:"page" form:"page" binding:"min=1" comment:"页码"`
	PageSize    int     `json:"page_size" form:"page_size" binding:"min=1,max=100" comment:"每页大小"`
}

// TaskExportRequest 任务导出请求
type TaskExportRequest struct {
	Format      string   `json:"format" binding:"required,oneof=excel csv" comment:"导出格式"`
	TaskTypes   []int    `json:"task_types" comment:"任务类型"`
	Statuses    []int    `json:"statuses" comment:"状态"`
	Priorities  []int    `json:"priorities" comment:"优先级"`
	OperatorIDs []int64  `json:"operator_ids" comment:"操作员ID"`
	DateStart   string   `json:"date_start" comment:"开始日期"`
	DateEnd     string   `json:"date_end" comment:"结束日期"`
	Fields      []string `json:"fields" comment:"导出字段"`
}

// TaskHistoryResponse 任务历史响应
type TaskHistoryResponse struct {
	TaskID       int64      `json:"task_id"`
	OrderID      int64      `json:"order_id"`
	TaskType     int        `json:"task_type"`
	TaskTypeText string     `json:"task_type_text"`
	Status       int        `json:"status"`
	StatusText   string     `json:"status_text"`
	CreatedAt    time.Time  `json:"created_at"`
	CompletedAt  *time.Time `json:"completed_at"`
	ProcessTime  *int       `json:"process_time"` // 处理时长(分钟)
	OperatorName string     `json:"operator_name"`
	Result       int        `json:"result"`
	ResultText   string     `json:"result_text"`
}
