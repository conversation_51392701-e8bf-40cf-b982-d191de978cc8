package dto

import "time"

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	PageSize int    `form:"page_size,default=10" binding:"min=1,max=100"`
	Status   *int   `form:"status"`  // 状态筛选：1正常 2禁用
	Role     string `form:"role"`    // 角色筛选：user/attendant/admin
	Keyword  string `form:"keyword"` // 关键词搜索（昵称、手机号）
	Gender   *int   `form:"gender"`  // 性别筛选：0未知 1男 2女
	City     string `form:"city"`    // 城市筛选
}

// UserResponse 用户响应
type UserResponse struct {
	ID         uint      `json:"id"`
	OpenID     string    `json:"open_id"`
	UnionID    string    `json:"union_id"`
	Phone      string    `json:"phone"`
	Nickname   string    `json:"nickname"`
	Avatar     string    `json:"avatar"`     // 前端期望的字段名
	AvatarURL  string    `json:"avatar_url"` // 保持向后兼容
	Gender     int       `json:"gender"`
	GenderText string    `json:"gender_text"`
	Country    string    `json:"country"`
	Province   string    `json:"province"`
	City       string    `json:"city"`
	Language   string    `json:"language"`
	Status     int       `json:"status"`
	StatusText string    `json:"status_text"`
	Role       string    `json:"role"`
	RoleText   string    `json:"role_text"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	List  []UserResponse `json:"list"`
	Total int64          `json:"total"`
	Page  int            `json:"page"`
	Size  int            `json:"size"`
}

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	Phone     string `json:"phone" binding:"required"`
	Password  string `json:"password" binding:"required,min=6"`
	Nickname  string `json:"nickname" binding:"required"`
	AvatarURL string `json:"avatar_url"`
	Gender    int    `json:"gender"`
	Country   string `json:"country"`
	Province  string `json:"province"`
	City      string `json:"city"`
	Language  string `json:"language"`
	Role      string `json:"role" binding:"required,oneof=user attendant admin"`
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	ID        uint   `json:"id" binding:"required"`
	Phone     string `json:"phone"`
	Nickname  string `json:"nickname"`
	AvatarURL string `json:"avatar_url"`
	Gender    int    `json:"gender"`
	Country   string `json:"country"`
	Province  string `json:"province"`
	City      string `json:"city"`
	Language  string `json:"language"`
	Status    int    `json:"status"`
	Role      string `json:"role"`
}

// UserResetPasswordRequest 重置密码请求
type UserResetPasswordRequest struct {
	NewPassword string `json:"new_password" binding:"required,min=6"`
}

// UserStatsResponse 用户统计响应
type UserStatsResponse struct {
	TotalUsers     int64 `json:"total_users"`
	ActiveUsers    int64 `json:"active_users"`
	DisabledUsers  int64 `json:"disabled_users"`
	AttendantUsers int64 `json:"attendant_users"`
	TodayNewUsers  int64 `json:"today_new_users"`
	WeekNewUsers   int64 `json:"week_new_users"`
	MonthNewUsers  int64 `json:"month_new_users"`
}
