package dto

import "time"

// AccountInfoResponse 账户信息响应
type AccountInfoResponse struct {
	UserID           uint    `json:"user_id"`           // 用户ID
	UserName         string  `json:"user_name"`         // 用户名
	Phone            string  `json:"phone"`             // 手机号
	Balance          float64 `json:"balance"`           // 总余额
	Frozen           float64 `json:"frozen"`            // 冻结金额
	AvailableBalance float64 `json:"available_balance"` // 可用余额
	TotalIncome      float64 `json:"total_income"`      // 总收入
	TotalWithdrawal  float64 `json:"total_withdrawal"`  // 总提现
}

// AccountTransactionRequest 账户变动记录请求
type AccountTransactionRequest struct {
	UserID   uint `json:"user_id" binding:"required"` // 用户ID
	Page     int  `json:"page" binding:"min=1"`       // 页码
	PageSize int  `json:"page_size" binding:"min=1,max=100"` // 每页数量
}

// AccountTransactionResponse 账户变动记录响应
type AccountTransactionResponse struct {
	Total        int64                      `json:"total"`        // 总数
	Page         int                        `json:"page"`         // 当前页
	PageSize     int                        `json:"page_size"`    // 每页数量
	Transactions []*AccountTransactionItem `json:"transactions"` // 变动记录列表
}

// AccountTransactionItem 账户变动记录项
type AccountTransactionItem struct {
	ID              uint      `json:"id"`               // 记录ID
	Type            int       `json:"type"`             // 变动类型
	TypeText        string    `json:"type_text"`        // 变动类型文本
	Amount          float64   `json:"amount"`           // 变动金额
	Balance         float64   `json:"balance"`          // 变动后余额
	FrozenBalance   float64   `json:"frozen_balance"`   // 变动后冻结金额
	RelatedType     *string   `json:"related_type"`     // 关联类型
	RelatedID       *uint     `json:"related_id"`       // 关联ID
	Description     string    `json:"description"`      // 描述
	OperatorID      uint      `json:"operator_id"`      // 操作员ID
	OperatorType    int       `json:"operator_type"`    // 操作员类型
	OperatorTypeText string   `json:"operator_type_text"` // 操作员类型文本
	OperatedAt      time.Time `json:"operated_at"`      // 操作时间
	CreatedAt       time.Time `json:"created_at"`       // 创建时间
}

// BalanceAdjustmentRequest 余额调整请求
type BalanceAdjustmentRequest struct {
	UserID uint    `json:"user_id" binding:"required"` // 用户ID
	Amount float64 `json:"amount" binding:"required"`  // 调整金额（正数为增加，负数为减少）
	Reason string  `json:"reason" binding:"required"`  // 调整原因
}

// BalanceAdjustmentResponse 余额调整响应
type BalanceAdjustmentResponse struct {
	UserID        uint    `json:"user_id"`        // 用户ID
	UserName      string  `json:"user_name"`      // 用户名
	AdjustAmount  float64 `json:"adjust_amount"`  // 调整金额
	BeforeBalance float64 `json:"before_balance"` // 调整前余额
	AfterBalance  float64 `json:"after_balance"`  // 调整后余额
	Reason        string  `json:"reason"`         // 调整原因
	OperatorID    uint    `json:"operator_id"`    // 操作员ID
}