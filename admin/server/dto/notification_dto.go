package dto

import (
	"time"
)

// ============ 短信通知相关 ============

// SMSRequest 短信发送请求
type SMSRequest struct {
	Phone        string                 `json:"phone" binding:"required" comment:"手机号"`
	Message      string                 `json:"message" comment:"短信内容"`
	TemplateCode string                 `json:"template_code" comment:"模板代码"`
	Params       map[string]interface{} `json:"params" comment:"模板参数"`
	Priority     int                    `json:"priority" comment:"优先级:1=低,2=中,3=高"`
	ScheduleTime *time.Time             `json:"schedule_time" comment:"定时发送时间"`
}

// SMSResponse 短信发送响应
type SMSResponse struct {
	MessageID    string    `json:"message_id"`
	Phone        string    `json:"phone"`
	Status       string    `json:"status"`
	Message      string    `json:"message"`
	SentTime     time.Time `json:"sent_time"`
	Cost         float64   `json:"cost"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// SMSStatusResponse 短信状态查询响应
type SMSStatusResponse struct {
	MessageID     string     `json:"message_id"`
	Phone         string     `json:"phone"`
	Status        string     `json:"status"` // PENDING, SENT, DELIVERED, FAILED
	StatusText    string     `json:"status_text"`
	SentTime      time.Time  `json:"sent_time"`
	DeliveredTime *time.Time `json:"delivered_time,omitempty"`
	ErrorCode     string     `json:"error_code,omitempty"`
	ErrorMessage  string     `json:"error_message,omitempty"`
	RetryCount    int        `json:"retry_count"`
	Cost          float64    `json:"cost"`
}

// SMSTemplate 短信模板
type SMSTemplate struct {
	ID           int64     `json:"id"`
	TemplateCode string    `json:"template_code"`
	TemplateName string    `json:"template_name"`
	Content      string    `json:"content"`
	Params       []string  `json:"params"`
	Type         string    `json:"type"` // NOTIFICATION, VERIFICATION, MARKETING
	Status       int       `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// ============ 微信小程序推送相关 ============

// WechatPushMessage 微信推送消息
type WechatPushMessage struct {
	ToUser          string                 `json:"touser" binding:"required"`
	TemplateID      string                 `json:"template_id" binding:"required"`
	Page            string                 `json:"page,omitempty"`
	Data            map[string]interface{} `json:"data"`
	EmphasisKeyword string                 `json:"emphasis_keyword,omitempty"`
}

// WechatPushResponse 微信推送响应
type WechatPushResponse struct {
	MessageID    string    `json:"message_id"`
	UserID       int64     `json:"user_id"`
	OpenID       string    `json:"openid"`
	Status       string    `json:"status"`
	SentTime     time.Time `json:"sent_time"`
	ErrorCode    string    `json:"error_code,omitempty"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// TaskNotificationData 任务通知数据
type TaskNotificationData struct {
	TaskID       int64     `json:"task_id"`
	OrderID      int64     `json:"order_id"`
	TaskType     string    `json:"task_type"`
	Priority     string    `json:"priority"`
	Deadline     time.Time `json:"deadline"`
	PatientName  string    `json:"patient_name"`
	ServiceName  string    `json:"service_name"`
	HospitalName string    `json:"hospital_name"`
	AppointmentTime  time.Time `json:"appointment_time"`
}

// RefundNotificationData 退款通知数据
type RefundNotificationData struct {
	RefundID     int64     `json:"refund_id"`
	OrderID      int64     `json:"order_id"`
	RefundAmount float64   `json:"refund_amount"`
	RefundStatus string    `json:"refund_status"`
	RefundReason string    `json:"refund_reason"`
	RefundTime   time.Time `json:"refund_time"`
	PatientName  string    `json:"patient_name"`
}

// StatusUpdateData 状态更新通知数据
type StatusUpdateData struct {
	OrderID     int64     `json:"order_id"`
	OldStatus   string    `json:"old_status"`
	NewStatus   string    `json:"new_status"`
	UpdatedBy   string    `json:"updated_by"`
	UpdatedTime time.Time `json:"updated_time"`
	Reason      string    `json:"reason"`
	PatientName string    `json:"patient_name"`
}

// UserSubscriptionStatus 用户订阅状态
type UserSubscriptionStatus struct {
	UserID        int64           `json:"user_id"`
	OpenID        string          `json:"openid"`
	Subscriptions map[string]bool `json:"subscriptions"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

// PushTemplate 推送模板
type PushTemplate struct {
	ID         string    `json:"id"`
	TemplateID string    `json:"template_id"`
	Title      string    `json:"title"`
	Content    string    `json:"content"`
	Type       string    `json:"type"`
	Keywords   []string  `json:"keywords"`
	Status     int       `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
}

// PushStatusResponse 推送状态响应
type PushStatusResponse struct {
	MessageID    string     `json:"message_id"`
	UserID       int64      `json:"user_id"`
	Status       string     `json:"status"`
	SentTime     time.Time  `json:"sent_time"`
	ReadTime     *time.Time `json:"read_time,omitempty"`
	ErrorMessage string     `json:"error_message,omitempty"`
}

// ============ 系统内部通知相关 ============

// AdminNotification 管理员通知
type AdminNotification struct {
	ID        int64                  `json:"id"`
	AdminID   int64                  `json:"admin_id"`
	Title     string                 `json:"title"`
	Content   string                 `json:"content"`
	Type      string                 `json:"type"` // TASK, REFUND, SYSTEM, URGENT
	Priority  int                    `json:"priority"`
	IsRead    bool                   `json:"is_read"`
	ReadTime  *time.Time             `json:"read_time,omitempty"`
	CreatedAt time.Time              `json:"created_at"`
	ExtraData map[string]interface{} `json:"extra_data,omitempty"`
}

// TaskCreatedNotification 任务创建通知
type TaskCreatedNotification struct {
	TaskID        int64     `json:"task_id"`
	OrderID       int64     `json:"order_id"`
	TaskType      string    `json:"task_type"`
	Priority      string    `json:"priority"`
	FailureReason string    `json:"failure_reason"`
	PatientName   string    `json:"patient_name"`
	CreatedAt     time.Time `json:"created_at"`
}

// UrgentTaskInfo 紧急任务信息
type UrgentTaskInfo struct {
	TaskID        int64     `json:"task_id"`
	OrderID       int64     `json:"order_id"`
	UrgencyLevel  string    `json:"urgency_level"` // HIGH, CRITICAL
	UrgencyReason string    `json:"urgency_reason"`
	Deadline      time.Time `json:"deadline"`
	PatientInfo   string    `json:"patient_info"`
}

// RefundCompletedInfo 退款完成信息
type RefundCompletedInfo struct {
	RefundID     int64     `json:"refund_id"`
	OrderID      int64     `json:"order_id"`
	RefundAmount float64   `json:"refund_amount"`
	PatientName  string    `json:"patient_name"`
	CompletedAt  time.Time `json:"completed_at"`
	OperatorName string    `json:"operator_name"`
}

// InternalNotification 内部通知
type InternalNotification struct {
	AdminID      int64                  `json:"admin_id" binding:"required"`
	Title        string                 `json:"title" binding:"required"`
	Content      string                 `json:"content" binding:"required"`
	Type         string                 `json:"type" binding:"required"`
	Priority     int                    `json:"priority"`
	ExtraData    map[string]interface{} `json:"extra_data,omitempty"`
	ScheduleTime *time.Time             `json:"schedule_time,omitempty"`
}

// ============ 通用请求响应 ============

// NotificationListRequest 通知列表请求
type NotificationListRequest struct {
	Page      int    `json:"page" form:"page" binding:"min=1"`
	PageSize  int    `json:"page_size" form:"page_size" binding:"min=1,max=100"`
	Type      string `json:"type" form:"type"`
	Priority  *int   `json:"priority" form:"priority"`
	IsRead    *bool  `json:"is_read" form:"is_read"`
	DateStart string `json:"date_start" form:"date_start"`
	DateEnd   string `json:"date_end" form:"date_end"`
}

// NotificationListResponse 通知列表响应
type NotificationListResponse struct {
	List        []*AdminNotification `json:"list"`
	Total       int64                `json:"total"`
	Page        int                  `json:"page"`
	PageSize    int                  `json:"page_size"`
	TotalPages  int                  `json:"total_pages"`
	UnreadCount int64                `json:"unread_count"`
}

// NotificationHistoryRequest 通知历史请求
type NotificationHistoryRequest struct {
	Page       int    `json:"page" form:"page" binding:"min=1"`
	PageSize   int    `json:"page_size" form:"page_size" binding:"min=1,max=100"`
	NotifyType string `json:"notify_type" form:"notify_type"` // SMS, WECHAT, INTERNAL
	Status     string `json:"status" form:"status"`
	UserID     *int64 `json:"user_id" form:"user_id"`
	Phone      string `json:"phone" form:"phone"`
	DateStart  string `json:"date_start" form:"date_start"`
	DateEnd    string `json:"date_end" form:"date_end"`
}

// NotificationHistoryItem 通知历史项
type NotificationHistoryItem struct {
	ID           int64      `json:"id"`
	NotifyType   string     `json:"notify_type"`
	Recipient    string     `json:"recipient"`
	Title        string     `json:"title"`
	Content      string     `json:"content"`
	Status       string     `json:"status"`
	ErrorMessage string     `json:"error_message,omitempty"`
	SentTime     time.Time  `json:"sent_time"`
	ReadTime     *time.Time `json:"read_time,omitempty"`
	Cost         float64    `json:"cost"`
	RetryCount   int        `json:"retry_count"`
}

// NotificationHistoryResponse 通知历史响应
type NotificationHistoryResponse struct {
	List       []*NotificationHistoryItem `json:"list"`
	Total      int64                      `json:"total"`
	Page       int                        `json:"page"`
	PageSize   int                        `json:"page_size"`
	TotalPages int                        `json:"total_pages"`
}

// NotificationStatsResponse 通知统计响应
type NotificationStatsResponse struct {
	// SMS统计
	SMSStats struct {
		TotalSent      int64   `json:"total_sent"`
		TotalDelivered int64   `json:"total_delivered"`
		TotalFailed    int64   `json:"total_failed"`
		DeliveryRate   float64 `json:"delivery_rate"`
		TotalCost      float64 `json:"total_cost"`
		AvgCost        float64 `json:"avg_cost"`
	} `json:"sms_stats"`

	// 微信推送统计
	WechatStats struct {
		TotalSent   int64   `json:"total_sent"`
		TotalRead   int64   `json:"total_read"`
		TotalFailed int64   `json:"total_failed"`
		ReadRate    float64 `json:"read_rate"`
		AvgReadTime int     `json:"avg_read_time"` // 分钟
	} `json:"wechat_stats"`

	// 内部通知统计
	InternalStats struct {
		TotalCreated    int64   `json:"total_created"`
		TotalRead       int64   `json:"total_read"`
		TotalUnread     int64   `json:"total_unread"`
		ReadRate        float64 `json:"read_rate"`
		AvgResponseTime int     `json:"avg_response_time"` // 分钟
	} `json:"internal_stats"`

	// 按类型统计
	TypeStats map[string]int64 `json:"type_stats"`

	// 时间范围
	DateStart   string    `json:"date_start"`
	DateEnd     string    `json:"date_end"`
	GeneratedAt time.Time `json:"generated_at"`
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	ID           int64                  `json:"id"`
	Name         string                 `json:"name" binding:"required"`
	Type         string                 `json:"type" binding:"required"` // SMS, WECHAT, INTERNAL
	TemplateCode string                 `json:"template_code" binding:"required"`
	Title        string                 `json:"title"`
	Content      string                 `json:"content" binding:"required"`
	Params       []string               `json:"params"`
	Variables    map[string]interface{} `json:"variables"`
	Status       int                    `json:"status"`
	CreatedBy    int64                  `json:"created_by"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// DateRange 日期范围
type DateRange struct {
	StartDate string `json:"start_date" binding:"required"`
	EndDate   string `json:"end_date" binding:"required"`
}

// ============ 批量操作相关 ============

// BatchSMSRequest 批量短信发送请求
type BatchSMSRequest struct {
	Phones       []string               `json:"phones" binding:"required,min=1,max=100"`
	Message      string                 `json:"message"`
	TemplateCode string                 `json:"template_code"`
	Params       map[string]interface{} `json:"params"`
	Priority     int                    `json:"priority"`
	ScheduleTime *time.Time             `json:"schedule_time"`
}

// BatchSMSResponse 批量短信发送响应
type BatchSMSResponse struct {
	TotalCount     int               `json:"total_count"`
	SuccessCount   int               `json:"success_count"`
	FailureCount   int               `json:"failure_count"`
	TotalCost      float64           `json:"total_cost"`
	Results        []*SMSResponse    `json:"results"`
	FailureReasons map[string]string `json:"failure_reasons"`
}

// BatchWechatPushRequest 批量微信推送请求
type BatchWechatPushRequest struct {
	UserIDs    []int64                `json:"user_ids" binding:"required,min=1,max=100"`
	TemplateID string                 `json:"template_id" binding:"required"`
	Page       string                 `json:"page"`
	Data       map[string]interface{} `json:"data"`
}

// BatchWechatPushResponse 批量微信推送响应
type BatchWechatPushResponse struct {
	TotalCount     int                   `json:"total_count"`
	SuccessCount   int                   `json:"success_count"`
	FailureCount   int                   `json:"failure_count"`
	Results        []*WechatPushResponse `json:"results"`
	FailureReasons map[int64]string      `json:"failure_reasons"`
}
