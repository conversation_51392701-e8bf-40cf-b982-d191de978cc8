package dto

import (
	"time"
)

// RefundRequest 退款请求
type RefundRequest struct {
	OrderID      int64   `json:"order_id" binding:"required" comment:"订单ID"`
	RefundType   int     `json:"refund_type" binding:"required,min=1,max=3" comment:"退款类型:1=服务失败,2=用户取消,3=特殊情况"`
	RefundAmount float64 `json:"refund_amount" binding:"required,min=0" comment:"退款金额"`
	RefundReason string  `json:"refund_reason" binding:"required,max=500" comment:"退款原因"`
	RefundNote   string  `json:"refund_note" binding:"max=1000" comment:"退款说明"`
	OperatorID   int64   `json:"operator_id" binding:"required" comment:"操作员ID"`
}

// RefundListRequest 退款列表请求
type RefundListRequest struct {
	Page    int    `json:"page" form:"page"`
	Limit   int    `json:"limit" form:"limit"`
	Status  string `json:"status" form:"status"`
	OrderNo string `json:"order_no" form:"order_no"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	ID                 uint    `json:"id"`
	RefundNo           string  `json:"refund_no"`
	OrderID            uint    `json:"order_id"`
	Amount             float64 `json:"amount"`
	Status             string  `json:"status"`
	StatusText         string  `json:"status_text"`
	Reason             string  `json:"reason"`
	RefundType         int     `json:"refund_type"`
	RefundTypeText     string  `json:"refund_type_text"`
	ApprovalStatus     string  `json:"approval_status"`
	ApprovalStatusText string  `json:"approval_status_text"`
	Message            string  `json:"message,omitempty"`
	CreatedAt          string  `json:"created_at"`
	UpdatedAt          string  `json:"updated_at"`
}

// RefundListResponse 退款列表响应
type RefundListResponse struct {
	List       []*RefundResponse `json:"list"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// RefundDetailResponse 退款详情响应
type RefundDetailResponse struct {
	*RefundResponse
	PaymentInfo    *PaymentInfo        `json:"payment_info"`    // 支付信息
	RefundProgress []*RefundProgress   `json:"refund_progress"` // 退款进度
	OperationLogs  []*OperationLogInfo `json:"operation_logs"`  // 操作日志
}

// PaymentInfo 支付信息
type PaymentInfo struct {
	PaymentMethod string    `json:"payment_method"`
	PaymentNo     string    `json:"payment_no"`
	PaymentTime   time.Time `json:"payment_time"`
	PaymentAmount float64   `json:"payment_amount"`
	PaymentStatus int       `json:"payment_status"`
}

// RefundProgress 退款进度
type RefundProgress struct {
	Step        int       `json:"step"`
	StepName    string    `json:"step_name"`
	Status      int       `json:"status"`
	StatusText  string    `json:"status_text"`
	Message     string    `json:"message"`
	ProcessTime time.Time `json:"process_time"`
}

// RefundStatsResponse 退款统计响应
type RefundStatsResponse struct {
	TotalRefunds      int64 `json:"total_refunds"`      // 总退款数
	ProcessingRefunds int64 `json:"processing_refunds"` // 处理中退款
	CompletedRefunds  int64 `json:"completed_refunds"`  // 已完成退款
	FailedRefunds     int64 `json:"failed_refunds"`     // 失败退款

	// 金额统计
	TotalRefundAmount float64 `json:"total_refund_amount"` // 总退款金额
	TodayRefundAmount float64 `json:"today_refund_amount"` // 今日退款金额
	MonthRefundAmount float64 `json:"month_refund_amount"` // 本月退款金额

	// 今日统计
	TodayRefunds   int64 `json:"today_refunds"`   // 今日退款数
	TodayCompleted int64 `json:"today_completed"` // 今日完成
	TodayFailed    int64 `json:"today_failed"`    // 今日失败

	// 按类型统计
	ServiceFailedRefunds int64 `json:"service_failed_refunds"` // 服务失败退款
	UserCancelRefunds    int64 `json:"user_cancel_refunds"`    // 用户取消退款
	SpecialRefunds       int64 `json:"special_refunds"`        // 特殊情况退款

	// 成功率
	RefundSuccessRate float64 `json:"refund_success_rate"` // 退款成功率
	AverageRefundTime int     `json:"average_refund_time"` // 平均退款时间(分钟)
}

// RefundCalculateRequest 退款计算请求
type RefundCalculateRequest struct {
	OrderID       int64  `json:"order_id" binding:"required" comment:"订单ID"`
	RefundType    int    `json:"refund_type" binding:"required,min=1,max=3" comment:"退款类型"`
	CancelTime    string `json:"cancel_time" comment:"取消时间"`
	SpecialReason string `json:"special_reason" comment:"特殊原因"`
}

// RefundCalculateResponse 退款计算响应
type RefundCalculateResponse struct {
	OrderID           int64   `json:"order_id"`
	OriginalAmount    float64 `json:"original_amount"`
	RefundAmount      float64 `json:"refund_amount"`
	RefundRate        float64 `json:"refund_rate"`
	RefundRatePercent int     `json:"refund_rate_percent"`
	DeductAmount      float64 `json:"deduct_amount"`
	RefundRule        string  `json:"refund_rule"`
	Explanation       string  `json:"explanation"`
}

// RefundReasonOption 退款原因选项
type RefundReasonOption struct {
	Value string `json:"value"`
	Label string `json:"label"`
	Type  int    `json:"type"`
}

// RefundReasonsResponse 退款原因列表响应
type RefundReasonsResponse struct {
	ServiceFailedReasons []RefundReasonOption `json:"service_failed_reasons"`
	UserCancelReasons    []RefundReasonOption `json:"user_cancel_reasons"`
	SpecialReasons       []RefundReasonOption `json:"special_reasons"`
}

// BatchRefundRequest 批量退款请求
type BatchRefundRequest struct {
	OrderIDs     []int64 `json:"order_ids" binding:"required,min=1,max=10" comment:"订单ID列表"`
	RefundType   int     `json:"refund_type" binding:"required,min=1,max=3" comment:"退款类型"`
	RefundReason string  `json:"refund_reason" binding:"required,max=500" comment:"退款原因"`
	RefundNote   string  `json:"refund_note" binding:"max=1000" comment:"退款说明"`
	OperatorID   int64   `json:"operator_id" binding:"required" comment:"操作员ID"`
}

// BatchRefundResponse 批量退款响应
type BatchRefundResponse struct {
	SuccessCount int             `json:"success_count"` // 成功数量
	FailureCount int             `json:"failure_count"` // 失败数量
	TotalAmount  float64         `json:"total_amount"`  // 总退款金额
	Results      []*RefundResult `json:"results"`       // 详细结果
}

// RefundResult 退款结果
type RefundResult struct {
	OrderID      int64   `json:"order_id"`
	Success      bool    `json:"success"`
	RefundAmount float64 `json:"refund_amount,omitempty"`
	RefundID     *int64  `json:"refund_id,omitempty"`
	Message      string  `json:"message"`
}

// CancelRefundRequest 取消退款请求
type CancelRefundRequest struct {
	RefundID   int64  `json:"refund_id" binding:"required" comment:"退款ID"`
	OperatorID int64  `json:"operator_id" binding:"required" comment:"操作员ID"`
	Reason     string `json:"reason" binding:"required,max=500" comment:"取消原因"`
}

// RefundCallbackData 退款回调数据
type RefundCallbackData struct {
	RefundNo      string    `json:"refund_no"`
	OrderNo       string    `json:"order_no"`
	RefundAmount  float64   `json:"refund_amount"`
	RefundStatus  string    `json:"refund_status"`
	RefundTime    time.Time `json:"refund_time"`
	FailureReason string    `json:"failure_reason,omitempty"`
}

// RefundExportRequest 退款导出请求
type RefundExportRequest struct {
	RefundType *int   `json:"refund_type" form:"refund_type" comment:"退款类型"`
	Status     *int   `json:"status" form:"status" comment:"退款状态"`
	DateStart  string `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd    string `json:"date_end" form:"date_end" comment:"结束日期"`
	Format     string `json:"format" form:"format" comment:"导出格式:excel,csv"`
}

// CreateRefundRequest 创建退款请求
type CreateRefundRequest struct {
	OrderID      int64   `json:"order_id" binding:"required" comment:"订单ID"`
	RefundType   int     `json:"refund_type" binding:"required,min=1,max=3" comment:"退款类型:1=服务失败,2=用户取消,3=特殊情况"`
	RefundAmount float64 `json:"refund_amount" binding:"required,min=0" comment:"退款金额"`
	RefundReason string  `json:"refund_reason" binding:"required,max=500" comment:"退款原因"`
	RefundNote   string  `json:"refund_note" binding:"max=1000" comment:"退款说明"`
	OperatorID   int64   `json:"operator_id" binding:"required" comment:"操作员ID"`
}

// UpdateRefundRequest 更新退款请求
type UpdateRefundRequest struct {
	RefundAmount *float64 `json:"refund_amount,omitempty" binding:"min=0" comment:"退款金额"`
	RefundReason *string  `json:"refund_reason,omitempty" binding:"max=500" comment:"退款原因"`
	RefundNote   *string  `json:"refund_note,omitempty" binding:"max=1000" comment:"退款说明"`
	Status       *int     `json:"status,omitempty" comment:"退款状态"`
}

// RefundCalculationResponse 退款计算响应
type RefundCalculationResponse struct {
	OrderID           int64    `json:"order_id"`
	OriginalAmount    float64  `json:"original_amount"`
	RefundAmount      float64  `json:"refund_amount"`
	RefundRate        float64  `json:"refund_rate"`
	RefundRatePercent int      `json:"refund_rate_percent"`
	DeductAmount      float64  `json:"deduct_amount"`
	RefundRule        string   `json:"refund_rule"`
	Explanation       string   `json:"explanation"`
	Eligible          bool     `json:"eligible"`
	RefundPolicy      string   `json:"refund_policy"`
	TimeLimit         string   `json:"time_limit"`
	Warnings          []string `json:"warnings,omitempty"`
}

// RefundExecutionResponse 退款执行响应
type RefundExecutionResponse struct {
	RefundID      int64     `json:"refund_id"`
	RefundNo      string    `json:"refund_no"`
	Status        int       `json:"status"`
	StatusText    string    `json:"status_text"`
	RefundAmount  float64   `json:"refund_amount"`
	ExecutedAt    time.Time `json:"executed_at"`
	TransactionID string    `json:"transaction_id"`
	Message       string    `json:"message"`
}

// RefundRulesResponse 退款规则响应
type RefundRulesResponse struct {
	ServiceFailedRules []*RefundRule `json:"service_failed_rules"`
	UserCancelRules    []*RefundRule `json:"user_cancel_rules"`
	SpecialRules       []*RefundRule `json:"special_rules"`
	DefaultPolicy      string        `json:"default_policy"`
	LastUpdated        time.Time     `json:"last_updated"`
}

// RefundRule 退款规则
type RefundRule struct {
	ID          int64   `json:"id"`
	RuleType    int     `json:"rule_type"`
	TimeRange   string  `json:"time_range"`
	RefundRate  float64 `json:"refund_rate"`
	Description string  `json:"description"`
	Enabled     bool    `json:"enabled"`
}

// EligibilityResult 资格检查结果
type EligibilityResult struct {
	Eligible     bool     `json:"eligible"`
	Reason       string   `json:"reason"`
	RefundRate   float64  `json:"refund_rate"`
	MaxAmount    float64  `json:"max_amount"`
	TimeLimit    string   `json:"time_limit"`
	Requirements []string `json:"requirements"`
	Restrictions []string `json:"restrictions"`
}

// LimitCheckResult 限制检查结果
type LimitCheckResult struct {
	Allowed          bool     `json:"allowed"`
	DailyLimit       float64  `json:"daily_limit"`
	MonthlyLimit     float64  `json:"monthly_limit"`
	UsedDaily        float64  `json:"used_daily"`
	UsedMonthly      float64  `json:"used_monthly"`
	RemainingDaily   float64  `json:"remaining_daily"`
	RemainingMonthly float64  `json:"remaining_monthly"`
	Warnings         []string `json:"warnings,omitempty"`
}

// RefundTrendsResponse 退款趋势响应
type RefundTrendsResponse struct {
	Period    string                 `json:"period"`
	TrendData []*RefundTrendPoint    `json:"trend_data"`
	Summary   map[string]interface{} `json:"summary"`
}

// RefundTrendPoint 退款趋势数据点
type RefundTrendPoint struct {
	Date         string  `json:"date"`
	RefundCount  int64   `json:"refund_count"`
	RefundAmount float64 `json:"refund_amount"`
	SuccessRate  float64 `json:"success_rate"`
}

// OperatorRefundStatsResponse 操作员退款统计响应
type OperatorRefundStatsResponse struct {
	OperatorID       int64            `json:"operator_id"`
	OperatorName     string           `json:"operator_name"`
	Period           string           `json:"period"`
	TotalRefunds     int64            `json:"total_refunds"`
	TotalAmount      float64          `json:"total_amount"`
	SuccessCount     int64            `json:"success_count"`
	FailureCount     int64            `json:"failure_count"`
	SuccessRate      float64          `json:"success_rate"`
	AvgProcessTime   float64          `json:"avg_process_time"`
	TypeDistribution map[string]int64 `json:"type_distribution"`
}

// RefundApprovalRequest 退款审批请求
type RefundApprovalRequest struct {
	Approved bool   `json:"approved"` // 是否批准
	Reason   string `json:"reason"`   // 审批原因/拒绝原因
}

// RefundApprovalResponse 退款审批响应
type RefundApprovalResponse struct {
	RefundID  uint   `json:"refund_id"`
	Status    string `json:"status"`
	Message   string `json:"message"`
	Processed bool   `json:"processed"`
}

// RefundBatchApprovalRequest 批量退款审批请求
type RefundBatchApprovalRequest struct {
	RefundIDs []uint `json:"refund_ids"` // 退款ID列表
	Approved  bool   `json:"approved"`   // 是否批准
	Reason    string `json:"reason"`     // 审批原因/拒绝原因
}

// RefundBatchApprovalResponse 批量退款审批响应
type RefundBatchApprovalResponse struct {
	SuccessCount int                      `json:"success_count"` // 成功数量
	FailCount    int                      `json:"fail_count"`    // 失败数量
	Results      []RefundApprovalResponse `json:"results"`       // 详细结果
}

// SimpleApprovalRequest 简化审批请求
type SimpleApprovalRequest struct {
	Reason string `json:"reason" binding:"required" example:"符合退款条件"` // 审批原因
	Remark string `json:"remark" example:"快速审批通过" validate:"max=500"` // 审批备注
}

// RefundReportRequest 退款报告请求
type RefundReportRequest struct {
	ReportType string `json:"report_type" binding:"required" comment:"报告类型"`
	Period     string `json:"period" binding:"required" comment:"报告周期"`
	DateStart  string `json:"date_start" comment:"开始日期"`
	DateEnd    string `json:"date_end" comment:"结束日期"`
	RefundType *int   `json:"refund_type" comment:"退款类型"`
	OperatorID *int64 `json:"operator_id" comment:"操作员ID"`
	Format     string `json:"format" binding:"required,oneof=pdf excel" comment:"报告格式"`
}

// RefundReportResponse 退款报告响应
type RefundReportResponse struct {
	ReportID    string               `json:"report_id"`
	ReportType  string               `json:"report_type"`
	Period      string               `json:"period"`
	GeneratedAt time.Time            `json:"generated_at"`
	GeneratedBy int64                `json:"generated_by"`
	FileURL     string               `json:"file_url"`
	Summary     *RefundReportSummary `json:"summary"`
}

// RefundReportSummary 退款报告摘要
type RefundReportSummary struct {
	TotalRefunds      int64    `json:"total_refunds"`
	TotalAmount       float64  `json:"total_amount"`
	SuccessfulRefunds int64    `json:"successful_refunds"`
	FailedRefunds     int64    `json:"failed_refunds"`
	SuccessRate       float64  `json:"success_rate"`
	AvgRefundAmount   float64  `json:"avg_refund_amount"`
	KeyFindings       []string `json:"key_findings"`
}

// RefundHealthResponse 退款健康响应
type RefundHealthResponse struct {
	OverallHealth   string         `json:"overall_health"`
	ProcessingCount int64          `json:"processing_count"`
	FailureRate     float64        `json:"failure_rate"`
	AvgProcessTime  float64        `json:"avg_process_time"`
	SystemLoad      float64        `json:"system_load"`
	Alerts          []*HealthAlert `json:"alerts"`
	Recommendations []string       `json:"recommendations"`
	LastCheck       time.Time      `json:"last_check"`
}

// RefundErrorStatsResponse 退款错误统计响应
type RefundErrorStatsResponse struct {
	Period          string               `json:"period"`
	TotalErrors     int64                `json:"total_errors"`
	ErrorRate       float64              `json:"error_rate"`
	CommonErrors    []*RefundErrorReason `json:"common_errors"`
	ErrorTrends     []*ErrorTrendPoint   `json:"error_trends"`
	Recommendations []string             `json:"recommendations"`
}

// RefundErrorReason 退款错误原因
type RefundErrorReason struct {
	ErrorCode  string  `json:"error_code"`
	ErrorMsg   string  `json:"error_msg"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
	Trend      string  `json:"trend"`
}

// ErrorTrendPoint 错误趋势数据点
type ErrorTrendPoint struct {
	Date       string  `json:"date"`
	ErrorCount int64   `json:"error_count"`
	ErrorRate  float64 `json:"error_rate"`
}

// RefundHistoryResponse 退款历史响应
type RefundHistoryResponse struct {
	RefundID       int64      `json:"refund_id"`
	OrderID        int64      `json:"order_id"`
	RefundType     int        `json:"refund_type"`
	RefundTypeText string     `json:"refund_type_text"`
	RefundAmount   float64    `json:"refund_amount"`
	Status         int        `json:"status"`
	StatusText     string     `json:"status_text"`
	CreatedAt      time.Time  `json:"created_at"`
	CompletedAt    *time.Time `json:"completed_at"`
	ProcessTime    *int       `json:"process_time"`
	OperatorName   string     `json:"operator_name"`
	RefundReason   string     `json:"refund_reason"`
}

// RefundProgressResponse 退款进度响应
type RefundProgressResponse struct {
	RefundID      int64             `json:"refund_id"`
	CurrentStep   int               `json:"current_step"`
	TotalSteps    int               `json:"total_steps"`
	Progress      float64           `json:"progress"`
	EstimatedTime *int              `json:"estimated_time"`
	Steps         []*RefundProgress `json:"steps"`
	LastUpdate    time.Time         `json:"last_update"`
}

// PaymentRefundRequest 支付退款请求
type PaymentRefundRequest struct {
	RefundID     int64   `json:"refund_id"`
	OrderNo      string  `json:"order_no"`
	RefundNo     string  `json:"refund_no"`
	RefundAmount float64 `json:"refund_amount"`
	RefundReason string  `json:"refund_reason"`
	NotifyURL    string  `json:"notify_url"`
}

// PaymentRefundResponse 支付退款响应
type PaymentRefundResponse struct {
	RefundID      int64   `json:"refund_id"`
	RefundNo      string  `json:"refund_no"`
	Status        string  `json:"status"`
	RefundAmount  float64 `json:"refund_amount"`
	TransactionID string  `json:"transaction_id"`
	Message       string  `json:"message"`
	Success       bool    `json:"success"`
}

// RefundStatusResponse 退款状态响应
type RefundStatusResponse struct {
	RefundID      int64      `json:"refund_id"`
	RefundNo      string     `json:"refund_no"`
	Status        int        `json:"status"`
	StatusText    string     `json:"status_text"`
	RefundAmount  float64    `json:"refund_amount"`
	ProcessedAt   *time.Time `json:"processed_at"`
	TransactionID string     `json:"transaction_id"`
	FailureReason string     `json:"failure_reason,omitempty"`
}

// BatchProcessResult 批量处理结果
type BatchProcessResult struct {
	TotalCount   int                    `json:"total_count"`
	SuccessCount int                    `json:"success_count"`
	FailureCount int                    `json:"failure_count"`
	Results      []*ProcessResult       `json:"results"`
	Summary      map[string]interface{} `json:"summary"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	ID      int64       `json:"id"`
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// RefundRiskAssessment 退款风险评估
type RefundRiskAssessment struct {
	RefundID         int64    `json:"refund_id"`
	RiskLevel        string   `json:"risk_level"` // low/medium/high
	RiskScore        float64  `json:"risk_score"`
	RiskFactors      []string `json:"risk_factors"`
	Recommendation   string   `json:"recommendation"`
	RequiresApproval bool     `json:"requires_approval"`
	AutoApproved     bool     `json:"auto_approved"`
}

// PlatformRefundStatus 平台退款状态
type PlatformRefundStatus struct {
	RefundID         int64      `json:"refund_id"`
	PlatformType     string     `json:"platform_type"` // wechat/alipay/bank
	PlatformRefundID string     `json:"platform_refund_id"`
	Status           string     `json:"status"`
	StatusCode       string     `json:"status_code"`
	RefundAmount     float64    `json:"refund_amount"`
	ProcessedAt      *time.Time `json:"processed_at"`
	FailureReason    string     `json:"failure_reason,omitempty"`
	RetryCount       int        `json:"retry_count"`
	NextRetryAt      *time.Time `json:"next_retry_at,omitempty"`
}

// ConfigTestResult 配置测试结果
type ConfigTestResult struct {
	Success  bool                   `json:"success"`
	TestType string                 `json:"test_type"`
	Message  string                 `json:"message"`
	Details  map[string]interface{} `json:"details,omitempty"`
	Errors   []string               `json:"errors,omitempty"`
	Warnings []string               `json:"warnings,omitempty"`
	TestedAt time.Time              `json:"tested_at"`
	Duration int64                  `json:"duration"` // 测试耗时(毫秒)
}

// RefundUpdateRequest 退款更新请求
type RefundUpdateRequest struct {
	Reason string `json:"reason" binding:"required"`
}

// RefundReceiptResponse 退款凭证响应
type RefundReceiptResponse struct {
	RefundID            uint      `json:"refund_id"`             // 退款ID
	RefundNo            string    `json:"refund_no"`             // 退款单号
	WechatRefundNo      string    `json:"wechat_refund_no"`      // 微信退款单号
	OrderID             uint      `json:"order_id"`              // 订单ID
	OrderNo             string    `json:"order_no"`              // 订单号
	RefundAmount        float64   `json:"refund_amount"`         // 退款金额
	RefundTime          time.Time `json:"refund_time"`           // 退款时间
	RefundReason        string    `json:"refund_reason"`         // 退款原因
	RefundChannel       string    `json:"refund_channel"`        // 退款渠道
	UserReceivedAccount string    `json:"user_received_account"` // 用户收款账户
	RefundStatus        string    `json:"refund_status"`         // 退款状态
	RefundStatusText    string    `json:"refund_status_text"`    // 退款状态文本
	OperatorName        string    `json:"operator_name"`         // 操作员姓名
	ProcessTime         time.Time `json:"process_time"`          // 处理时间

	// 支付相关信息
	PaymentMethod  string  `json:"payment_method"`  // 支付方式
	PaymentNo      string  `json:"payment_no"`      // 支付单号
	OriginalAmount float64 `json:"original_amount"` // 原订单金额

	// 凭证信息
	ReceiptURL         string  `json:"receipt_url"`          // 凭证URL(如果有)
	TransactionID      string  `json:"transaction_id"`       // 交易流水号
	RefundFee          float64 `json:"refund_fee"`           // 退款手续费
	ActualRefundAmount float64 `json:"actual_refund_amount"` // 实际退款金额

	// 时间戳
	GeneratedAt time.Time `json:"generated_at"` // 凭证生成时间
}
