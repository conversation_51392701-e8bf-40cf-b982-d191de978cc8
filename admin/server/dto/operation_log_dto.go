package dto

import (
	"time"
)

// CreateLogRequest 创建日志请求
type CreateLogRequest struct {
	OperatorID      int64  `json:"operator_id" binding:"required" comment:"操作员ID"`
	OperationType   string `json:"operation_type" binding:"required,max=50" comment:"操作类型"`
	ResourceType    string `json:"resource_type" binding:"required,max=50" comment:"资源类型"`
	ResourceID      int64  `json:"resource_id" comment:"资源ID"`
	OperationDetail string `json:"operation_detail" binding:"max=1000" comment:"操作详情"`
	OperationResult int    `json:"operation_result" binding:"min=0,max=2" comment:"操作结果:0=失败,1=成功,2=部分成功"`
	ErrorMessage    string `json:"error_message,omitempty" binding:"max=500" comment:"错误信息"`
	IPAddress       string `json:"ip_address,omitempty" comment:"IP地址"`
	UserAgent       string `json:"user_agent,omitempty" comment:"用户代理"`
	RequestData     string `json:"request_data,omitempty" comment:"请求数据"`
	ResponseData    string `json:"response_data,omitempty" comment:"响应数据"`
}

// LogListRequest 日志列表请求
type LogListRequest struct {
	Page          int    `json:"page" form:"page" binding:"min=1" comment:"页码"`
	PageSize      int    `json:"page_size" form:"page_size" binding:"min=1,max=100" comment:"每页大小"`
	OperatorID    *int64 `json:"operator_id" form:"operator_id" comment:"操作员ID"`
	OperationType string `json:"operation_type" form:"operation_type" comment:"操作类型"`
	ResourceType  string `json:"resource_type" form:"resource_type" comment:"资源类型"`
	ResourceID    *int64 `json:"resource_id" form:"resource_id" comment:"资源ID"`
	Result        *int   `json:"result" form:"result" comment:"操作结果"`
	DateStart     string `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd       string `json:"date_end" form:"date_end" comment:"结束日期"`
	IPAddress     string `json:"ip_address" form:"ip_address" comment:"IP地址"`
	OnlyFailures  bool   `json:"only_failures" form:"only_failures" comment:"仅失败记录"`
}

// LogSearchRequest 日志搜索请求
type LogSearchRequest struct {
	Keyword        string   `json:"keyword" form:"keyword" comment:"关键词"`
	OperationTypes []string `json:"operation_types" form:"operation_types" comment:"操作类型列表"`
	ResourceTypes  []string `json:"resource_types" form:"resource_types" comment:"资源类型列表"`
	OperatorIDs    []int64  `json:"operator_ids" form:"operator_ids" comment:"操作员ID列表"`
	Results        []int    `json:"results" form:"results" comment:"结果列表"`
	IPAddresses    []string `json:"ip_addresses" form:"ip_addresses" comment:"IP地址列表"`
	DateStart      string   `json:"date_start" form:"date_start" comment:"开始日期"`
	DateEnd        string   `json:"date_end" form:"date_end" comment:"结束日期"`
	SortBy         string   `json:"sort_by" form:"sort_by" comment:"排序字段"`
	SortOrder      string   `json:"sort_order" form:"sort_order" comment:"排序方向"`
	Page           int      `json:"page" form:"page" binding:"min=1" comment:"页码"`
	PageSize       int      `json:"page_size" form:"page_size" binding:"min=1,max=100" comment:"每页大小"`
}

// LogListResponse 日志列表响应
type LogListResponse struct {
	List       []*OperationLogInfo `json:"list"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

// OperationChainResponse 操作链响应
type OperationChainResponse struct {
	ChainID    string              `json:"chain_id"`
	Operations []*OperationLogInfo `json:"operations"`
	StartTime  time.Time           `json:"start_time"`
	EndTime    *time.Time          `json:"end_time"`
	Duration   *int                `json:"duration"` // 持续时间(秒)
	Success    bool                `json:"success"`
	Summary    string              `json:"summary"`
}

// LogStatsResponse 日志统计响应
type LogStatsResponse struct {
	Period               string                  `json:"period"`
	TotalOperations      int64                   `json:"total_operations"`
	SuccessCount         int64                   `json:"success_count"`
	FailureCount         int64                   `json:"failure_count"`
	PartialSuccessCount  int64                   `json:"partial_success_count"`
	SuccessRate          float64                 `json:"success_rate"`
	TypeDistribution     map[string]int64        `json:"type_distribution"`
	OperatorDistribution map[string]int64        `json:"operator_distribution"`
	HourlyDistribution   map[string]int64        `json:"hourly_distribution"`
	TopOperations        []*TopOperationResponse `json:"top_operations"`
}

// OperatorLogStatsResponse 操作员日志统计响应
type OperatorLogStatsResponse struct {
	OperatorID       int64            `json:"operator_id"`
	OperatorName     string           `json:"operator_name"`
	Period           string           `json:"period"`
	TotalOperations  int64            `json:"total_operations"`
	SuccessCount     int64            `json:"success_count"`
	FailureCount     int64            `json:"failure_count"`
	SuccessRate      float64          `json:"success_rate"`
	TypeDistribution map[string]int64 `json:"type_distribution"`
	ActiveHours      []int            `json:"active_hours"`
	AvgDailyOps      float64          `json:"avg_daily_ops"`
	PeakActivity     string           `json:"peak_activity"`
}

// OperationTypeStatsResponse 操作类型统计响应
type OperationTypeStatsResponse struct {
	OperationType   string                 `json:"operation_type"`
	Period          string                 `json:"period"`
	TotalCount      int64                  `json:"total_count"`
	SuccessCount    int64                  `json:"success_count"`
	FailureCount    int64                  `json:"failure_count"`
	SuccessRate     float64                `json:"success_rate"`
	AvgResponseTime float64                `json:"avg_response_time"`
	TopOperators    []*TopOperatorResponse `json:"top_operators"`
	TrendData       []*TrendDataPoint      `json:"trend_data"`
}

// HourlyStatsResponse 小时统计响应
type HourlyStatsResponse struct {
	Hour        int   `json:"hour"`
	TotalOps    int64 `json:"total_ops"`
	SuccessOps  int64 `json:"success_ops"`
	FailureOps  int64 `json:"failure_ops"`
	UniqueUsers int64 `json:"unique_users"`
}

// DailyStatsResponse 日统计响应
type DailyStatsResponse struct {
	Date        string `json:"date"`
	TotalOps    int64  `json:"total_ops"`
	SuccessOps  int64  `json:"success_ops"`
	FailureOps  int64  `json:"failure_ops"`
	UniqueUsers int64  `json:"unique_users"`
	PeakHour    int    `json:"peak_hour"`
}

// OperationHealthResponse 操作健康响应
type OperationHealthResponse struct {
	OverallHealth   string         `json:"overall_health"` // healthy/warning/critical
	ActiveOperators int64          `json:"active_operators"`
	RecentErrors    int64          `json:"recent_errors"`
	ErrorRate       float64        `json:"error_rate"`
	ResponseTime    float64        `json:"response_time"`
	SystemLoad      float64        `json:"system_load"`
	Alerts          []*HealthAlert `json:"alerts"`
	Recommendations []string       `json:"recommendations"`
	LastCheck       time.Time      `json:"last_check"`
}

// TopOperatorResponse 顶级操作员响应
type TopOperatorResponse struct {
	OperatorID     int64   `json:"operator_id"`
	OperatorName   string  `json:"operator_name"`
	OperationCount int64   `json:"operation_count"`
	SuccessRate    float64 `json:"success_rate"`
	Rank           int     `json:"rank"`
}

// AnomalyResponse 异常响应
type AnomalyResponse struct {
	AnomalyType    string    `json:"anomaly_type"`
	Description    string    `json:"description"`
	Severity       string    `json:"severity"` // low/medium/high/critical
	DetectedAt     time.Time `json:"detected_at"`
	OperatorID     int64     `json:"operator_id"`
	OperatorName   string    `json:"operator_name"`
	Evidence       []string  `json:"evidence"`
	Recommendation string    `json:"recommendation"`
}

// ConcurrentOperationResponse 并发操作响应
type ConcurrentOperationResponse struct {
	TimeWindow    string               `json:"time_window"`
	MaxConcurrent int                  `json:"max_concurrent"`
	AvgConcurrent float64              `json:"avg_concurrent"`
	Operations    []*OperationLogInfo  `json:"operations"`
	Conflicts     []*ConflictOperation `json:"conflicts"`
}

// ConflictOperation 冲突操作
type ConflictOperation struct {
	OperationID1 int64     `json:"operation_id_1"`
	OperationID2 int64     `json:"operation_id_2"`
	ConflictType string    `json:"conflict_type"`
	ResourceType string    `json:"resource_type"`
	ResourceID   int64     `json:"resource_id"`
	ConflictTime time.Time `json:"conflict_time"`
}

// FailureAnalysisResponse 失败分析响应
type FailureAnalysisResponse struct {
	Period          string                  `json:"period"`
	TotalFailures   int64                   `json:"total_failures"`
	FailureRate     float64                 `json:"failure_rate"`
	CommonReasons   []*FailureReason        `json:"common_reasons"`
	OperatorImpact  []*OperatorFailureStats `json:"operator_impact"`
	TimePattern     map[string]int64        `json:"time_pattern"`
	Recommendations []string                `json:"recommendations"`
}

// FailureReason 失败原因
type FailureReason struct {
	Reason     string  `json:"reason"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
	Trend      string  `json:"trend"` // increasing/decreasing/stable
}

// OperatorFailureStats 操作员失败统计
type OperatorFailureStats struct {
	OperatorID   int64    `json:"operator_id"`
	OperatorName string   `json:"operator_name"`
	FailureCount int64    `json:"failure_count"`
	FailureRate  float64  `json:"failure_rate"`
	MainReasons  []string `json:"main_reasons"`
}

// LogExportRequest 日志导出请求
type LogExportRequest struct {
	Format         string   `json:"format" binding:"required,oneof=excel csv json" comment:"导出格式"`
	OperationTypes []string `json:"operation_types" comment:"操作类型"`
	OperatorIDs    []int64  `json:"operator_ids" comment:"操作员ID"`
	Results        []int    `json:"results" comment:"结果"`
	DateStart      string   `json:"date_start" comment:"开始日期"`
	DateEnd        string   `json:"date_end" comment:"结束日期"`
	Fields         []string `json:"fields" comment:"导出字段"`
	IncludeDetails bool     `json:"include_details" comment:"包含详细信息"`
}

// AuditReportRequest 审计报告请求
type AuditReportRequest struct {
	ReportType    string  `json:"report_type" binding:"required" comment:"报告类型"`
	Period        string  `json:"period" binding:"required" comment:"报告周期"`
	OperatorIDs   []int64 `json:"operator_ids" comment:"操作员ID"`
	IncludeFailed bool    `json:"include_failed" comment:"包含失败操作"`
	Format        string  `json:"format" binding:"required,oneof=pdf excel" comment:"报告格式"`
}

// AuditReportResponse 审计报告响应
type AuditReportResponse struct {
	ReportID    string         `json:"report_id"`
	ReportType  string         `json:"report_type"`
	Period      string         `json:"period"`
	GeneratedAt time.Time      `json:"generated_at"`
	GeneratedBy int64          `json:"generated_by"`
	FileURL     string         `json:"file_url"`
	Summary     *ReportSummary `json:"summary"`
}

// ReportSummary 报告摘要
type ReportSummary struct {
	TotalOperations    int64    `json:"total_operations"`
	SuccessfulOps      int64    `json:"successful_ops"`
	FailedOps          int64    `json:"failed_ops"`
	UniqueOperators    int64    `json:"unique_operators"`
	OperationTypes     int      `json:"operation_types"`
	AverageSuccessRate float64  `json:"average_success_rate"`
	KeyFindings        []string `json:"key_findings"`
}

// LogStorageStatsResponse 日志存储统计响应
type LogStorageStatsResponse struct {
	TotalLogs       int64     `json:"total_logs"`
	TotalSize       int64     `json:"total_size"` // 字节
	AvgLogSize      float64   `json:"avg_log_size"`
	OldestLog       time.Time `json:"oldest_log"`
	NewestLog       time.Time `json:"newest_log"`
	RetentionDays   int       `json:"retention_days"`
	ArchiveSize     int64     `json:"archive_size"`
	CompressionRate float64   `json:"compression_rate"`
}

// LogRetentionPolicyResponse 日志保留策略响应
type LogRetentionPolicyResponse struct {
	DefaultRetentionDays int               `json:"default_retention_days"`
	CategoryPolicies     []*CategoryPolicy `json:"category_policies"`
	ArchiveEnabled       bool              `json:"archive_enabled"`
	ArchiveLocation      string            `json:"archive_location"`
	CompressionEnabled   bool              `json:"compression_enabled"`
	LastCleanup          *time.Time        `json:"last_cleanup"`
}

// LogRetentionPolicyRequest 日志保留策略请求
type LogRetentionPolicyRequest struct {
	DefaultRetentionDays int               `json:"default_retention_days" binding:"required,min=1" comment:"默认保留天数"`
	CategoryPolicies     []*CategoryPolicy `json:"category_policies" comment:"分类策略"`
	ArchiveEnabled       bool              `json:"archive_enabled" comment:"启用归档"`
	ArchiveLocation      string            `json:"archive_location" comment:"归档位置"`
	CompressionEnabled   bool              `json:"compression_enabled" comment:"启用压缩"`
}

// CategoryPolicy 分类策略
type CategoryPolicy struct {
	Category         string `json:"category"`
	RetentionDays    int    `json:"retention_days"`
	ArchiveAfter     int    `json:"archive_after"`
	CompressionLevel int    `json:"compression_level"`
}

// LogCategoryResponse 日志分类响应
type LogCategoryResponse struct {
	Category    string `json:"category"`
	Description string `json:"description"`
	LogLevel    int    `json:"log_level"`
	Count       int64  `json:"count"`
	Enabled     bool   `json:"enabled"`
}

// IntegrityCheckResponse 完整性检查响应
type IntegrityCheckResponse struct {
	LogID      int64     `json:"log_id"`
	Valid      bool      `json:"valid"`
	Checksum   string    `json:"checksum"`
	VerifiedAt time.Time `json:"verified_at"`
	Issues     []string  `json:"issues,omitempty"`
}

// TamperingDetectionResponse 篡改检测响应
type TamperingDetectionResponse struct {
	LogID         int64     `json:"log_id"`
	Tampered      bool      `json:"tampered"`
	TamperingType string    `json:"tampering_type"`
	DetectedAt    time.Time `json:"detected_at"`
	Evidence      []string  `json:"evidence"`
	Severity      string    `json:"severity"`
}

// LogAlertConfig 日志告警配置
type LogAlertConfig struct {
	AlertType  string   `json:"alert_type" binding:"required" comment:"告警类型"`
	Enabled    bool     `json:"enabled" comment:"是否启用"`
	Threshold  float64  `json:"threshold" comment:"阈值"`
	TimeWindow int      `json:"time_window" comment:"时间窗口(分钟)"`
	Recipients []string `json:"recipients" comment:"接收人"`
	Conditions []string `json:"conditions" comment:"触发条件"`
	Severity   string   `json:"severity" comment:"严重级别"`
}

// HealthAlert 健康告警
type HealthAlert struct {
	AlertType string    `json:"alert_type"`
	Message   string    `json:"message"`
	Severity  string    `json:"severity"`
	Timestamp time.Time `json:"timestamp"`
	Affected  []string  `json:"affected"`
	Action    string    `json:"action"`
}

// TopOperationResponse 顶级操作响应
type TopOperationResponse struct {
	OperationType string  `json:"operation_type"`
	Count         int64   `json:"count"`
	SuccessRate   float64 `json:"success_rate"`
	AvgDuration   float64 `json:"avg_duration"`
	Rank          int     `json:"rank"`
}

// TrendDataPoint 趋势数据点
type TrendDataPoint struct {
	Date   string  `json:"date"`
	Value  int64   `json:"value"`
	Change float64 `json:"change"` // 变化百分比
}

// FrontendOperationLogInfo 前端操作日志信息（适配前端页面期望的字段）
type FrontendOperationLogInfo struct {
	ID        int64     `json:"id"`
	AdminName string    `json:"admin_name"` // 操作人
	IP        string    `json:"ip"`         // IP地址
	Method    string    `json:"method"`     // 请求方法
	Path      string    `json:"path"`       // 请求路径
	Action    string    `json:"action"`     // 操作描述
	Params    string    `json:"params"`     // 请求参数
	Status    int       `json:"status"`     // 状态
	Duration  int       `json:"duration"`   // 耗时(ms)
	CreatedAt time.Time `json:"created_at"` // 操作时间
}
