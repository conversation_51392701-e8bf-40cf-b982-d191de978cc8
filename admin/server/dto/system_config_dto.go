package dto

import "time"

// SystemConfigListRequest 系统配置列表请求
type SystemConfigListRequest struct {
	Category string `form:"category" json:"category"`                     // 配置分类
	Status   *int   `form:"status" json:"status"`                         // 状态
	Page     int    `form:"page" json:"page" binding:"min=1"`             // 页码
	PageSize int    `form:"page_size" json:"page_size" binding:"max=100"` // 每页数量
}

// SystemConfigListResponse 系统配置列表响应
type SystemConfigListResponse struct {
	List  []*SystemConfigItem `json:"list"`  // 配置列表
	Total int64               `json:"total"` // 总数
	Page  int                 `json:"page"`  // 当前页码
}

// SystemConfigItem 系统配置项
type SystemConfigItem struct {
	ID          uint      `json:"id"`          // 配置ID
	Key         string    `json:"key"`         // 配置键
	Value       string    `json:"value"`       // 配置值
	Description string    `json:"description"` // 配置描述
	Status      int       `json:"status"`      // 状态（1：启用，0：禁用）
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}

// SystemConfigDetailResponse 系统配置详情响应
type SystemConfigDetailResponse struct {
	*SystemConfigItem
}

// CreateSystemConfigRequest 创建系统配置请求
type CreateSystemConfigRequest struct {
	Key         string `json:"key" binding:"required,max=50"` // 配置键
	Value       string `json:"value" binding:"required"`      // 配置值
	Description string `json:"description" binding:"max=255"` // 配置描述
}

// UpdateSystemConfigRequest 更新系统配置请求
type UpdateSystemConfigRequest struct {
	Value       string `json:"value"`                 // 配置值
	Description string `json:"description,omitempty"` // 配置描述
	Status      *int   `json:"status,omitempty"`      // 状态
}

// SystemConfigResponse 系统配置响应
type SystemConfigResponse struct {
	*SystemConfigItem
	Message string `json:"message"` // 响应消息
}

// BatchUpdateSystemConfigRequest 批量更新系统配置请求
type BatchUpdateSystemConfigRequest struct {
	Configs map[string]string `json:"configs" binding:"required"` // 配置项映射 key->value
	Reason  string            `json:"reason"`                     // 变更原因
}

// BatchUpdateSystemConfigResponse 批量更新系统配置响应
type BatchUpdateSystemConfigResponse struct {
	UpdatedCount  int               `json:"updated_count"`  // 更新数量
	SuccessKeys   []string          `json:"success_keys"`   // 成功的配置键
	FailedKeys    []string          `json:"failed_keys"`    // 失败的配置键
	ErrorMessages map[string]string `json:"error_messages"` // 错误消息
	UpdatedAt     time.Time         `json:"updated_at"`     // 更新时间
}

// TestSystemConfigRequest 测试系统配置请求
type TestSystemConfigRequest struct {
	Key   string `json:"key" binding:"required"`   // 配置键
	Value string `json:"value" binding:"required"` // 配置值
}

// TestSystemConfigResponse 测试系统配置响应
type TestSystemConfigResponse struct {
	Key         string `json:"key"`          // 配置键
	TestResult  bool   `json:"test_result"`  // 测试结果
	TestMessage string `json:"test_message"` // 测试消息
}
