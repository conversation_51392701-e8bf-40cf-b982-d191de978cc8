package dto

import "time"

// WechatRefundNotifyRequest 微信退款回调请求
type WechatRefundNotifyRequest struct {
	ID           string                 `json:"id"`
	CreateTime   time.Time              `json:"create_time"`
	ResourceType string                 `json:"resource_type"`
	EventType    string                 `json:"event_type"`
	Summary      string                 `json:"summary"`
	Resource     WechatRefundNotifyData `json:"resource"`
}

// WechatRefundNotifyData 微信退款回调数据
type WechatRefundNotifyData struct {
	Algorithm      string `json:"algorithm"`
	Ciphertext     string `json:"ciphertext"`
	AssociatedData string `json:"associated_data"`
	Nonce          string `json:"nonce"`
}

// WechatRefundNotifyDecrypted 解密后的微信退款回调数据
type WechatRefundNotifyDecrypted struct {
	OutTradeNo   string    `json:"out_trade_no"`
	OutRefundNo  string    `json:"out_refund_no"`
	RefundID     string    `json:"refund_id"`
	RefundStatus string    `json:"refund_status"`
	SuccessTime  time.Time `json:"success_time"`
	Amount       struct {
		Total    int64  `json:"total"`
		Refund   int64  `json:"refund"`
		Currency string `json:"currency"`
	} `json:"amount"`
	UserReceivedAccount string `json:"user_received_account"`
}

// WechatNotifyResponse 微信回调响应
type WechatNotifyResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}
