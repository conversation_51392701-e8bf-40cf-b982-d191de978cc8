package dto

import (
	"time"
)

// OrderStatusUpdateRequest 订单状态更新请求
type OrderStatusUpdateRequest struct {
	OrderID      uint   `json:"order_id" binding:"required"`
	NewStatus    int    `json:"new_status" binding:"required,min=1,max=6"`
	Reason       string `json:"reason"`
	OperatorID   *uint  `json:"operator_id"`
	OperatorName string `json:"operator_name"`
	Remark       string `json:"remark"`
}

// MatchingStatusUpdateRequest 匹配状态更新请求
type MatchingStatusUpdateRequest struct {
	OrderID           uint   `json:"order_id" binding:"required"`
	NewMatchingStatus int    `json:"new_matching_status" binding:"required,min=0,max=3"`
	Reason            string `json:"reason"`
	OperatorID        *uint  `json:"operator_id"`
	OperatorName      string `json:"operator_name"`
	Remark            string `json:"remark"`
}

// OrderStatusResponse 订单状态响应
type OrderStatusResponse struct {
	OrderID             uint      `json:"order_id"`
	OrderNo             string    `json:"order_no"`
	CurrentStatus       int       `json:"current_status"`
	CurrentStatusText   string    `json:"current_status_text"`
	MatchingStatus      int       `json:"matching_status"`
	MatchingStatusText  string    `json:"matching_status_text"`
	LastUpdateTime      time.Time `json:"last_update_time"`
	CanTransitionTo     []int     `json:"can_transition_to"`
	CanTransitionToText []string  `json:"can_transition_to_text"`
}

// StatusStatsRequest 状态统计请求
type StatusStatsRequest struct {
	StartDate string `json:"start_date" form:"start_date"`
	EndDate   string `json:"end_date" form:"end_date"`
	Status    *int   `json:"status,omitempty" form:"status"`
}

// StatusStatsResponse 状态统计响应
type StatusStatsResponse struct {
	TotalOrders     int64            `json:"total_orders"`
	StatusStats     []StatusStat     `json:"status_stats"`
	MatchingStats   []MatchingStat   `json:"matching_stats"`
	TransitionStats []TransitionStat `json:"transition_stats"`
}

// StatusStat 状态统计
type StatusStat struct {
	Status     int     `json:"status"`
	StatusText string  `json:"status_text"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// MatchingStat 匹配状态统计
type MatchingStat struct {
	MatchingStatus     int     `json:"matching_status"`
	MatchingStatusText string  `json:"matching_status_text"`
	Count              int64   `json:"count"`
	Percentage         float64 `json:"percentage"`
}

// TransitionStat 状态转换统计
type TransitionStat struct {
	FromStatus     int     `json:"from_status"`
	ToStatus       int     `json:"to_status"`
	FromStatusText string  `json:"from_status_text"`
	ToStatusText   string  `json:"to_status_text"`
	Count          int64   `json:"count"`
	Percentage     float64 `json:"percentage"`
}

// BatchStatusUpdateRequest 批量状态更新请求
type BatchStatusUpdateRequest struct {
	OrderIDs     []uint `json:"order_ids" binding:"required"`
	NewStatus    int    `json:"new_status" binding:"required,min=1,max=6"`
	Reason       string `json:"reason"`
	OperatorID   *uint  `json:"operator_id"`
	OperatorName string `json:"operator_name"`
	Remark       string `json:"remark"`
}

// OrderStatusHistoryResponse 订单状态历史响应
type OrderStatusHistoryResponse struct {
	ID                         uint      `json:"id"`
	OrderID                    uint      `json:"order_id"`
	OrderNo                    string    `json:"order_no"`
	PreviousStatus             int       `json:"previous_status"`
	NewStatus                  int       `json:"new_status"`
	PreviousStatusText         string    `json:"previous_status_text"`
	NewStatusText              string    `json:"new_status_text"`
	PreviousMatchingStatus     int       `json:"previous_matching_status"`
	NewMatchingStatus          int       `json:"new_matching_status"`
	PreviousMatchingStatusText string    `json:"previous_matching_status_text"`
	NewMatchingStatusText      string    `json:"new_matching_status_text"`
	ChangeReason               string    `json:"change_reason"`
	ChangeType                 string    `json:"change_type"`
	ChangeTypeText             string    `json:"change_type_text"`
	OperatorID                 *uint     `json:"operator_id"`
	OperatorName               string    `json:"operator_name"`
	Remark                     string    `json:"remark"`
	CreatedAt                  time.Time `json:"created_at"`
}

// OrderStatusHistoryListRequest 订单状态历史列表请求
type OrderStatusHistoryListRequest struct {
	OrderID       *uint  `json:"order_id" form:"order_id"`
	OrderNo       string `json:"order_no" form:"order_no"`
	Status        *int   `json:"status" form:"status"`
	ChangeType    string `json:"change_type" form:"change_type"`
	OperatorID    *uint  `json:"operator_id" form:"operator_id"`
	StartDate     string `json:"start_date" form:"start_date"`
	EndDate       string `json:"end_date" form:"end_date"`
	Page          int    `json:"page" form:"page" binding:"required,min=1"`
	Limit         int    `json:"limit" form:"limit" binding:"required,min=1,max=100"`
	SortBy        string `json:"sort_by" form:"sort_by"`
	SortDirection string `json:"sort_direction" form:"sort_direction"`
}

// OrderStatusHistoryListResponse 订单状态历史列表响应
type OrderStatusHistoryListResponse struct {
	List  []*OrderStatusHistoryResponse `json:"list"`
	Total int64                         `json:"total"`
	Page  int                           `json:"page"`
	Limit int                           `json:"limit"`
}

// OrderStatusValidationResponse 订单状态验证响应
type OrderStatusValidationResponse struct {
	Valid                  bool     `json:"valid"`
	Reason                 string   `json:"reason"`
	CurrentStatus          int      `json:"current_status"`
	CurrentStatusText      string   `json:"current_status_text"`
	TargetStatus           int      `json:"target_status"`
	TargetStatusText       string   `json:"target_status_text"`
	AllowedTransitions     []int    `json:"allowed_transitions"`
	AllowedTransitionsText []string `json:"allowed_transitions_text"`
}

// OrderTimeoutProcessRequest 订单超时处理请求
type OrderTimeoutProcessRequest struct {
	TimeoutMinutes int  `json:"timeout_minutes" binding:"required,min=1"`
	DryRun         bool `json:"dry_run"`
}

// OrderTimeoutProcessResponse 订单超时处理响应
type OrderTimeoutProcessResponse struct {
	ProcessedCount  int64                       `json:"processed_count"`
	FailedCount     int64                       `json:"failed_count"`
	ErrorMessages   []string                    `json:"error_messages"`
	ProcessedOrders []OrderTimeoutProcessDetail `json:"processed_orders"`
}

// OrderTimeoutProcessDetail 订单超时处理详情
type OrderTimeoutProcessDetail struct {
	OrderID        uint      `json:"order_id"`
	OrderNo        string    `json:"order_no"`
	PreviousStatus int       `json:"previous_status"`
	NewStatus      int       `json:"new_status"`
	ProcessTime    time.Time `json:"process_time"`
	Reason         string    `json:"reason"`
}

// MatchingFailureProcessRequest 匹配失败处理请求
type MatchingFailureProcessRequest struct {
	OrderID      uint     `json:"order_id" binding:"required"`
	Reason       string   `json:"reason" binding:"required"`
	CreateRefund bool     `json:"create_refund"`
	RefundAmount *float64 `json:"refund_amount"`
	OperatorID   *uint    `json:"operator_id"`
	OperatorName string   `json:"operator_name"`
}

// MatchingFailureProcessResponse 匹配失败处理响应
type MatchingFailureProcessResponse struct {
	Success       bool      `json:"success"`
	OrderID       uint      `json:"order_id"`
	OrderNo       string    `json:"order_no"`
	NewStatus     int       `json:"new_status"`
	NewStatusText string    `json:"new_status_text"`
	RefundCreated bool      `json:"refund_created"`
	RefundID      *uint     `json:"refund_id"`
	ProcessTime   time.Time `json:"process_time"`
	Message       string    `json:"message"`
}
