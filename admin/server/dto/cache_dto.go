package dto

import "time"

// ============ 缓存统计DTO ============

// CacheStatsResponse 缓存使用统计响应
type CacheStatsResponse struct {
	// 基本统计
	TotalKeys  int64   `json:"total_keys"`  // 总键数
	UsedMemory int64   `json:"used_memory"` // 已使用内存(字节)
	HitRate    float64 `json:"hit_rate"`    // 命中率
	MissRate   float64 `json:"miss_rate"`   // 未命中率

	// 操作统计
	TotalHits    int64 `json:"total_hits"`    // 总命中次数
	TotalMisses  int64 `json:"total_misses"`  // 总未命中次数
	TotalSets    int64 `json:"total_sets"`    // 总设置次数
	TotalGets    int64 `json:"total_gets"`    // 总获取次数
	TotalDeletes int64 `json:"total_deletes"` // 总删除次数

	// 性能统计
	AvgSetTime    float64 `json:"avg_set_time"`    // 平均设置时间(ms)
	AvgGetTime    float64 `json:"avg_get_time"`    // 平均获取时间(ms)
	AvgDeleteTime float64 `json:"avg_delete_time"` // 平均删除时间(ms)

	// 分类统计
	TaskStatsCache         *CacheCategoryStats `json:"task_stats_cache"`
	RefundStatsCache       *CacheCategoryStats `json:"refund_stats_cache"`
	NotificationStatsCache *CacheCategoryStats `json:"notification_stats_cache"`
	UserSessionCache       *CacheCategoryStats `json:"user_session_cache"`
	SystemConfigCache      *CacheCategoryStats `json:"system_config_cache"`

	// 时间信息
	StatsPeriod string    `json:"stats_period"` // 统计周期
	GeneratedAt time.Time `json:"generated_at"` // 生成时间
}

// CacheCategoryStats 缓存分类统计
type CacheCategoryStats struct {
	KeyCount     int64   `json:"key_count"`     // 键数量
	TotalSize    int64   `json:"total_size"`    // 总大小(字节)
	HitCount     int64   `json:"hit_count"`     // 命中次数
	MissCount    int64   `json:"miss_count"`    // 未命中次数
	HitRate      float64 `json:"hit_rate"`      // 命中率
	AvgTTL       int64   `json:"avg_ttl"`       // 平均TTL(秒)
	ExpiredCount int64   `json:"expired_count"` // 过期数量
}

// ============ 用户会话DTO ============

// UserSessionData 用户会话数据
type UserSessionData struct {
	UserID         int64                  `json:"user_id"`
	Username       string                 `json:"username"`
	Role           string                 `json:"role"`
	Permissions    []string               `json:"permissions"`
	LoginTime      time.Time              `json:"login_time"`
	LastActiveTime time.Time              `json:"last_active_time"`
	IPAddress      string                 `json:"ip_address"`
	UserAgent      string                 `json:"user_agent"`
	SessionToken   string                 `json:"session_token"`
	RefreshToken   string                 `json:"refresh_token"`
	ExpiresAt      time.Time              `json:"expires_at"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// ============ 性能监控DTO ============

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// API性能指标
	APIMetrics *APIPerformanceMetrics `json:"api_metrics"`

	// 数据库性能指标
	DatabaseMetrics *DatabasePerformanceMetrics `json:"database_metrics"`

	// 缓存性能指标
	CacheMetrics *CachePerformanceMetrics `json:"cache_metrics"`

	// 系统性能指标
	SystemMetrics *SystemPerformanceMetrics `json:"system_metrics"`

	// 业务性能指标
	BusinessMetrics *BusinessPerformanceMetrics `json:"business_metrics"`

	// 统计时间范围
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Duration  int64     `json:"duration"` // 秒
}

// APIPerformanceMetrics API性能指标
type APIPerformanceMetrics struct {
	TotalRequests     int64                    `json:"total_requests"`
	SuccessRequests   int64                    `json:"success_requests"`
	ErrorRequests     int64                    `json:"error_requests"`
	AvgResponseTime   float64                  `json:"avg_response_time"` // ms
	P50ResponseTime   float64                  `json:"p50_response_time"` // ms
	P90ResponseTime   float64                  `json:"p90_response_time"` // ms
	P99ResponseTime   float64                  `json:"p99_response_time"` // ms
	MaxResponseTime   float64                  `json:"max_response_time"` // ms
	MinResponseTime   float64                  `json:"min_response_time"` // ms
	RequestsPerSecond float64                  `json:"requests_per_second"`
	ErrorRate         float64                  `json:"error_rate"` // 错误率
	EndpointStats     map[string]*EndpointStat `json:"endpoint_stats"`
}

// EndpointStat 接口统计
type EndpointStat struct {
	Path         string  `json:"path"`
	Method       string  `json:"method"`
	RequestCount int64   `json:"request_count"`
	AvgTime      float64 `json:"avg_time"`
	MaxTime      float64 `json:"max_time"`
	MinTime      float64 `json:"min_time"`
	ErrorCount   int64   `json:"error_count"`
	ErrorRate    float64 `json:"error_rate"`
}

// DatabasePerformanceMetrics 数据库性能指标
type DatabasePerformanceMetrics struct {
	TotalQueries       int64                 `json:"total_queries"`
	SuccessQueries     int64                 `json:"success_queries"`
	ErrorQueries       int64                 `json:"error_queries"`
	AvgQueryTime       float64               `json:"avg_query_time"` // ms
	SlowQueries        int64                 `json:"slow_queries"`   // 慢查询数量
	ActiveConnections  int64                 `json:"active_connections"`
	IdleConnections    int64                 `json:"idle_connections"`
	MaxConnections     int64                 `json:"max_connections"`
	ConnectionPoolSize int64                 `json:"connection_pool_size"`
	QueryStats         map[string]*QueryStat `json:"query_stats"`
}

// QueryStat 查询统计
type QueryStat struct {
	Table     string  `json:"table"`
	Operation string  `json:"operation"`
	Count     int64   `json:"count"`
	AvgTime   float64 `json:"avg_time"`
	MaxTime   float64 `json:"max_time"`
	TotalTime float64 `json:"total_time"`
}

// CachePerformanceMetrics 缓存性能指标
type CachePerformanceMetrics struct {
	TotalOperations int64   `json:"total_operations"`
	GetOperations   int64   `json:"get_operations"`
	SetOperations   int64   `json:"set_operations"`
	DeleteOps       int64   `json:"delete_operations"`
	HitCount        int64   `json:"hit_count"`
	MissCount       int64   `json:"miss_count"`
	HitRate         float64 `json:"hit_rate"`
	AvgGetTime      float64 `json:"avg_get_time"` // ms
	AvgSetTime      float64 `json:"avg_set_time"` // ms
	MemoryUsage     int64   `json:"memory_usage"` // bytes
	KeyCount        int64   `json:"key_count"`
}

// SystemPerformanceMetrics 系统性能指标
type SystemPerformanceMetrics struct {
	CPUUsage        float64 `json:"cpu_usage"`         // CPU使用率 %
	MemoryUsage     float64 `json:"memory_usage"`      // 内存使用率 %
	DiskUsage       float64 `json:"disk_usage"`        // 磁盘使用率 %
	NetworkInBytes  int64   `json:"network_in_bytes"`  // 网络入流量
	NetworkOutBytes int64   `json:"network_out_bytes"` // 网络出流量
	GoroutineCount  int64   `json:"goroutine_count"`   // 协程数量
	GCPauses        float64 `json:"gc_pauses"`         // GC暂停时间 ms
	HeapSize        int64   `json:"heap_size"`         // 堆大小 bytes
	StackSize       int64   `json:"stack_size"`        // 栈大小 bytes
}

// BusinessPerformanceMetrics 业务性能指标
type BusinessPerformanceMetrics struct {
	TaskProcessingMetrics   *TaskProcessingMetrics   `json:"task_processing_metrics"`
	RefundProcessingMetrics *RefundProcessingMetrics `json:"refund_processing_metrics"`
	NotificationMetrics     *NotificationMetrics     `json:"notification_metrics"`
	UserActivityMetrics     *UserActivityMetrics     `json:"user_activity_metrics"`
}

// TaskProcessingMetrics 任务处理指标
type TaskProcessingMetrics struct {
	TotalTasks         int64   `json:"total_tasks"`
	CompletedTasks     int64   `json:"completed_tasks"`
	PendingTasks       int64   `json:"pending_tasks"`
	ExpiredTasks       int64   `json:"expired_tasks"`
	AvgProcessingTime  float64 `json:"avg_processing_time"`  // 分钟
	TaskCompletionRate float64 `json:"task_completion_rate"` // 完成率
	TaskTimeoutRate    float64 `json:"task_timeout_rate"`    // 超时率
}

// RefundProcessingMetrics 退款处理指标
type RefundProcessingMetrics struct {
	TotalRefunds      int64   `json:"total_refunds"`
	SuccessRefunds    int64   `json:"success_refunds"`
	FailedRefunds     int64   `json:"failed_refunds"`
	PendingRefunds    int64   `json:"pending_refunds"`
	TotalRefundAmount float64 `json:"total_refund_amount"`
	AvgRefundAmount   float64 `json:"avg_refund_amount"`
	AvgProcessingTime float64 `json:"avg_processing_time"` // 分钟
	RefundSuccessRate float64 `json:"refund_success_rate"`
}

// NotificationMetrics 通知指标
type NotificationMetrics struct {
	TotalNotifications      int64   `json:"total_notifications"`
	SMSNotifications        int64   `json:"sms_notifications"`
	WechatNotifications     int64   `json:"wechat_notifications"`
	InternalNotifications   int64   `json:"internal_notifications"`
	SuccessNotifications    int64   `json:"success_notifications"`
	FailedNotifications     int64   `json:"failed_notifications"`
	NotificationSuccessRate float64 `json:"notification_success_rate"`
	AvgDeliveryTime         float64 `json:"avg_delivery_time"` // 秒
}

// UserActivityMetrics 用户活动指标
type UserActivityMetrics struct {
	ActiveUsers        int64   `json:"active_users"`
	TotalSessions      int64   `json:"total_sessions"`
	AvgSessionDuration float64 `json:"avg_session_duration"` // 分钟
	ConcurrentUsers    int64   `json:"concurrent_users"`
	LoginCount         int64   `json:"login_count"`
	LogoutCount        int64   `json:"logout_count"`
}

// ============ 监控告警DTO ============

// AlertRule 告警规则
type AlertRule struct {
	ID             int64     `json:"id"`
	Name           string    `json:"name"`
	Description    string    `json:"description"`
	MetricType     string    `json:"metric_type"` // api, database, cache, system, business
	MetricName     string    `json:"metric_name"`
	Operator       string    `json:"operator"` // >, <, >=, <=, ==, !=
	Threshold      float64   `json:"threshold"`
	Duration       int64     `json:"duration"` // 持续时间(秒)
	Severity       string    `json:"severity"` // low, medium, high, critical
	Enabled        bool      `json:"enabled"`
	NotifyChannels []string  `json:"notify_channels"` // sms, email, wechat
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// AlertEvent 告警事件
type AlertEvent struct {
	ID           int64                  `json:"id"`
	RuleID       int64                  `json:"rule_id"`
	RuleName     string                 `json:"rule_name"`
	Status       string                 `json:"status"` // triggered, resolved
	Severity     string                 `json:"severity"`
	Message      string                 `json:"message"`
	MetricValue  float64                `json:"metric_value"`
	Threshold    float64                `json:"threshold"`
	TriggerTime  time.Time              `json:"trigger_time"`
	ResolveTime  *time.Time             `json:"resolve_time,omitempty"`
	Duration     int64                  `json:"duration"`      // 持续时间(秒)
	NotifyStatus string                 `json:"notify_status"` // pending, sent, failed
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
}

// ============ 请求响应DTO ============

// CacheKeyRequest 缓存键请求
type CacheKeyRequest struct {
	Key string `json:"key" binding:"required"`
}

// CacheSetRequest 缓存设置请求
type CacheSetRequest struct {
	Key   string      `json:"key" binding:"required"`
	Value interface{} `json:"value" binding:"required"`
	TTL   int64       `json:"ttl"` // 秒，0表示永不过期
}

// PerformanceStatsRequest 性能统计请求
type PerformanceStatsRequest struct {
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	MetricType  string    `json:"metric_type"` // api, database, cache, system, business, all
	Granularity string    `json:"granularity"` // minute, hour, day
}

// AlertRuleRequest 告警规则请求
type AlertRuleRequest struct {
	Name           string   `json:"name" binding:"required"`
	Description    string   `json:"description"`
	MetricType     string   `json:"metric_type" binding:"required"`
	MetricName     string   `json:"metric_name" binding:"required"`
	Operator       string   `json:"operator" binding:"required"`
	Threshold      float64  `json:"threshold" binding:"required"`
	Duration       int64    `json:"duration" binding:"required"`
	Severity       string   `json:"severity" binding:"required"`
	Enabled        bool     `json:"enabled"`
	NotifyChannels []string `json:"notify_channels"`
}

// AlertEventListRequest 告警事件列表请求
type AlertEventListRequest struct {
	Page      int       `json:"page" binding:"min=1"`
	PageSize  int       `json:"page_size" binding:"min=1,max=100"`
	RuleID    int64     `json:"rule_id"`
	Status    string    `json:"status"`   // triggered, resolved
	Severity  string    `json:"severity"` // low, medium, high, critical
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}
