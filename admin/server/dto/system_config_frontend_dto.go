package dto

import "time"

// FrontendSystemConfigItem 前端系统配置项 (字段名与前端页面匹配)
type FrontendSystemConfigItem struct {
	ID          uint      `json:"id"`          // 配置ID
	Key         string    `json:"key"`         // 配置键
	Name        string    `json:"name"`        // 配置名称
	Value       string    `json:"value"`       // 配置值
	Description string    `json:"description"` // 配置描述
	Status      int       `json:"status"`      // 状态（1：启用，0：禁用）
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}

// FrontendSystemConfigListResponse 前端系统配置列表响应
type FrontendSystemConfigListResponse struct {
	List  []*FrontendSystemConfigItem `json:"list"`  // 配置列表
	Total int64                       `json:"total"` // 总数
	Page  int                         `json:"page"`  // 当前页码
}

// ConvertToFrontendItem 将系统配置模型转换为前端DTO
func ConvertToFrontendItem(config *SystemConfigItem) *FrontendSystemConfigItem {
	return &FrontendSystemConfigItem{
		ID:          config.ID,
		Key:         config.Key,
		Name:        config.Key, // 使用配置键作为名称
		Value:       config.Value,
		Description: config.Description,
		Status:      config.Status,
		UpdatedAt:   config.UpdatedAt,
	}
}

// ConvertToFrontendListResponse 将系统配置列表响应转换为前端响应格式
func ConvertToFrontendListResponse(response *SystemConfigListResponse) *FrontendSystemConfigListResponse {
	items := make([]*FrontendSystemConfigItem, len(response.List))
	for i, item := range response.List {
		items[i] = ConvertToFrontendItem(item)
	}

	return &FrontendSystemConfigListResponse{
		List:  items,
		Total: response.Total,
		Page:  response.Page,
	}
}
