package dto

import "time"

// ============ 健康检查DTO ============

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status           string                      `json:"status"` // healthy, degraded, unhealthy
	Timestamp        time.Time                   `json:"timestamp"`
	Duration         int64                       `json:"duration"` // 检查耗时(ms)
	Services         map[string]*ServiceStatus   `json:"services"`
	SystemInfo       *SystemInfo                 `json:"system_info"`
	DatabaseStatus   *DatabaseStatus             `json:"database_status"`
	CacheStatus      *CacheStatus                `json:"cache_status"`
	ExternalServices map[string]*ExternalService `json:"external_services"`
}

// ServiceStatus 服务状态
type ServiceStatus struct {
	Status      string    `json:"status"` // up, down, unknown
	LastCheck   time.Time `json:"last_check"`
	CheckCount  int64     `json:"check_count"`
	SuccessRate float64   `json:"success_rate"`
	AvgLatency  float64   `json:"avg_latency"` // ms
	ErrorCount  int64     `json:"error_count"`
	Message     string    `json:"message"`
}

// SystemInfo 系统信息
type SystemInfo struct {
	GoVersion      string    `json:"go_version"`
	StartTime      time.Time `json:"start_time"`
	Uptime         int64     `json:"uptime"` // 秒
	GoroutineCount int64     `json:"goroutine_count"`
	MemStats       *MemStats `json:"mem_stats"`
}

// MemStats 内存统计
type MemStats struct {
	Alloc        uint64 `json:"alloc"`       // bytes
	TotalAlloc   uint64 `json:"total_alloc"` // bytes
	Sys          uint64 `json:"sys"`         // bytes
	NumGC        uint32 `json:"num_gc"`
	PauseTotalNs uint64 `json:"pause_total_ns"`
}

// DatabaseStatus 数据库状态
type DatabaseStatus struct {
	Status           string    `json:"status"`
	OpenConnections  int       `json:"open_connections"`
	IdleConnections  int       `json:"idle_connections"`
	InUseConnections int       `json:"in_use_connections"`
	MaxOpenConns     int       `json:"max_open_conns"`
	MaxIdleConns     int       `json:"max_idle_conns"`
	ConnMaxLifetime  string    `json:"conn_max_lifetime"`
	LastPingTime     time.Time `json:"last_ping_time"`
	LastPingDuration int64     `json:"last_ping_duration"` // ms
}

// CacheStatus 缓存状态
type CacheStatus struct {
	Status     string    `json:"status"`
	KeyCount   int64     `json:"key_count"`
	MemoryUsed int64     `json:"memory_used"` // bytes
	HitRate    float64   `json:"hit_rate"`
	LastAccess time.Time `json:"last_access"`
}

// ExternalService 外部服务状态
type ExternalService struct {
	Name         string    `json:"name"`
	URL          string    `json:"url"`
	Status       string    `json:"status"`
	LastCheck    time.Time `json:"last_check"`
	Latency      int64     `json:"latency"` // ms
	Timeout      int64     `json:"timeout"` // ms
	ErrorMessage string    `json:"error_message,omitempty"`
}

// ServiceStatusResponse 服务状态响应
type ServiceStatusResponse struct {
	OverallStatus string                    `json:"overall_status"`
	Services      map[string]*ServiceDetail `json:"services"`
	GeneratedAt   time.Time                 `json:"generated_at"`
}

// ServiceDetail 服务详情
type ServiceDetail struct {
	Name         string                 `json:"name"`
	Status       string                 `json:"status"`
	Version      string                 `json:"version"`
	Description  string                 `json:"description"`
	Dependencies []string               `json:"dependencies"`
	Metrics      map[string]interface{} `json:"metrics"`
	LastUpdated  time.Time              `json:"last_updated"`
}

// ============ 性能优化DTO ============

// QueryOptimizationSuggestion 查询优化建议
type QueryOptimizationSuggestion struct {
	Query          string    `json:"query"`
	Table          string    `json:"table"`
	ExecutionTime  float64   `json:"execution_time"` // ms
	Frequency      int64     `json:"frequency"`      // 执行频率
	Suggestion     string    `json:"suggestion"`
	Priority       string    `json:"priority"`       // high, medium, low
	EstimatedGain  float64   `json:"estimated_gain"` // 预期性能提升%
	Implementation string    `json:"implementation"` // 实施建议
	CreatedAt      time.Time `json:"created_at"`
}

// CacheOptimizationSuggestion 缓存优化建议
type CacheOptimizationSuggestion struct {
	CacheKey        string    `json:"cache_key"`
	HitRate         float64   `json:"hit_rate"`
	AccessFrequency int64     `json:"access_frequency"`
	DataSize        int64     `json:"data_size"`     // bytes
	TTL             int64     `json:"ttl"`           // 当前TTL(秒)
	SuggestedTTL    int64     `json:"suggested_ttl"` // 建议TTL(秒)
	Suggestion      string    `json:"suggestion"`
	Reason          string    `json:"reason"`
	CreatedAt       time.Time `json:"created_at"`
}

// APIOptimizationSuggestion API优化建议
type APIOptimizationSuggestion struct {
	Endpoint         string    `json:"endpoint"`
	Method           string    `json:"method"`
	AvgResponseTime  float64   `json:"avg_response_time"` // ms
	RequestCount     int64     `json:"request_count"`
	ErrorRate        float64   `json:"error_rate"`
	Suggestion       string    `json:"suggestion"`
	OptimizationType string    `json:"optimization_type"` // caching, query, logic, infrastructure
	Priority         string    `json:"priority"`
	CreatedAt        time.Time `json:"created_at"`
}

// OptimizationReport 优化报告
type OptimizationReport struct {
	GeneratedAt      time.Time                      `json:"generated_at"`
	ReportPeriod     string                         `json:"report_period"`
	OverallScore     float64                        `json:"overall_score"` // 0-100
	QuerySuggestions []*QueryOptimizationSuggestion `json:"query_suggestions"`
	CacheSuggestions []*CacheOptimizationSuggestion `json:"cache_suggestions"`
	APISuggestions   []*APIOptimizationSuggestion   `json:"api_suggestions"`
	SystemMetrics    *SystemPerformanceMetrics      `json:"system_metrics"`
	TrendAnalysis    *PerformanceTrendAnalysis      `json:"trend_analysis"`
}

// PerformanceTrendAnalysis 性能趋势分析
type PerformanceTrendAnalysis struct {
	Period         string                  `json:"period"`
	APITrends      map[string]*MetricTrend `json:"api_trends"`
	DatabaseTrends map[string]*MetricTrend `json:"database_trends"`
	CacheTrends    map[string]*MetricTrend `json:"cache_trends"`
	SystemTrends   map[string]*MetricTrend `json:"system_trends"`
	BusinessTrends map[string]*MetricTrend `json:"business_trends"`
}

// MetricTrend 指标趋势
type MetricTrend struct {
	MetricName     string  `json:"metric_name"`
	CurrentValue   float64 `json:"current_value"`
	PreviousValue  float64 `json:"previous_value"`
	ChangePercent  float64 `json:"change_percent"`
	Trend          string  `json:"trend"`    // improving, degrading, stable
	Severity       string  `json:"severity"` // low, medium, high
	Recommendation string  `json:"recommendation"`
}

// ============ 数据库优化DTO ============

// SlowQuery 慢查询记录
type SlowQuery struct {
	ID            int64     `json:"id"`
	Query         string    `json:"query"`
	QueryHash     string    `json:"query_hash"`
	ExecutionTime float64   `json:"execution_time"` // ms
	LockTime      float64   `json:"lock_time"`      // ms
	RowsExamined  int64     `json:"rows_examined"`
	RowsSent      int64     `json:"rows_sent"`
	Database      string    `json:"database"`
	User          string    `json:"user"`
	Host          string    `json:"host"`
	Timestamp     time.Time `json:"timestamp"`
}

// DatabaseOptimizationReport 数据库优化报告
type DatabaseOptimizationReport struct {
	GeneratedAt      time.Time             `json:"generated_at"`
	SlowQueries      []*SlowQuery          `json:"slow_queries"`
	IndexSuggestions []*IndexSuggestion    `json:"index_suggestions"`
	TableStats       []*TableStatistics    `json:"table_stats"`
	ConnectionStats  *ConnectionStatistics `json:"connection_stats"`
	QueryPatterns    []*QueryPattern       `json:"query_patterns"`
}

// IndexSuggestion 索引建议
type IndexSuggestion struct {
	Table         string   `json:"table"`
	Columns       []string `json:"columns"`
	IndexType     string   `json:"index_type"` // btree, hash, fulltext
	Reason        string   `json:"reason"`
	Impact        string   `json:"impact"` // high, medium, low
	CreateSQL     string   `json:"create_sql"`
	EstimatedGain float64  `json:"estimated_gain"`
}

// TableStatistics 表统计信息
type TableStatistics struct {
	TableName     string    `json:"table_name"`
	RowCount      int64     `json:"row_count"`
	DataSize      int64     `json:"data_size"`     // bytes
	IndexSize     int64     `json:"index_size"`    // bytes
	Fragmentation float64   `json:"fragmentation"` // %
	LastAnalyzed  time.Time `json:"last_analyzed"`
	Suggestions   []string  `json:"suggestions"`
}

// ConnectionStatistics 连接统计
type ConnectionStatistics struct {
	MaxConnections     int     `json:"max_connections"`
	CurrentConnections int     `json:"current_connections"`
	PeakConnections    int     `json:"peak_connections"`
	AvgConnectionTime  float64 `json:"avg_connection_time"` // ms
	ConnectionPoolSize int     `json:"connection_pool_size"`
	IdleConnections    int     `json:"idle_connections"`
}

// QueryPattern 查询模式
type QueryPattern struct {
	Pattern      string  `json:"pattern"`
	Count        int64   `json:"count"`
	AvgTime      float64 `json:"avg_time"`   // ms
	TotalTime    float64 `json:"total_time"` // ms
	Percentage   float64 `json:"percentage"` // 占总查询时间的百分比
	Optimization string  `json:"optimization"`
}

// ============ 告警历史DTO ============

// AlertHistory 告警历史
type AlertHistory struct {
	EventID     int64      `json:"event_id"`
	RuleID      int64      `json:"rule_id"`
	RuleName    string     `json:"rule_name"`
	AlertType   string     `json:"alert_type"`
	Severity    string     `json:"severity"`
	Status      string     `json:"status"`
	TriggerTime time.Time  `json:"trigger_time"`
	ResolveTime *time.Time `json:"resolve_time,omitempty"`
	Duration    int64      `json:"duration"` // 秒
	Message     string     `json:"message"`
	Actions     []string   `json:"actions"` // 采取的行动
}

// AlertStatistics 告警统计
type AlertStatistics struct {
	TotalAlerts    int64                  `json:"total_alerts"`
	ActiveAlerts   int64                  `json:"active_alerts"`
	ResolvedAlerts int64                  `json:"resolved_alerts"`
	CriticalAlerts int64                  `json:"critical_alerts"`
	HighAlerts     int64                  `json:"high_alerts"`
	MediumAlerts   int64                  `json:"medium_alerts"`
	LowAlerts      int64                  `json:"low_alerts"`
	AvgResolveTime float64                `json:"avg_resolve_time"` // 分钟
	AlertsByType   map[string]int64       `json:"alerts_by_type"`
	AlertTrends    map[string]*AlertTrend `json:"alert_trends"`
}

// AlertTrend 告警趋势
type AlertTrend struct {
	Date       string  `json:"date"`
	Count      int64   `json:"count"`
	Severity   string  `json:"severity"`
	Percentage float64 `json:"percentage"`
}

// ============ 请求响应DTO ============

// HealthCheckRequest 健康检查请求
type HealthCheckRequest struct {
	IncludeServices         bool `json:"include_services"`
	IncludeSystemInfo       bool `json:"include_system_info"`
	IncludeDatabaseStatus   bool `json:"include_database_status"`
	IncludeCacheStatus      bool `json:"include_cache_status"`
	IncludeExternalServices bool `json:"include_external_services"`
}

// OptimizationReportRequest 优化报告请求
type OptimizationReportRequest struct {
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	IncludeQuery bool      `json:"include_query"`
	IncludeCache bool      `json:"include_cache"`
	IncludeAPI   bool      `json:"include_api"`
	IncludeTrend bool      `json:"include_trend"`
}

// AlertStatisticsRequest 告警统计请求
type AlertStatisticsRequest struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Severity  string    `json:"severity"` // all, critical, high, medium, low
	Status    string    `json:"status"`   // all, active, resolved
	GroupBy   string    `json:"group_by"` // type, severity, date
}
