package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
)

// TimeManagementHandler 时间管理处理器
type TimeManagementHandler struct {
	timeService service.TimeManagementService
}

// NewTimeManagementHandler 创建时间管理处理器
func NewTimeManagementHandler(timeService service.TimeManagementService) *TimeManagementHandler {
	return &TimeManagementHandler{
		timeService: timeService,
	}
}

// StartService 开始服务
func (h *TimeManagementHandler) StartService(c *gin.Context) {
	var req dto.StartServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Failed(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	if err := h.timeService.StartService(c.Request.Context(), &req); err != nil {
		response.Failed(c, http.StatusInternalServerError, "开始服务失败", err)
		return
	}

	response.Success(c, "开始服务成功")
}

// EndService 结束服务
func (h *TimeManagementHandler) EndService(c *gin.Context) {
	var req dto.EndServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Failed(c, http.StatusBadRequest, "请求参数错误", err)
		return
	}

	if err := h.timeService.EndService(c.Request.Context(), &req); err != nil {
		response.Failed(c, http.StatusInternalServerError, "结束服务失败", err)
		return
	}

	response.Success(c, "结束服务成功")
}

// GetOrderTimeInfo 获取订单时间信息
func (h *TimeManagementHandler) GetOrderTimeInfo(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Failed(c, http.StatusBadRequest, "订单ID格式错误", err)
		return
	}

	timeInfo, err := h.timeService.GetOrderTimeInfo(c.Request.Context(), uint(orderID))
	if err != nil {
		response.Failed(c, http.StatusInternalServerError, "获取订单时间信息失败", err)
		return
	}

	response.Success(c, timeInfo)
}

// ValidateServiceTime 验证服务时间
func (h *TimeManagementHandler) ValidateServiceTime(c *gin.Context) {
	orderIDStr := c.Param("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Failed(c, http.StatusBadRequest, "订单ID格式错误", err)
		return
	}

	serviceTimeStr := c.Query("service_time")
	if serviceTimeStr == "" {
		response.Failed(c, http.StatusBadRequest, "服务时间不能为空", nil)
		return
	}

	serviceTime, err := time.Parse(time.RFC3339, serviceTimeStr)
	if err != nil {
		response.Failed(c, http.StatusBadRequest, "服务时间格式错误", err)
		return
	}

	result, err := h.timeService.ValidateServiceTime(c.Request.Context(), uint(orderID), serviceTime)
	if err != nil {
		response.Failed(c, http.StatusInternalServerError, "验证服务时间失败", err)
		return
	}

	response.Success(c, result)
}
