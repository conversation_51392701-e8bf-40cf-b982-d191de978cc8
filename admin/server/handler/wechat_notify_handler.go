package handler

import (
	"encoding/json"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/gemeijie/peizhen/admin/server/constants"
	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// WechatNotifyHandler 微信通知处理器
type WechatNotifyHandler struct {
	wechatRefundSvc service.IWechatRefundService
	refundRepo      repository.IRefundRepository
	logger          *zap.Logger
}

// NewWechatNotifyHandler 创建微信通知处理器
func NewWechatNotifyHandler(wechatRefundSvc service.IWechatRefundService, refundRepo repository.IRefundRepository, logger *zap.Logger) *WechatNotifyHandler {
	return &WechatNotifyHandler{
		wechatRefundSvc: wechatRefundSvc,
		refundRepo:      refundRepo,
		logger:          logger,
	}
}

// HandleRefundNotify 处理微信退款回调通知
func (h *WechatNotifyHandler) HandleRefundNotify(c *gin.Context) {
	// 1. 读取请求体
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.Error("读取微信退款回调请求体失败", zap.Error(err))
		response.Error(c, http.StatusBadRequest, "读取请求体失败")
		return
	}

	h.logger.Info("收到微信退款回调通知", zap.String("body", string(body)))

	// 2. 解析请求体为通知数据
	var notifyReq dto.WechatRefundNotifyRequest
	if err := json.Unmarshal(body, &notifyReq); err != nil {
		h.logger.Error("解析微信退款回调通知数据失败", zap.Error(err), zap.String("body", string(body)))
		response.Error(c, http.StatusBadRequest, "解析通知数据失败")
		return
	}

	// 3. 调用服务处理退款通知
	resp, err := h.wechatRefundSvc.HandleRefundNotify(c.Request.Context(), body)
	if err != nil {
		h.logger.Error("处理微信退款通知失败", zap.Error(err), zap.String("out_refund_no", notifyReq.Resource.Ciphertext))
		response.Error(c, http.StatusInternalServerError, "处理退款通知失败")
		return
	}

	// 4. 根据通知更新退款记录
	if resp != nil {
		refund, err := h.refundRepo.GetRefundByNo(c.Request.Context(), resp.OutRefundNo)
		if err != nil {
			h.logger.Error("查询退款记录失败", zap.Error(err), zap.String("out_refund_no", resp.OutRefundNo))
			response.Error(c, http.StatusInternalServerError, "查询退款记录失败")
			return
		}

		if refund != nil {
			h.logger.Info("开始更新退款记录",
				zap.String("out_refund_no", resp.OutRefundNo),
				zap.String("old_status", refund.Status),
				zap.String("new_status", resp.Status),
			)

			// 映射微信状态到统一状态
			newStatus := h.mapWechatRefundStatus(resp.Status)

			// 更新退款记录状态和相关字段
			refund.Status = newStatus
			refund.WechatRefundNo = resp.WechatRefundNo
			now := time.Now()
			refund.WechatNotifyAt = &now

			// 如果退款成功且成功时间存在，更新处理时间
			if newStatus == constants.RefundStatusCompleted && resp.SuccessTime != nil {
				refund.ProcessedAt = resp.SuccessTime
				refund.RefundTime = resp.SuccessTime
			}

			// 更新重试计数器为0（成功接收到回调）
			refund.RetryCount = 0
			refund.NextRetryAt = nil

			// 设置微信退款ID
			if resp.WechatRefundNo != "" {
				refund.WechatRefundID = resp.WechatRefundNo
			}

			// 更新审批状态（如果退款成功，自动设为已批准）
			if newStatus == constants.RefundStatusCompleted && refund.ApprovalStatus == constants.ApprovalStatusPending {
				refund.ApprovalStatus = constants.ApprovalStatusApproved
				refund.ApprovalTime = &now
				refund.ApprovalRemark = "微信回调自动审批通过"
			}

			// 更新数据库
			if err := h.refundRepo.Update(c.Request.Context(), refund); err != nil {
				h.logger.Error("更新退款记录失败", zap.Error(err), zap.String("out_refund_no", resp.OutRefundNo))
				response.Error(c, http.StatusInternalServerError, "更新退款记录失败")
				return
			}

			h.logger.Info("退款记录更新成功",
				zap.String("out_refund_no", resp.OutRefundNo),
				zap.String("final_status", newStatus),
				zap.String("wechat_refund_no", resp.WechatRefundNo),
			)
		} else {
			h.logger.Warn("未找到对应的退款记录", zap.String("out_refund_no", resp.OutRefundNo))
		}
	}

	// 5. 返回成功响应（必须按照微信的格式）
	c.JSON(200, dto.WechatNotifyResponse{
		Code:    "SUCCESS",
		Message: "成功",
	})
}

// mapWechatRefundStatus 映射微信退款状态到统一状态
func (h *WechatNotifyHandler) mapWechatRefundStatus(wechatStatus string) string {
	switch wechatStatus {
	case "SUCCESS":
		return constants.RefundStatusCompleted
	case "PROCESSING":
		return constants.RefundStatusProcessing
	case "ABNORMAL":
		return constants.RefundStatusFailed
	case "CLOSED":
		return constants.RefundStatusFailed
	default:
		h.logger.Warn("未知的微信退款状态", zap.String("status", wechatStatus))
		return constants.RefundStatusPending
	}
}
