package handler

import (
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/model"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
	"github.com/gin-gonic/gin"
)

// UserHandler 用户管理处理器
type UserHandler struct {
	userService service.IUserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.IUserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// ListUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取用户列表，支持筛选和分页
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认10"
// @Param status query int false "状态筛选：1正常 2禁用"
// @Param role query string false "角色筛选：user/attendant/admin"
// @Param keyword query string false "关键词搜索（昵称、手机号）"
// @Param gender query int false "性别筛选：0未知 1男 2女"
// @Param city query string false "城市筛选"
// @Success 200 {object} response.Response{data=dto.UserListResponse}
// @Router /api/admin/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	var req dto.UserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建筛选条件
	filters := make(map[string]interface{})
	if req.Status != nil {
		filters["status"] = *req.Status
	}
	if req.Role != "" {
		filters["role"] = req.Role
	}
	if req.Keyword != "" {
		filters["keyword"] = req.Keyword
	}
	if req.Gender != nil {
		filters["gender"] = *req.Gender
	}
	if req.City != "" {
		filters["city"] = req.City
	}

	// 获取用户列表
	users, total, err := h.userService.ListUsers(req.Page, req.PageSize, filters)
	if err != nil {
		response.ServerError(c, "获取用户列表失败", err)
		return
	}

	// 转换为响应格式
	userResponses := make([]dto.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = h.convertToUserResponse(user)
	}

	result := dto.UserListResponse{
		List:  userResponses,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}

	response.Success(c, result)
}

// GetUser 获取用户详情
// @Summary 获取用户详情
// @Description 根据ID获取用户详情
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response{data=dto.UserResponse}
// @Router /api/admin/users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	user, err := h.userService.GetUserByID(uint(id))
	if err != nil {
		response.ServerError(c, "获取用户详情失败", err)
		return
	}

	result := h.convertToUserResponse(*user)
	response.Success(c, result)
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body dto.UserCreateRequest true "创建用户请求"
// @Success 200 {object} response.Response{data=dto.UserResponse}
// @Router /api/admin/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req dto.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	user := &model.User{
		Phone:     req.Phone,
		Password:  req.Password,
		Nickname:  req.Nickname,
		AvatarURL: req.AvatarURL,
		Gender:    req.Gender,
		Country:   req.Country,
		Province:  req.Province,
		City:      req.City,
		Language:  req.Language,
		Status:    1, // 默认为正常状态
		Role:      req.Role,
	}

	err := h.userService.CreateUser(user)
	if err != nil {
		response.ServerError(c, "创建用户失败", err)
		return
	}

	result := h.convertToUserResponse(*user)
	response.Success(c, result)
}

// UpdateUser 更新用户
// @Summary 更新用户
// @Description 更新用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body dto.UserUpdateRequest true "更新用户请求"
// @Success 200 {object} response.Response{data=dto.UserResponse}
// @Router /api/admin/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req dto.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	req.ID = uint(id)

	// 获取原用户信息
	existingUser, err := h.userService.GetUserByID(uint(id))
	if err != nil {
		response.ServerError(c, "用户不存在", err)
		return
	}

	// 更新字段
	if req.Phone != "" {
		existingUser.Phone = req.Phone
	}
	if req.Nickname != "" {
		existingUser.Nickname = req.Nickname
	}
	if req.AvatarURL != "" {
		existingUser.AvatarURL = req.AvatarURL
	}
	existingUser.Gender = req.Gender
	if req.Country != "" {
		existingUser.Country = req.Country
	}
	if req.Province != "" {
		existingUser.Province = req.Province
	}
	if req.City != "" {
		existingUser.City = req.City
	}
	if req.Language != "" {
		existingUser.Language = req.Language
	}
	if req.Status > 0 {
		existingUser.Status = req.Status
	}
	if req.Role != "" {
		existingUser.Role = req.Role
	}

	err = h.userService.UpdateUser(existingUser)
	if err != nil {
		response.ServerError(c, "更新用户失败", err)
		return
	}

	result := h.convertToUserResponse(*existingUser)
	response.Success(c, result)
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除用户（软删除）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response
// @Router /api/admin/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.userService.DeleteUser(uint(id))
	if err != nil {
		response.ServerError(c, "删除用户失败", err)
		return
	}

	response.Success(c, nil)
}

// ToggleUserStatus 切换用户状态
// @Summary 切换用户状态
// @Description 切换用户状态（启用/禁用）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response
// @Router /api/admin/users/{id}/toggle-status [post]
func (h *UserHandler) ToggleUserStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.userService.ToggleUserStatus(uint(id))
	if err != nil {
		response.ServerError(c, "切换用户状态失败", err)
		return
	}

	response.Success(c, nil)
}

// ResetPassword 重置用户密码
// @Summary 重置用户密码
// @Description 重置用户密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body dto.UserResetPasswordRequest true "重置密码请求"
// @Success 200 {object} response.Response
// @Router /api/admin/users/{id}/reset-password [post]
func (h *UserHandler) ResetPassword(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req dto.UserResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	err = h.userService.ResetUserPassword(uint(id), req.NewPassword)
	if err != nil {
		response.ServerError(c, "重置密码失败", err)
		return
	}

	response.Success(c, nil)
}

// GetUserStats 获取用户统计
// @Summary 获取用户统计
// @Description 获取用户统计信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.UserStatsResponse}
// @Router /api/admin/users/stats [get]
func (h *UserHandler) GetUserStats(c *gin.Context) {
	// 这里可以实现用户统计逻辑
	// 暂时返回模拟数据
	stats := dto.UserStatsResponse{
		TotalUsers:     1000,
		ActiveUsers:    800,
		DisabledUsers:  200,
		AttendantUsers: 50,
		TodayNewUsers:  10,
		WeekNewUsers:   70,
		MonthNewUsers:  300,
	}

	response.Success(c, stats)
}

// convertToUserResponse 转换用户模型为响应格式
func (h *UserHandler) convertToUserResponse(user model.User) dto.UserResponse {
	// 性别文本转换
	genderText := "未知"
	switch user.Gender {
	case 1:
		genderText = "男"
	case 2:
		genderText = "女"
	}

	// 状态文本转换
	statusText := "未知"
	switch user.Status {
	case 1:
		statusText = "正常"
	case 2:
		statusText = "禁用"
	}

	// 角色文本转换
	roleText := "用户"
	switch user.Role {
	case "attendant":
		roleText = "陪诊师"
	case "admin":
		roleText = "管理员"
	}

	return dto.UserResponse{
		ID:         user.ID,
		OpenID:     user.OpenID,
		UnionID:    user.UnionID,
		Phone:      user.Phone,
		Nickname:   user.Nickname,
		Avatar:     user.AvatarURL, // 前端期望的字段名
		AvatarURL:  user.AvatarURL, // 保持向后兼容
		Gender:     user.Gender,
		GenderText: genderText,
		Country:    user.Country,
		Province:   user.Province,
		City:       user.City,
		Language:   user.Language,
		Status:     user.Status,
		StatusText: statusText,
		Role:       user.Role,
		RoleText:   roleText,
		CreatedAt:  user.CreatedAt,
		UpdatedAt:  user.UpdatedAt,
	}
}
