package handler

import (
	"net/http"
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SystemConfigHandler 系统配置处理器
type SystemConfigHandler struct {
	logger        *zap.Logger
	configService service.ISystemConfigService
}

// NewSystemConfigHandler 创建系统配置处理器
func NewSystemConfigHandler(logger *zap.Logger, configService service.ISystemConfigService) *SystemConfigHandler {
	return &SystemConfigHandler{
		logger:        logger,
		configService: configService,
	}
}

// GetConfigList 获取配置列表
// @Summary 获取系统配置列表
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param category query string false "配置分类"
// @Param status query int false "状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs [get]
func (h *SystemConfigHandler) GetConfigList(c *gin.Context) {
	var req dto.SystemConfigListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("获取系统配置列表",
		zap.String("category", req.Category),
		zap.Any("status", req.Status),
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize))

	// 调用配置服务获取配置列表
	response, err := h.configService.GetConfigList(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("获取配置列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取配置列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取配置列表成功",
		"data":    response,
	})
}

// GetConfigDetail 获取配置详情
// @Summary 获取系统配置详情
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param id path int true "配置ID"
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs/{id} [get]
func (h *SystemConfigHandler) GetConfigDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "配置ID格式错误",
		})
		return
	}

	h.logger.Info("获取配置详情", zap.Int("config_id", id))

	// 调用配置服务获取详情
	response, err := h.configService.GetConfigDetail(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("获取配置详情失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取配置详情失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取配置详情成功",
		"data":    response,
	})
}

// UpdateConfig 更新配置
// @Summary 更新系统配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param id path int true "配置ID"
// @Param request body dto.UpdateSystemConfigRequest true "更新参数"
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs/{id} [put]
func (h *SystemConfigHandler) UpdateConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "配置ID格式错误",
		})
		return
	}

	var req dto.UpdateSystemConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("更新系统配置",
		zap.Int("config_id", id),
		zap.Any("request", req))

	// 调用配置服务更新配置
	response, err := h.configService.UpdateConfig(c.Request.Context(), uint(id), &req)
	if err != nil {
		h.logger.Error("更新配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "更新配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "配置更新成功",
		"data":    response,
	})
}

// BatchUpdateConfigs 批量更新配置
// @Summary 批量更新系统配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateSystemConfigRequest true "批量更新参数"
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs/batch [put]
func (h *SystemConfigHandler) BatchUpdateConfigs(c *gin.Context) {
	var req dto.BatchUpdateSystemConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("批量更新系统配置",
		zap.Any("configs", req.Configs),
		zap.String("reason", req.Reason))

	// 调用配置服务批量更新
	response, err := h.configService.BatchUpdateConfigs(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("批量更新配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "批量更新配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "批量更新配置成功",
		"data":    response,
	})
}

// 注意：GetConfigHistory 和 GetConfigCategories 方法已被移除
// 因为系统配置已简化，不再支持历史记录和分类功能

// CreateConfig 创建配置
// @Summary 创建系统配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param request body dto.CreateSystemConfigRequest true "创建参数"
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs [post]
func (h *SystemConfigHandler) CreateConfig(c *gin.Context) {
	var req dto.CreateSystemConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("创建系统配置", zap.String("config_key", req.Key))

	// 调用配置服务创建配置
	response, err := h.configService.CreateConfig(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("创建配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "创建配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "配置创建成功",
		"data":    response,
	})
}

// DeleteConfig 删除配置
// @Summary 删除系统配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param id path int true "配置ID"
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs/{id} [delete]
func (h *SystemConfigHandler) DeleteConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "配置ID格式错误",
		})
		return
	}

	h.logger.Info("删除系统配置", zap.Int("config_id", id))

	// 调用配置服务删除配置
	err = h.configService.DeleteConfig(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("删除配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "删除配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "配置删除成功",
	})
}

// TestConfig 测试配置
// @Summary 测试系统配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param request body dto.TestSystemConfigRequest true "测试参数"
// @Success 200 {object} map[string]interface{}
// @Router /admin/system/configs/test [post]
func (h *SystemConfigHandler) TestConfig(c *gin.Context) {
	var req dto.TestSystemConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("测试系统配置", zap.String("config_key", req.Key))

	// 调用配置服务测试配置
	response, err := h.configService.TestConfig(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("测试配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "测试配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "配置测试完成",
		"data":    response,
	})
}
