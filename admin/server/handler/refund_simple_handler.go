package handler

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/gemeijie/peizhen/admin/server/model"
)

type RefundSimpleHandler struct {
	db *gorm.DB
}

// NewRefundSimpleHandler 创建简化版退款处理器
func NewRefundSimpleHandler(db *gorm.DB) *RefundSimpleHandler {
	return &RefundSimpleHandler{
		db: db,
	}
}

// GetRefundStatistics 获取退款统计
func (h *RefundSimpleHandler) GetRefundStatistics(c *gin.Context) {
	var stats model.RefundStatistics

	// 统计各状态的退款数量
	h.db.Model(&model.Refund{}).Where("status = ?", "pending").Count(&stats.PendingCount)
	h.db.Model(&model.Refund{}).Where("status = ?", "processing").Count(&stats.ProcessingCount)
	h.db.Model(&model.Refund{}).Where("status = ?", "completed").Count(&stats.CompletedCount)
	h.db.Model(&model.Refund{}).Where("status = ?", "failed").Count(&stats.FailedCount)

	// 统计今日退款金额
	today := time.Now().Format("2006-01-02")
	h.db.Model(&model.Refund{}).
		Where("status = ? AND DATE(created_at) = ?", "completed", today).
		Select("COALESCE(SUM(amount), 0)").Scan(&stats.TodayAmount)

	// 统计总退款金额
	h.db.Model(&model.Refund{}).
		Where("status = ?", "completed").
		Select("COALESCE(SUM(amount), 0)").Scan(&stats.TotalAmount)

	c.JSON(200, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    stats,
	})
}

// GetRefundList 处理退款列表
func (h *RefundSimpleHandler) GetRefundList(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	orderNo := c.Query("order_no")

	// 构建查询
	query := h.db.Model(&model.Refund{}).Preload("Order").Preload("User").Preload("Operator")

	// 应用过滤条件
	if status != "" {
		query = query.Where("status = ?", status)
	}

	if orderNo != "" {
		// 通过关联的订单号搜索
		query = query.Joins("JOIN orders ON refunds.order_id = orders.id").
			Where("orders.order_no LIKE ?", "%"+orderNo+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "查询总数失败",
			"error":   err.Error(),
		})
		return
	}

	// 分页查询
	var refunds []model.Refund
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Order("id DESC").Find(&refunds).Error; err != nil {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "查询退款列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"list":  refunds,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// GetRefundDetail 处理退款详情
func (h *RefundSimpleHandler) GetRefundDetail(c *gin.Context) {
	id := c.Param("id")
	refundID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "无效的退款ID",
		})
		return
	}

	var refund model.Refund
	if err := h.db.Preload("Order").Preload("User").Preload("Operator").
		First(&refund, uint(refundID)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{
				"code":    404,
				"message": "退款记录不存在",
			})
			return
		}
		c.JSON(500, gin.H{
			"code":    500,
			"message": "查询退款失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    refund,
	})
}

// ExecuteRefund 处理执行退款
func (h *RefundSimpleHandler) ExecuteRefund(c *gin.Context) {
	id := c.Param("id")
	refundID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "无效的退款ID",
		})
		return
	}

	var refund model.Refund
	if err := h.db.First(&refund, uint(refundID)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{
				"code":    404,
				"message": "退款记录不存在",
			})
			return
		}
		c.JSON(500, gin.H{
			"code":    500,
			"message": "查询退款失败",
			"error":   err.Error(),
		})
		return
	}

	if refund.Status != "pending" && refund.Status != "failed" {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "该退款不可执行",
		})
		return
	}

	// 更新退款状态为已完成
	now := time.Now()
	updates := map[string]interface{}{
		"status":       "completed",
		"processed_at": &now,
		"updated_at":   now,
	}

	if err := h.db.Model(&refund).Updates(updates).Error; err != nil {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "执行退款失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"code":    200,
		"message": "退款执行成功",
	})
}

// CancelRefund 处理取消退款
func (h *RefundSimpleHandler) CancelRefund(c *gin.Context) {
	id := c.Param("id")
	refundID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "无效的退款ID",
		})
		return
	}

	var req struct {
		Reason string `json:"reason" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	var refund model.Refund
	if err := h.db.First(&refund, uint(refundID)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(404, gin.H{
				"code":    404,
				"message": "退款记录不存在",
			})
			return
		}
		c.JSON(500, gin.H{
			"code":    500,
			"message": "查询退款失败",
			"error":   err.Error(),
		})
		return
	}

	if refund.Status == "completed" {
		c.JSON(400, gin.H{
			"code":    400,
			"message": "已完成的退款无法取消",
		})
		return
	}

	// 更新退款状态为失败
	updates := map[string]interface{}{
		"status":     "failed",
		"remark":     "取消原因: " + req.Reason,
		"updated_at": time.Now(),
	}

	if err := h.db.Model(&refund).Updates(updates).Error; err != nil {
		c.JSON(500, gin.H{
			"code":    500,
			"message": "取消退款失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"code":    200,
		"message": "退款取消成功",
	})
}
