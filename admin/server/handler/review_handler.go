package handler

import (
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/gemeijie/peizhen/admin/server/config"
	"github.com/gemeijie/peizhen/admin/server/model"
	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
)

// ReviewHandler 审核处理器
type ReviewHandler struct {
	db        *gorm.DB
	logger    *zap.Logger
	orderRepo repository.IOrderRepository
	config    *config.Config
}

// NewReviewHandler 创建审核处理器
func NewReviewHandler(db *gorm.DB, logger *zap.Logger, orderRepo repository.IOrderRepository, cfg *config.Config) *ReviewHandler {
	return &ReviewHandler{
		db:        db,
		logger:    logger,
		orderRepo: orderRepo,
		config:    cfg,
	}
}

// GetPendingReviews 获取待审核订单列表
func (h *ReviewHandler) GetPendingReviews(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	completionType := c.Query("completion_type")
	dateStart := c.Query("date_start")
	dateEnd := c.Query("date_end")

	// 构建查询条件
	query := h.db.Model(&model.Order{}).Where("status = ?", 8) // 待审核状态

	// 添加完成类型筛选
	if completionType != "" {
		// 这里需要根据实际的数据模型调整
		// 假设订单表中有completion_type字段
		query = query.Where("completion_type = ?", completionType)
	}

	// 添加日期范围筛选
	if dateStart != "" {
		query = query.Where("created_at >= ?", dateStart)
	}
	if dateEnd != "" {
		query = query.Where("created_at <= ?", dateEnd+" 23:59:59")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		h.logger.Error("获取待审核订单总数失败", zap.Error(err))
		response.ServerError(c, "获取数据失败", err)
		return
	}

	// 分页查询
	var orders []model.Order
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").Offset(offset).Limit(limit).Find(&orders).Error; err != nil {
		h.logger.Error("获取待审核订单列表失败", zap.Error(err))
		response.ServerError(c, "获取数据失败", err)
		return
	}

	// 转换为审核数据格式
	reviews := make([]map[string]interface{}, 0, len(orders))
	for _, order := range orders {
		// 计算等待时间
		waitingHours := int(time.Since(order.CreatedAt).Hours())

		// 获取完成类型 - 从审核记录表中获取
		var completionType int = 0
		var reviewRecord struct {
			CompletionType int `json:"completion_type"`
		}
		if err := h.db.Table("order_review_records").
			Select("completion_type").
			Where("order_id = ?", order.ID).
			First(&reviewRecord).Error; err == nil {
			completionType = reviewRecord.CompletionType
		}
		completionTypeText := getCompletionTypeText(completionType)

		// 获取服务记录
		var serviceRecord model.ServiceRecord
		serviceNotes := "暂无服务总结"
		if err := h.db.Where("order_id = ?", order.ID).First(&serviceRecord).Error; err == nil {
			if serviceRecord.ServiceNotes != "" {
				serviceNotes = serviceRecord.ServiceNotes
			}
		}

		review := map[string]interface{}{
			"order_id":             order.ID,
			"order_no":             order.OrderNo,
			"completion_type":      completionType,
			"completion_type_text": completionTypeText,
			"created_at":           order.CreatedAt.Format("2006-01-02 15:04:05"),
			"waiting_hours":        waitingHours,
			"patient_id":           order.PatientID,
			"attendant_id":         order.AttendantID,
			"service_fee":          order.ServiceFee,
			"amount":               order.Amount,
			"service_summary":      serviceNotes,
		}
		reviews = append(reviews, review)
	}

	// 获取统计数据
	stats := h.getReviewStats()

	response.Success(c, gin.H{
		"reviews": reviews,
		"total":   total,
		"page":    page,
		"limit":   limit,
		"stats":   stats,
	})
}

// ReviewOrder 审核订单
func (h *ReviewHandler) ReviewOrder(c *gin.Context) {
	// 获取订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的订单ID")
		return
	}

	// 获取请求参数
	var req struct {
		ReviewResult int    `json:"review_result" binding:"required"` // 1: 通过, 2: 拒绝
		ReviewNotes  string `json:"review_notes"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 验证审核结果
	if req.ReviewResult != 1 && req.ReviewResult != 2 {
		response.BadRequest(c, "无效的审核结果")
		return
	}

	// 获取管理员ID
	adminID, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录或会话已过期")
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取订单信息
	var order model.Order
	if err := tx.First(&order, uint(orderID)).Error; err != nil {
		tx.Rollback()
		response.NotFound(c, "订单不存在")
		return
	}

	// 检查订单状态
	if order.Status != 8 { // 待审核状态
		tx.Rollback()
		response.BadRequest(c, "订单状态不允许审核")
		return
	}

	// 更新订单状态
	newStatus := 10 // 已完成
	if req.ReviewResult == 2 {
		newStatus = 3 // 拒绝后回到服务中状态
	}

	if err := tx.Model(&order).Updates(map[string]interface{}{
		"status":     newStatus,
		"updated_at": time.Now(),
	}).Error; err != nil {
		tx.Rollback()
		h.logger.Error("更新订单状态失败", zap.Error(err))
		response.ServerError(c, "审核失败", err)
		return
	}

	// 记录审核日志（如果有审核日志表的话）
	// 这里可以添加审核记录的创建逻辑

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		h.logger.Error("提交审核事务失败", zap.Error(err))
		response.ServerError(c, "审核失败", err)
		return
	}

	h.logger.Info("订单审核成功",
		zap.Uint("order_id", uint(orderID)),
		zap.Int("review_result", req.ReviewResult),
		zap.Any("admin_id", adminID),
	)

	response.Success(c, gin.H{
		"message": "审核成功",
	})
}

// GetReviewDetail 获取审核详情
func (h *ReviewHandler) GetReviewDetail(c *gin.Context) {
	// 获取订单ID
	orderIDStr := c.Param("orderId")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的订单ID")
		return
	}

	// 获取订单详情
	var order model.Order
	if err := h.db.First(&order, uint(orderID)).Error; err != nil {
		response.NotFound(c, "订单不存在")
		return
	}

	// 获取服务记录
	var serviceRecord model.ServiceRecord
	serviceNotes := "暂无服务总结"
	servicePhotos := []string{}
	if err := h.db.Where("order_id = ?", orderID).First(&serviceRecord).Error; err == nil {
		if serviceRecord.ServiceNotes != "" {
			serviceNotes = serviceRecord.ServiceNotes
		}
		rawPhotos := serviceRecord.GetServicePhotos()
		// 处理图片URL，确保包含完整路径
		for _, photo := range rawPhotos {
			if photo != "" {
				// 如果是相对路径，添加域名前缀
				if !strings.HasPrefix(photo, "http") {
					fullURL := strings.TrimRight(h.config.URLs.UploadDomain, "/") + "/" + strings.TrimLeft(photo, "/")
					servicePhotos = append(servicePhotos, fullURL)
				} else {
					servicePhotos = append(servicePhotos, photo)
				}
			}
		}
	}

	// 获取完成类型 - 从审核记录表中获取
	var completionType int = 0
	var reviewRecord struct {
		CompletionType int `json:"completion_type"`
	}
	if err := h.db.Table("order_review_records").
		Select("completion_type").
		Where("order_id = ?", orderID).
		First(&reviewRecord).Error; err == nil {
		completionType = reviewRecord.CompletionType
	}

	// 构建详情数据
	detail := map[string]interface{}{
		"order_id":             order.ID,
		"order_no":             order.OrderNo,
		"completion_type":      completionType,
		"completion_type_text": getCompletionTypeText(completionType),
		"status":               order.Status,
		"created_at":           order.CreatedAt.Format("2006-01-02 15:04:05"),
		"patient_id":           order.PatientID,
		"attendant_id":         order.AttendantID,
		"service_fee":          order.ServiceFee,
		"amount":               order.Amount,
		"appointment_time":     order.AppointmentTime.Format("2006-01-02 15:04:05"),
		"service_hours":        order.ServiceHours,
		"service_summary":      serviceNotes,
		"service_photos":       servicePhotos,
	}

	response.Success(c, detail)
}

// GetReviewStats 获取审核统计信息
func (h *ReviewHandler) GetReviewStats(c *gin.Context) {
	stats := h.getReviewStats()
	response.Success(c, stats)
}

// getReviewStats 获取审核统计数据
func (h *ReviewHandler) getReviewStats() map[string]interface{} {
	var pendingCount, approvedToday, rejectedToday int64

	// 待审核数量
	h.db.Model(&model.Order{}).Where("status = ?", 8).Count(&pendingCount)

	// 今日审核通过数量
	today := time.Now().Format("2006-01-02")
	h.db.Model(&model.Order{}).
		Where("status = ? AND DATE(updated_at) = ?", 10, today).
		Count(&approvedToday)

	// 今日审核拒绝数量（这里需要根据实际业务逻辑调整）
	// 假设有审核记录表记录拒绝的情况
	rejectedToday = 0 // 暂时设为0，需要根据实际数据模型调整

	return map[string]interface{}{
		"pending_count":  pendingCount,
		"approved_today": approvedToday,
		"rejected_today": rejectedToday,
	}
}

// getCompletionTypeText 获取完成类型文本
func getCompletionTypeText(completionType int) string {
	switch completionType {
	case 1:
		return "陪诊师完成"
	case 2:
		return "管理员强制完成"
	case 3:
		return "异常处理完成"
	case 4:
		return "系统自动完成"
	default:
		return "未知类型"
	}
}
