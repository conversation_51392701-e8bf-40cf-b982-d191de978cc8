package handler

import (
	"fmt"
	"strconv"
	"time"

	"github.com/gemeijie/peizhen/admin/server/client"
	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// WithdrawalHandler 提现管理处理器
type WithdrawalHandler struct {
	withdrawalService service.IWithdrawalService
	backendClient     *client.BackendAPIClient
	logger            *zap.SugaredLogger
}

// NewWithdrawalHandler 创建提现管理处理器
func NewWithdrawalHandler(withdrawalService service.IWithdrawalService, backendClient *client.BackendAPIClient, logger *zap.SugaredLogger) *WithdrawalHandler {
	return &WithdrawalHandler{
		withdrawalService: withdrawalService,
		backendClient:     backendClient,
		logger:            logger,
	}
}

// GetWithdrawalList 获取提现列表
// @Summary 获取提现列表
// @Description 获取提现申请列表，支持筛选和分页
// @Tags 提现管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Param status query int false "状态筛选：1待审核 2审核通过 3已打款 4已驳回"
// @Param user_id query int false "用户ID筛选"
// @Param method query int false "提现方式筛选：1微信 2支付宝 3银行卡"
// @Param start_date query string false "开始日期(格式: 2024-12-31)"
// @Param end_date query string false "结束日期(格式: 2024-12-31)"
// @Success 200 {object} response.Response{data=dto.WithdrawalListResponse}
// @Router /api/admin/withdrawals [get]
func (h *WithdrawalHandler) GetWithdrawalList(c *gin.Context) {
	var req dto.WithdrawalListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 处理空字符串参数 - 如果字符串为空，则将对应的指针字段设为nil
	statusStr := c.Query("status")
	if statusStr == "" {
		req.Status = nil
	}

	methodStr := c.Query("method")
	if methodStr == "" {
		req.Method = nil
	}

	userIDStr := c.Query("user_id")
	if userIDStr == "" {
		req.UserID = nil
	}

	// 处理空的日期字符串
	if req.StartDate == "" {
		req.StartDate = ""
	}
	if req.EndDate == "" {
		req.EndDate = ""
	}

	result, err := h.withdrawalService.GetWithdrawalList(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, "获取提现列表失败", err)
		return
	}

	response.Success(c, result)
}

// GetWithdrawal 获取提现详情
// @Summary 获取提现详情
// @Description 根据ID获取提现申请详情
// @Tags 提现管理
// @Accept json
// @Produce json
// @Param id path int true "提现ID"
// @Success 200 {object} response.Response{data=dto.WithdrawalDetailResponse}
// @Router /api/admin/withdrawals/{id} [get]
func (h *WithdrawalHandler) GetWithdrawal(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的提现ID")
		return
	}

	result, err := h.withdrawalService.GetWithdrawal(c.Request.Context(), uint(id))
	if err != nil {
		response.ServerError(c, "获取提现详情失败", err)
		return
	}

	response.Success(c, result)
}

// GetWithdrawalStats 获取提现统计
// @Summary 获取提现统计
// @Description 获取提现申请的统计数据
// @Tags 提现管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.WithdrawalStatsResponse}
// @Router /api/admin/withdrawals/stats [get]
func (h *WithdrawalHandler) GetWithdrawalStats(c *gin.Context) {
	result, err := h.withdrawalService.GetWithdrawalStats(c.Request.Context())
	if err != nil {
		response.ServerError(c, "获取提现统计失败", err)
		return
	}

	response.Success(c, result)
}

// GetTodayStats 获取今日提现统计
// @Summary 获取今日提现统计
// @Description 获取今日的提现申请统计数据
// @Tags 提现管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.WithdrawalStatsResponse}
// @Router /api/admin/withdrawals/stats/today [get]
func (h *WithdrawalHandler) GetTodayStats(c *gin.Context) {
	result, err := h.withdrawalService.GetTodayStats(c.Request.Context())
	if err != nil {
		response.ServerError(c, "获取今日提现统计失败", err)
		return
	}

	response.Success(c, result)
}

// ApproveWithdrawal 审批提现
// @Summary 审批提现
// @Description 审批通过或拒绝提现申请
// @Tags 提现管理
// @Accept json
// @Produce json
// @Param id path int true "提现ID"
// @Param request body dto.WithdrawalApprovalRequest true "审批请求"
// @Success 200 {object} response.Response{data=dto.WithdrawalApprovalResponse}
// @Router /api/admin/withdrawals/{id}/approve [post]
func (h *WithdrawalHandler) ApproveWithdrawal(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的提现ID")
		return
	}

	var req dto.WithdrawalApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	result, err := h.withdrawalService.ApproveWithdrawal(c.Request.Context(), uint(id), &req, operatorID)
	if err != nil {
		response.ServerError(c, "审批提现失败", err)
		return
	}

	response.Success(c, result)
}

// BatchApproveWithdrawals 批量审批提现
// @Summary 批量审批提现
// @Description 批量审批通过或拒绝提现申请
// @Tags 提现管理
// @Accept json
// @Produce json
// @Param request body dto.WithdrawalBatchApprovalRequest true "批量审批请求"
// @Success 200 {object} response.Response{data=dto.WithdrawalBatchApprovalResponse}
// @Router /api/admin/withdrawals/batch-approve [post]
func (h *WithdrawalHandler) BatchApproveWithdrawals(c *gin.Context) {
	var req dto.WithdrawalBatchApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证参数
	if len(req.WithdrawalIDs) == 0 {
		response.BadRequest(c, "提现ID列表不能为空")
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	result, err := h.withdrawalService.BatchApproveWithdrawals(c.Request.Context(), &req, operatorID)
	if err != nil {
		response.ServerError(c, "批量审批提现失败", err)
		return
	}

	response.Success(c, result)
}

// PayWithdrawal 确认打款
// @Summary 确认打款
// @Description 确认提现打款操作
// @Tags 提现管理
// @Accept json
// @Produce json
// @Param request body dto.WithdrawalPaymentRequest true "打款请求"
// @Success 200 {object} response.Response{data=dto.WithdrawalPaymentResponse}
// @Router /api/admin/withdrawals/pay [post]
// PayWithdrawal 确认打款
// @Summary 确认打款
// @Description 管理员确认打款，集成微信转账服务
// @Tags 提现管理
// @Accept json
// @Produce json
// @Param request body dto.WithdrawalPaymentRequest true "打款请求"
// @Success 200 {object} response.Response{data=dto.WithdrawalPaymentResponse}
// @Router /api/admin/withdrawals/pay [post]
func (h *WithdrawalHandler) PayWithdrawal(c *gin.Context) {
	var req dto.WithdrawalPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证参数
	if len(req.WithdrawalIDs) == 0 {
		response.BadRequest(c, "提现ID列表不能为空")
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	h.logger.Infow("收到提现打款请求",
		"withdrawal_ids", req.WithdrawalIDs,
		"operator_id", operatorID,
		"payment_method", req.PaymentMethod,
	)

	// 检查Backend API健康状态
	if !h.backendClient.IsHealthy() {
		h.logger.Errorw("Backend API不可用，使用降级处理")
		// 降级处理：使用原有的服务
		result, err := h.withdrawalService.PayWithdrawal(c.Request.Context(), &req, operatorID)
		if err != nil {
			response.ServerError(c, "确认打款失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 调用Backend API发起转账
	transferReq := client.TransferRequest{
		WithdrawalIDs: req.WithdrawalIDs,
		OperatorID:    operatorID,
		Remark:        req.Remark,
	}

	transferResp, err := h.backendClient.InitiateTransfer(transferReq)
	if err != nil {
		h.logger.Errorw("Backend转账API调用失败",
			"error", err.Error(),
			"withdrawal_ids", req.WithdrawalIDs,
		)

		// 降级处理：使用原有的服务
		result, err := h.withdrawalService.PayWithdrawal(c.Request.Context(), &req, operatorID)
		if err != nil {
			response.ServerError(c, "确认打款失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 转换响应格式并填充完整数据
	result := &dto.WithdrawalPaymentResponse{
		SuccessCount:    transferResp.SuccessCount,
		FailCount:       transferResp.FailCount,
		TotalAmount:     0,
		TransferBatchNo: "",
		Results:         make([]dto.WithdrawalPaymentResult, len(transferResp.Results)),
	}

	// 获取提现记录详情以填充完整数据
	for i, r := range transferResp.Results {
		withdrawalDetail, err := h.withdrawalService.GetWithdrawal(c.Request.Context(), r.WithdrawalID)
		if err != nil {
			h.logger.Errorw("获取提现详情失败", "withdrawal_id", r.WithdrawalID, "error", err)
			result.Results[i] = dto.WithdrawalPaymentResult{
				WithdrawalID: r.WithdrawalID,
				WithdrawNo:   "",
				Amount:       0,
				Status:       r.Status,
				Message:      r.Message,
				Processed:    r.Processed,
				TransferNo:   r.TransferNo,
			}
			continue
		}

		// 填充完整的响应数据
		result.Results[i] = dto.WithdrawalPaymentResult{
			WithdrawalID: r.WithdrawalID,
			WithdrawNo:   withdrawalDetail.WithdrawNo,
			Amount:       withdrawalDetail.Amount,
			Status:       r.Status,
			Message:      r.Message,
			Processed:    r.Processed,
			TransferNo:   r.TransferNo,
		}

		// 计算总金额（只计算成功处理的）
		if r.Processed {
			result.TotalAmount += withdrawalDetail.Amount
		}

		// 设置批次号（使用第一个成功的转账批次号）
		if result.TransferBatchNo == "" && r.TransferNo != "" && r.Processed {
			result.TransferBatchNo = r.TransferNo
		}
	}

	h.logger.Infow("提现打款处理完成",
		"success_count", result.SuccessCount,
		"fail_count", result.FailCount,
		"total_count", len(req.WithdrawalIDs),
	)

	response.Success(c, result)
}

// QueryTransferStatus 查询转账状态
// @Summary 查询转账状态
// @Description 查询微信转账状态
// @Tags 转账管理
// @Accept json
// @Produce json
// @Param request body dto.TransferStatusQueryRequest true "查询请求"
// @Success 200 {object} response.Response{data=[]dto.TransferStatusResponse}
// @Router /api/admin/transfers/status [post]
func (h *WithdrawalHandler) QueryTransferStatus(c *gin.Context) {
	var req dto.TransferStatusQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	h.logger.Infow("收到查询转账状态请求",
		"withdrawal_ids", req.WithdrawalIDs,
	)

	// 检查Backend API健康状态
	if !h.backendClient.IsHealthy() {
		h.logger.Errorw("Backend API不可用，使用降级处理")
		// 降级处理：使用原有的服务
		result, err := h.withdrawalService.QueryTransferStatus(c.Request.Context(), &req)
		if err != nil {
			response.ServerError(c, "查询转账状态失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 调用Backend API批量查询转账状态
	statusList, err := h.backendClient.BatchQueryTransferStatus(req.WithdrawalIDs)
	if err != nil {
		h.logger.Errorw("Backend查询转账状态API调用失败",
			"error", err.Error(),
			"withdrawal_ids", req.WithdrawalIDs,
		)

		// 降级处理：使用原有的服务
		result, err := h.withdrawalService.QueryTransferStatus(c.Request.Context(), &req)
		if err != nil {
			response.ServerError(c, "查询转账状态失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 转换响应格式
	result := make([]dto.TransferStatusResponse, len(statusList))
	for i, status := range statusList {
		// 解析时间字符串
		var transferTime *time.Time
		if status.TransferTime != "" {
			if t, err := time.Parse(time.RFC3339, status.TransferTime); err == nil {
				transferTime = &t
			}
		}

		result[i] = dto.TransferStatusResponse{
			WithdrawalID:    status.WithdrawalID,
			WithdrawNo:      "", // client结构体中没有此字段
			TransferBatchNo: status.WechatBatchNo,
			TransferNo:      status.TransferNo,
			TransferStatus:  fmt.Sprintf("%d", status.Status),
			Amount:          float64(status.Amount),
			TransferTime:    transferTime,
			FailReason:      status.FailReason,
			UpdatedAt:       time.Now(), // client结构体中没有此字段，使用当前时间
		}
	}

	h.logger.Infow("查询转账状态完成",
		"requested_count", len(req.WithdrawalIDs),
		"found_count", len(result),
	)

	response.Success(c, result)
}

// RetryTransfer 重试转账
// @Summary 重试转账
// @Description 重试失败的转账
// @Tags 转账管理
// @Accept json
// @Produce json
// @Param request body dto.TransferRetryRequest true "重试请求"
// @Success 200 {object} response.Response{data=dto.WithdrawalPaymentResponse}
// @Router /api/admin/withdrawals/transfer/retry [post]
func (h *WithdrawalHandler) RetryTransfer(c *gin.Context) {
	var req dto.TransferRetryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证参数
	if len(req.WithdrawalIDs) == 0 {
		response.BadRequest(c, "提现ID列表不能为空")
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	h.logger.Infow("收到重试转账请求",
		"withdrawal_ids", req.WithdrawalIDs,
		"operator_id", operatorID,
		"remark", req.Remark,
	)

	// 检查Backend API健康状态
	if !h.backendClient.IsHealthy() {
		h.logger.Errorw("Backend API不可用，使用降级处理")
		// 降级处理：使用原有的服务
		result, err := h.withdrawalService.RetryTransfer(c.Request.Context(), &req, operatorID)
		if err != nil {
			response.ServerError(c, "重试转账失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 对每个提现ID调用Backend API重试转账
	results := make([]dto.WithdrawalPaymentResult, 0, len(req.WithdrawalIDs))
	successCount := 0
	failCount := 0

	for _, withdrawalID := range req.WithdrawalIDs {
		retryReq := client.RetryTransferRequest{
			OperatorID: operatorID,
			Reason:     req.Remark,
		}

		retryResp, err := h.backendClient.RetryTransfer(withdrawalID, retryReq)
		if err != nil {
			h.logger.Errorw("Backend重试转账API调用失败",
				"error", err.Error(),
				"withdrawal_id", withdrawalID,
			)

			results = append(results, dto.WithdrawalPaymentResult{
				WithdrawalID: withdrawalID,
				Status:       0, // 失败状态
				Message:      err.Error(),
				Processed:    false,
			})
			failCount++
		} else {
			results = append(results, dto.WithdrawalPaymentResult{
				WithdrawalID: withdrawalID,
				TransferNo:   (*retryResp)["transfer_no"].(string),
				Status:       int((*retryResp)["status"].(float64)),
				Message:      (*retryResp)["message"].(string),
				Processed:    true,
			})
			successCount++
		}
	}

	result := &dto.WithdrawalPaymentResponse{
		SuccessCount: successCount,
		FailCount:    failCount,
		Results:      results,
	}

	h.logger.Infow("重试转账处理完成",
		"success_count", successCount,
		"fail_count", failCount,
		"total_count", len(req.WithdrawalIDs),
	)

	response.Success(c, result)
}

// GetTransferDetail 获取转账详情
// @Summary 获取转账详情
// @Description 获取转账记录详情
// @Tags 转账管理
// @Accept json
// @Produce json
// @Param withdrawal_id path uint true "提现ID"
// @Success 200 {object} response.Response{data=dto.TransferDetailResponse}
// @Router /api/admin/transfers/{withdrawal_id}/detail [get]
func (h *WithdrawalHandler) GetTransferDetail(c *gin.Context) {
	withdrawalIDStr := c.Param("withdrawal_id")
	withdrawalID, err := strconv.ParseUint(withdrawalIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的提现ID")
		return
	}

	h.logger.Infow("收到获取转账详情请求",
		"withdrawal_id", withdrawalID,
	)

	// 检查Backend API健康状态
	if !h.backendClient.IsHealthy() {
		h.logger.Errorw("Backend API不可用，使用降级处理")
		// 降级处理：使用原有的服务
		req := &dto.TransferDetailRequest{
			WithdrawalID: uint(withdrawalID),
		}
		result, err := h.withdrawalService.GetTransferDetail(c.Request.Context(), req)
		if err != nil {
			response.ServerError(c, "获取转账详情失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 调用Backend API查询转账状态
	statusResp, err := h.backendClient.QueryTransferStatus(uint(withdrawalID))
	if err != nil {
		h.logger.Errorw("Backend查询转账详情API调用失败",
			"error", err.Error(),
			"withdrawal_id", withdrawalID,
		)

		// 降级处理：使用原有的服务
		req := &dto.TransferDetailRequest{
			WithdrawalID: uint(withdrawalID),
		}
		result, err := h.withdrawalService.GetTransferDetail(c.Request.Context(), req)
		if err != nil {
			response.ServerError(c, "获取转账详情失败", err)
			return
		}
		response.Success(c, result)
		return
	}

	// 解析时间字符串
	var transferTime *time.Time
	if statusResp.TransferTime != "" {
		if t, err := time.Parse(time.RFC3339, statusResp.TransferTime); err == nil {
			transferTime = &t
		}
	}

	// 转换响应格式
	result := &dto.TransferDetailResponse{
		WithdrawalID:    statusResp.WithdrawalID,
		WithdrawNo:      "", // client结构体中没有此字段
		TransferBatchNo: statusResp.WechatBatchNo,
		TransferNo:      statusResp.TransferNo,
		Amount:          float64(statusResp.Amount),
		TransferStatus:  fmt.Sprintf("%d", statusResp.Status),
		TransferTime:    transferTime,
		FailReason:      statusResp.FailReason,
		RetryCount:      statusResp.RetryCount,
		LastRetryTime:   nil,        // client结构体中没有此字段
		CreatedAt:       time.Now(), // client结构体中没有此字段，使用当前时间
		UpdatedAt:       time.Now(), // client结构体中没有此字段，使用当前时间
	}

	h.logger.Infow("获取转账详情完成",
		"withdrawal_id", withdrawalID,
		"transfer_no", result.TransferNo,
		"transfer_status", result.TransferStatus,
	)

	response.Success(c, result)
}
