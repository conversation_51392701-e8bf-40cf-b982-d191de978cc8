package handler

import (
	"net/http"
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// OperationLogHandler 操作日志处理器
type OperationLogHandler struct {
	service service.IOperationLogService
	logger  *zap.Logger
}

// NewOperationLogHandler 创建操作日志处理器实例
func NewOperationLogHandler(service service.IOperationLogService, logger *zap.Logger) *OperationLogHandler {
	return &OperationLogHandler{
		service: service,
		logger:  logger,
	}
}

// GetOperationLogs 获取操作日志列表
func (h *OperationLogHandler) GetOperationLogs(c *gin.Context) {
	var req dto.LogListRequest

	// 解析查询参数
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}

	pageSize, _ := strconv.Atoi(c.Query("page_size"))
	if pageSize <= 0 {
		pageSize = 10
	}

	req.Page = page
	req.PageSize = pageSize
	req.DateStart = c.Query("start_time")
	req.DateEnd = c.Query("end_time")

	// 解析其他可选参数
	if operatorIDStr := c.Query("operator_id"); operatorIDStr != "" {
		if operatorID, err := strconv.ParseInt(operatorIDStr, 10, 64); err == nil {
			req.OperatorID = &operatorID
		}
	}

	if operationType := c.Query("operation_type"); operationType != "" {
		req.OperationType = operationType
	}

	if resourceType := c.Query("resource_type"); resourceType != "" {
		req.ResourceType = resourceType
	}

	if resourceIDStr := c.Query("resource_id"); resourceIDStr != "" {
		if resourceID, err := strconv.ParseInt(resourceIDStr, 10, 64); err == nil {
			req.ResourceID = &resourceID
		}
	}

	if resultStr := c.Query("result"); resultStr != "" {
		if result, err := strconv.Atoi(resultStr); err == nil {
			req.Result = &result
		}
	}

	if ipAddress := c.Query("ip_address"); ipAddress != "" {
		req.IPAddress = ipAddress
	}

	// 调用服务获取日志列表
	result, err := h.service.GetLogList(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("获取操作日志列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取操作日志列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"data":    result,
		"message": "success",
	})
}

// GetOperationLogDetail 获取操作日志详情
func (h *OperationLogHandler) GetOperationLogDetail(c *gin.Context) {
	logID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的日志ID",
		})
		return
	}

	logInfo, err := h.service.GetLog(c.Request.Context(), logID)
	if err != nil {
		h.logger.Error("获取操作日志详情失败", zap.Error(err), zap.Int64("log_id", logID))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取操作日志详情失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"data":    logInfo,
		"message": "success",
	})
}

// ExportOperationLogs 导出操作日志
func (h *OperationLogHandler) ExportOperationLogs(c *gin.Context) {
	// TODO: 实现导出逻辑
	c.JSON(http.StatusNotImplemented, gin.H{
		"code":    501,
		"message": "导出功能暂未实现",
	})
}

// CleanupOperationLogs 清理操作日志
func (h *OperationLogHandler) CleanupOperationLogs(c *gin.Context) {
	// TODO: 实现清理逻辑
	c.JSON(http.StatusNotImplemented, gin.H{
		"code":    501,
		"message": "清理功能暂未实现",
	})
}
