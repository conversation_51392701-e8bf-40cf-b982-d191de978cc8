package handler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/repository"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/logger"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
)

// RefundHandler 退款处理器
type RefundHandler struct {
	refundService      service.IRefundService
	refundRepo         repository.IRefundRepository
	orderService       service.IOrderService
	orderStatusService service.IOrderStatusService
}

// NewRefundHandler 创建退款处理器实例
func NewRefundHandler(refundService service.IRefundService, refundRepo repository.IRefundRepository, orderService service.IOrderService, orderStatusService service.IOrderStatusService) *RefundHandler {
	return &RefundHandler{
		refundService:      refundService,
		refundRepo:         refundRepo,
		orderService:       orderService,
		orderStatusService: orderStatusService,
	}
}

// GetRefundList 获取退款列表
// @Summary 获取退款列表
// @Description 分页获取退款列表，支持状态过滤
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Param status query string false "退款状态"
// @Param order_no query string false "订单号"
// @Success 200 {object} response.Response{data=dto.RefundListResponse}
// @Router /api/admin/refunds [get]
func (h *RefundHandler) GetRefundList(c *gin.Context) {
	// 添加调试日志
	logger.API("======= RefundHandler.GetRefundList 被调用 =======")

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	status := c.Query("status")
	orderNo := c.Query("order_no")

	req := &dto.RefundListRequest{
		Page:    page,
		Limit:   limit,
		Status:  status,
		OrderNo: orderNo,
	}

	result, err := h.refundService.GetRefundList(c.Request.Context(), req)
	if err != nil {
		logger.Error("获取退款列表失败: %v", err)
		response.ServerError(c, "获取退款列表失败", err)
		return
	}

	logger.Info("获取退款列表成功，返回 %d 条记录", len(result.List))
	response.Success(c, result)
}

// GetRefund 获取退款详情
// @Summary 获取退款详情
// @Description 根据退款ID获取详细信息
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Success 200 {object} response.Response{data=dto.RefundDetailResponse}
// @Router /api/admin/refunds/{id} [get]
func (h *RefundHandler) GetRefund(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的退款ID")
		return
	}

	result, err := h.refundService.GetRefund(c.Request.Context(), uint(id))
	if err != nil {
		response.ServerError(c, "查询退款详情失败", err)
		return
	}

	response.Success(c, result)
}

// CreateRefund 创建退款申请
// @Summary 创建退款申请
// @Description 根据订单创建全额退款申请
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param request body dto.CreateRefundRequest true "创建退款请求"
// @Success 200 {object} response.Response{data=dto.RefundResponse}
// @Router /api/admin/refunds [post]
func (h *RefundHandler) CreateRefund(c *gin.Context) {
	var req dto.CreateRefundRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证基本参数
	if req.OrderID <= 0 {
		response.BadRequest(c, "订单ID不能为空")
		return
	}
	if req.RefundReason == "" {
		response.BadRequest(c, "退款原因不能为空")
		return
	}
	if req.OperatorID <= 0 {
		response.BadRequest(c, "操作员ID不能为空")
		return
	}

	result, err := h.refundService.CreateRefund(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, "创建退款申请失败", err)
		return
	}

	response.Success(c, result)
}

// ProcessRefund 处理退款申请
// @Summary 处理退款申请
// @Description 审核通过或拒绝退款申请
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Param request body dto.RefundUpdateRequest true "处理退款请求"
// @Success 200 {object} response.Response
// @Router /api/admin/refunds/{id}/process [put]
func (h *RefundHandler) ProcessRefund(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的退款ID")
		return
	}

	var req dto.RefundUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证处理原因
	if req.Reason == "" {
		response.BadRequest(c, "处理原因不能为空")
		return
	}

	err = h.refundService.ProcessRefund(c.Request.Context(), uint(id), &req)
	if err != nil {
		response.ServerError(c, "处理退款申请失败", err)
		return
	}

	response.Success(c, gin.H{"message": "退款处理成功"})
}

// GetRefundStats 获取退款统计
// @Summary 获取退款统计
// @Description 获取退款相关统计数据
// @Tags 退款管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.RefundStatsResponse}
// @Router /api/admin/refunds/stats [get]
func (h *RefundHandler) GetRefundStats(c *gin.Context) {
	result, err := h.refundService.GetRefundStats(c.Request.Context())
	if err != nil {
		response.ServerError(c, "查询退款统计失败", err)
		return
	}

	response.Success(c, result)
}

// GetTodayStats 获取今日退款统计
// @Summary 获取今日退款统计
// @Description 获取今日退款相关统计数据
// @Tags 退款管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.RefundStatsResponse}
// @Router /api/admin/refunds/stats/today [get]
func (h *RefundHandler) GetTodayStats(c *gin.Context) {
	result, err := h.refundService.GetTodayStats(c.Request.Context())
	if err != nil {
		response.ServerError(c, "查询今日退款统计失败", err)
		return
	}

	response.Success(c, result)
}

// CheckRefundEligibility 检查退款资格
// @Summary 检查退款资格
// @Description 检查订单是否可以申请退款
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param order_id query int true "订单ID"
// @Success 200 {object} response.Response{data=dto.RefundCalculateResponse}
// @Router /api/admin/refunds/check [get]
func (h *RefundHandler) CheckRefundEligibility(c *gin.Context) {
	orderIDStr := c.Query("order_id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的订单ID")
		return
	}

	result, err := h.refundService.CanCreateRefund(c.Request.Context(), uint(orderID))
	if err != nil {
		response.ServerError(c, "检查退款资格失败", err)
		return
	}

	response.Success(c, result)
}

// BatchProcessRefunds 批量处理退款
// @Summary 批量处理退款
// @Description 批量创建退款申请
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param request body dto.BatchRefundRequest true "批量退款请求"
// @Success 200 {object} response.Response{data=dto.BatchRefundResponse}
// @Router /api/admin/refunds/batch [post]
func (h *RefundHandler) BatchProcessRefunds(c *gin.Context) {
	var req dto.BatchRefundRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证批量处理参数
	if len(req.OrderIDs) == 0 {
		response.BadRequest(c, "订单ID列表不能为空")
		return
	}
	if len(req.OrderIDs) > 10 {
		response.BadRequest(c, "批量处理最多支持10个订单")
		return
	}
	if req.RefundReason == "" {
		response.BadRequest(c, "退款原因不能为空")
		return
	}

	result, err := h.refundService.BatchProcessRefunds(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, "批量处理退款失败", err)
		return
	}

	response.Success(c, result)
}

// ExportRefundReport 导出退款报表
// @Summary 导出退款报表
// @Description 导出退款数据为Excel文件
// @Tags 退款管理
// @Accept json
// @Produce application/octet-stream
// @Param status query string false "退款状态"
// @Param start_date query string false "开始日期(格式: 2024-12-31)"
// @Param end_date query string false "结束日期(格式: 2024-12-31)"
// @Success 200 {file} file "Excel文件"
// @Router /api/admin/refunds/export [get]
func (h *RefundHandler) ExportRefundReport(c *gin.Context) {
	// 暂时返回开发中提示
	response.Success(c, gin.H{
		"message": "导出功能开发中",
		"status":  "developing",
	})
}

// ApproveRefund 审批退款
// @Summary 审批退款
// @Description 审批通过或拒绝退款申请
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Param request body dto.RefundApprovalRequest true "审批请求"
// @Success 200 {object} response.Response{data=dto.RefundApprovalResponse}
// @Router /api/admin/refunds/{id}/approve [post]
func (h *RefundHandler) ApproveRefund(c *gin.Context) {

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的退款ID")
		return
	}

	var req dto.RefundApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	result, err := h.refundService.ApproveRefund(c.Request.Context(), uint(id), &req, operatorID)
	if err != nil {
		response.ServerError(c, "审批退款失败", err)
		return
	}

	// 🔧 修复：退款审批成功后，更新相关订单状态为已退款
	if result != nil && result.Status == "approved" {
		// 获取退款记录以获取订单ID
		refund, err := h.refundRepo.GetByID(c.Request.Context(), uint(id))
		if err != nil {
			logger.Error("获取退款记录失败, 退款ID: %d, 错误: %v", id, err)
		} else {
			// 异步更新订单状态为已退款，避免阻塞响应
			go func() {
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				defer cancel()

				if h.orderStatusService != nil {
					if err := h.orderStatusService.HandleOrderRefund(ctx, refund.OrderID, "退款审批成功"); err != nil {
						logger.Error("更新订单状态为已退款失败, 订单ID: %d, 错误: %v", refund.OrderID, err)
					} else {
						logger.Info("退款审批成功，已更新订单状态为已退款, 退款ID: %d, 订单ID: %d", id, refund.OrderID)
					}
				}
			}()
		}
	}

	response.Success(c, result)
}

// BatchApproveRefunds 批量审批退款
// @Summary 批量审批退款
// @Description 批量审批通过或拒绝退款申请
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param request body dto.RefundBatchApprovalRequest true "批量审批请求"
// @Success 200 {object} response.Response{data=dto.RefundBatchApprovalResponse}
// @Router /api/admin/refunds/batch-approve [post]
func (h *RefundHandler) BatchApproveRefunds(c *gin.Context) {
	var req dto.RefundBatchApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 验证参数
	if len(req.RefundIDs) == 0 {
		response.BadRequest(c, "退款ID列表不能为空")
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	result, err := h.refundService.BatchApproveRefunds(c.Request.Context(), &req, operatorID)
	if err != nil {
		response.ServerError(c, "批量审批退款失败", err)
		return
	}

	// 🔧 修复：批量审批成功后，更新相关订单状态为已退款
	if result != nil && len(result.Results) > 0 {
		for _, refundResult := range result.Results {
			if refundResult.Status == "approved" {
				// 通过退款ID获取关联的订单ID
				refund, err := h.refundRepo.GetByID(c.Request.Context(), refundResult.RefundID)
				if err != nil {
					logger.Error("获取退款记录失败, 退款ID: %d, 错误: %v", refundResult.RefundID, err)
					continue
				}

				// 异步更新订单状态为已退款，避免阻塞响应
				go func(orderID uint, refundID uint) {
					ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
					defer cancel()

					if h.orderStatusService != nil {
						if err := h.orderStatusService.HandleOrderRefund(ctx, orderID, "批量退款审批成功"); err != nil {
							logger.Error("更新订单状态为已退款失败, 订单ID: %d, 错误: %v", orderID, err)
						} else {
							logger.Info("批量退款审批成功，已更新订单状态为已退款, 退款ID: %d, 订单ID: %d", refundID, orderID)
						}
					}
				}(refund.OrderID, refundResult.RefundID)
			}
		}
	}

	response.Success(c, result)
}

// SyncRefundStatus 同步退款状态
// @Summary 同步退款状态
// @Description 同步微信退款状态
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Success 200 {object} response.Response{data=dto.RefundResponse}
// @Router /api/admin/refunds/{id}/sync [post]
func (h *RefundHandler) SyncRefundStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("无效的退款ID: %s, 错误: %v", idStr, err)
		response.BadRequest(c, "无效的退款ID")
		return
	}

	logger.API("开始同步退款状态, 退款ID: %d", id)
	result, err := h.refundService.SyncWechatRefundStatus(c.Request.Context(), uint(id))
	if err != nil {
		logger.Error("同步退款状态失败, 退款ID: %d, 错误: %v", id, err)
		response.ServerError(c, "同步退款状态失败", err)
		return
	}

	logger.Info("同步退款状态成功, 退款ID: %d, 状态: %s", id, result.Status)
	response.Success(c, result)
}

// GetRefundReceipt 获取退款凭证
// @Summary 获取退款凭证
// @Description 获取已完成退款的凭证信息
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Success 200 {object} response.Response{data=dto.RefundReceiptResponse}
// @Router /api/admin/refunds/{id}/receipt [get]
func (h *RefundHandler) GetRefundReceipt(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("无效的退款ID: %s, 错误: %v", idStr, err)
		response.BadRequest(c, "无效的退款ID")
		return
	}

	logger.API("开始获取退款凭证, 退款ID: %d", id)
	result, err := h.refundService.GetRefundReceipt(c.Request.Context(), uint(id))
	if err != nil {
		logger.Error("获取退款凭证失败, 退款ID: %d, 错误: %v", id, err)
		response.ServerError(c, "获取退款凭证失败", err)
		return
	}

	logger.Info("获取退款凭证成功, 退款ID: %d, 退款单号: %s", id, result.RefundNo)
	response.Success(c, result)
}

// ApproveRefundSimple 简化版单条退款审批通过
// @Summary 简化版单条退款审批通过
// @Description 直接通过单条退款申请，无需复杂的审批流程
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Param request body dto.SimpleApprovalRequest true "简化审批请求"
// @Success 200 {object} response.Response{data=dto.RefundResponse}
// @Router /api/admin/refunds/{id}/approve-simple [post]
func (h *RefundHandler) ApproveRefundSimple(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的退款ID")
		return
	}

	var req dto.SimpleApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	result, err := h.refundService.ApproveRefundSimple(c.Request.Context(), uint(id), &req, operatorID)
	if err != nil {
		logger.Error("简化审批通过失败, 退款ID: %d, 错误: %v", id, err)
		response.ServerError(c, "审批失败", err)
		return
	}

	logger.Info("简化审批通过成功, 退款ID: %d, 操作员ID: %d", id, operatorID)
	response.Success(c, result)
}

// RejectRefundSimple 简化版单条退款审批拒绝
// @Summary 简化版单条退款审批拒绝
// @Description 直接拒绝单条退款申请
// @Tags 退款管理
// @Accept json
// @Produce json
// @Param id path int true "退款ID"
// @Param request body dto.SimpleApprovalRequest true "简化审批请求"
// @Success 200 {object} response.Response{data=dto.RefundResponse}
// @Router /api/admin/refunds/{id}/reject-simple [post]
func (h *RefundHandler) RejectRefundSimple(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的退款ID")
		return
	}

	var req dto.SimpleApprovalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 获取操作员ID
	operatorIDRaw, exists := c.Get("admin_id")
	if !exists {
		response.Unauthorized(c, "未登录")
		return
	}
	operatorID := operatorIDRaw.(uint)

	result, err := h.refundService.RejectRefundSimple(c.Request.Context(), uint(id), &req, operatorID)
	if err != nil {
		logger.Error("简化审批拒绝失败, 退款ID: %d, 错误: %v", id, err)
		response.ServerError(c, "审批拒绝失败", err)
		return
	}

	logger.Info("简化审批拒绝成功, 退款ID: %d, 操作员ID: %d", id, operatorID)
	response.Success(c, result)
}

// ExportRefundReceipt 导出退款凭证
// @Summary 导出退款凭证
// @Description 导出已完成退款的凭证为PDF文件
// @Tags 退款管理
// @Accept json
// @Produce application/pdf
// @Param id path int true "退款ID"
// @Success 200 {file} file "PDF凭证文件"
// @Router /api/admin/refunds/{id}/receipt/export [get]
func (h *RefundHandler) ExportRefundReceipt(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("无效的退款ID: %s, 错误: %v", idStr, err)
		response.BadRequest(c, "无效的退款ID")
		return
	}

	logger.API("开始导出退款凭证, 退款ID: %d", id)

	// 1. 获取退款凭证数据
	receiptData, err := h.refundService.GetRefundReceipt(c.Request.Context(), uint(id))
	if err != nil {
		logger.Error("获取退款凭证失败, 退款ID: %d, 错误: %v", id, err)
		response.ServerError(c, "获取退款凭证失败: "+err.Error(), err)
		return
	}

	// 2. 生成PDF凭证 - 先使用简单的HTML生成方案
	pdfData, err := h.generateSimpleReceiptPDF(receiptData)
	if err != nil {
		logger.Error("生成PDF凭证失败, 退款ID: %d, 错误: %v", id, err)
		response.ServerError(c, "生成PDF凭证失败: "+err.Error(), err)
		return
	}

	// 3. 设置响应头
	filename := fmt.Sprintf("退款凭证_%s_%s.txt", receiptData.RefundNo, time.Now().Format("20060102"))
	c.Header("Content-Type", "text/plain; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.Header("Content-Length", fmt.Sprintf("%d", len(pdfData)))

	// 4. 返回文本数据
	c.Data(http.StatusOK, "text/plain; charset=utf-8", pdfData)

	logger.Info("导出退款凭证成功, 退款ID: %d, 文件大小: %d bytes", id, len(pdfData))
}

// generateSimpleReceiptPDF 生成简单的退款凭证文件
// 当前实现：生成文本格式凭证，后续可以扩展为真正的PDF
func (h *RefundHandler) generateSimpleReceiptPDF(receiptData *dto.RefundReceiptResponse) ([]byte, error) {
	// 构建凭证内容
	content := fmt.Sprintf(`
==================================================
                退款凭证
==================================================

退款凭证信息:
退款单号: %s
第三方退款单号: %s
订单ID: %d
订单号: %s
退款金额: ¥%.2f

时间信息:
退款时间: %s
处理时间: %s
生成时间: %s

支付信息:
支付方式: %s
支付单号: %s
原始金额: ¥%.2f

退款详情:
退款原因: %s
退款渠道: %s
退款状态: %s (%s)
操作员: %s

手续费信息:
退款手续费: ¥%.2f
实际退款金额: ¥%.2f

交易信息:
交易流水号: %s

==================================================
                凭证说明
==================================================
1. 此凭证为退款完成的证明文件
2. 如有疑问请联系客服
3. 退款金额将在1-3个工作日内到账

生成时间: %s
系统: 陪诊管理后台
==================================================
`,
		receiptData.RefundNo,
		receiptData.WechatRefundNo,
		receiptData.OrderID,
		receiptData.OrderNo,
		receiptData.RefundAmount,
		receiptData.RefundTime.Format("2006-01-02 15:04:05"),
		receiptData.ProcessTime.Format("2006-01-02 15:04:05"),
		receiptData.GeneratedAt.Format("2006-01-02 15:04:05"),
		receiptData.PaymentMethod,
		receiptData.PaymentNo,
		receiptData.OriginalAmount,
		receiptData.RefundReason,
		receiptData.RefundChannel,
		receiptData.RefundStatus,
		receiptData.RefundStatusText,
		receiptData.OperatorName,
		receiptData.RefundFee,
		receiptData.ActualRefundAmount,
		receiptData.TransactionID,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	return []byte(content), nil
}
