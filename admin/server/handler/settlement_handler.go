package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SettlementHandler 分账管理处理器
type SettlementHandler struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewSettlementHandler 创建分账管理处理器
func NewSettlementHandler(db *gorm.DB, logger *zap.Logger) *SettlementHandler {
	return &SettlementHandler{
		db:     db,
		logger: logger,
	}
}

// GetSettlementList 获取分账记录列表
// @Summary 获取分账记录列表
// @Tags 分账管理
// @Accept json
// @Produce json
// @Param order_no query string false "订单号"
// @Param attendant_id query int false "陪诊师ID"
// @Param status query int false "结算状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/list [get]
func (h *SettlementHandler) GetSettlementList(c *gin.Context) {
	var filter struct {
		OrderNo     string `form:"order_no"`
		AttendantID *int   `form:"attendant_id"`
		Status      *int   `form:"status"`
		Page        int    `form:"page"`
		PageSize    int    `form:"page_size"`
	}

	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}

	// 构建查询
	query := `
		SELECT 
			ai.id,
			ai.attendant_id,
			ai.order_id,
			ai.amount,
			ai.platform_amount,
			ai.commission_rate,
			ai.net_amount,
			ai.status,
			ai.settle_time,
			ai.created_at,
			o.order_no,
			a.name as attendant_name,
			o.amount as order_amount
		FROM attendant_income ai
		LEFT JOIN orders o ON ai.order_id = o.id
		LEFT JOIN attendants a ON ai.attendant_id = a.user_id
		WHERE ai.deleted_at IS NULL
	`

	var args []interface{}
	argIndex := 1

	// 添加过滤条件
	if filter.OrderNo != "" {
		query += " AND o.order_no LIKE ?"
		args = append(args, "%"+filter.OrderNo+"%")
		argIndex++
	}
	if filter.AttendantID != nil {
		query += " AND ai.attendant_id = ?"
		args = append(args, *filter.AttendantID)
		argIndex++
	}
	if filter.Status != nil {
		query += " AND ai.status = ?"
		args = append(args, *filter.Status)
		argIndex++
	}

	// 计算总数
	countQuery := "SELECT COUNT(*) FROM (" + query + ") as t"
	var total int64
	if err := h.db.Raw(countQuery, args...).Count(&total).Error; err != nil {
		h.logger.Error("获取分账记录总数失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取分账记录列表失败",
		})
		return
	}

	// 分页查询
	query += " ORDER BY ai.created_at DESC LIMIT ? OFFSET ?"
	args = append(args, filter.PageSize, (filter.Page-1)*filter.PageSize)

	var results []map[string]interface{}
	rows, err := h.db.Raw(query, args...).Rows()
	if err != nil {
		h.logger.Error("获取分账记录列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取分账记录列表失败",
		})
		return
	}
	defer rows.Close()

	for rows.Next() {
		var record struct {
			ID             uint       `json:"id"`
			AttendantID    uint       `json:"attendant_id"`
			OrderID        uint       `json:"order_id"`
			Amount         float64    `json:"amount"`
			PlatformAmount float64    `json:"platform_amount"`
			CommissionRate float64    `json:"commission_rate"`
			NetAmount      float64    `json:"net_amount"`
			Status         int        `json:"status"`
			SettleTime     *time.Time `json:"settle_time"`
			CreatedAt      time.Time  `json:"created_at"`
			OrderNo        string     `json:"order_no"`
			AttendantName  string     `json:"attendant_name"`
			OrderAmount    float64    `json:"order_amount"`
		}

		if err := rows.Scan(
			&record.ID, &record.AttendantID, &record.OrderID, &record.Amount,
			&record.PlatformAmount, &record.CommissionRate, &record.NetAmount,
			&record.Status, &record.SettleTime, &record.CreatedAt,
			&record.OrderNo, &record.AttendantName, &record.OrderAmount,
		); err != nil {
			h.logger.Error("扫描分账记录失败", zap.Error(err))
			continue
		}

		results = append(results, map[string]interface{}{
			"id":              record.ID,
			"attendant_id":    record.AttendantID,
			"order_id":        record.OrderID,
			"amount":          record.Amount,
			"platform_amount": record.PlatformAmount,
			"commission_rate": record.CommissionRate,
			"net_amount":      record.NetAmount,
			"status":          record.Status,
			"status_name":     getSettlementStatusName(record.Status),
			"settle_time":     record.SettleTime,
			"created_at":      record.CreatedAt,
			"order_no":        record.OrderNo,
			"attendant_name":  record.AttendantName,
			"order_amount":    record.OrderAmount,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  results,
			"total": total,
			"page":  filter.Page,
			"limit": filter.PageSize,
		},
	})
}

// TriggerBatchSettlement 触发批量结算
// @Summary 触发批量结算
// @Tags 分账管理
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "结算参数"
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/batch [post]
func (h *SettlementHandler) TriggerBatchSettlement(c *gin.Context) {
	var req struct {
		IncomeIDs     []uint `json:"income_ids"`                     // 收入记录ID列表
		SettleMode    string `json:"settle_mode" binding:"required"` // 结算模式: immediate, delayed
		SettleRemark  string `json:"settle_remark"`                  // 结算备注
		ForceSettle   bool   `json:"force_settle"`                   // 是否强制结算
		BatchSize     int    `json:"batch_size"`                     // 批量处理大小
		NotifyEnabled bool   `json:"notify_enabled"`                 // 是否启用通知
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("触发批量结算",
		zap.Any("income_ids", req.IncomeIDs),
		zap.String("settle_mode", req.SettleMode),
		zap.String("settle_remark", req.SettleRemark),
		zap.Bool("force_settle", req.ForceSettle))

	// 从上下文获取操作人信息
	operatorID, operatorName := h.getOperatorFromContext(c)

	// 设置默认参数
	if req.BatchSize <= 0 {
		req.BatchSize = 50 // 默认每批处理50条记录
	}

	// 开启事务处理批量结算
	var processedCount int
	var failedCount int
	var successResults []map[string]interface{}
	var errorMessages []string

	err := h.db.Transaction(func(tx *gorm.DB) error {
		// 如果没有指定具体的income_ids，查询待结算的记录
		var incomeIDs []uint
		if len(req.IncomeIDs) == 0 {
			// 查询所有待结算的收入记录
			var pendingIncomes []struct {
				ID uint `json:"id"`
			}

			statusCondition := "status = 0" // 待结算状态
			if req.ForceSettle {
				statusCondition = "status IN (0, 3)" // 包含失败状态
			}

			if err := tx.Raw(`
				SELECT id FROM attendant_income 
				WHERE `+statusCondition+` AND deleted_at IS NULL 
				ORDER BY created_at ASC 
				LIMIT ?
			`, req.BatchSize).Scan(&pendingIncomes).Error; err != nil {
				return err
			}

			for _, income := range pendingIncomes {
				incomeIDs = append(incomeIDs, income.ID)
			}
		} else {
			incomeIDs = req.IncomeIDs
		}

		// 分批处理
		batchSize := req.BatchSize
		for i := 0; i < len(incomeIDs); i += batchSize {
			end := i + batchSize
			if end > len(incomeIDs) {
				end = len(incomeIDs)
			}

			batchIDs := incomeIDs[i:end]
			batchResults, batchErrors := h.processSettlementBatch(tx, batchIDs, req.SettleMode, req.SettleRemark, operatorID, operatorName)

			successResults = append(successResults, batchResults...)
			errorMessages = append(errorMessages, batchErrors...)

			processedCount += len(batchIDs)
			failedCount += len(batchErrors)
		}

		return nil
	})

	if err != nil {
		h.logger.Error("批量结算失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "批量结算失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("批量结算完成",
		zap.Int("processed_count", processedCount),
		zap.Int("success_count", len(successResults)),
		zap.Int("failed_count", failedCount))

	// 如果启用通知，发送结算完成通知
	if req.NotifyEnabled && len(successResults) > 0 {
		h.sendSettlementNotification(successResults, operatorName)
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "批量结算完成",
		"data": gin.H{
			"processed_count": processedCount,
			"success_count":   len(successResults),
			"failed_count":    failedCount,
			"success_results": successResults,
			"error_messages":  errorMessages,
			"settle_mode":     req.SettleMode,
			"operator":        operatorName,
			"settle_time":     time.Now(),
		},
	})
}

// RecalculateSettlement 重新计算分账
// @Summary 重新计算分账
// @Tags 分账管理
// @Accept json
// @Produce json
// @Param request body map[string]interface{} true "重算参数"
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/recalculate [post]
func (h *SettlementHandler) RecalculateSettlement(c *gin.Context) {
	var req struct {
		OrderID           uint     `json:"order_id" binding:"required"`
		NewCommissionRate *float64 `json:"new_commission_rate"`
		Reason            string   `json:"reason" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// TODO: 调用backend的重新计算分账服务
	// 这里先简单返回成功，实际应该调用backend的settlement服务

	h.logger.Info("重新计算分账",
		zap.Uint("orderID", req.OrderID),
		zap.String("reason", req.Reason))

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "分账重算已完成",
		"data": gin.H{
			"order_id": req.OrderID,
			"status":   "recalculated",
		},
	})
}

// GetSettlementDetail 获取分账详情
// @Summary 获取分账详情
// @Tags 分账管理
// @Accept json
// @Produce json
// @Param id path int true "分账记录ID"
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/{id} [get]
func (h *SettlementHandler) GetSettlementDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "无效的记录ID",
		})
		return
	}

	query := `
		SELECT 
			ai.id,
			ai.attendant_id,
			ai.order_id,
			ai.amount,
			ai.platform_amount,
			ai.commission_rate,
			ai.net_amount,
			ai.status,
			ai.settle_time,
			ai.created_at,
			o.order_no,
			o.amount as order_amount,
			o.status as order_status,
			a.name as attendant_name,
			u.phone as attendant_phone
		FROM attendant_income ai
		LEFT JOIN orders o ON ai.order_id = o.id
		LEFT JOIN attendants a ON ai.attendant_id = a.user_id
		LEFT JOIN users u ON ai.attendant_id = u.id
		WHERE ai.id = ? AND ai.deleted_at IS NULL
	`

	var detail map[string]interface{}
	row := h.db.Raw(query, uint(id)).Row()

	var record struct {
		ID             uint       `json:"id"`
		AttendantID    uint       `json:"attendant_id"`
		OrderID        uint       `json:"order_id"`
		Amount         float64    `json:"amount"`
		PlatformAmount float64    `json:"platform_amount"`
		CommissionRate float64    `json:"commission_rate"`
		NetAmount      float64    `json:"net_amount"`
		Status         int        `json:"status"`
		SettleTime     *time.Time `json:"settle_time"`
		CreatedAt      time.Time  `json:"created_at"`
		OrderNo        string     `json:"order_no"`
		OrderAmount    float64    `json:"order_amount"`
		OrderStatus    int        `json:"order_status"`
		AttendantName  string     `json:"attendant_name"`
		AttendantPhone string     `json:"attendant_phone"`
	}

	if err := row.Scan(
		&record.ID, &record.AttendantID, &record.OrderID, &record.Amount,
		&record.PlatformAmount, &record.CommissionRate, &record.NetAmount,
		&record.Status, &record.SettleTime, &record.CreatedAt,
		&record.OrderNo, &record.OrderAmount, &record.OrderStatus,
		&record.AttendantName, &record.AttendantPhone,
	); err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    http.StatusNotFound,
				"message": "分账记录不存在",
			})
			return
		}
		h.logger.Error("获取分账详情失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取分账详情失败",
		})
		return
	}

	detail = map[string]interface{}{
		"id":              record.ID,
		"attendant_id":    record.AttendantID,
		"order_id":        record.OrderID,
		"amount":          record.Amount,
		"platform_amount": record.PlatformAmount,
		"commission_rate": record.CommissionRate,
		"net_amount":      record.NetAmount,
		"status":          record.Status,
		"status_name":     getSettlementStatusName(record.Status),
		"settle_time":     record.SettleTime,
		"created_at":      record.CreatedAt,
		"order_no":        record.OrderNo,
		"order_amount":    record.OrderAmount,
		"order_status":    record.OrderStatus,
		"attendant_name":  record.AttendantName,
		"attendant_phone": record.AttendantPhone,
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    detail,
	})
}

// 获取结算状态名称
func getSettlementStatusName(status int) string {
	statusMap := map[int]string{
		1: "待结算",
		2: "已结算",
		3: "已提现",
	}
	if name, exists := statusMap[status]; exists {
		return name
	}
	return "未知状态"
}

// getOperatorFromContext 从上下文获取操作人信息
func (h *SettlementHandler) getOperatorFromContext(c *gin.Context) (uint, string) {
	// 从JWT中间件设置的上下文获取管理员ID
	adminIDValue, exists := c.Get("admin_id")
	if !exists {
		h.logger.Warn("无法从上下文获取管理员ID，使用默认值")
		return 1, "system"
	}

	adminID, ok := adminIDValue.(uint)
	if !ok {
		h.logger.Warn("管理员ID类型断言失败，使用默认值")
		return 1, "system"
	}

	// 从上下文获取管理员用户名
	usernameValue, exists := c.Get("admin_name")
	if !exists {
		return adminID, "admin"
	}

	username, ok := usernameValue.(string)
	if !ok {
		return adminID, "admin"
	}

	return adminID, username
}

// processSettlementBatch 处理批量结算
func (h *SettlementHandler) processSettlementBatch(tx *gorm.DB, incomeIDs []uint, settleMode, settleRemark string, operatorID uint, operatorName string) ([]map[string]interface{}, []string) {
	var successResults []map[string]interface{}
	var errorMessages []string

	for _, incomeID := range incomeIDs {
		// 获取收入记录详情
		var income struct {
			ID             uint    `json:"id"`
			AttendantID    uint    `json:"attendant_id"`
			OrderID        uint    `json:"order_id"`
			Amount         float64 `json:"amount"`
			NetAmount      float64 `json:"net_amount"`
			Status         int     `json:"status"`
			CommissionRate float64 `json:"commission_rate"`
		}

		if err := tx.Raw(`
			SELECT id, attendant_id, order_id, amount, net_amount, status, commission_rate
			FROM attendant_income 
			WHERE id = ? AND deleted_at IS NULL
		`, incomeID).Scan(&income).Error; err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("收入记录 %d 不存在或已删除", incomeID))
			continue
		}

		// 检查状态是否允许结算
		if income.Status == 2 { // 已结算
			errorMessages = append(errorMessages, fmt.Sprintf("收入记录 %d 已结算，无需重复处理", incomeID))
			continue
		}

		// 更新结算状态
		settleTime := time.Now()
		var newStatus int
		switch settleMode {
		case "immediate":
			newStatus = 2 // 已结算
		case "delayed":
			newStatus = 1 // 待结算
		default:
			newStatus = 1
		}

		if err := tx.Exec(`
			UPDATE attendant_income 
			SET status = ?, settle_time = ?, updated_at = ? 
			WHERE id = ?
		`, newStatus, settleTime, time.Now(), incomeID).Error; err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("更新收入记录 %d 状态失败: %v", incomeID, err))
			continue
		}

		// 记录结算日志
		if err := tx.Exec(`
			INSERT INTO attendant_income_logs (income_id, action_type, old_status, new_status, operator_id, operator_name, remark, created_at)
			VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		`, incomeID, "batch_settle", income.Status, newStatus, operatorID, operatorName, settleRemark, time.Now()).Error; err != nil {
			h.logger.Warn("记录结算日志失败", zap.Error(err), zap.Uint("income_id", incomeID))
		}

		successResults = append(successResults, map[string]interface{}{
			"income_id":     incomeID,
			"attendant_id":  income.AttendantID,
			"order_id":      income.OrderID,
			"amount":        income.Amount,
			"net_amount":    income.NetAmount,
			"old_status":    income.Status,
			"new_status":    newStatus,
			"settle_mode":   settleMode,
			"settle_time":   settleTime,
			"operator_id":   operatorID,
			"operator_name": operatorName,
		})
	}

	return successResults, errorMessages
}

// sendSettlementNotification 发送结算完成通知
func (h *SettlementHandler) sendSettlementNotification(results []map[string]interface{}, operatorName string) {
	// 这里可以实现通知逻辑，例如：
	// 1. 发送邮件通知
	// 2. 发送企业微信通知
	// 3. 记录通知日志
	// 4. 推送到消息队列

	h.logger.Info("发送结算完成通知",
		zap.Int("result_count", len(results)),
		zap.String("operator", operatorName))

	// 统计结算信息
	var totalAmount float64
	var attendantIDs []uint
	for _, result := range results {
		if amount, ok := result["net_amount"].(float64); ok {
			totalAmount += amount
		}
		if attendantID, ok := result["attendant_id"].(uint); ok {
			attendantIDs = append(attendantIDs, attendantID)
		}
	}

	// 实际的通知实现可以在这里添加
	h.logger.Info("批量结算通知统计",
		zap.Float64("total_amount", totalAmount),
		zap.Any("attendant_ids", attendantIDs),
		zap.String("operator", operatorName),
		zap.Time("notify_time", time.Now()))
}
