package handler

import (
	"net/http"
	"runtime"
	"time"

	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/logger"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// MonitoringHandler 监控系统处理器
type MonitoringHandler struct {
	monitoringService service.IRefundMonitoringService
	logger            *logrus.Logger
}

// NewMonitoringHandler 创建监控处理器实例
func NewMonitoringHandler(
	monitoringService service.IRefundMonitoringService,
	logger *logrus.Logger,
) *MonitoringHandler {
	return &MonitoringHandler{
		monitoringService: monitoringService,
		logger:            logger,
	}
}

// GetRefundMetrics 获取退款监控指标
// @Summary 获取退款监控指标
// @Description 获取指定时间范围内的退款监控指标数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} Response{data=service.RefundMetrics} "成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/refund-metrics [get]
func (h *MonitoringHandler) GetRefundMetrics(c *gin.Context) {
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	// 默认查询最近24小时的数据
	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			h.logger.WithError(err).Error("解析开始时间失败")
			response.BadRequest(c, "开始时间格式错误")
			return
		}
	} else {
		startTime = time.Now().Add(-24 * time.Hour)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			h.logger.WithError(err).Error("解析结束时间失败")
			response.BadRequest(c, "结束时间格式错误")
			return
		}
	} else {
		endTime = time.Now()
	}

	// 调用服务获取监控指标
	metrics, err := h.monitoringService.GetRefundMetrics(c.Request.Context(), startTime, endTime)
	if err != nil {
		h.logger.WithError(err).Error("获取退款监控指标失败")
		response.ServerError(c, "获取退款监控指标失败", err)
		return
	}

	h.logger.Info("成功获取退款监控指标")
	response.Success(c, metrics)
}

// GetRealTimeMetrics 获取实时监控数据
// @Summary 获取实时监控数据
// @Description 获取当前实时的监控数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=service.RealTimeMetrics} "成功"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/real-time [get]
func (h *MonitoringHandler) GetRealTimeMetrics(c *gin.Context) {
	metrics, err := h.monitoringService.GetRealTimeMetrics(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("获取实时监控数据失败")
		response.ServerError(c, "获取实时监控数据失败", err)
		return
	}

	h.logger.Info("成功获取实时监控数据")
	response.Success(c, metrics)
}

// GetApprovalMetrics 获取审批监控指标
// @Summary 获取审批监控指标
// @Description 获取指定时间范围内的审批监控指标数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} Response{data=service.ApprovalMetrics} "成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/approval-metrics [get]
func (h *MonitoringHandler) GetApprovalMetrics(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		response.BadRequest(c, "时间参数错误: "+err.Error())
		return
	}

	metrics, err := h.monitoringService.GetApprovalMetrics(c.Request.Context(), startTime, endTime)
	if err != nil {
		h.logger.WithError(err).Error("获取审批监控指标失败")
		response.ServerError(c, "获取审批监控指标失败", err)
		return
	}

	h.logger.Info("成功获取审批监控指标")
	response.Success(c, metrics)
}

// GetRetryMetrics 获取重试监控指标
// @Summary 获取重试监控指标
// @Description 获取指定时间范围内的重试监控指标数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} Response{data=service.RetryMetrics} "成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/retry-metrics [get]
func (h *MonitoringHandler) GetRetryMetrics(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		response.BadRequest(c, "时间参数错误: "+err.Error())
		return
	}

	metrics, err := h.monitoringService.GetRetryMetrics(c.Request.Context(), startTime, endTime)
	if err != nil {
		h.logger.WithError(err).Error("获取重试监控指标失败")
		response.ServerError(c, "获取重试监控指标失败", err)
		return
	}

	h.logger.Info("成功获取重试监控指标")
	response.Success(c, metrics)
}

// GetPerformanceMetrics 获取性能监控数据
// @Summary 获取性能监控数据
// @Description 获取指定时间范围内的性能监控数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} Response{data=service.PerformanceMetrics} "成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/performance [get]
func (h *MonitoringHandler) GetPerformanceMetrics(c *gin.Context) {
	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		response.BadRequest(c, "时间参数错误: "+err.Error())
		return
	}

	metrics, err := h.monitoringService.GetPerformanceMetrics(c.Request.Context(), startTime, endTime)
	if err != nil {
		h.logger.WithError(err).Error("获取性能监控数据失败")
		response.ServerError(c, "获取性能监控数据失败", err)
		return
	}

	h.logger.Info("成功获取性能监控数据")
	response.Success(c, metrics)
}

// GetTrendAnalysis 获取趋势分析数据
// @Summary 获取趋势分析数据
// @Description 获取指定时间范围内的趋势分析数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param period query string true "时间周期 (hour/day/week/month)"
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} Response{data=service.TrendAnalysis} "成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/trend-analysis [get]
func (h *MonitoringHandler) GetTrendAnalysis(c *gin.Context) {
	period := c.Query("period")
	if period == "" {
		period = "day" // 默认按天分析
	}

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		response.BadRequest(c, "时间参数错误: "+err.Error())
		return
	}

	// 根据周期调整默认时间范围
	if c.Query("start_time") == "" || c.Query("end_time") == "" {
		switch period {
		case "hour":
			startTime = time.Now().Add(-24 * time.Hour)
			endTime = time.Now()
		case "day":
			startTime = time.Now().AddDate(0, 0, -30) // 最近30天
			endTime = time.Now()
		case "week":
			startTime = time.Now().AddDate(0, 0, -84) // 最近12周
			endTime = time.Now()
		case "month":
			startTime = time.Now().AddDate(0, -12, 0) // 最近12个月
			endTime = time.Now()
		}
	}

	analysis, err := h.monitoringService.GetTrendAnalysis(c.Request.Context(), period, startTime, endTime)
	if err != nil {
		h.logger.WithError(err).Error("获取趋势分析数据失败")
		response.ServerError(c, "获取趋势分析数据失败", err)
		return
	}

	h.logger.Info("成功获取趋势分析数据")
	response.Success(c, analysis)
}

// CheckAlerts 检查告警条件
// @Summary 检查告警条件
// @Description 检查当前系统的告警条件，返回活跃的告警列表
// @Tags 监控系统
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]service.Alert} "成功"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/alerts [get]
func (h *MonitoringHandler) CheckAlerts(c *gin.Context) {
	alerts, err := h.monitoringService.CheckAlerts(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("检查告警条件失败")
		response.ServerError(c, "检查告警条件失败", err)
		return
	}

	h.logger.WithField("alert_count", len(alerts)).Info("成功检查告警条件")
	response.Success(c, alerts)
}

// GetDashboardData 获取监控仪表板数据
// @Summary 获取监控仪表板数据
// @Description 获取监控仪表板的综合数据
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param timeRange query string false "时间范围 (1h/6h/12h/24h/7d/30d)" default(24h)
// @Success 200 {object} Response{data=map[string]interface{}} "成功"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/dashboard [get]
func (h *MonitoringHandler) GetDashboardData(c *gin.Context) {
	timeRange := c.DefaultQuery("timeRange", "24h")

	// 解析时间范围
	var duration time.Duration
	switch timeRange {
	case "1h":
		duration = time.Hour
	case "6h":
		duration = 6 * time.Hour
	case "12h":
		duration = 12 * time.Hour
	case "24h":
		duration = 24 * time.Hour
	case "7d":
		duration = 7 * 24 * time.Hour
	case "30d":
		duration = 30 * 24 * time.Hour
	default:
		duration = 24 * time.Hour
	}

	endTime := time.Now()
	startTime := endTime.Add(-duration)

	// 并发获取各类监控数据
	type dashboardData struct {
		RealTime    interface{} `json:"realTime"`
		Refund      interface{} `json:"refund"`
		Approval    interface{} `json:"approval"`
		Retry       interface{} `json:"retry"`
		Performance interface{} `json:"performance"`
		Alerts      interface{} `json:"alerts"`
	}

	data := &dashboardData{}

	// 获取实时数据
	if realTime, err := h.monitoringService.GetRealTimeMetrics(c.Request.Context()); err == nil {
		data.RealTime = realTime
	}

	// 获取退款指标
	if refund, err := h.monitoringService.GetRefundMetrics(c.Request.Context(), startTime, endTime); err == nil {
		data.Refund = refund
	}

	// 获取审批指标
	if approval, err := h.monitoringService.GetApprovalMetrics(c.Request.Context(), startTime, endTime); err == nil {
		data.Approval = approval
	}

	// 获取重试指标
	if retry, err := h.monitoringService.GetRetryMetrics(c.Request.Context(), startTime, endTime); err == nil {
		data.Retry = retry
	}

	// 获取性能指标
	if performance, err := h.monitoringService.GetPerformanceMetrics(c.Request.Context(), startTime, endTime); err == nil {
		data.Performance = performance
	}

	// 获取告警信息
	if alerts, err := h.monitoringService.CheckAlerts(c.Request.Context()); err == nil {
		data.Alerts = alerts
	}

	h.logger.WithField("timeRange", timeRange).Info("成功获取监控仪表板数据")
	response.Success(c, data)
}

// GetSystemHealth 获取系统健康状态
// @Summary 获取系统健康状态
// @Description 获取系统整体健康状态信息
// @Tags 监控系统
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=map[string]interface{}} "成功"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/health [get]
func (h *MonitoringHandler) GetSystemHealth(c *gin.Context) {
	// 系统健康检查
	systemHealth := map[string]interface{}{
		"uptime":       "运行中", // 简化uptime显示
		"memory_usage": h.getMemoryUsage(),
		"goroutines":   runtime.NumGoroutine(),
		"cpu_cores":    runtime.NumCPU(),
		"timestamp":    time.Now().Format("2006-01-02 15:04:05"),
		"go_version":   runtime.Version(),
	}

	// 添加日志系统统计
	logStats := h.getLogSystemStats()
	systemHealth["log_system"] = logStats

	response.Success(c, systemHealth)
}

// getMemoryUsage 获取内存使用情况
func (h *MonitoringHandler) getMemoryUsage() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"alloc_mb":       m.Alloc / 1024 / 1024,
		"total_alloc_mb": m.TotalAlloc / 1024 / 1024,
		"sys_mb":         m.Sys / 1024 / 1024,
		"num_gc":         m.NumGC,
	}
}

// getLogSystemStats 获取日志系统统计信息
func (h *MonitoringHandler) getLogSystemStats() map[string]interface{} {
	logStats := map[string]interface{}{
		"deduplication": logger.GetDeduplicationStats(),
		"rotation":      map[string]interface{}{},
		"files":         map[string]interface{}{},
	}

	// 获取日志轮转管理器统计
	if rotationManager := logger.GetRotationManager(); rotationManager != nil {
		// 这里可以添加轮转管理器的统计信息
		logStats["rotation"] = map[string]interface{}{
			"enabled": true,
			"status":  "running",
		}
	} else {
		logStats["rotation"] = map[string]interface{}{
			"enabled": false,
			"status":  "not_initialized",
		}
	}

	// 尝试获取日志文件统计
	// 这里可以添加日志文件大小、数量等统计信息
	logStats["files"] = map[string]interface{}{
		"total_categories": 7,
		"daily_rotation":   true,
	}

	return logStats
}

// ExportMonitoringReport 导出监控报告
// @Summary 导出监控报告
// @Description 导出指定时间范围的监控报告
// @Tags 监控系统
// @Accept json
// @Produce application/octet-stream
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Param format query string false "导出格式 (csv/xlsx)" default(csv)
// @Success 200 {file} binary "导出的文件"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/export [get]
func (h *MonitoringHandler) ExportMonitoringReport(c *gin.Context) {
	format := c.DefaultQuery("format", "csv")

	startTime, endTime, err := h.parseTimeRange(c)
	if err != nil {
		response.BadRequest(c, "时间参数错误: "+err.Error())
		return
	}

	// 模拟导出功能
	filename := "monitoring_report_" + startTime.Format("20060102") + "_" + endTime.Format("20060102") + "." + format

	h.logger.WithFields(logrus.Fields{
		"format":     format,
		"start_time": startTime,
		"end_time":   endTime,
		"filename":   filename,
	}).Info("导出监控报告")

	// 设置响应头
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", "attachment; filename="+filename)

	// 模拟文件内容
	content := "This is a sample monitoring report file\nGenerated at: " + time.Now().Format(time.RFC3339)
	c.String(http.StatusOK, content)
}

// parseTimeRange 解析时间范围参数
func (h *MonitoringHandler) parseTimeRange(c *gin.Context) (startTime, endTime time.Time, err error) {
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	// 默认查询最近24小时的数据
	if startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return
		}
	} else {
		startTime = time.Now().Add(-24 * time.Hour)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return
		}
	} else {
		endTime = time.Now()
	}

	return
}

// GetAlertConfig 获取告警配置
// @Summary 获取告警配置
// @Description 获取当前的告警配置参数
// @Tags 监控系统
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=service.AlertConfig} "成功"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/alert-config [get]
func (h *MonitoringHandler) GetAlertConfig(c *gin.Context) {
	// 模拟获取告警配置
	config := &service.AlertConfig{
		SuccessRateThreshold:       90.0,
		SuccessRateCheckInterval:   5,
		ProcessTimeThreshold:       300,
		ProcessTimeCheckInterval:   10,
		BacklogCountThreshold:      100,
		BacklogTimeThreshold:       2,
		BacklogCheckInterval:       15,
		RetryRateThreshold:         30.0,
		MaxRetriesReachedThreshold: 10,
		ResponseTimeThreshold:      1000,
		ErrorRateThreshold:         5.0,
		MemoryUsageThreshold:       80.0,
	}

	h.logger.Info("成功获取告警配置")
	response.Success(c, config)
}

// UpdateAlertConfig 更新告警配置
// @Summary 更新告警配置
// @Description 更新告警配置参数
// @Tags 监控系统
// @Accept json
// @Produce json
// @Param config body service.AlertConfig true "告警配置"
// @Success 200 {object} Response "成功"
// @Failure 400 {object} Response "参数错误"
// @Failure 500 {object} Response "服务器错误"
// @Router /admin/monitoring/alert-config [put]
func (h *MonitoringHandler) UpdateAlertConfig(c *gin.Context) {
	var config service.AlertConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		h.logger.WithError(err).Error("解析告警配置失败")
		response.BadRequest(c, "参数格式错误: "+err.Error())
		return
	}

	// 模拟保存配置
	h.logger.WithField("config", config).Info("更新告警配置")

	response.Success(c, "告警配置更新成功")
}
