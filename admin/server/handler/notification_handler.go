package handler

import (
	"net/http"
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"

	"github.com/gin-gonic/gin"
)

// NotificationHandler 消息通知处理器
type NotificationHandler struct {
	notificationService service.INotificationService
}

// NewNotificationHandler 创建消息通知处理器
func NewNotificationHandler(notificationService service.INotificationService) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
	}
}

// ============ 短信通知相关接口 ============

// SendSMS 发送短信
// @Summary 发送短信
// @Description 发送短信到指定手机号
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param request body dto.SMSRequest true "短信请求"
// @Success 200 {object} response.Response{data=dto.SMSResponse} "发送成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/sms/send [post]
func (h *NotificationHandler) SendSMS(c *gin.Context) {
	var req dto.SMSRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 根据是否有模板代码选择发送方式
	var err error
	if req.TemplateCode != "" {
		err = h.notificationService.SendSMSWithTemplate(c.Request.Context(), req.Phone, req.TemplateCode, req.Params)
	} else {
		err = h.notificationService.SendSMS(c.Request.Context(), req.Phone, req.Message)
	}

	if err != nil {
		response.Error(c, http.StatusInternalServerError, "发送短信失败: "+err.Error())
		return
	}

	// 构建响应
	smsResponse := &dto.SMSResponse{
		MessageID: "sms_" + strconv.FormatInt(getCurrentTimestamp(), 10),
		Phone:     req.Phone,
		Status:    "SENT",
		Message:   "短信发送成功",
	}

	response.Success(c, smsResponse)
}

// SendTaskAssignmentSMS 发送任务分配短信
// @Summary 发送任务分配短信
// @Description 发送任务分配通知短信给陪诊师
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param phone query string true "手机号"
// @Param attendant_name query string true "陪诊师姓名"
// @Param task_info query string true "任务信息"
// @Success 200 {object} response.Response "发送成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/sms/task-assignment [post]
func (h *NotificationHandler) SendTaskAssignmentSMS(c *gin.Context) {
	phone := c.Query("phone")
	attendantName := c.Query("attendant_name")
	taskInfo := c.Query("task_info")

	if phone == "" || attendantName == "" || taskInfo == "" {
		response.Error(c, http.StatusBadRequest, "手机号、陪诊师姓名和任务信息不能为空")
		return
	}

	err := h.notificationService.SendTaskAssignmentSMS(c.Request.Context(), phone, attendantName, taskInfo)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "发送任务分配短信失败: "+err.Error())
		return
	}

	response.Success(c, "任务分配短信发送成功")
}

// GetSMSStatus 获取短信状态
// @Summary 获取短信状态
// @Description 查询短信发送状态
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param message_id path string true "短信ID"
// @Success 200 {object} response.Response{data=dto.SMSStatusResponse} "查询成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/sms/status/{message_id} [get]
func (h *NotificationHandler) GetSMSStatus(c *gin.Context) {
	messageID := c.Param("message_id")
	if messageID == "" {
		response.Error(c, http.StatusBadRequest, "短信ID不能为空")
		return
	}

	status, err := h.notificationService.GetSMSStatus(c.Request.Context(), messageID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "查询短信状态失败: "+err.Error())
		return
	}

	response.Success(c, status)
}

// ============ 微信小程序推送相关接口 ============

// SendWechatPush 发送微信推送
// @Summary 发送微信推送
// @Description 发送微信小程序推送消息
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param user_id query int true "用户ID"
// @Param request body dto.WechatPushMessage true "推送消息"
// @Success 200 {object} response.Response "发送成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/wechat/push [post]
func (h *NotificationHandler) SendWechatPush(c *gin.Context) {
	userIDStr := c.Query("user_id")
	if userIDStr == "" {
		response.Error(c, http.StatusBadRequest, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "用户ID格式错误")
		return
	}

	var req dto.WechatPushMessage
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	err = h.notificationService.SendWechatPush(c.Request.Context(), userID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "发送微信推送失败: "+err.Error())
		return
	}

	response.Success(c, "微信推送发送成功")
}

// SendTaskNotification 发送任务通知
// @Summary 发送任务通知
// @Description 发送任务相关的微信推送通知
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param user_id query int true "用户ID"
// @Param request body dto.TaskNotificationData true "任务通知数据"
// @Success 200 {object} response.Response "发送成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/wechat/task [post]
func (h *NotificationHandler) SendTaskNotification(c *gin.Context) {
	userIDStr := c.Query("user_id")
	if userIDStr == "" {
		response.Error(c, http.StatusBadRequest, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "用户ID格式错误")
		return
	}

	var req dto.TaskNotificationData
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	err = h.notificationService.SendTaskNotification(c.Request.Context(), userID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "发送任务通知失败: "+err.Error())
		return
	}

	response.Success(c, "任务通知发送成功")
}

// ============ 系统内部通知相关接口 ============

// GetAdminNotifications 获取管理员通知列表
// @Summary 获取管理员通知列表
// @Description 获取管理员的通知列表
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param admin_id query int true "管理员ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param type query string false "通知类型"
// @Param is_read query bool false "是否已读"
// @Success 200 {object} response.Response{data=dto.NotificationListResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/internal/list [get]
func (h *NotificationHandler) GetAdminNotifications(c *gin.Context) {
	adminIDStr := c.Query("admin_id")
	if adminIDStr == "" {
		response.Error(c, http.StatusBadRequest, "管理员ID不能为空")
		return
	}

	adminID, err := strconv.ParseInt(adminIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "管理员ID格式错误")
		return
	}

	var req dto.NotificationListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	notifications, err := h.notificationService.GetAdminNotifications(c.Request.Context(), adminID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取通知列表失败: "+err.Error())
		return
	}

	response.Success(c, notifications)
}

// MarkNotificationAsRead 标记通知为已读
// @Summary 标记通知为已读
// @Description 标记指定通知为已读状态
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param notification_id path int true "通知ID"
// @Success 200 {object} response.Response "标记成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/internal/{notification_id}/read [put]
func (h *NotificationHandler) MarkNotificationAsRead(c *gin.Context) {
	notificationIDStr := c.Param("notification_id")
	if notificationIDStr == "" {
		response.Error(c, http.StatusBadRequest, "通知ID不能为空")
		return
	}

	notificationID, err := strconv.ParseInt(notificationIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "通知ID格式错误")
		return
	}

	err = h.notificationService.MarkNotificationAsRead(c.Request.Context(), notificationID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "标记已读失败: "+err.Error())
		return
	}

	response.Success(c, "通知已标记为已读")
}

// GetUnreadNotificationCount 获取未读通知数量
// @Summary 获取未读通知数量
// @Description 获取指定管理员的未读通知数量
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param admin_id query int true "管理员ID"
// @Success 200 {object} response.Response{data=int64} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/internal/unread-count [get]
func (h *NotificationHandler) GetUnreadNotificationCount(c *gin.Context) {
	adminIDStr := c.Query("admin_id")
	if adminIDStr == "" {
		response.Error(c, http.StatusBadRequest, "管理员ID不能为空")
		return
	}

	adminID, err := strconv.ParseInt(adminIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "管理员ID格式错误")
		return
	}

	count, err := h.notificationService.GetUnreadNotificationCount(c.Request.Context(), adminID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取未读通知数量失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{"unread_count": count})
}

// ============ 通知历史和统计相关接口 ============

// GetNotificationHistory 获取通知历史
// @Summary 获取通知历史
// @Description 获取系统通知发送历史记录
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param notify_type query string false "通知类型"
// @Param status query string false "状态"
// @Param date_start query string false "开始日期"
// @Param date_end query string false "结束日期"
// @Success 200 {object} response.Response{data=dto.NotificationHistoryResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/history [get]
func (h *NotificationHandler) GetNotificationHistory(c *gin.Context) {
	var req dto.NotificationHistoryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	history, err := h.notificationService.GetNotificationHistory(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取通知历史失败: "+err.Error())
		return
	}

	response.Success(c, history)
}

// GetNotificationStats 获取通知统计
// @Summary 获取通知统计
// @Description 获取通知发送统计数据
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期" format(date)
// @Param end_date query string true "结束日期" format(date)
// @Success 200 {object} response.Response{data=dto.NotificationStatsResponse} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/stats [get]
func (h *NotificationHandler) GetNotificationStats(c *gin.Context) {
	var dateRange dto.DateRange
	if err := c.ShouldBindQuery(&dateRange); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	stats, err := h.notificationService.GetNotificationStats(c.Request.Context(), &dateRange)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取通知统计失败: "+err.Error())
		return
	}

	response.Success(c, stats)
}

// ============ 通知模板管理相关接口 ============

// CreateNotificationTemplate 创建通知模板
// @Summary 创建通知模板
// @Description 创建新的通知模板
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param request body dto.NotificationTemplate true "模板信息"
// @Success 200 {object} response.Response "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/template [post]
func (h *NotificationHandler) CreateNotificationTemplate(c *gin.Context) {
	var req dto.NotificationTemplate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	err := h.notificationService.CreateNotificationTemplate(c.Request.Context(), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建通知模板失败: "+err.Error())
		return
	}

	response.Success(c, "通知模板创建成功")
}

// UpdateNotificationTemplate 更新通知模板
// @Summary 更新通知模板
// @Description 更新指定的通知模板
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param template_id path int true "模板ID"
// @Param request body dto.NotificationTemplate true "模板信息"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/template/{template_id} [put]
func (h *NotificationHandler) UpdateNotificationTemplate(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	if templateIDStr == "" {
		response.Error(c, http.StatusBadRequest, "模板ID不能为空")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "模板ID格式错误")
		return
	}

	var req dto.NotificationTemplate
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	err = h.notificationService.UpdateNotificationTemplate(c.Request.Context(), templateID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "更新通知模板失败: "+err.Error())
		return
	}

	response.Success(c, "通知模板更新成功")
}

// GetNotificationTemplate 获取通知模板
// @Summary 获取通知模板
// @Description 获取指定的通知模板详情
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param template_id path int true "模板ID"
// @Success 200 {object} response.Response{data=dto.NotificationTemplate} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/template/{template_id} [get]
func (h *NotificationHandler) GetNotificationTemplate(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	if templateIDStr == "" {
		response.Error(c, http.StatusBadRequest, "模板ID不能为空")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "模板ID格式错误")
		return
	}

	template, err := h.notificationService.GetNotificationTemplate(c.Request.Context(), templateID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取通知模板失败: "+err.Error())
		return
	}

	response.Success(c, template)
}

// DeleteNotificationTemplate 删除通知模板
// @Summary 删除通知模板
// @Description 删除指定的通知模板
// @Tags 通知管理
// @Accept json
// @Produce json
// @Param template_id path int true "模板ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /admin/notification/template/{template_id} [delete]
func (h *NotificationHandler) DeleteNotificationTemplate(c *gin.Context) {
	templateIDStr := c.Param("template_id")
	if templateIDStr == "" {
		response.Error(c, http.StatusBadRequest, "模板ID不能为空")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "模板ID格式错误")
		return
	}

	err = h.notificationService.DeleteNotificationTemplate(c.Request.Context(), templateID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "删除通知模板失败: "+err.Error())
		return
	}

	response.Success(c, "通知模板删除成功")
}

// ============ 辅助方法 ============

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return 1703080000 // 示例时间戳
}
