package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/logger"
	"go.uber.org/zap"
)

// OrderCompletionHandler 订单完成处理器（管理员版本）
type OrderCompletionHandler struct {
	db                     *gorm.DB
	logger                 *zap.Logger
	orderCompletionService service.IOrderCompletionService
}

// NewOrderCompletionHandler 创建订单完成处理器
func NewOrderCompletionHandler(db *gorm.DB, orderCompletionService service.IOrderCompletionService) *OrderCompletionHandler {
	return &OrderCompletionHandler{
		db:                     db,
		logger:                 logger.GetLogger(),
		orderCompletionService: orderCompletionService,
	}
}

// ForceCompleteOrderByIDWithAdmin 管理员强制完成订单（带订单ID参数）
// @Summary 管理员强制完成订单（带订单ID参数）
// @Description 管理员通过订单ID强制完成订单，自动审核通过
// @Tags 订单完成
// @Accept json
// @Produce json
// @Param id path uint true "订单ID"
// @Param request body ForceCompleteByAdminRequest true "强制完成订单请求"
// @Success 200 {object} Response{data=string}
// @Router /api/admin/orders/{id}/force-complete [post]
func (h *OrderCompletionHandler) ForceCompleteOrderByIDWithAdmin(c *gin.Context) {
	// 获取订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		h.logger.Error("无效的订单ID", zap.String("order_id", orderIDStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的订单ID",
		})
		return
	}

	// 获取管理员ID（从JWT中获取）
	adminID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("未找到管理员ID")
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未登录",
		})
		return
	}

	var req ForceCompleteByAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("请求参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
		})
		return
	}

	h.logger.Info("管理员强制完成订单",
		zap.Uint64("order_id", orderID),
		zap.Any("admin_id", adminID),
		zap.String("reason", req.Reason),
	)

	// 调用OrderCompletionService
	response, err := h.orderCompletionService.ForceCompleteByAdmin(c.Request.Context(), uint(orderID), adminID.(uint), req.Reason)
	if err != nil {
		h.logger.Error("管理员强制完成订单失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "订单强制完成失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": response.Message,
		"data":    response,
	})
}

// CompleteOrderByExceptionHandlerWithID 异常处理完成订单（带订单ID参数）
// @Summary 异常处理完成订单（带订单ID参数）
// @Description 异常处理系统通过订单ID完成订单，自动审核通过
// @Tags 订单完成
// @Accept json
// @Produce json
// @Param id path uint true "订单ID"
// @Param request body CompleteByExceptionHandlerWithIDRequest true "异常处理完成订单请求"
// @Success 200 {object} Response{data=string}
// @Router /api/admin/orders/{id}/exception-complete [post]
func (h *OrderCompletionHandler) CompleteOrderByExceptionHandlerWithID(c *gin.Context) {
	// 获取订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		h.logger.Error("无效的订单ID", zap.String("order_id", orderIDStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的订单ID",
		})
		return
	}

	// 获取管理员ID（从JWT中获取）
	adminID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("未找到管理员ID")
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "未登录",
		})
		return
	}

	var req CompleteByExceptionHandlerWithIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("请求参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
		})
		return
	}

	h.logger.Info("异常处理完成订单",
		zap.Uint64("order_id", orderID),
		zap.Any("admin_id", adminID),
		zap.String("handler_id", req.HandlerID),
		zap.String("reason", req.Reason),
	)

	// 调用OrderCompletionService
	response, err := h.orderCompletionService.CompleteByExceptionHandler(c.Request.Context(), uint(orderID), req.HandlerID, req.Reason)
	if err != nil {
		h.logger.Error("异常处理完成订单失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "订单异常处理完成失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": response.Message,
		"data":    response,
	})
}

// AutoCompleteOrderByID 系统自动完成订单（带订单ID参数）
// @Summary 系统自动完成订单（带订单ID参数）
// @Description 系统通过订单ID自动完成订单，自动审核通过
// @Tags 订单完成
// @Accept json
// @Produce json
// @Param id path uint true "订单ID"
// @Param request body AutoCompleteWithIDRequest true "自动完成订单请求"
// @Success 200 {object} Response{data=string}
// @Router /api/admin/orders/{id}/auto-complete [post]
func (h *OrderCompletionHandler) AutoCompleteOrderByID(c *gin.Context) {
	// 获取订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		h.logger.Error("无效的订单ID", zap.String("order_id", orderIDStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的订单ID",
		})
		return
	}

	var req AutoCompleteWithIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("请求参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的请求参数",
		})
		return
	}

	h.logger.Info("系统自动完成订单",
		zap.Uint64("order_id", orderID),
		zap.String("reason", req.Reason),
	)

	// 调用OrderCompletionService
	response, err := h.orderCompletionService.AutoComplete(c.Request.Context(), uint(orderID), req.Reason)
	if err != nil {
		h.logger.Error("系统自动完成订单失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "订单自动完成失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": response.Message,
		"data":    response,
	})
}

// GetOrderCompletion 获取订单完成记录
// @Summary 获取订单完成记录
// @Description 根据订单ID获取完成记录详情
// @Tags 订单完成
// @Accept json
// @Produce json
// @Param id path uint true "订单ID"
// @Success 200 {object} Response{data=object}
// @Router /api/admin/orders/{id}/completion [get]
func (h *OrderCompletionHandler) GetOrderCompletion(c *gin.Context) {
	// 获取订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		h.logger.Error("无效的订单ID", zap.String("order_id", orderIDStr), zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "无效的订单ID",
		})
		return
	}

	h.logger.Info("获取订单完成记录", zap.Uint64("order_id", orderID))

	// 调用OrderCompletionService获取完成记录
	response, err := h.orderCompletionService.GetOrderCompletion(c.Request.Context(), uint(orderID))
	if err != nil {
		h.logger.Error("获取订单完成记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取订单完成记录失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    response,
	})
}

// GetPendingCompletionOrders 获取待完成订单列表
// @Summary 获取待完成订单列表
// @Description 获取需要管理员处理的待完成订单列表
// @Tags 订单完成
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]object}
// @Router /api/admin/orders/pending-completion [get]
func (h *OrderCompletionHandler) GetPendingCompletionOrders(c *gin.Context) {
	h.logger.Info("获取待完成订单列表")

	// 调用OrderCompletionService获取待完成订单
	response, err := h.orderCompletionService.GetPendingCompletionOrders(c.Request.Context(), 100)
	if err != nil {
		h.logger.Error("获取待完成订单列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取待完成订单列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "获取成功",
		"data":    response,
	})
}

// 请求结构体定义（使用服务层的结构体）
type ForceCompleteByAdminRequest = service.ForceCompleteByAdminRequest

type CompleteByExceptionHandlerWithIDRequest struct {
	HandlerID string `json:"handler_id" binding:"required"`
	Reason    string `json:"reason" binding:"required,max=500"`
}

type AutoCompleteWithIDRequest struct {
	Reason string `json:"reason" binding:"required,max=500"`
}
