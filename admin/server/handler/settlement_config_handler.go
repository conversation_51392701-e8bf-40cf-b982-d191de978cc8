package handler

import (
	"context"
	"net/http"
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SettlementConfigHandler 结算配置处理器
type SettlementConfigHandler struct {
	systemConfigService service.ISystemConfigService
	logger              *zap.Logger
}

// NewSettlementConfigHandler 创建结算配置处理器
func NewSettlementConfigHandler(
	systemConfigService service.ISystemConfigService,
	logger *zap.Logger,
) *SettlementConfigHandler {
	return &SettlementConfigHandler{
		systemConfigService: systemConfigService,
		logger:              logger,
	}
}

// GetSettlementConfig 获取结算配置
// @Summary 获取结算配置
// @Description 获取T+N结算天数、冻结期天数等结算相关配置
// @Tags 结算配置
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功"
// @Router /api/admin/settlement/config [get]
func (h *SettlementConfigHandler) GetSettlementConfig(c *gin.Context) {
	// 获取各项配置
	config := map[string]interface{}{
		"settlement_review_period_days":  h.getConfigValue(c.Request.Context(), "settlement.review_period_days", "2"),
		"settlement_review_period_hours": h.getConfigValue(c.Request.Context(), "settlement.review_period_hours", "48"),
		"finance_freeze_days":            h.getConfigValue(c.Request.Context(), "finance.freeze_days", "7"),
		"dispute_timeout_days":           h.getConfigValue(c.Request.Context(), "settlement.dispute_timeout_days", "7"),
		"min_auto_settlement_amount":     h.getConfigValue(c.Request.Context(), "settlement.min_auto_amount", "0.01"),
		"max_auto_settlement_amount":     h.getConfigValue(c.Request.Context(), "settlement.max_auto_amount", "10000.00"),
		"auto_settlement_enabled":        h.getConfigValue(c.Request.Context(), "settlement.auto_settlement_enabled", "true"),
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    config,
	})
}

// UpdateSettlementConfig 更新结算配置
// @Summary 更新结算配置
// @Description 更新结算相关配置项
// @Tags 结算配置
// @Accept json
// @Produce json
// @Param data body UpdateSettlementConfigRequest true "配置更新请求"
// @Success 200 {object} map[string]interface{} "成功"
// @Router /api/admin/settlement/config [post]
func (h *SettlementConfigHandler) UpdateSettlementConfig(c *gin.Context) {
	var req UpdateSettlementConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	ctx := c.Request.Context()
	operatorID := h.getOperatorID(c)

	// 验证和更新各项配置
	updates := make(map[string]string)

	// T+N结算天数
	if req.SettlementReviewPeriodDays != nil {
		days := *req.SettlementReviewPeriodDays
		if days < 1 || days > 30 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "T+N结算天数必须在1-30天之间",
			})
			return
		}
		updates["settlement.review_period_days"] = strconv.Itoa(days)
	}

	// 审核期限小时数
	if req.SettlementReviewPeriodHours != nil {
		hours := *req.SettlementReviewPeriodHours
		if hours < 1 || hours > 720 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "审核期限必须在1-720小时之间",
			})
			return
		}
		updates["settlement.review_period_hours"] = strconv.Itoa(hours)
	}

	// 冻结期天数
	if req.FinanceFreezeDays != nil {
		days := *req.FinanceFreezeDays
		if days < 0 || days > 365 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "冻结期天数必须在0-365天之间",
			})
			return
		}
		updates["finance.freeze_days"] = strconv.Itoa(days)
	}

	// 争议超时天数
	if req.DisputeTimeoutDays != nil {
		days := *req.DisputeTimeoutDays
		if days < 1 || days > 30 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "争议超时天数必须在1-30天之间",
			})
			return
		}
		updates["settlement.dispute_timeout_days"] = strconv.Itoa(days)
	}

	// 最小自动结算金额
	if req.MinAutoSettlementAmount != nil {
		amount := *req.MinAutoSettlementAmount
		if amount < 0.01 || amount > 1000 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "最小自动结算金额必须在0.01-1000元之间",
			})
			return
		}
		updates["settlement.min_auto_amount"] = strconv.FormatFloat(amount, 'f', 2, 64)
	}

	// 最大自动结算金额
	if req.MaxAutoSettlementAmount != nil {
		amount := *req.MaxAutoSettlementAmount
		if amount < 1 || amount > 100000 {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "最大自动结算金额必须在1-100000元之间",
			})
			return
		}
		updates["settlement.max_auto_amount"] = strconv.FormatFloat(amount, 'f', 2, 64)
	}

	// 自动结算开关
	if req.AutoSettlementEnabled != nil {
		updates["settlement.auto_settlement_enabled"] = strconv.FormatBool(*req.AutoSettlementEnabled)
	}

	// 逐个更新配置
	if len(updates) > 0 {
		for key, value := range updates {
			err := h.systemConfigService.SetConfigValue(ctx, key, value, operatorID)
			if err != nil {
				h.logger.Error("更新结算配置失败",
					zap.String("key", key),
					zap.String("value", value),
					zap.Error(err))
				c.JSON(http.StatusInternalServerError, gin.H{
					"code":    500,
					"message": "更新配置失败: " + err.Error(),
				})
				return
			}
		}

		h.logger.Info("结算配置更新成功",
			zap.Uint("operator_id", operatorID),
			zap.String("reason", req.Reason),
			zap.Any("updates", updates))
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新成功",
		"data": gin.H{
			"updated_count": len(updates),
		},
	})
}

// GetSettlementConfigHistory 获取结算配置变更历史
// @Summary 获取结算配置变更历史
// @Description 获取结算配置的变更历史记录
// @Tags 结算配置
// @Accept json
// @Produce json
// @Param limit query int false "限制数量" default(20)
// @Success 200 {object} map[string]interface{} "成功"
// @Router /api/admin/settlement/config/history [get]
func (h *SettlementConfigHandler) GetSettlementConfigHistory(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 由于简化后的系统配置服务不支持历史记录，返回空数据
	allHistory := make([]interface{}, 0)

	h.logger.Info("获取结算配置历史记录",
		zap.Int("limit", limit),
		zap.String("note", "简化版系统配置不支持历史记录"))

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"history": allHistory,
			"total":   len(allHistory),
		},
	})
}

// UpdateSettlementConfigRequest 更新结算配置请求
type UpdateSettlementConfigRequest struct {
	SettlementReviewPeriodDays  *int     `json:"settlement_review_period_days"`  // T+N结算天数
	SettlementReviewPeriodHours *int     `json:"settlement_review_period_hours"` // 审核期限小时数
	FinanceFreezeDays           *int     `json:"finance_freeze_days"`            // 冻结期天数
	DisputeTimeoutDays          *int     `json:"dispute_timeout_days"`           // 争议超时天数
	MinAutoSettlementAmount     *float64 `json:"min_auto_settlement_amount"`     // 最小自动结算金额
	MaxAutoSettlementAmount     *float64 `json:"max_auto_settlement_amount"`     // 最大自动结算金额
	AutoSettlementEnabled       *bool    `json:"auto_settlement_enabled"`        // 自动结算开关
	Reason                      string   `json:"reason"`                         // 变更原因
}

// getConfigValue 获取配置值，如果失败则返回默认值
func (h *SettlementConfigHandler) getConfigValue(ctx context.Context, key, defaultValue string) string {
	value, err := h.systemConfigService.GetConfigValue(ctx, key)
	if err != nil {
		h.logger.Debug("获取配置失败，使用默认值",
			zap.String("key", key),
			zap.String("default", defaultValue),
			zap.Error(err))
		return defaultValue
	}
	if strValue, ok := value.(string); ok {
		return strValue
	}
	return defaultValue
}

// getOperatorID 获取操作员ID
func (h *SettlementConfigHandler) getOperatorID(c *gin.Context) uint {
	// 从JWT token或session中获取操作员ID
	// 这里简化处理，实际应该从认证中间件获取
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id
		}
	}
	return 1 // 默认管理员ID
}
