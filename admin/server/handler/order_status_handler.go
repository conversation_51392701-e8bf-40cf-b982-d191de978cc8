package handler

import (
	"net/http"
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
	"github.com/gin-gonic/gin"
)

// OrderStatusHandler 订单状态处理器
type OrderStatusHandler struct {
	orderStatusService service.IOrderStatusService
}

// NewOrderStatusHandler 创建订单状态处理器
func NewOrderStatusHandler(orderStatusService service.IOrderStatusService) *OrderStatusHandler {
	return &OrderStatusHandler{
		orderStatusService: orderStatusService,
	}
}

// GetOrderStatusHistory 获取订单状态历史
func (h *OrderStatusHandler) GetOrderStatusHistory(c *gin.Context) {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的订单ID")
		return
	}

	history, err := h.orderStatusService.GetOrderStatusHistory(c, uint(orderID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取状态历史失败: "+err.Error())
		return
	}

	response.Success(c, history)
}

// GetOrderCurrentStatus 获取订单当前状态
func (h *OrderStatusHandler) GetOrderCurrentStatus(c *gin.Context) {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的订单ID")
		return
	}

	status, err := h.orderStatusService.GetOrderCurrentStatus(c, uint(orderID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取订单状态失败: "+err.Error())
		return
	}

	response.Success(c, status)
}

// UpdateOrderStatus 更新订单状态
func (h *OrderStatusHandler) UpdateOrderStatus(c *gin.Context) {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的订单ID")
		return
	}

	var req dto.OrderStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	req.OrderID = uint(orderID)

	if err := h.orderStatusService.UpdateOrderStatus(c, &req); err != nil {
		response.Error(c, http.StatusInternalServerError, "更新订单状态失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// UpdateMatchingStatus 更新匹配状态
func (h *OrderStatusHandler) UpdateMatchingStatus(c *gin.Context) {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的订单ID")
		return
	}

	var req dto.MatchingStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	req.OrderID = uint(orderID)

	if err := h.orderStatusService.UpdateMatchingStatus(c, &req); err != nil {
		response.Error(c, http.StatusInternalServerError, "更新匹配状态失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// HandleMatchingFailure 处理匹配失败
func (h *OrderStatusHandler) HandleMatchingFailure(c *gin.Context) {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的订单ID")
		return
	}

	var req dto.MatchingFailureProcessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	req.OrderID = uint(orderID)

	if err := h.orderStatusService.HandleMatchingFailure(c, uint(orderID), req.Reason); err != nil {
		response.Error(c, http.StatusInternalServerError, "处理匹配失败: "+err.Error())
		return
	}

	response.Success(c, "匹配失败处理成功")
}

// ValidateStatusTransition 验证状态转换
func (h *OrderStatusHandler) ValidateStatusTransition(c *gin.Context) {
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseUint(orderIDStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的订单ID")
		return
	}

	newStatusStr := c.Query("new_status")
	newStatus, err := strconv.Atoi(newStatusStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "无效的状态值")
		return
	}

	if err := h.orderStatusService.ValidateStatusTransition(c, uint(orderID), newStatus); err != nil {
		response.Error(c, http.StatusBadRequest, "状态转换不合法: "+err.Error())
		return
	}

	response.Success(c, "状态转换合法")
}

// GetStatusStats 获取状态统计
func (h *OrderStatusHandler) GetStatusStats(c *gin.Context) {
	var req dto.StatusStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	stats, err := h.orderStatusService.GetStatusChangeStats(c, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "获取状态统计失败: "+err.Error())
		return
	}

	response.Success(c, stats)
}

// ProcessTimeoutOrders 处理超时订单
func (h *OrderStatusHandler) ProcessTimeoutOrders(c *gin.Context) {
	var req dto.OrderTimeoutProcessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	var count int64
	var err error

	if req.TimeoutMinutes > 0 {
		count, err = h.orderStatusService.ProcessUnpaidOrders(c, req.TimeoutMinutes)
	} else {
		count, err = h.orderStatusService.HandleTimeoutOrders(c)
	}

	if err != nil {
		response.Error(c, http.StatusInternalServerError, "处理超时订单失败: "+err.Error())
		return
	}

	response.Success(c, map[string]interface{}{
		"processed_count": count,
		"message":         "处理超时订单成功",
	})
}

// ProcessMatchingFailedOrders 处理匹配失败订单
func (h *OrderStatusHandler) ProcessMatchingFailedOrders(c *gin.Context) {
	count, err := h.orderStatusService.ProcessMatchingFailedOrders(c)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "处理匹配失败订单失败: "+err.Error())
		return
	}

	response.Success(c, map[string]interface{}{
		"processed_count": count,
		"message":         "处理匹配失败订单成功",
	})
}
