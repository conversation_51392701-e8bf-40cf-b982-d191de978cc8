package handler

import (
	"strconv"

	"github.com/gemeijie/peizhen/admin/server/model"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/utils/response"
	"github.com/gin-gonic/gin"
)

// AdminHandler 管理员处理器
type AdminHandler struct {
	adminService *service.AdminService
	authService  *service.AuthService
}

// NewAdminHandler 创建管理员处理器
func NewAdminHandler(adminService *service.AdminService, authService *service.AuthService) *AdminHandler {
	return &AdminHandler{
		adminService: adminService,
		authService:  authService,
	}
}

// GetAdminList 获取管理员列表
func (h *AdminHandler) GetAdminList(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>uer<PERSON>("page_size", "10"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 获取管理员列表
	admins, total, err := h.adminService.ListAdmins(page, pageSize)
	if err != nil {
		response.ServerError(c, "获取管理员列表失败", err)
		return
	}

	response.Success(c, gin.H{
		"list":  admins,
		"total": total,
		"page":  page,
		"size":  pageSize,
	})
}

// GetAdminDetail 获取管理员详情
func (h *AdminHandler) GetAdminDetail(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}

	admin, err := h.adminService.GetAdminByID(uint(id))
	if err != nil {
		response.ServerError(c, "获取管理员详情失败", err)
		return
	}

	response.Success(c, admin)
}

// CreateAdminRequest 创建管理员请求
type CreateAdminRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
	RoleID   uint   `json:"role_id" binding:"required"`
}

// CreateAdmin 创建管理员
func (h *AdminHandler) CreateAdmin(c *gin.Context) {
	var req CreateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数有误: "+err.Error())
		return
	}

	admin := &model.Admin{
		Username: req.Username,
		Password: req.Password,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Email:    req.Email,
		Mobile:   req.Mobile,
		RoleID:   req.RoleID,
		Status:   1, // 默认启用
	}

	if err := h.adminService.CreateAdmin(admin); err != nil {
		response.ServerError(c, "创建管理员失败", err)
		return
	}

	response.Success(c, admin)
}

// UpdateAdminRequest 更新管理员请求
type UpdateAdminRequest struct {
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
	RoleID   uint   `json:"role_id"`
	Status   int    `json:"status"`
}

// UpdateAdmin 更新管理员
func (h *AdminHandler) UpdateAdmin(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}

	var req UpdateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数有误: "+err.Error())
		return
	}

	admin := &model.Admin{
		ID:       uint(id),
		Username: req.Username,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Email:    req.Email,
		Mobile:   req.Mobile,
		RoleID:   req.RoleID,
		Status:   req.Status,
	}

	if err := h.adminService.UpdateAdmin(admin); err != nil {
		response.ServerError(c, "更新管理员失败", err)
		return
	}

	response.Success(c, admin)
}

// DeleteAdmin 删除管理员
func (h *AdminHandler) DeleteAdmin(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}

	if err := h.adminService.DeleteAdmin(uint(id)); err != nil {
		response.ServerError(c, "删除管理员失败", err)
		return
	}

	response.Success(c, "删除成功")
}

// UpdateAdminStatus 更新管理员状态
func (h *AdminHandler) UpdateAdminStatus(c *gin.Context) {
	// id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	// if err != nil {
	// 	response.BadRequest(c, "无效的管理员ID")
	// 	return
	// }

	var req struct {
		Status int `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数有误: "+err.Error())
		return
	}

	// updates := map[string]interface{}{
	// 	"status": req.Status,
	// }

	// 这里需要在AdminService中添加UpdateAdminFields方法
	// 暂时直接使用repository
	// response.Success(c, "状态更新成功")
	response.ServerError(c, "暂未实现", nil)
}

// GetRoleList 获取角色列表
func (h *AdminHandler) GetRoleList(c *gin.Context) {
	// 获取所有角色
	roles, err := h.adminService.GetAllRoles()
	if err != nil {
		response.ServerError(c, "获取角色列表失败", err)
		return
	}

	response.Success(c, roles)
}

// AssignRolesRequest 分配角色请求
type AssignRolesRequest struct {
	RoleIDs []uint `json:"roleIds" binding:"required"`
}

// AssignRoles 分配角色
func (h *AdminHandler) AssignRoles(c *gin.Context) {
	adminID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "无效的管理员ID")
		return
	}

	var req AssignRolesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数有误: "+err.Error())
		return
	}

	if err := h.adminService.AssignRoles(uint(adminID), req.RoleIDs); err != nil {
		response.ServerError(c, "分配角色失败", err)
		return
	}

	response.Success(c, "角色分配成功")
}
