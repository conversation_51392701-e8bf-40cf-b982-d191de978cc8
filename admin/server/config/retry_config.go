package config

import "time"

// RetryConfig 重试配置
type RetryConfig struct {
	// 最大重试次数
	MaxRetryCount int `yaml:"max_retry_count" json:"max_retry_count"`

	// 基础重试间隔
	RetryInterval time.Duration `yaml:"retry_interval" json:"retry_interval"`

	// 退避因子（每次重试间隔乘以此因子）
	BackoffFactor float64 `yaml:"backoff_factor" json:"backoff_factor"`

	// 最大重试延迟
	MaxRetryDelay time.Duration `yaml:"max_retry_delay" json:"max_retry_delay"`

	// 重试检查间隔
	CheckInterval time.Duration `yaml:"check_interval" json:"check_interval"`

	// 是否启用重试机制
	Enabled bool `yaml:"enabled" json:"enabled"`
}

// GetDefaultRetryConfig 获取默认重试配置
func GetDefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetryCount: 3,                // 最多重试3次
		RetryInterval: 30 * time.Second, // 基础间隔30秒
		BackoffFactor: 2.0,              // 指数退避，间隔翻倍
		MaxRetryDelay: 10 * time.Minute, // 最大延迟10分钟
		CheckInterval: 1 * time.Minute,  // 每分钟检查一次
		Enabled:       true,             // 默认启用
	}
}

// CalculateRetryDelay 计算重试延迟时间
func (c *RetryConfig) CalculateRetryDelay(retryCount int) time.Duration {
	if retryCount <= 0 {
		return c.RetryInterval
	}

	// 指数退避算法：baseInterval * (backoffFactor ^ retryCount)
	delay := c.RetryInterval
	for i := 0; i < retryCount; i++ {
		delay = time.Duration(float64(delay) * c.BackoffFactor)
	}

	// 不超过最大延迟
	if delay > c.MaxRetryDelay {
		delay = c.MaxRetryDelay
	}

	return delay
}

// ShouldRetry 判断是否应该重试
func (c *RetryConfig) ShouldRetry(retryCount int) bool {
	return c.Enabled && retryCount < c.MaxRetryCount
}
