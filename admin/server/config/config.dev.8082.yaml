# 🔧 开发环境配置（修复版）
# 解决viper环境变量冲突问题

# 服务器配置
server:
  address: ":8082"
  mode: "debug"
  port: 8081

# 数据库配置（开发环境）
database:
  host: "127.0.0.1"
  port: 3306
  username: "care_go_dev"
  password: "4F7ADD93-52BE-48D1-9E0E-0494020FE220"
  database: "carego"

# JWT配置（开发环境）
jwt:
  secret: "carego_admin_dev_secret_key"
  expire_days: 30
  refresh_days: 60

# 后端配置
backend:
  base_url: "http://localhost:8000/api"
  token: ""

# Redis配置（开发环境）
redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0

# 微信配置（开发环境 - 测试模式）
wechat:
  app_id: "wx2ce88b500238c799"
  mch_id: "1717184423"
  apiv3_key: "0C9821B2517645438B93B1B21CC62901"
  serial_no: "3B2F1BB6FBF9CD4D2448AB6310720C15CD668247"
  public_key_id: "PUB_KEY_ID_0117171844232025052600452088000602"
  pay_notify_url: "http://localhost:8082/api/wechat/notify/payment"
  refund_notify_url: "http://localhost:8082/api/wechat/notify/refund"
  
  # 🔒 证书文件路径（优先使用环境变量，否则使用本地路径）
  private_key_file: "${WECHAT_PRIVATE_KEY_PATH:./certs/wechat_private_key.pem}"
  cert_file: "${WECHAT_PUBLIC_KEY_PATH:./certs/wechat_public_key.pem}"
  
  # 开发环境配置
  environment: "dev"
  dev_amount: 1
  refund_enabled: true              # 开发环境启用退款功能用于测试
  use_real_api: false              # 开发环境使用模拟API
  test_mode: true                  # 开发环境启用测试模式
  
  # SDK配置模式
  use_public_key_mode: true        # 使用公钥验证模式
  use_auto_cert_mode: false        # 不使用自动证书下载模式
  
  # 测试用户配置
  test_user_ids: [1, 2, 3]         # 开发环境测试用户ID
  max_test_amount: 100.0           # 开发环境最大测试金额

# CORS配置（开发环境）
cors:
  allow_origins: "*"
  allow_methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allow_headers:
    - Authorization
    - Content-Type
    - X-Requested-With

# 日志配置（开发环境）
log:
  level: info  # 从debug改为info，减少日志量
  filename: logs/admin_dev.log
  max_size: 50
  max_backups: 5
  max_age: 7
  compress: false

# 订单超时配置（开发环境）
order_timeout:
  check_interval: "5m"                     # 开发环境更频繁检查
  unpaid_timeout_minutes: 15               # 开发环境较短超时
  paid_timeout_minutes: 60                 # 开发环境较短超时
  matching_timeout_minutes: 10             # 开发环境较短匹配超时
  protection_window_minutes: 15            # 开发环境较短保护窗口
  manual_operation_timeout_minutes: 15     # 开发环境较短手动操作保护期
  enabled: true

# 退款审批配置
refund_approval:
  auto_approve_enabled: false
  auto_approve_limit: 50.0
  force_manual_approval: true
