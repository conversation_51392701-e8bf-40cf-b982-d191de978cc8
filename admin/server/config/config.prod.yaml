# 🔧 生产环境配置（管理后台）
# 与后端API认证配置保持一致

# 服务器配置
server:
  address: ":8081"
  mode: "release"
  port: 8081

# 数据库配置（生产环境）
database:
  host: "${DB_HOST}"
  port: 3306
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  database: "${DB_NAME}"

# JWT配置（生产环境）
jwt:
  secret: "${JWT_SECRET}"
  expire_days: 7
  refresh_days: 14

# 后端配置（生产环境）
backend:
  base_url: "https://www.kanghuxing.cn"
  token: ""
  # 🔧 Backend API配置（与后端保持一致）
  api:
    base_url: "https://www.kanghuxing.cn"
    timeout: 30
    api_key:
      key_id: "${BACKEND_API_KEY_ID}"
      secret_key: "${BACKEND_API_SECRET_KEY}"
      algorithm: "${BACKEND_API_ALGORITHM:HMAC-SHA256}"
      ttl: 300

# URL配置（生产环境）
urls:
  api_base: "https://www.kanghuxing.cn"
  admin_base: "https://admin.kanghuxing.cn"
  frontend_base: "https://www.kanghuxing.cn"
  miniapp_base: "https://www.kanghuxing.cn"
  backend_base: "https://www.kanghuxing.cn"
  upload_domain: "https://www.kanghuxing.cn"

# Redis配置（生产环境）
redis:
  host: "${REDIS_HOST}"
  port: 6379
  password: "${REDIS_PASSWORD}"
  database: 0

# 微信配置（生产环境）
wechat:
  app_id: "${WECHAT_APP_ID}"
  mch_id: "${WECHAT_MCH_ID}"
  apiv3_key: "${WECHAT_API_V3_KEY}"
  serial_no: "${WECHAT_SERIAL_NO}"
  public_key_id: "${WECHAT_PUBLIC_KEY_ID}"
  pay_notify_url: "https://www.kanghuxing.cn/api/wechat/notify/payment"
  refund_notify_url: "https://www.kanghuxing.cn/api/wechat/notify/refund"
  
  # 🔒 证书文件路径（生产环境）
  private_key_file: "${WECHAT_PRIVATE_KEY_PATH}"
  cert_file: "${WECHAT_PUBLIC_KEY_PATH}"
  
  # 生产环境配置
  environment: "production"
  dev_amount: 0
  refund_enabled: true
  use_real_api: true                # 生产环境使用真实API
  test_mode: false                  # 生产环境关闭测试模式
  
  # SDK配置模式
  use_public_key_mode: true
  use_auto_cert_mode: true
  
  # 生产环境限制
  max_test_amount: 0.0              # 生产环境不允许测试金额

# CORS配置（生产环境）
cors:
  allow_origins:
    - "https://admin.kanghuxing.cn"
    - "https://www.kanghuxing.cn"
  allow_methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allow_headers:
    - Authorization
    - Content-Type
    - X-Requested-With

# 日志配置（生产环境）
log:
  level: info
  filename: logs/admin_prod.log
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true

# 订单超时配置（生产环境）
order_timeout:
  check_interval: "1m"
  unpaid_timeout_minutes: 30
  paid_timeout_minutes: 300
  matching_timeout_minutes: 5
  protection_window_minutes: 30
  manual_operation_timeout_minutes: 30
  enabled: true

# 退款审批配置（生产环境）
refund_approval:
  auto_approve_enabled: false
  auto_approve_limit: 0.0           # 生产环境不自动审批
  force_manual_approval: true       # 生产环境强制手动审批

# 安全配置（生产环境）
security:
  enable_https_redirect: true
  session_timeout: 3600
  max_request_size: 10485760
  
  # 调试接口保护（生产环境）
  debug_protection:
    enabled: true
    require_admin_role: true
    require_ip_whitelist: false