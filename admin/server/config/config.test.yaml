# ============================================
# 测试环境配置 - 会覆盖 config.yaml 中的相同配置
# ============================================

server:
  address: ":8081"
  mode: "test"                    # 🧪 测试模式
  port: 8081

database:
  host: "test-db.kanghuxing.cn"   # 🧪 测试数据库
  port: 3306
  username: "${TEST_DB_USER}"
  password: "${TEST_DB_PASSWORD}"
  database: "${TEST_DB_NAME}"

# 🌐 域名配置
domains:
  main_domain: "test.kanghuxing.cn"
  api_domain: "api-test.kanghuxing.cn"
  admin_domain: "admin-test.kanghuxing.cn"
  miniapp_domain: "test.kanghuxing.cn"

# 🔗 URL配置
urls:
  api_base: "https://api-test.kanghuxing.cn"
  admin_base: "https://admin-test.kanghuxing.cn"
  miniapp_base: "https://test.kanghuxing.cn"
  backend_base: "https://api-test.kanghuxing.cn"
  upload_domain: "https://test.kanghuxing.cn"

# 📞 回调地址配置
callbacks:
  wechat_pay_notify: "https://admin-test.kanghuxing.cn/api/wechat/notify/payment"
  wechat_refund_notify: "https://admin-test.kanghuxing.cn/api/wechat/notify/refund"

# 📧 邮箱配置
emails:
  system: "<EMAIL>"
  admin: "<EMAIL>"
  support: "<EMAIL>"

# 🔌 端口配置
ports:
  backend: 8080
  admin_server: 8081
  admin_web: 3001
  mysql: 3306
  redis: 6379

# 🌐 CORS配置
cors:
  allow_origins:
    - "https://test.kanghuxing.cn"
    - "https://api-test.kanghuxing.cn"
  allow_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allow_headers:
    - "Content-Type"
    - "Authorization"

# 🔐 API配置
api:
  prefix: "/api/admin"
  timeout: "30s"
  rate_limit: 1000

# 🛡️ 安全配置
security:
  session_timeout: "2h"
  password_min_length: 8
  max_login_attempts: 10
  lockout_duration: "30m"

wechat:
  # 注意：微信商户配置继承自 config.yaml
  environment: "testing"           # 🧪 测试环境标识
  dev_amount: 1
  refund_enabled: true            # 🧪 测试环境启用退款功能
  use_real_api: false             # 🧪 测试环境使用模拟API
  
  # 🧪 测试环境安全控制
  test_mode: true                 # 启用测试模式
  test_user_ids: [1, 2, 3, 4, 5] # 🧪 测试环境测试用户ID列表
  max_test_amount: 50.0           # 🧪 最大测试金额50元
  
  # 微信回调地址
  pay_notify_url: "https://admin-test.kanghuxing.cn/api/wechat/notify/payment"
  refund_notify_url: "https://admin-test.kanghuxing.cn/api/wechat/notify/refund"

refund_approval:
  auto_approve_enabled: true      # 测试环境启用自动审批
  auto_approve_limit: 50.0        # 🧪 测试环境阈值
  force_manual_approval: false    # 测试环境根据配置决定审批方式

# 测试环境日志配置
log:
  level: "debug"                  # 🧪 测试环境使用debug级别
  filename: "/var/log/app-test/app.log" # 🧪 测试环境日志路径 