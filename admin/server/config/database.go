package config

import (
	"log"

	"github.com/gemeijie/peizhen/admin/server/model"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDatabase 初始化数据库连接
func InitDatabase() (*gorm.DB, error) {
	// 从配置文件获取数据库配置
	cfg := GetConfig()
	dsn := cfg.Database.DSN()

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		return nil, err
	}

	// 测试连接
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	if err := sqlDB.Ping(); err != nil {
		return nil, err
	}

	// 自动迁移数据表
	err = db.AutoMigrate(
		&model.IncomeRule{},
		&model.IncomeRuleHistory{},
	)
	if err != nil {
		log.Printf("自动迁移数据表失败: %v", err)
		return nil, err
	}

	log.Println("Database connected successfully")
	log.Println("Auto migration completed")
	return db, nil
}
