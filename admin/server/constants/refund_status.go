package constants

// 退款状态常量定义
const (
	// 基础退款状态
	RefundStatusPending    = "pending"    // 待处理
	RefundStatusProcessing = "processing" // 处理中
	RefundStatusCompleted  = "completed"  // 已完成
	RefundStatusRejected   = "rejected"   // 已拒绝
	RefundStatusFailed     = "failed"     // 失败
	RefundStatusCancelled  = "cancelled"  // 已取消
)

// 退款类型常量定义
const (
	RefundTypeServiceFailed = 1 // 服务失败
	RefundTypeUserCancel    = 2 // 用户取消
	RefundTypeSpecialCase   = 3 // 特殊情况
	RefundTypeSystemError   = 4 // 系统错误
)

// 操作人类型常量定义
const (
	OperatorTypeUser   = 1 // 用户
	OperatorTypeAdmin  = 2 // 管理员
	OperatorTypeSystem = 3 // 系统
)

// 审批状态常量定义
const (
	ApprovalStatusPending  = "pending"  // 待审批
	ApprovalStatusApproved = "approved" // 已通过
	ApprovalStatusRejected = "rejected" // 已拒绝
)

// 渠道常量定义
const (
	ChannelWechat = "wechat" // 微信支付
	ChannelAlipay = "alipay" // 支付宝
	ChannelBank   = "bank"   // 银行卡
)

// GetRefundStatusText 获取退款状态文本
func GetRefundStatusText(status string) string {
	switch status {
	case RefundStatusPending:
		return "待处理"
	case RefundStatusProcessing:
		return "处理中"
	case RefundStatusCompleted:
		return "已完成"
	case RefundStatusRejected:
		return "已拒绝"
	case RefundStatusFailed:
		return "失败"
	case RefundStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// GetRefundTypeText 获取退款类型文本
func GetRefundTypeText(refundType int) string {
	switch refundType {
	case RefundTypeServiceFailed:
		return "服务失败"
	case RefundTypeUserCancel:
		return "用户取消"
	case RefundTypeSpecialCase:
		return "特殊情况"
	case RefundTypeSystemError:
		return "系统错误"
	default:
		return "未知类型"
	}
}

// GetOperatorTypeText 获取操作人类型文本
func GetOperatorTypeText(operatorType int) string {
	switch operatorType {
	case OperatorTypeUser:
		return "用户"
	case OperatorTypeAdmin:
		return "管理员"
	case OperatorTypeSystem:
		return "系统"
	default:
		return "未知"
	}
}

// GetApprovalStatusText 获取审批状态文本
func GetApprovalStatusText(status string) string {
	switch status {
	case ApprovalStatusPending:
		return "待审批"
	case ApprovalStatusApproved:
		return "已通过"
	case ApprovalStatusRejected:
		return "已拒绝"
	default:
		return "未知状态"
	}
}

// IsValidRefundStatus 验证退款状态是否有效
func IsValidRefundStatus(status string) bool {
	validStatuses := []string{
		RefundStatusPending,
		RefundStatusProcessing,
		RefundStatusCompleted,
		RefundStatusRejected,
		RefundStatusFailed,
		RefundStatusCancelled,
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// IsValidRefundType 验证退款类型是否有效
func IsValidRefundType(refundType int) bool {
	return refundType >= RefundTypeServiceFailed && refundType <= RefundTypeSystemError
}

// IsValidOperatorType 验证操作人类型是否有效
func IsValidOperatorType(operatorType int) bool {
	return operatorType >= OperatorTypeUser && operatorType <= OperatorTypeSystem
}
