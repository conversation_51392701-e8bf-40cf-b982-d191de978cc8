#!/bin/bash

# 手动结算测试 - 通过数据库操作模拟结算过程
# 这个脚本模拟T+2审核通过后的结算流程

echo "=== 手动结算测试 ==="

DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"
ORDER_ID=10017
ATTENDANT_ID=13

echo "1. 检查测试前状态..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    '测试前状态' as check_type,
    o.id as order_id,
    o.order_no,
    o.status as order_status,
    o.settlement_status,
    ai.status as income_status,
    CASE 
        WHEN o.status = 10 THEN '审核通过'
        WHEN o.status = 13 THEN '已结算'
        ELSE CONCAT('状态:', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 1 THEN '待结算'
        WHEN ai.status = 2 THEN '已结算'
        ELSE CONCAT('状态:', ai.status)
    END as income_status_text
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = $ATTENDANT_ID
WHERE o.id = $ORDER_ID;
"

echo -e "\n2. 模拟T+2审核通过..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
-- 更新T+2审核记录为通过状态
UPDATE order_settlement_reviews 
SET 
    review_status = 3,  -- 审核通过
    reviewed_at = NOW(),
    updated_at = NOW()
WHERE order_id = $ORDER_ID AND review_status = 1;
"

echo "✅ T+2审核记录已更新为通过状态"

echo -e "\n3. 模拟结算处理..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
-- 开始事务处理
START TRANSACTION;

-- 更新订单状态为已结算
UPDATE orders 
SET 
    status = 13,  -- 已结算
    settlement_status = 1,  -- 已结算
    settlement_time = NOW(),
    updated_at = NOW()
WHERE id = $ORDER_ID;

-- 更新收入记录状态为已结算
UPDATE attendant_income 
SET 
    status = 2,  -- 已结算
    settle_time = NOW(),
    updated_at = NOW()
WHERE attendant_id = $ATTENDANT_ID AND order_id = $ORDER_ID;

-- 提交事务
COMMIT;
"

echo "✅ 结算处理完成"

echo -e "\n4. 检查结算结果..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    '结算后状态' as check_type,
    o.id as order_id,
    o.order_no,
    o.status as order_status,
    o.settlement_status,
    o.settlement_time,
    ai.status as income_status,
    ai.settle_time,
    CASE 
        WHEN o.status = 10 THEN '⏳ 审核通过'
        WHEN o.status = 13 THEN '✅ 已结算'
        ELSE CONCAT('❓ 状态:', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 1 THEN '⏳ 待结算'
        WHEN ai.status = 2 THEN '✅ 已结算'
        ELSE CONCAT('❓ 状态:', ai.status)
    END as income_status_text
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = $ATTENDANT_ID
WHERE o.id = $ORDER_ID;
"

echo -e "\n5. 检查T+2审核记录状态..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    id,
    order_id,
    review_status,
    reviewed_at,
    CASE 
        WHEN review_status = 1 THEN '待审核'
        WHEN review_status = 2 THEN '审核中'
        WHEN review_status = 3 THEN '✅ 审核通过'
        ELSE CONCAT('状态:', review_status)
    END as review_status_text
FROM order_settlement_reviews 
WHERE order_id = $ORDER_ID;
"

echo -e "\n=== 手动结算测试完成 ==="
echo "预期结果："
echo "- 订单状态: 10 (审核通过) → 13 (已结算)"
echo "- 收入状态: 1 (待结算) → 2 (已结算)"
echo "- T+2审核: 1 (待审核) → 3 (审核通过)"
echo ""
echo "现在可以检查陪诊师收入页面，应该不再显示'正在结算中'"