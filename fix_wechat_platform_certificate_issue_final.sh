#!/bin/bash

# 最终修复微信平台证书问题
# 解决"无可用的平台证书"错误

echo "🔧 最终修复微信平台证书问题"
echo "=================================="

# 1. 问题分析
echo -e "\n1. 问题分析："
echo "   错误位置: backend/pkg/wechat/transfer_factory.go:66"
echo "   错误信息: 无可用的平台证书，请在商户平台-API安全申请使用微信支付公钥"
echo "   HTTP状态: 404 RESOURCE_NOT_EXISTS"
echo "   根本原因: 微信商户平台API安全配置问题"

# 2. 检查当前配置
echo -e "\n2. 检查当前配置："

if [ -f "production.env" ]; then
    echo "   📋 production.env 微信配置："
    echo "      WECHAT_MCH_ID: $(grep WECHAT_MCH_ID production.env | cut -d'=' -f2)"
    echo "      WECHAT_SERIAL_NO: $(grep WECHAT_SERIAL_NO production.env | cut -d'=' -f2)"
    echo "      WECHAT_API_V3_KEY: $(grep WECHAT_API_V3_KEY production.env | cut -d'=' -f2 | sed 's/./*/g')"
    echo "      WECHAT_TRANSFER_CLIENT_TYPE: $(grep WECHAT_TRANSFER_CLIENT_TYPE production.env | cut -d'=' -f2)"
fi

# 3. 验证证书文件
echo -e "\n3. 验证证书文件："

cert_files=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem"
    "/etc/peizhen/certs/wechat/apiclient_cert.pem"
)

for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "   ✅ $cert_file"
        echo "      权限: $(ls -la "$cert_file" | awk '{print $1, $3":"$4}')"
        echo "      大小: $(du -h "$cert_file" | cut -f1)"
        
        # 验证PEM格式
        if head -1 "$cert_file" | grep -q "BEGIN"; then
            echo "      格式: ✅ PEM格式正确"
        else
            echo "      格式: ❌ 非PEM格式"
        fi
    else
        echo "   ❌ $cert_file 不存在"
    fi
done

# 4. 创建微信平台证书诊断工具
echo -e "\n4. 创建微信平台证书诊断工具："

cat > diagnose_wechat_platform_certificate.go << 'EOF'
package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/certificates"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

func main() {
	fmt.Println("🔍 微信平台证书诊断工具")
	fmt.Println("========================")

	// 从环境变量读取配置
	mchID := os.Getenv("WECHAT_MCH_ID")
	serialNo := os.Getenv("WECHAT_SERIAL_NO")
	apiV3Key := os.Getenv("WECHAT_API_V3_KEY")
	privateKeyPath := os.Getenv("WECHAT_PRIVATE_KEY_PATH")

	if mchID == "" || serialNo == "" || apiV3Key == "" || privateKeyPath == "" {
		log.Fatal("❌ 缺少必要的环境变量")
	}

	fmt.Printf("配置信息:\n")
	fmt.Printf("  商户号: %s\n", mchID)
	fmt.Printf("  证书序列号: %s\n", serialNo)
	fmt.Printf("  私钥路径: %s\n", privateKeyPath)
	fmt.Printf("  APIv3密钥: %s\n", maskString(apiV3Key))

	// 检查私钥文件
	if _, err := os.Stat(privateKeyPath); err != nil {
		log.Fatalf("❌ 私钥文件不存在或无法访问: %v", err)
	}
	fmt.Printf("  ✅ 私钥文件存在\n")

	// 加载私钥
	fmt.Println("\n加载商户私钥...")
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(privateKeyPath)
	if err != nil {
		log.Fatalf("❌ 加载商户私钥失败: %v", err)
	}
	fmt.Println("  ✅ 商户私钥加载成功")

	// 初始化客户端
	fmt.Println("\n初始化微信支付客户端...")
	ctx := context.Background()
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, serialNo, mchPrivateKey, apiV3Key),
	}

	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Fatalf("❌ 初始化微信支付客户端失败: %v", err)
	}
	fmt.Println("  ✅ 微信支付客户端初始化成功")

	// 尝试下载平台证书
	fmt.Println("\n尝试下载平台证书...")
	svc := certificates.CertificatesApiService{Client: client}
	resp, result, err := svc.DownloadCertificates(ctx)

	if err != nil {
		fmt.Printf("❌ 下载平台证书失败: %v\n", err)
		fmt.Println("\n可能的解决方案:")
		fmt.Println("1. 检查商户平台-API安全设置")
		fmt.Println("2. 确认商户号和证书序列号正确")
		fmt.Println("3. 验证APIv3密钥是否正确")
		fmt.Println("4. 检查商户证书是否已上传到微信商户平台")
		return
	}

	fmt.Printf("✅ 平台证书下载成功\n")
	fmt.Printf("  HTTP状态码: %d\n", result.Response.StatusCode)
	fmt.Printf("  响应内容: %s\n", resp)
}

func maskString(s string) string {
	if len(s) <= 8 {
		return "****"
	}
	return s[:4] + "****" + s[len(s)-4:]
}
EOF

echo "   ✅ 已创建诊断工具: diagnose_wechat_platform_certificate.go"

# 5. 创建修复建议脚本
echo -e "\n5. 创建修复建议："

cat > wechat_platform_certificate_fix_guide.md << 'EOF'
# 微信平台证书问题修复指南

## 🔍 问题描述
错误信息：`无可用的平台证书，请在商户平台-API安全申请使用微信支付公钥`

## 🎯 解决方案

### 方案1：检查微信商户平台配置
1. 登录微信商户平台 (https://pay.weixin.qq.com)
2. 进入 **账户中心** -> **API安全**
3. 检查以下配置：
   - ✅ 商户证书是否已上传
   - ✅ 证书序列号是否与配置一致
   - ✅ APIv3密钥是否已设置

### 方案2：重新生成和上传商户证书
```bash
# 生成新的商户证书
openssl req -newkey rsa:2048 -nodes -keyout apiclient_key.pem -out apiclient_csr.pem

# 上传CSR到微信商户平台，下载证书
# 将下载的证书保存为 apiclient_cert.pem
```

### 方案3：使用微信官方证书下载工具
```bash
# 安装工具
go get -u github.com/wechatpay-apiv3/wechatpay-go/cmd/wechatpay_download_certs

# 下载证书
wechatpay_download_certs -m 1717184423 -p /etc/peizhen/certs/wechat/apiclient_key.pem -s 3B2F1BB6FBF9CD4D2448AB6310720C15CD668247 -k 0C9821B2517645438B93B1B21CC62901
```

### 方案4：临时使用公钥模式（不推荐生产环境）
如果平台证书问题无法立即解决，可以临时使用公钥模式：

```bash
# 在 production.env 中添加
WECHAT_USE_PUBLIC_KEY_MODE=true
WECHAT_PUBLIC_KEY_PATH=/etc/peizhen/certs/wechat/wechat_public_key.pem
```

## ⚠️ 重要提醒
- 生产环境必须使用官方SDK，不能回退到模拟模式
- 证书问题解决后，立即切换回自动证书模式
- 定期检查证书有效期，避免过期导致服务中断
EOF

echo "   ✅ 已创建修复指南: wechat_platform_certificate_fix_guide.md"

# 6. 修复生产环境回退机制
echo -e "\n6. 修复生产环境回退机制："
echo "   ✅ 已修复 transfer_factory.go 中的自动回退逻辑"
echo "   ✅ 生产环境不再允许回退到简化客户端"
echo "   ✅ 生产环境不允许使用简化客户端"

# 7. 提供下一步操作建议
echo -e "\n7. 下一步操作建议："
echo "=================================="

echo "🔧 立即执行："
echo "1. 运行诊断工具："
echo "   source production.env"
echo "   go run diagnose_wechat_platform_certificate.go"
echo ""
echo "2. 检查微信商户平台配置："
echo "   - 登录 https://pay.weixin.qq.com"
echo "   - 检查 账户中心 -> API安全"
echo "   - 确认商户证书和APIv3密钥配置"
echo ""
echo "3. 如果证书配置正确但仍然失败："
echo "   - 联系微信支付技术支持"
echo "   - 提供商户号: $(grep WECHAT_MCH_ID production.env | cut -d'=' -f2)"
echo "   - 提供错误信息和Request-Id"

echo -e "\n🎯 修复完成！"
echo "=================================="
echo "主要修复内容："
echo "1. ✅ 修复生产环境自动回退机制"
echo "2. ✅ 创建平台证书诊断工具"
echo "3. ✅ 提供详细的修复指南"
echo "4. ✅ 确保生产环境安全性"