# 服务时间字段分离设计文档

## 概述

本设计文档详细描述了如何解决订单预约时间与实际服务时间字段混用的问题。通过数据库字段重构、代码适配和界面更新，实现时间字段的清晰分离，确保数据准确性和业务逻辑正确性。

## 架构设计

### 数据库架构变更

#### 当前架构问题
```sql
-- 当前问题：service_time字段既用于预约时间，又用于实际服务时间
CREATE TABLE orders (
    id bigint PRIMARY KEY,
    service_time timestamp NOT NULL COMMENT '服务时间', -- 问题字段
    -- 其他字段...
);
```

#### 目标架构设计
```sql
-- 解决方案：字段分离，语义明确
CREATE TABLE orders (
    id bigint PRIMARY KEY,
    appointment_time timestamp NOT NULL COMMENT '用户预约的服务时间',
    actual_service_start_time timestamp NULL COMMENT '陪诊师实际开始服务时间',
    actual_service_end_time timestamp NULL COMMENT '陪诊师实际结束服务时间',
    -- 其他字段...
);
```

#### 字段映射关系
| 原字段 | 新字段 | 用途 | 数据来源 |
|--------|--------|------|----------|
| `service_time` | `appointment_time` | 用户预约时间 | 用户下单时设置，永不修改 |
| 无 | `actual_service_start_time` | 实际服务开始时间 | 陪诊师开始服务时记录 |
| 无 | `actual_service_end_time` | 实际服务结束时间 | 陪诊师结束服务时记录 |

### 系统组件设计

#### 1. 数据迁移组件
```mermaid
graph TD
    A[数据迁移脚本] --> B[备份现有数据]
    B --> C[重命名service_time为appointment_time]
    C --> D[添加新字段]
    D --> E[创建索引]
    E --> F[数据验证]
    F --> G[迁移完成]
```

#### 2. 后端服务架构
```mermaid
graph TD
    A[订单服务] --> B[时间管理服务]
    B --> C[预约时间管理]
    B --> D[实际服务时间管理]
    C --> E[数据库 - appointment_time]
    D --> F[数据库 - actual_service_start_time]
    D --> G[数据库 - actual_service_end_time]
```

#### 3. API设计架构
```mermaid
graph TD
    A[前端请求] --> B[API网关]
    B --> C[订单控制器]
    C --> D[订单服务]
    D --> E[时间字段转换器]
    E --> F[数据库访问层]
    F --> G[返回区分的时间字段]
```

## 组件和接口设计

### 1. 数据模型更新

#### Order模型更新
```go
// 更新前
type Order struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    ServiceTime time.Time `json:"service_time" gorm:"not null"` // 问题字段
    // 其他字段...
}

// 更新后
type Order struct {
    ID                      uint       `json:"id" gorm:"primaryKey"`
    AppointmentTime         time.Time  `json:"appointment_time" gorm:"not null;comment:用户预约的服务时间"`
    ActualServiceStartTime  *time.Time `json:"actual_service_start_time" gorm:"comment:陪诊师实际开始服务时间"`
    ActualServiceEndTime    *time.Time `json:"actual_service_end_time" gorm:"comment:陪诊师实际结束服务时间"`
    // 其他字段...
}
```

#### DTO对象更新
```go
// 订单详情响应DTO
type OrderDetailResponse struct {
    ID                     uint       `json:"id"`
    AppointmentTime        string     `json:"appointment_time"`        // 预约时间（格式化）
    ActualServiceStartTime *string    `json:"actual_service_start_time"` // 实际开始时间（格式化）
    ActualServiceEndTime   *string    `json:"actual_service_end_time"`   // 实际结束时间（格式化）
    ServiceStatus          string     `json:"service_status"`          // 服务状态：未开始/进行中/已完成
    // 其他字段...
}
```

### 2. 服务接口设计

#### 时间管理服务接口
```go
type TimeManagementService interface {
    // 获取订单的预约时间
    GetAppointmentTime(orderID uint) (time.Time, error)
    
    // 记录服务开始时间
    RecordServiceStartTime(orderID uint, startTime time.Time) error
    
    // 记录服务结束时间
    RecordServiceEndTime(orderID uint, endTime time.Time) error
    
    // 检查预约时间是否已过期
    IsAppointmentExpired(orderID uint) (bool, error)
    
    // 获取服务持续时间
    GetServiceDuration(orderID uint) (*time.Duration, error)
}
```

#### 订单服务接口更新
```go
type OrderService interface {
    // 现有方法保持不变，内部实现更新
    GetOrderDetail(orderID uint) (*OrderDetailResponse, error)
    
    // 新增方法
    StartService(orderID uint, attendantID uint) error
    EndService(orderID uint, attendantID uint) error
    
    // 更新现有方法以使用新字段
    GetPendingOrders() ([]*OrderSummary, error)
    CheckExpiredOrders() ([]*Order, error)
}
```

### 3. API接口设计

#### 订单详情API更新
```http
GET /api/orders/{id}

Response:
{
    "code": 0,
    "data": {
        "id": 10001,
        "order_no": "ORD202507210001",
        "appointment_time": "2025-07-22 14:00:00",
        "actual_service_start_time": "2025-07-22 14:05:00",
        "actual_service_end_time": null,
        "service_status": "进行中",
        "service_duration_minutes": null,
        // 其他字段...
    }
}
```

#### 服务时间记录API
```http
POST /api/orders/{id}/start-service
{
    "attendant_id": 123,
    "start_time": "2025-07-22T14:05:00Z"
}

POST /api/orders/{id}/end-service
{
    "attendant_id": 123,
    "end_time": "2025-07-22T16:30:00Z"
}
```

### 4. 前端组件设计

#### 时间显示组件
```vue
<template>
  <div class="time-display">
    <div class="appointment-time">
      <label>预约时间：</label>
      <span>{{ formatTime(appointmentTime) }}</span>
    </div>
    
    <div class="actual-service-time" v-if="actualServiceStartTime">
      <label>实际服务时间：</label>
      <span>{{ formatTime(actualServiceStartTime) }}</span>
      <span v-if="actualServiceEndTime"> - {{ formatTime(actualServiceEndTime) }}</span>
      <span v-else class="status-tag">进行中</span>
    </div>
    
    <div class="service-status">
      <label>服务状态：</label>
      <el-tag :type="getStatusType(serviceStatus)">{{ serviceStatus }}</el-tag>
    </div>
  </div>
</template>
```

## 数据模型设计

### 数据库表结构变更

#### 迁移脚本设计
```sql
-- 第一步：添加新字段
ALTER TABLE orders 
ADD COLUMN appointment_time timestamp NULL COMMENT '用户预约的服务时间',
ADD COLUMN actual_service_start_time timestamp NULL COMMENT '陪诊师实际开始服务时间',
ADD COLUMN actual_service_end_time timestamp NULL COMMENT '陪诊师实际结束服务时间';

-- 第二步：数据迁移
UPDATE orders SET appointment_time = service_time WHERE appointment_time IS NULL;

-- 第三步：设置约束
ALTER TABLE orders MODIFY COLUMN appointment_time timestamp NOT NULL;

-- 第四步：创建索引
CREATE INDEX idx_orders_appointment_time ON orders(appointment_time);
CREATE INDEX idx_orders_actual_service_start_time ON orders(actual_service_start_time);
CREATE INDEX idx_orders_actual_service_end_time ON orders(actual_service_end_time);

-- 第五步：删除旧字段（可选，建议保留一段时间）
-- ALTER TABLE orders DROP COLUMN service_time;
```

#### 数据完整性约束
```sql
-- 确保实际服务结束时间不早于开始时间
ALTER TABLE orders ADD CONSTRAINT chk_service_time_order 
CHECK (actual_service_end_time IS NULL OR actual_service_start_time IS NULL OR actual_service_end_time >= actual_service_start_time);

-- 确保实际服务开始时间不早于预约时间（允许提前30分钟）
ALTER TABLE orders ADD CONSTRAINT chk_service_start_reasonable 
CHECK (actual_service_start_time IS NULL OR actual_service_start_time >= DATE_SUB(appointment_time, INTERVAL 30 MINUTE));
```

### 索引优化设计

#### 查询性能优化
```sql
-- 复合索引：状态 + 预约时间（用于查询待服务订单）
CREATE INDEX idx_orders_status_appointment_time ON orders(status, appointment_time);

-- 复合索引：陪诊师 + 实际服务时间（用于陪诊师工作记录）
CREATE INDEX idx_orders_attendant_service_time ON orders(attendant_id, actual_service_start_time);

-- 复合索引：预约时间 + 状态（用于过期订单检查）
CREATE INDEX idx_orders_appointment_status ON orders(appointment_time, status);
```

## 错误处理设计

### 数据迁移错误处理
```go
type MigrationError struct {
    Step    string `json:"step"`
    Message string `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func (e *MigrationError) Error() string {
    return fmt.Sprintf("Migration failed at step %s: %s", e.Step, e.Message)
}

// 迁移回滚机制
type MigrationRollback struct {
    BackupTable string
    Steps       []string
}
```

### 业务逻辑错误处理
```go
var (
    ErrAppointmentTimeRequired = errors.New("预约时间不能为空")
    ErrServiceAlreadyStarted   = errors.New("服务已经开始，不能重复开始")
    ErrServiceNotStarted       = errors.New("服务尚未开始，不能结束")
    ErrInvalidTimeSequence     = errors.New("时间顺序不正确")
    ErrAppointmentExpired      = errors.New("预约时间已过期")
)
```

## 测试策略

### 数据迁移测试
```go
func TestDataMigration(t *testing.T) {
    // 1. 准备测试数据
    // 2. 执行迁移
    // 3. 验证数据完整性
    // 4. 验证字段映射正确性
    // 5. 验证索引创建成功
}
```

### API测试
```go
func TestOrderTimeAPIs(t *testing.T) {
    // 1. 测试订单详情API返回正确的时间字段
    // 2. 测试服务开始时间记录API
    // 3. 测试服务结束时间记录API
    // 4. 测试时间字段验证逻辑
}
```

### 性能测试
```go
func BenchmarkTimeFieldQueries(b *testing.B) {
    // 1. 基准测试：查询订单列表性能
    // 2. 基准测试：时间过期检查性能
    // 3. 基准测试：服务时间统计性能
}
```

## 部署策略

### 分阶段部署计划

#### 阶段1：数据库迁移
1. 在维护窗口执行数据库迁移
2. 验证数据完整性
3. 创建数据备份

#### 阶段2：后端代码部署
1. 部署支持新字段的后端代码
2. 保持向后兼容性
3. 监控系统性能

#### 阶段3：前端更新
1. 更新管理后台界面
2. 更新小程序端显示
3. 更新陪诊师端界面

#### 阶段4：清理和优化
1. 移除旧字段（可选）
2. 优化查询性能
3. 完善监控和日志

### 回滚计划
```sql
-- 紧急回滚脚本
-- 1. 恢复service_time字段
ALTER TABLE orders ADD COLUMN service_time timestamp;
UPDATE orders SET service_time = appointment_time;

-- 2. 回滚代码到上一版本
-- 3. 验证系统功能正常
```

## 监控和日志

### 关键指标监控
- 数据迁移成功率
- API响应时间变化
- 数据库查询性能
- 时间字段数据质量

### 日志记录
```go
// 服务时间记录日志
logger.Info("Service time recorded", 
    zap.Uint("order_id", orderID),
    zap.String("type", "start"), // start/end
    zap.Time("time", serviceTime),
    zap.Uint("attendant_id", attendantID))

// 数据迁移日志
logger.Info("Data migration progress",
    zap.String("step", "field_rename"),
    zap.Int("processed_records", count),
    zap.Duration("elapsed", elapsed))
```

## 安全考虑

### 数据访问控制
- 只有授权的陪诊师可以记录服务时间
- 管理员可以查看和修正时间记录
- 用户只能查看自己订单的时间信息

### 数据完整性保护
- 时间字段的合理性验证
- 防止时间数据被恶意篡改
- 重要时间变更的审计日志

## 总结

本设计通过清晰的字段分离、完善的错误处理和分阶段的部署策略，解决了服务时间字段混用的问题。设计确保了数据的准确性、系统的稳定性和良好的用户体验。