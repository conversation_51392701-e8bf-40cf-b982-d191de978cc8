# 服务时间字段分离需求文档

## 介绍

当前系统中存在一个关键问题：订单预约的服务时间与陪诊师实际开始服务的时间共用了同一个字段 `service_time`，这导致用户原始预约时间被覆盖，造成数据混乱和业务逻辑错误。本需求旨在通过字段分离来解决这个问题。

## 需求

### 需求1：数据库字段重构

**用户故事：** 作为系统管理员，我希望订单表中的时间字段能够清晰区分用户预约时间和实际服务时间，以便准确记录和查询订单的各个时间节点。

#### 验收标准

1. WHEN 执行数据库迁移 THEN 系统应将现有的 `service_time` 字段重命名为 `appointment_time`
2. WHEN 执行数据库迁移 THEN 系统应新增 `actual_service_start_time` 字段用于记录陪诊师实际开始服务时间
3. WHEN 执行数据库迁移 THEN 系统应新增 `actual_service_end_time` 字段用于记录陪诊师实际结束服务时间
4. WHEN 迁移完成 THEN 所有现有订单的预约时间数据应完整保留在 `appointment_time` 字段中
5. WHEN 迁移完成 THEN 新增字段应允许NULL值，因为历史订单可能没有实际服务时间记录

### 需求2：后端代码适配

**用户故事：** 作为后端开发者，我希望所有相关的服务和API能够正确使用新的时间字段，以确保业务逻辑的准确性。

#### 验收标准

1. WHEN 查询订单列表 THEN 系统应使用 `appointment_time` 进行服务时间过期检查
2. WHEN 陪诊师开始服务 THEN 系统应记录 `actual_service_start_time` 而不修改 `appointment_time`
3. WHEN 陪诊师结束服务 THEN 系统应记录 `actual_service_end_time`
4. WHEN API返回订单信息 THEN 响应应包含区分的预约时间和实际服务时间字段
5. WHEN 执行订单匹配逻辑 THEN 系统应基于 `appointment_time` 判断服务时间是否已过期
6. WHEN 生成订单报表 THEN 系统应能够区分预约时间和实际服务时间进行统计

### 需求3：管理后台界面更新

**用户故事：** 作为管理员，我希望在管理后台能够清楚地看到订单的预约时间和实际服务时间，以便更好地管理订单和服务质量。

#### 验收标准

1. WHEN 查看订单详情 THEN 界面应分别显示"预约时间"和"实际服务时间"
2. WHEN 实际服务时间为空 THEN 界面应显示"未开始服务"或类似提示
3. WHEN 订单列表显示 THEN 应优先显示预约时间，并标注实际服务状态
4. WHEN 筛选订单 THEN 管理员应能够基于预约时间或实际服务时间进行筛选
5. WHEN 导出订单数据 THEN 导出文件应包含区分的时间字段

### 需求4：微信小程序端适配

**用户故事：** 作为患者用户，我希望在小程序中能够看到我的预约时间始终保持不变，同时能够了解陪诊师的实际服务时间。

#### 验收标准

1. WHEN 查看订单详情 THEN 用户应能看到原始的预约时间
2. WHEN 服务开始后 THEN 用户应能看到陪诊师的实际服务开始时间
3. WHEN 服务完成后 THEN 用户应能看到完整的服务时间记录
4. WHEN 订单状态更新 THEN 时间显示应准确反映当前服务阶段

### 需求5：陪诊师端适配

**用户故事：** 作为陪诊师，我希望能够准确记录我的服务开始和结束时间，同时能够看到用户的原始预约时间。

#### 验收标准

1. WHEN 接受订单 THEN 陪诊师应能看到用户的预约时间
2. WHEN 开始服务 THEN 系统应记录实际服务开始时间
3. WHEN 结束服务 THEN 系统应记录实际服务结束时间
4. WHEN 查看服务历史 THEN 陪诊师应能看到完整的时间记录

### 需求6：数据一致性和完整性

**用户故事：** 作为系统架构师，我希望确保数据迁移过程中不会丢失任何重要信息，并且新旧系统能够平滑过渡。

#### 验收标准

1. WHEN 执行数据迁移 THEN 所有现有订单数据应完整保留
2. WHEN 迁移完成 THEN 应提供回滚机制以防出现问题
3. WHEN 新旧字段并存期间 THEN 系统应能够正常运行
4. WHEN 数据验证 THEN 迁移后的数据应与迁移前保持一致性
5. WHEN 系统升级 THEN 应提供详细的迁移日志和验证报告

### 需求7：性能和兼容性

**用户故事：** 作为运维工程师，我希望字段变更不会影响系统性能，并且能够保持向后兼容性。

#### 验收标准

1. WHEN 查询订单 THEN 新字段结构不应显著影响查询性能
2. WHEN 创建索引 THEN 应为新的时间字段创建适当的数据库索引
3. WHEN API调用 THEN 现有API应保持向后兼容，同时提供新的字段信息
4. WHEN 系统负载测试 THEN 性能指标应保持在可接受范围内
5. WHEN 第三方集成 THEN 现有的集成接口应继续正常工作