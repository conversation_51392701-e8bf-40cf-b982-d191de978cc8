# 服务时间字段分离实施计划

## 实施任务列表

- [x] 1. 数据库迁移脚本开发
  - 创建数据库迁移脚本，添加新的时间字段
  - 实现数据迁移逻辑，将现有service_time数据复制到appointment_time
  - 创建必要的数据库索引优化查询性能
  - 添加数据完整性约束确保时间逻辑正确
  - 实现迁移回滚脚本以防出现问题
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 6.2, 6.3, 6.4_

- [x] 2. 后端数据模型更新
  - 更新Order模型结构，添加新的时间字段定义
  - 创建时间管理服务接口和实现类
  - 更新所有DTO对象以支持新的时间字段结构
  - 实现时间字段的验证逻辑和业务规则
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 3. 订单服务层重构
  - 修改订单查询逻辑使用appointment_time进行时间过期检查
  - 实现服务开始时间记录功能，更新actual_service_start_time字段
  - 实现服务结束时间记录功能，更新actual_service_end_time字段
  - 更新订单匹配逻辑基于预约时间而非实际服务时间
  - 重构订单状态管理以正确处理时间字段分离
  - _需求: 2.1, 2.2, 2.3, 2.5_

- [x] 4. API接口层适配
  - 更新订单详情API返回区分的预约时间和实际服务时间
  - 创建服务开始时间记录API端点
  - 创建服务结束时间记录API端点
  - 更新订单列表API以正确显示时间信息
  - 确保API向后兼容性，支持现有客户端调用
  - _需求: 2.4, 7.3_

- [x] 5. 管理后台界面更新
  - 修改订单详情页面分别显示预约时间和实际服务时间
  - 更新订单列表页面的时间显示逻辑
  - 实现基于不同时间字段的订单筛选功能
  - 添加服务时间管理功能供管理员使用
  - 更新订单导出功能包含区分的时间字段
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [x] 6. 微信小程序端适配
  - 更新订单详情页面显示用户预约时间和实际服务时间
  - 修改订单列表页面的时间显示逻辑
  - 实现服务进度显示，区分预约阶段和实际服务阶段
  - 更新订单状态变更通知的时间信息
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 7. 陪诊师端功能实现
  - 实现陪诊师开始服务功能，记录actual_service_start_time
  - 实现陪诊师结束服务功能，记录actual_service_end_time
  - 更新陪诊师工作台显示预约时间和实际服务时间
  - 实现陪诊师服务历史查看功能
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [x] 8. 数据迁移执行和验证
  - 在测试环境执行数据迁移脚本
  - 验证迁移后数据的完整性和正确性
  - 执行性能测试确保查询效率不受影响
  - 创建数据验证报告和迁移日志
  - _需求: 6.1, 6.4, 6.5, 7.4_

- [ ] 9. 系统集成测试
  - 编写和执行API接口测试用例
  - 进行前端界面功能测试
  - 执行端到端业务流程测试
  - 验证时间字段在各个系统组件中的正确性
  - _需求: 2.6, 3.1, 4.1, 5.1_

- [ ] 10. 性能优化和监控
  - 优化数据库查询性能，调整索引策略
  - 实现关键指标监控和告警
  - 添加详细的操作日志记录
  - 进行系统负载测试验证性能指标
  - _需求: 7.1, 7.4, 7.5_

- [ ] 11. 部署和上线
  - 制定详细的生产环境部署计划
  - 在生产环境执行数据库迁移
  - 分阶段部署后端和前端代码更新
  - 监控系统运行状态和用户反馈
  - _需求: 6.3, 7.2, 7.3_

- [ ] 12. 文档和培训
  - 更新API文档反映新的时间字段结构
  - 创建用户使用指南说明时间显示变化
  - 编写运维手册包含迁移和回滚流程
  - 对相关团队进行功能培训
  - _需求: 6.5, 7.3_