# 订单页面UI优化设计文档

## 概述

本设计文档详细说明了订单页面UI优化的技术实现方案，主要解决订单状态显示不清晰和联系陪诊师按钮显示逻辑问题。

## 架构

### 前端架构
- **页面层**: `frontend/pages/order/index.wxml` - 订单列表页面模板
- **样式层**: `frontend/pages/order/index.wxss` - 页面样式定义
- **逻辑层**: `frontend/pages/order/index.js` - 页面交互逻辑
- **工具层**: `frontend/utils/order-status-constants.js` - 订单状态常量和工具函数

## 组件和接口

### 1. 订单状态样式组件

#### 状态标签样式映射
```css
.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}
```

#### 各状态具体样式
- **待支付 (status=1)**: 蓝色背景，白色文字，高对比度
- **已支付 (status=2)**: 青色背景，白色文字
- **匹配中 (status=3)**: 橙色背景，白色文字
- **已匹配 (status=4)**: 紫色背景，白色文字
- **已确认 (status=5)**: 绿色背景，白色文字
- **服务开始 (status=6)**: 深绿色背景，白色文字
- **已完成 (status=7)**: 灰色背景，白色文字
- **已取消 (status=8)**: 红色背景，白色文字
- **已退款 (status=9)**: 深灰色背景，白色文字

### 2. 联系陪诊师按钮显示逻辑

#### 显示条件判断函数
```javascript
shouldShowContactButton(status, attendant) {
  // 必须有陪诊师信息且电话号码存在
  if (!attendant || !attendant.phone) {
    return false;
  }
  
  // 根据订单状态判断
  const showContactStatuses = [2, 4, 5, 6]; // 已支付、已匹配、已确认、服务开始
  return showContactStatuses.includes(status);
}
```

#### 按钮状态映射
- **显示联系按钮**: 已支付(2)、已匹配(4)、已确认(5)、服务开始(6)
- **不显示联系按钮**: 待支付(1)、匹配中(3)、已完成(7)、已取消(8)、已退款(9)

### 3. 订单操作按钮组件

#### 按钮组布局
```xml
<view class="order-actions">
  <!-- 取消按钮 -->
  <button wx:if="{{shouldShowCancelButton}}" class="btn cancel-btn">取消订单</button>
  
  <!-- 支付按钮 -->
  <button wx:if="{{status === 1}}" class="btn pay-btn">去支付</button>
  
  <!-- 联系陪诊师按钮 -->
  <button wx:if="{{shouldShowContactButton}}" class="btn contact-btn">联系陪诊师</button>
  
  <!-- 评价按钮 -->
  <button wx:if="{{status === 7 && !hasRated}}" class="btn rate-btn">评价服务</button>
</view>
```

## 数据模型

### 订单状态枚举
```javascript
const ORDER_STATUS = {
  CREATED: 1,        // 待支付
  PAID: 2,           // 已支付
  MATCHING: 3,       // 匹配中
  MATCHED: 4,        // 已匹配
  CONFIRMED: 5,      // 已确认
  STARTED: 6,        // 服务开始
  COMPLETED: 7,      // 已完成
  CANCELLED: 8,      // 已取消
  REFUNDED: 9        // 已退款
};
```

### 陪诊师信息模型
```javascript
const AttendantInfo = {
  id: Number,
  name: String,
  phone: String,
  avatar: String,
  rating: Number,
  title: String
};
```

## 错误处理

### 1. 陪诊师信息缺失处理
- 当陪诊师信息不完整时，不显示联系按钮
- 显示默认的"未指定陪诊师"状态

### 2. 电话拨打失败处理
- 验证电话号码格式
- 处理拨打电话权限被拒绝的情况
- 提供友好的错误提示

### 3. 状态异常处理
- 处理未知订单状态
- 提供默认的状态显示样式
- 记录异常状态用于调试

## 测试策略

### 1. 视觉测试
- 验证各状态标签的颜色对比度
- 测试不同设备和光线条件下的可读性
- 检查字体大小和间距的合理性

### 2. 交互测试
- 验证联系按钮在正确状态下显示/隐藏
- 测试电话拨打功能的正常工作
- 验证按钮点击的响应性

### 3. 兼容性测试
- 测试不同微信版本的兼容性
- 验证不同手机型号的显示效果
- 检查深色模式下的显示效果

## 性能考虑

### 1. 样式优化
- 使用CSS变量减少重复代码
- 优化动画效果的性能
- 减少不必要的重绘和重排

### 2. 逻辑优化
- 缓存状态判断结果
- 避免频繁的DOM操作
- 优化条件判断的执行效率

## 可访问性

### 1. 颜色对比度
- 确保所有状态标签符合WCAG 2.1 AA标准
- 提供高对比度模式支持

### 2. 语义化标记
- 使用合适的ARIA标签
- 提供屏幕阅读器友好的文本描述

### 3. 交互反馈
- 提供清晰的按钮状态反馈
- 确保触摸目标大小符合无障碍标准