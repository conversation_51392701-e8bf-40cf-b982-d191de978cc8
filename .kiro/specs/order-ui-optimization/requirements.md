# 订单页面UI优化需求文档

## 介绍

本文档定义了陪诊服务订单页面的UI优化需求，主要解决订单状态显示不清晰和联系陪诊师按钮显示逻辑不合理的问题。

## 需求

### 需求 1: 订单状态样式优化

**用户故事:** 作为患者，我希望能够清楚地看到订单状态文字，以便快速了解订单当前状态

#### 验证标准
1. WHEN 用户查看订单列表 THEN 系统 SHALL 显示具有足够对比度的订单状态文字
2. WHEN 订单状态为任何状态 THEN 状态文字 SHALL 具有清晰可读的颜色和字体粗细
3. WHEN 用户在不同光线环境下使用 THEN 状态文字 SHALL 保持良好的可读性

### 需求 2: 联系陪诊师按钮显示逻辑优化

**用户故事:** 作为患者，我希望只在合适的时机看到联系陪诊师按钮，以便在需要时能够及时联系陪诊师

#### 验证标准
1. WHEN 订单状态为"已确认"(status=5) THEN 系统 SHALL 显示联系陪诊师按钮
2. WHEN 订单状态为"服务开始"(status=6) THEN 系统 SHALL 显示联系陪诊师按钮
3. WHEN 订单状态为"待支付"(status=1) THEN 系统 SHALL NOT 显示联系陪诊师按钮
4. WHEN 订单状态为"匹配中"(status=3) THEN 系统 SHALL NOT 显示联系陪诊师按钮
5. WHEN 订单状态为"已完成"(status=7) THEN 系统 SHALL NOT 显示联系陪诊师按钮
6. WHEN 订单状态为"已取消"(status=8) THEN 系统 SHALL NOT 显示联系陪诊师按钮
7. WHEN 订单状态为"已退款"(status=9) THEN 系统 SHALL NOT 显示联系陪诊师按钮
8. IF 订单状态为"已支付"(status=2) AND 已匹配到陪诊师 THEN 系统 SHALL 显示联系陪诊师按钮
9. IF 订单状态为"已匹配"(status=4) THEN 系统 SHALL 显示联系陪诊师按钮

### 需求 3: 用户体验一致性

**用户故事:** 作为患者，我希望订单页面的交互逻辑清晰一致，以便更好地理解和使用系统

#### 验证标准
1. WHEN 联系陪诊师按钮显示 THEN 陪诊师信息 SHALL 完整显示（姓名、头像、评分）
2. WHEN 联系陪诊师按钮不显示 THEN 系统 SHALL 显示相应的状态说明
3. WHEN 用户点击联系陪诊师按钮 THEN 系统 SHALL 验证陪诊师电话号码存在后才执行拨号