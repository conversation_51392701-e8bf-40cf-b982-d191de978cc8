# 订单状态字段优化需求文档

## 项目背景

当前订单系统中存在三个状态字段：
1. `orders.status` - 订单主状态
2. `orders.matching_status` - 订单匹配状态  
3. `order_matching.status` - 匹配记录状态

经过分析发现当前状态设计存在以下问题：
- `orders.status = 8` (人工处理) 与匹配状态存在语义重叠
- 匹配失败后的人工介入流程不够清晰
- 状态字段职责边界模糊，存在冗余

## 需求概述

优化订单状态字段设计，明确各状态字段的职责边界，简化状态流转逻辑，增强人工介入处理的可追踪性。

## 用户故事和需求

### 需求1：优化订单主状态设计

**用户故事：** 作为系统开发者，我希望订单主状态只关注订单的核心业务流程，不包含匹配相关的中间状态，以便状态逻辑更加清晰。

#### 验收标准
1. WHEN 订单创建时 THEN 订单状态应为"待支付"
2. WHEN 用户完成支付时 THEN 订单状态应为"已支付" 
3. WHEN 匹配成功且陪诊师确认服务时 THEN 订单状态应为"进行中"
4. WHEN 服务完成时 THEN 订单状态应为"已完成"
5. WHEN 订单被取消时 THEN 订单状态应为"已取消"
6. WHEN 订单退款时 THEN 订单状态应为"已退款"或"待退款"
7. 订单主状态不应包含"人工处理"等匹配相关的中间状态

### 需求2：完善匹配状态管理

**用户故事：** 作为系统管理员，我希望通过匹配状态字段就能完整了解订单的匹配进展，包括自动匹配失败后的人工介入处理状态。

#### 验收标准
1. WHEN 订单支付完成时 THEN 匹配状态应为"匹配中"
2. WHEN 自动匹配成功时 THEN 匹配状态应为"已匹配"
3. WHEN 自动匹配失败时 THEN 匹配状态应为"匹配失败"
4. WHEN 匹配失败后转入人工处理时 THEN 匹配状态应为"人工处理中"
5. WHEN 人工分配成功时 THEN 匹配状态应为"已匹配"
6. WHEN 人工处理也无法匹配时 THEN 匹配状态应为"人工处理失败"
7. WHEN 服务开始时 THEN 匹配状态应为"服务进行中"

### 需求3：增强人工介入处理的可追踪性

**用户故事：** 作为客服人员，我希望能够清楚地追踪哪些订单需要人工介入，以及人工处理的结果和历史记录。

#### 验收标准
1. WHEN 自动匹配失败时 THEN 系统应自动标记订单需要人工介入
2. WHEN 管理员手动分配陪诊师时 THEN 系统应记录人工操作日志
3. WHEN 人工处理完成时 THEN 系统应更新匹配状态并记录处理结果
4. IF 人工处理也失败 THEN 系统应自动触发退款流程（已支付订单不能取消）
5. 系统应提供人工处理订单的专门列表和管理界面

### 需求4：优化状态字段职责边界

**用户故事：** 作为系统架构师，我希望各个状态字段有明确的职责边界，避免状态冗余和逻辑混乱。

#### 验收标准
1. `orders.status` 只负责订单的核心业务状态流转
2. `orders.matching_status` 只负责匹配相关的状态管理
3. `order_matching.status` 只负责具体匹配记录的操作状态
4. 三个状态字段之间应有清晰的依赖关系和更新规则
5. 状态更新应保持原子性和一致性

### 需求5：向后兼容性保证

**用户故事：** 作为系统维护者，我希望状态优化不会影响现有功能，并能平滑迁移历史数据。

#### 验收标准
1. 现有API接口的响应格式应保持兼容
2. 历史订单数据应能正确映射到新的状态体系
3. 前端显示逻辑应能正确处理新的状态值
4. 数据库迁移应安全可靠，支持回滚

## 技术约束

1. 必须保持数据库表结构的向后兼容性
2. API接口变更应遵循版本控制策略
3. 状态更新必须支持事务处理
4. 需要提供数据迁移脚本和验证工具

## 验收标准

1. 所有状态字段职责明确，无语义重叠
2. 人工介入处理流程完整可追踪
3. 状态流转逻辑简化且易于理解
4. 现有功能无回归问题
5. 性能无明显下降
6. 代码可维护性提升

## 优先级

- P0: 优化订单主状态设计，移除冗余状态
- P0: 完善匹配状态管理，增加人工处理相关状态
- P1: 增强人工介入处理的可追踪性
- P1: 优化状态字段职责边界
- P2: 向后兼容性保证和数据迁移