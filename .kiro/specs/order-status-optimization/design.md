# 订单状态字段优化设计文档

## 设计概述

本设计旨在优化订单状态字段的职责边界，简化状态流转逻辑，增强人工介入处理的可追踪性。核心思想是让每个状态字段专注于自己的职责领域，避免语义重叠。

## 架构设计

### 状态字段职责重新定义

#### 1. orders.status (订单主状态)
**职责：** 管理订单的核心业务生命周期，不涉及匹配细节

```go
// 优化后的订单状态常量
const (
    OrderStatusCreated       = 1 // 待支付（已创建）
    OrderStatusPaid          = 2 // 已支付
    OrderStatusInProgress    = 3 // 进行中（服务进行中）
    OrderStatusCompleted     = 4 // 已完成
    OrderStatusCancelled     = 5 // 已取消
    OrderStatusRefunded      = 6 // 已退款
    OrderStatusPendingRefund = 7 // 待退款
    // 移除 OrderStatusManualHandling = 8 // 人工处理（冗余状态）
)
```

#### 2. orders.matching_status (匹配状态)
**职责：** 管理订单与陪诊师的匹配过程，包括人工介入处理

```go
// 优化后的匹配状态常量
const (
    MatchingStatusNone              = 0 // 未开始匹配
    MatchingStatusMatching          = 1 // 自动匹配中
    MatchingStatusMatched           = 2 // 已匹配成功
    MatchingStatusFailed            = 3 // 自动匹配失败
    MatchingStatusManualProcessing  = 4 // 人工处理中（新增）
    MatchingStatusManualFailed      = 5 // 人工处理失败（新增）
    MatchingStatusServiceStarted    = 6 // 服务已开始（调整编号）
)
```

#### 3. order_matching.status (匹配记录状态)
**职责：** 记录具体匹配操作的结果，保持不变

```go
// 匹配记录状态常量（保持不变）
const (
    OrderMatchingStatusPending    = 1 // 待确认
    OrderMatchingStatusAccepted   = 2 // 已接受
    OrderMatchingStatusRejected   = 3 // 已拒绝
    OrderMatchingStatusTimeout    = 4 // 已超时
    OrderMatchingStatusSent       = 5 // 已发送
    OrderMatchingStatusSendFailed = 6 // 发送失败
)
```

### 新增字段设计

#### 1. orders表新增字段

```sql
ALTER TABLE orders ADD COLUMN manual_assignment_admin_id BIGINT UNSIGNED NULL COMMENT '人工分配管理员ID';
ALTER TABLE orders ADD COLUMN manual_assignment_time TIMESTAMP NULL COMMENT '人工分配时间';
ALTER TABLE orders ADD COLUMN manual_assignment_reason VARCHAR(500) NULL COMMENT '人工分配原因';
```

#### 2. 新增人工处理日志表

```sql
CREATE TABLE order_manual_assignments (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    order_id BIGINT UNSIGNED NOT NULL COMMENT '订单ID',
    admin_id BIGINT UNSIGNED NOT NULL COMMENT '管理员ID',
    attendant_id BIGINT UNSIGNED NULL COMMENT '分配的陪诊师ID',
    assignment_type TINYINT NOT NULL DEFAULT 1 COMMENT '分配类型：1手动分配 2重新分配 3取消订单',
    reason VARCHAR(500) NULL COMMENT '分配原因',
    result TINYINT NOT NULL DEFAULT 1 COMMENT '处理结果：1成功 2失败',
    result_message VARCHAR(255) NULL COMMENT '结果消息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_admin_id (admin_id),
    KEY idx_attendant_id (attendant_id),
    KEY idx_assignment_type (assignment_type),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单人工分配记录表';
```

## 状态流转设计

### 优化后的状态流转图

```mermaid
graph TD
    A[订单创建] --> B[orders.status = 1 待支付<br/>orders.matching_status = 0 未开始匹配]
    B --> C[用户支付]
    C --> D[orders.status = 2 已支付<br/>orders.matching_status = 1 自动匹配中]
    D --> E{自动匹配结果}
    
    E -->|成功| F[orders.matching_status = 2 已匹配]
    E -->|失败| G[orders.matching_status = 3 自动匹配失败]
    
    G --> H[触发人工介入]
    H --> I[orders.matching_status = 4 人工处理中]
    
    I --> J{人工处理结果}
    J -->|分配成功| K[orders.matching_status = 2 已匹配<br/>记录人工分配日志]
    J -->|处理失败| L[orders.matching_status = 5 人工处理失败]
    
    F --> M[陪诊师确认服务]
    K --> M
    M --> N[orders.status = 3 进行中<br/>orders.matching_status = 6 服务已开始]
    
    N --> O[服务完成]
    O --> P[orders.status = 4 已完成]
    
    L --> R[自动触发退款流程]
    R --> S[orders.status = 7 待退款]
```

### 关键状态判断逻辑

#### 1. 需要人工介入的订单识别
```go
func (o *Order) NeedsManualIntervention() bool {
    return o.Status == OrderStatusPaid && 
           o.MatchingStatus == MatchingStatusFailed
}
```

#### 2. 人工处理中的订单识别
```go
func (o *Order) IsUnderManualProcessing() bool {
    return o.Status == OrderStatusPaid && 
           o.MatchingStatus == MatchingStatusManualProcessing
}
```

#### 3. 人工处理失败自动退款的订单识别
```go
func (o *Order) IsAutoRefundTriggered() bool {
    return o.Status == OrderStatusPendingRefund && 
           o.MatchingStatus == MatchingStatusManualFailed
}
```

## 业务规则

### 核心业务规则
1. **已支付订单不可取消原则**：一旦订单完成支付，无论匹配是否成功，都不能直接取消，只能通过退款处理
2. **自动退款触发原则**：当人工处理也无法匹配到合适陪诊师时，系统自动将订单状态更新为"待退款"
3. **匹配失败处理原则**：自动匹配失败 → 人工介入 → 人工处理失败 → 自动退款

### 状态流转约束
- `orders.status = 2` (已支付) 的订单不能直接变更为 `status = 5` (已取消)
- `orders.matching_status = 5` (人工处理失败) 必须触发 `orders.status = 7` (待退款)
- 退款完成后订单状态变更为 `orders.status = 6` (已退款)

## 组件设计

### 1. 人工分配服务 (ManualAssignmentService)

```go
type IManualAssignmentService interface {
    // 获取需要人工介入的订单列表
    GetPendingManualOrders(ctx context.Context, filter *ManualOrderFilter) ([]*Order, int64, error)
    
    // 手动分配陪诊师
    AssignAttendant(ctx context.Context, req *ManualAssignmentRequest) error
    
    // 标记人工处理失败并触发退款
    MarkManualProcessingFailedAndTriggerRefund(ctx context.Context, orderID uint, adminID uint, reason string) error
    
    // 获取人工分配历史
    GetManualAssignmentHistory(ctx context.Context, orderID uint) ([]*ManualAssignment, error)
}
```

### 2. 状态管理器优化 (MatchingStatusManager)

```go
type IMatchingStatusManager interface {
    // 开始自动匹配
    StartAutoMatching(ctx context.Context, orderID uint) error
    
    // 自动匹配成功
    MarkAutoMatchingSuccess(ctx context.Context, orderID uint, attendantID uint) error
    
    // 自动匹配失败，触发人工介入
    MarkAutoMatchingFailedAndTriggerManual(ctx context.Context, orderID uint) error
    
    // 开始人工处理
    StartManualProcessing(ctx context.Context, orderID uint, adminID uint) error
    
    // 人工分配成功
    MarkManualAssignmentSuccess(ctx context.Context, orderID uint, attendantID uint, adminID uint) error
    
    // 人工处理失败，自动触发退款
    MarkManualProcessingFailedAndTriggerRefund(ctx context.Context, orderID uint, adminID uint, reason string) error
    
    // 服务开始
    MarkServiceStarted(ctx context.Context, orderID uint) error
}

// 退款服务接口扩展
type IRefundService interface {
    // 自动触发匹配失败退款
    TriggerAutoRefundForMatchingFailure(ctx context.Context, orderID uint, reason string) error
    
    // 处理匹配失败退款
    ProcessMatchingFailureRefund(ctx context.Context, orderID uint) error
}
```

### 3. 自动退款处理流程

```go
// 人工处理失败自动退款流程
func (s *MatchingStatusManager) MarkManualProcessingFailedAndTriggerRefund(ctx context.Context, orderID uint, adminID uint, reason string) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 更新匹配状态为人工处理失败
        if err := s.updateMatchingStatus(ctx, tx, orderID, MatchingStatusManualFailed); err != nil {
            return err
        }
        
        // 2. 记录人工处理失败日志
        if err := s.recordManualAssignment(ctx, tx, orderID, adminID, 0, 3, reason, 2, "人工处理失败"); err != nil {
            return err
        }
        
        // 3. 自动触发退款流程
        if err := s.refundService.TriggerAutoRefundForMatchingFailure(ctx, orderID, "匹配失败自动退款: "+reason); err != nil {
            return err
        }
        
        // 4. 更新订单状态为待退款
        if err := s.updateOrderStatus(ctx, tx, orderID, OrderStatusPendingRefund); err != nil {
            return err
        }
        
        return nil
    })
}
```

### 4. 管理后台界面设计

#### 人工介入订单管理页面
- **待人工处理订单列表**：显示 `matching_status = 3` 的订单
- **人工处理中订单列表**：显示 `matching_status = 4` 的订单  
- **自动退款订单列表**：显示 `matching_status = 5 && status = 7` 的订单（人工处理失败自动退款）
- **人工分配操作界面**：支持选择陪诊师、填写分配原因
- **处理历史查看**：显示每个订单的人工处理记录

## 数据迁移设计

### 1. 历史数据处理

```sql
-- 迁移脚本：处理现有的 status = 8 的订单
UPDATE orders 
SET matching_status = CASE 
    WHEN attendant_id > 0 THEN 2  -- 已有陪诊师，标记为已匹配
    ELSE 4                        -- 无陪诊师，标记为人工处理中
END,
status = 2  -- 重置为已支付状态
WHERE status = 8;
```

### 2. 状态文本映射更新

```go
// 更新状态文本映射函数
func GetMatchingStatusText(status int) string {
    switch status {
    case MatchingStatusNone:
        return "未开始匹配"
    case MatchingStatusMatching:
        return "自动匹配中"
    case MatchingStatusMatched:
        return "已匹配"
    case MatchingStatusFailed:
        return "自动匹配失败"
    case MatchingStatusManualProcessing:
        return "人工处理中"
    case MatchingStatusManualFailed:
        return "人工处理失败"
    case MatchingStatusServiceStarted:
        return "服务进行中"
    default:
        return "未知状态"
    }
}
```

## API设计

### 1. 人工分配相关API

```go
// POST /api/v1/admin/orders/{order_id}/manual-assign
type ManualAssignmentRequest struct {
    AttendantID uint   `json:"attendant_id" binding:"required"`
    Reason      string `json:"reason" binding:"required,max=500"`
}

// POST /api/v1/admin/orders/{order_id}/manual-processing-failed
type ManualProcessingFailedRequest struct {
    Reason string `json:"reason" binding:"required,max=500"`
}

// 自动退款相关API
// GET /api/v1/admin/orders/auto-refund-pending
// GET /api/v1/admin/refunds/matching-failure

// GET /api/v1/admin/orders/manual-pending
// GET /api/v1/admin/orders/manual-processing  
// GET /api/v1/admin/orders/auto-refund-pending  // 人工处理失败自动退款的订单
```

### 2. 状态查询优化

```go
// 订单列表查询支持新的匹配状态过滤
type OrderListRequest struct {
    Status         *int `json:"status"`
    MatchingStatus *int `json:"matching_status"`
    // ... 其他字段
}
```

## 监控和告警设计

### 1. 关键指标监控
- 自动匹配失败率
- 人工介入处理时长
- 人工处理成功率
- 人工处理失败订单数量
- 自动退款触发频率
- 匹配失败退款金额统计

### 2. 告警规则
- 当人工处理中订单超过阈值时告警
- 当自动退款订单增长过快时告警
- 当自动匹配失败率过高时告警
- 当匹配失败退款金额超过预算时告警

## 优势分析

### 1. 职责清晰
- 订单主状态专注业务流程
- 匹配状态专注匹配过程
- 匹配记录状态专注操作结果

### 2. 可追踪性强
- 完整的人工介入处理链路
- 详细的操作日志记录
- 清晰的状态流转历史

### 3. 扩展性好
- 支持更复杂的匹配策略
- 支持多级人工处理
- 支持自定义处理流程

### 4. 维护性高
- 状态逻辑简化
- 代码可读性提升
- 测试覆盖更容易

## 风险评估

### 1. 数据迁移风险
- **风险**：历史数据迁移可能出现异常
- **缓解**：提供完整的迁移脚本和回滚方案

### 2. 兼容性风险  
- **风险**：前端和API可能需要适配
- **缓解**：保持API响应格式兼容，逐步迁移

### 3. 性能风险
- **风险**：新增表和字段可能影响查询性能
- **缓解**：合理设计索引，监控查询性能