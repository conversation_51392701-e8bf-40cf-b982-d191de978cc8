# 设计文档

## 概述

陪诊师佣金结算系统重构设计，从手动取现模式转变为自动按月结算模式。系统将保持向后兼容性，支持功能开关控制，并提供完整的结算数据管理和查看功能。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "小程序前端"
        A[佣金明细页面] --> B[结算状态查看]
        A --> C[历史记录查询]
    end
    
    subgraph "后端服务"
        D[结算服务] --> E[自动结算调度器]
        D --> F[佣金计算引擎]
        G[通知服务] --> H[消息推送]
        I[配置服务] --> J[功能开关管理]
    end
    
    subgraph "管理后台"
        K[结算数据上传] --> L[文件处理服务]
        M[结算管理界面] --> N[数据审核]
    end
    
    subgraph "数据层"
        O[结算记录表] --> P[佣金明细表]
        Q[打款记录表] --> R[通知记录表]
    end
    
    A --> D
    K --> D
    D --> O
    E --> G
    L --> O
```

### 核心模块设计

1. **结算管理模块**: 负责自动结算流程和数据管理
2. **文件上传模块**: 处理财务上传的结算数据
3. **佣金查看模块**: 为陪诊师提供明细查看功能
4. **通知推送模块**: 处理结算相关通知
5. **配置管理模块**: 管理功能开关和系统参数

## 组件和接口

### 数据库设计

#### 结算记录表 (settlement_records)
```sql
CREATE TABLE settlement_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    attendant_id BIGINT NOT NULL COMMENT '陪诊师ID',
    settlement_month VARCHAR(7) NOT NULL COMMENT '结算月份 YYYY-MM',
    base_commission DECIMAL(10,2) NOT NULL COMMENT '基础佣金',
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '纳税额度',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实际到账金额',
    settlement_status ENUM('pending', 'calculated', 'paid') NOT NULL DEFAULT 'pending' COMMENT '结算状态',
    order_count INT NOT NULL DEFAULT 0 COMMENT '订单数量',
    upload_batch_id VARCHAR(50) COMMENT '上传批次ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_attendant_month (attendant_id, settlement_month),
    INDEX idx_settlement_month (settlement_month),
    INDEX idx_status (settlement_status)
);
```

#### 打款记录表 (payment_records)
```sql
CREATE TABLE payment_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    settlement_id BIGINT NOT NULL COMMENT '结算记录ID',
    batch_no VARCHAR(100) NOT NULL COMMENT '打款批次号',
    payment_amount DECIMAL(10,2) NOT NULL COMMENT '打款金额',
    payment_time TIMESTAMP NOT NULL COMMENT '打款时间',
    payment_status ENUM('success', 'failed', 'pending') NOT NULL DEFAULT 'pending',
    remark TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (settlement_id) REFERENCES settlement_records(id),
    INDEX idx_batch_no (batch_no),
    INDEX idx_payment_time (payment_time)
);
```

#### 结算配置表 (settlement_config)
```sql
CREATE TABLE settlement_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(50) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 初始化配置数据
INSERT INTO settlement_config (config_key, config_value, description) VALUES
('withdrawal_enabled', 'false', '取现功能开关'),
('auto_settlement_enabled', 'true', '自动结算开关'),
('settlement_day', '1', '每月结算日'),
('tax_rate', '0.06', '税率配置');
```

### API接口设计

#### 后端API接口

**1. 陪诊师佣金明细查询**
```go
// GET /api/attendant/commission/details
type CommissionDetailsRequest struct {
    Month    string `form:"month" binding:"omitempty"` // YYYY-MM 格式
    Page     int    `form:"page" binding:"min=1"`
    PageSize int    `form:"page_size" binding:"min=1,max=50"`
}

type CommissionDetailsResponse struct {
    Records []CommissionRecord `json:"records"`
    Total   int64             `json:"total"`
    Summary CommissionSummary  `json:"summary"`
}

type CommissionRecord struct {
    ID              int64     `json:"id"`
    SettlementMonth string    `json:"settlement_month"`
    BaseCommission  float64   `json:"base_commission"`
    TaxAmount       float64   `json:"tax_amount"`
    ActualAmount    float64   `json:"actual_amount"`
    SettlementStatus string   `json:"settlement_status"`
    OrderCount      int       `json:"order_count"`
    PaymentTime     *time.Time `json:"payment_time,omitempty"`
    CreatedAt       time.Time `json:"created_at"`
}
```

**2. 管理后台结算数据上传**
```go
// POST /admin/api/settlement/upload
type SettlementUploadRequest struct {
    SettlementMonth string                `json:"settlement_month" binding:"required"`
    Records         []SettlementUploadRecord `json:"records" binding:"required"`
}

type SettlementUploadRecord struct {
    AttendantID     int64   `json:"attendant_id" binding:"required"`
    BaseCommission  float64 `json:"base_commission" binding:"min=0"`
    TaxAmount       float64 `json:"tax_amount" binding:"min=0"`
    ActualAmount    float64 `json:"actual_amount" binding:"min=0"`
    OrderCount      int     `json:"order_count" binding:"min=0"`
}
```

**3. 打款记录上传**
```go
// POST /admin/api/payment/upload
type PaymentUploadRequest struct {
    BatchNo     string                `json:"batch_no" binding:"required"`
    PaymentTime time.Time            `json:"payment_time" binding:"required"`
    Records     []PaymentUploadRecord `json:"records" binding:"required"`
}

type PaymentUploadRecord struct {
    SettlementID  int64   `json:"settlement_id" binding:"required"`
    PaymentAmount float64 `json:"payment_amount" binding:"min=0"`
    Remark        string  `json:"remark"`
}
```

#### 小程序页面接口

**佣金明细页面 (frontend/pages/attendant/commission/index.js)**
```javascript
// 页面数据结构
data: {
    commissionList: [],      // 佣金记录列表
    currentMonth: '',        // 当前查看月份
    totalCommission: 0,      // 总佣金
    totalTax: 0,            // 总税额
    totalActual: 0,         // 总实际到账
    loading: false,         // 加载状态
    hasMore: true,          // 是否有更多数据
    page: 1,                // 当前页码
    statusFilter: 'all'     // 状态筛选
}

// 主要方法
methods: {
    loadCommissionData(),    // 加载佣金数据
    onMonthChange(),        // 月份切换
    onStatusFilter(),       // 状态筛选
    onPullDownRefresh(),    // 下拉刷新
    onReachBottom()         // 上拉加载更多
}
```

### 服务层设计

#### 结算服务 (SettlementService)
```go
type ISettlementService interface {
    // 自动结算
    AutoSettlement(month string) error
    
    // 获取陪诊师佣金明细
    GetCommissionDetails(attendantID int64, req *CommissionDetailsRequest) (*CommissionDetailsResponse, error)
    
    // 上传结算数据
    UploadSettlementData(req *SettlementUploadRequest) error
    
    // 上传打款记录
    UploadPaymentRecords(req *PaymentUploadRequest) error
    
    // 计算月度佣金
    CalculateMonthlyCommission(attendantID int64, month string) (*CommissionCalculation, error)
}

type CommissionCalculation struct {
    AttendantID     int64   `json:"attendant_id"`
    Month          string  `json:"month"`
    BaseCommission float64 `json:"base_commission"`
    OrderCount     int     `json:"order_count"`
    CompletedOrders []OrderCommission `json:"completed_orders"`
}
```

#### 配置服务 (ConfigService)
```go
type IConfigService interface {
    // 获取功能开关状态
    IsWithdrawalEnabled() bool
    IsAutoSettlementEnabled() bool
    
    // 获取结算配置
    GetSettlementDay() int
    GetTaxRate() float64
    
    // 更新配置
    UpdateConfig(key, value string) error
}
```

## 数据模型

### 核心实体模型

```go
// 结算记录模型
type SettlementRecord struct {
    ID              int64     `gorm:"primaryKey;autoIncrement" json:"id"`
    AttendantID     int64     `gorm:"not null;index" json:"attendant_id"`
    SettlementMonth string    `gorm:"size:7;not null" json:"settlement_month"`
    BaseCommission  float64   `gorm:"type:decimal(10,2);not null" json:"base_commission"`
    TaxAmount       float64   `gorm:"type:decimal(10,2);not null;default:0" json:"tax_amount"`
    ActualAmount    float64   `gorm:"type:decimal(10,2);not null" json:"actual_amount"`
    SettlementStatus string   `gorm:"type:enum('pending','calculated','paid');default:'pending'" json:"settlement_status"`
    OrderCount      int       `gorm:"not null;default:0" json:"order_count"`
    UploadBatchID   string    `gorm:"size:50" json:"upload_batch_id"`
    CreatedAt       time.Time `gorm:"autoCreateTime" json:"created_at"`
    UpdatedAt       time.Time `gorm:"autoUpdateTime" json:"updated_at"`
    
    // 关联关系
    Attendant      Attendant       `gorm:"foreignKey:AttendantID" json:"attendant,omitempty"`
    PaymentRecords []PaymentRecord `gorm:"foreignKey:SettlementID" json:"payment_records,omitempty"`
}

// 打款记录模型
type PaymentRecord struct {
    ID            int64     `gorm:"primaryKey;autoIncrement" json:"id"`
    SettlementID  int64     `gorm:"not null;index" json:"settlement_id"`
    BatchNo       string    `gorm:"size:100;not null;index" json:"batch_no"`
    PaymentAmount float64   `gorm:"type:decimal(10,2);not null" json:"payment_amount"`
    PaymentTime   time.Time `gorm:"not null;index" json:"payment_time"`
    PaymentStatus string    `gorm:"type:enum('success','failed','pending');default:'pending'" json:"payment_status"`
    Remark        string    `gorm:"type:text" json:"remark"`
    CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
    
    // 关联关系
    SettlementRecord SettlementRecord `gorm:"foreignKey:SettlementID" json:"settlement_record,omitempty"`
}
```

## 错误处理

### 错误码定义
```go
const (
    ErrSettlementNotFound     = "SETTLEMENT_NOT_FOUND"
    ErrInvalidSettlementMonth = "INVALID_SETTLEMENT_MONTH"
    ErrUploadDataInvalid      = "UPLOAD_DATA_INVALID"
    ErrSettlementAlreadyExists = "SETTLEMENT_ALREADY_EXISTS"
    ErrPaymentRecordNotFound  = "PAYMENT_RECORD_NOT_FOUND"
    ErrWithdrawalDisabled     = "WITHDRAWAL_DISABLED"
    ErrAutoSettlementFailed   = "AUTO_SETTLEMENT_FAILED"
)
```

### 异常处理策略
1. **数据上传异常**: 提供详细的错误报告，支持部分成功处理
2. **自动结算异常**: 记录错误日志，发送管理员通知，支持手动重试
3. **数据一致性异常**: 提供数据校验和修复工具
4. **系统切换异常**: 支持回滚到原有取现模式

## 测试策略

### 单元测试
- 结算服务核心逻辑测试
- 佣金计算算法测试
- 数据上传验证测试
- 配置管理功能测试

### 集成测试
- 自动结算流程端到端测试
- 文件上传和数据处理集成测试
- 小程序页面和后端API集成测试
- 通知推送功能集成测试

### 性能测试
- 大批量数据上传性能测试
- 月度结算计算性能测试
- 佣金明细查询性能测试

### 兼容性测试
- 新旧系统数据迁移测试
- 功能开关切换测试
- 历史数据完整性测试