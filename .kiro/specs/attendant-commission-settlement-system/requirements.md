# 需求文档

## 介绍

陪诊师佣金结算系统需要进行重大调整，从原有的手动取现模式转变为系统自动按月结算模式。此变更涉及暂停取现功能、实现自动结算流程、增加佣金明细查看页面等多个方面，旨在提高结算效率和财务管理的规范性。

## 需求

### 需求 1

**用户故事:** 作为系统管理员，我希望能够暂停陪诊师的取现功能，以便为新的结算模式做准备，同时保留原有功能代码以备后续使用。

#### 验收标准

1. WHEN 陪诊师访问取现管理页面 THEN 系统 SHALL 显示功能暂停提示信息
2. WHEN 陪诊师尝试发起取现申请 THEN 系统 SHALL 阻止操作并显示相应提示
3. WHEN 管理员需要恢复取现功能 THEN 系统 SHALL 能够通过配置快速重新启用
4. WHEN 系统暂停取现功能 THEN 原有的取现相关代码 SHALL 保持完整不被删除

### 需求 2

**用户故事:** 作为财务人员，我希望能够在管理后台上传每月的结算明细数据和打款订单数据，以便完成线下计算后的数据录入工作。

#### 验收标准

1. WHEN 财务人员登录管理后台 THEN 系统 SHALL 提供结算数据上传功能入口
2. WHEN 财务人员上传结算明细文件 THEN 系统 SHALL 验证文件格式和数据完整性
3. WHEN 上传结算明细数据 THEN 系统 SHALL 包含陪诊师ID、结算月份、佣金金额、纳税额度、结算状态等字段
4. WHEN 财务人员上传打款订单数据 THEN 系统 SHALL 记录打款批次号、打款时间、打款金额等信息
5. WHEN 数据上传成功 THEN 系统 SHALL 自动更新相关陪诊师的结算状态
6. IF 上传数据存在错误 THEN 系统 SHALL 提供详细的错误报告和修正建议

### 需求 3

**用户故事:** 作为陪诊师，我希望能够查看每月的佣金明细，包括纳税额度、佣金、结算进度和状态等信息，以便了解自己的收入情况。

#### 验收标准

1. WHEN 陪诊师访问原取现管理页面位置 THEN 系统 SHALL 显示佣金明细查看页面
2. WHEN 陪诊师查看佣金明细 THEN 系统 SHALL 按月份展示结算记录
3. WHEN 显示月度佣金明细 THEN 系统 SHALL 包含基础佣金、纳税额度、实际到账金额、结算状态等信息
4. WHEN 陪诊师查看结算进度 THEN 系统 SHALL 显示"待结算"、"已结算"、"已打款"等状态
5. WHEN 陪诊师查看历史记录 THEN 系统 SHALL 支持按时间范围筛选和查询
6. WHEN 结算状态发生变化 THEN 系统 SHALL 及时更新页面显示信息

### 需求 4

**用户故事:** 作为系统，我希望能够按月自动触发结算流程，以便替代原有的手动取现模式，提高结算效率。

#### 验收标准

1. WHEN 每月结算日到达 THEN 系统 SHALL 自动计算所有陪诊师的月度佣金
2. WHEN 计算月度佣金 THEN 系统 SHALL 基于已完成订单的佣金比例进行统计
3. WHEN 生成结算数据 THEN 系统 SHALL 创建待财务处理的结算记录
4. WHEN 结算记录生成 THEN 系统 SHALL 通知财务人员进行线下计算和处理
5. IF 结算过程中出现异常 THEN 系统 SHALL 记录错误日志并通知管理员
6. WHEN 结算完成 THEN 系统 SHALL 更新陪诊师的佣金余额和结算状态

### 需求 5

**用户故事:** 作为产品经理，我希望新旧系统能够平滑过渡，确保数据一致性和业务连续性，避免影响陪诊师的正常收入。

#### 验收标准

1. WHEN 系统切换到新结算模式 THEN 原有的佣金数据 SHALL 完整迁移到新系统
2. WHEN 进行数据迁移 THEN 系统 SHALL 确保陪诊师历史收入记录的准确性
3. WHEN 新系统上线 THEN 陪诊师 SHALL 能够查看到切换前后的完整收入记录
4. WHEN 发生系统异常 THEN 系统 SHALL 支持回滚到原有取现模式
5. IF 数据不一致 THEN 系统 SHALL 提供数据校验和修复工具
6. WHEN 系统稳定运行 THEN 管理员 SHALL 能够监控结算流程的执行状态

### 需求 6

**用户故事:** 作为陪诊师，我希望能够收到结算相关的通知，及时了解佣金结算的进展情况。

#### 验收标准

1. WHEN 月度结算开始 THEN 系统 SHALL 向陪诊师发送结算通知
2. WHEN 财务完成线下计算 THEN 系统 SHALL 通知陪诊师查看结算明细
3. WHEN 佣金打款完成 THEN 系统 SHALL 发送到账通知给陪诊师
4. WHEN 结算出现异常 THEN 系统 SHALL 及时通知相关陪诊师
5. IF 陪诊师对结算有疑问 THEN 系统 SHALL 提供客服联系方式
6. WHEN 发送通知 THEN 系统 SHALL 支持小程序内消息和短信双重通知方式