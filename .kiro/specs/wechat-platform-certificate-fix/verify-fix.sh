#!/bin/bash

# 微信支付平台证书修复验证脚本
# 用于验证修复是否成功部署

set -e

echo "=== 微信支付平台证书修复验证 ==="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_status() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
        return 1
    fi
}

warn_status() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

info_status() {
    echo -e "${GREEN}ℹ️  $1${NC}"
}

# 1. 检查配置文件
echo "1. 检查配置文件修复状态..."
if [ -f "backend/config/conf/config.prod.yaml" ]; then
    if grep -q "use_auto_cert_mode.*true" "backend/config/conf/config.prod.yaml"; then
        check_status "配置文件中已启用自动证书模式"
    else
        warn_status "配置文件中未启用自动证书模式"
    fi
else
    warn_status "配置文件不存在"
fi

# 2. 检查环境变量
echo ""
echo "2. 检查环境变量配置..."
if [ -f "production.env" ]; then
    if grep -q "APP_WECHAT_USE_AUTO_CERT_MODE=true" "production.env"; then
        check_status "环境变量中已启用自动证书模式"
    else
        warn_status "环境变量中未启用自动证书模式"
    fi
    
    if grep -q "WECHAT_PRIVATE_KEY_PATH" "production.env"; then
        check_status "私钥路径环境变量已配置"
    else
        warn_status "私钥路径环境变量未配置"
    fi
    
    if grep -q "WECHAT_CERT_SERIAL_NUMBER" "production.env"; then
        check_status "证书序列号环境变量已配置"
    else
        warn_status "证书序列号环境变量未配置"
    fi
else
    warn_status "环境变量文件不存在"
fi

# 3. 检查代码修复
echo ""
echo "3. 检查代码修复状态..."
if [ -f "backend/pkg/wechat/official_transfer_client_v2.go" ]; then
    if grep -q "WithWechatPayAutoAuthCipher" "backend/pkg/wechat/official_transfer_client_v2.go"; then
        check_status "代码中已使用自动证书下载模式"
    else
        warn_status "代码中未使用自动证书下载模式"
    fi
else
    warn_status "微信转账客户端文件不存在"
fi

# 4. 生成部署命令
echo ""
echo "4. 生成部署命令..."
info_status "请在生产服务器上执行以下命令："
echo ""
echo "# 1. 上传代码到生产服务器"
echo "scp -r backend/ root@your-server:/var/www/html/peizhen/"
echo "scp backend/config/conf/config.prod.yaml root@your-server:/var/www/html/peizhen/backend/config/conf/"
echo "scp production.env root@your-server:/var/www/html/peizhen/"
echo ""
echo "# 2. 在生产服务器上重新编译和重启"
echo "ssh root@your-server"
echo "cd /var/www/html/peizhen/backend"
echo "go build -o ../bin/backend main.go"
echo "sudo systemctl restart peizhen-backend"
echo "sudo systemctl status peizhen-backend"
echo ""
echo "# 3. 查看启动日志"
echo "tail -f /var/www/html/peizhen/logs/app.prod.log | grep -i cert"
echo ""

# 5. 测试命令
echo "5. 生成测试命令..."
info_status "部署完成后，使用以下命令测试转账功能："
echo ""
echo "# 测试转账接口（需要替换实际的token和withdrawal_id）"
echo "curl -X POST https://admin.kanghuxing.cn/api/admin/withdrawals/pay \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -H \"Authorization: Bearer YOUR_ADMIN_TOKEN\" \\"
echo "  -d '{\"withdrawal_ids\": [TEST_WITHDRAWAL_ID]}'"
echo ""

# 6. 预期结果
echo "6. 预期结果..."
info_status "修复成功的标志："
echo "- 不再出现 '无可用的平台证书' 错误"
echo "- 转账接口返回成功响应（batch_id、out_batch_no等）"
echo "- 日志中显示 '微信支付客户端初始化成功'"
echo "- 日志中显示 '使用自动获取平台证书模式'"
echo ""

# 7. 故障排除
echo "7. 故障排除提示..."
warn_status "如果仍然出现问题，请检查："
echo "- 证书文件权限：chmod 600 /etc/peizhen/certs/wechat/apiclient_key.pem"
echo "- 网络连接：curl -I https://api.mch.weixin.qq.com/v3/certificates"
echo "- 服务器时间同步：ntpdate -s time.nist.gov"
echo "- 环境变量加载：sudo systemctl show peizhen-backend --property=Environment"
echo ""

echo "=== 验证完成 ==="
echo ""
info_status "请按照上述步骤在生产环境部署修复，然后测试转账功能。"
info_status "如有问题，请查看详细的部署指南：.kiro/specs/wechat-platform-certificate-fix/deployment-guide.md"
echo ""