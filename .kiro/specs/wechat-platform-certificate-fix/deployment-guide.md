# 微信支付平台证书问题部署指南

## 问题状态

✅ **代码修复完成**: 微信转账客户端已使用 `WithWechatPayAutoAuthCipher` 自动证书下载模式  
✅ **配置修复完成**: `use_auto_cert_mode: true` 已启用  
✅ **环境变量完成**: `APP_WECHAT_USE_AUTO_CERT_MODE=true` 已设置  
✅ **证书文件确认**: 线上环境证书文件已存在  

## 线上环境证书文件状态

根据用户确认，线上环境 `/etc/peizhen/certs/wechat/` 目录下已存在：

```bash
-rw-r--r-- 1 <USER> <GROUP> 1497 Jul  3 15:52 apiclient_cert.pem
-rw-r--r-- 1 <USER> <GROUP> 1708 Jul  3 15:52 apiclient_key.pem
-rw-r--r-- 1 <USER> <GROUP>  451 Jul  3 15:52 wechat_public_key.pem
```

## 部署步骤

### 第一步：上传修复后的代码到生产环境

```bash
# 1. 上传后端代码
scp -r backend/ root@your-server:/var/www/html/peizhen/

# 2. 上传配置文件
scp backend/config/conf/config.prod.yaml root@your-server:/var/www/html/peizhen/backend/config/conf/

# 3. 上传环境变量文件
scp production.env root@your-server:/var/www/html/peizhen/
```

### 第二步：在生产服务器上重新编译

```bash
# 登录生产服务器
ssh root@your-server

# 进入项目目录
cd /var/www/html/peizhen

# 重新编译后端
cd backend
go build -o ../bin/backend main.go

# 检查编译结果
ls -la ../bin/backend
```

### 第三步：重启服务

```bash
# 重启后端服务
sudo systemctl restart peizhen-backend

# 检查服务状态
sudo systemctl status peizhen-backend

# 如果有管理后台服务也需要重启
sudo systemctl restart peizhen-admin
sudo systemctl status peizhen-admin
```

### 第四步：验证修复效果

#### 1. 检查服务启动日志

```bash
# 查看后端启动日志
tail -f /var/www/html/peizhen/logs/app.prod.log | grep -i cert

# 查看系统服务日志
sudo journalctl -u peizhen-backend -f --no-pager
```

#### 2. 测试微信转账功能

```bash
# 测试转账接口（替换为实际的管理员token）
curl -X POST https://admin.kanghuxing.cn/api/admin/withdrawals/pay \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{"withdrawal_ids": [TEST_WITHDRAWAL_ID]}'
```

#### 3. 预期成功响应

修复成功后，应该看到类似以下的成功响应：

```json
{
  "code": 200,
  "message": "转账成功",
  "data": {
    "batch_id": "1030000071100999991182020315100019480001",
    "out_batch_no": "plfk2020031010165407",
    "batch_status": "ACCEPTED",
    "total_amount": 4000,
    "total_num": 1
  }
}
```

## 故障排除

### 如果仍然出现证书错误

1. **检查证书文件权限**：
   ```bash
   ls -la /etc/peizhen/certs/wechat/
   # 确保 apiclient_key.pem 权限为 600 或 644
   chmod 600 /etc/peizhen/certs/wechat/apiclient_key.pem
   ```

2. **验证证书序列号**：
   ```bash
   # 检查证书序列号是否正确
   openssl x509 -in /etc/peizhen/certs/wechat/apiclient_cert.pem -noout -serial
   ```

3. **检查环境变量加载**：
   ```bash
   # 确认环境变量已正确加载
   sudo systemctl show peizhen-backend --property=Environment
   ```

4. **网络连接测试**：
   ```bash
   # 测试是否能访问微信支付API
   curl -I https://api.mch.weixin.qq.com/v3/certificates
   ```

### 常见错误及解决方案

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|----------|
| `无可用的平台证书` | 自动证书下载失败 | 检查网络连接和API密钥 |
| `商户私钥加载失败` | 私钥文件路径错误或权限问题 | 检查文件路径和权限 |
| `证书序列号不匹配` | 环境变量中的序列号与实际证书不符 | 重新获取正确的序列号 |
| `签名验证失败` | 时间不同步或密钥错误 | 同步服务器时间，检查API密钥 |

## 监控建议

### 1. 添加证书监控

```bash
# 创建证书监控脚本
cat > /etc/cron.daily/check-wechat-certs << 'EOF'
#!/bin/bash
# 检查微信支付证书有效期
cert_file="/etc/peizhen/certs/wechat/apiclient_cert.pem"
if [ -f "$cert_file" ]; then
    expire_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
    expire_timestamp=$(date -d "$expire_date" +%s)
    current_timestamp=$(date +%s)
    days_left=$(( (expire_timestamp - current_timestamp) / 86400 ))
    
    if [ $days_left -lt 30 ]; then
        echo "警告：微信支付证书将在 $days_left 天后过期" | \
        mail -s "证书过期提醒" <EMAIL>
    fi
fi
EOF

chmod +x /etc/cron.daily/check-wechat-certs
```

### 2. 日志监控

```bash
# 监控转账相关错误
tail -f /var/www/html/peizhen/logs/app.prod.log | \
grep -E "(证书|certificate|转账|transfer|RESOURCE_NOT_EXISTS)"
```

## 完成确认

部署完成后，请确认以下检查项：

- [ ] 服务重启成功，无错误日志
- [ ] 微信转账功能测试通过
- [ ] 不再出现"无可用的平台证书"错误
- [ ] 系统日志中显示证书自动下载成功

## 技术原理说明

### 自动证书下载机制

1. **初始化时**：SDK使用商户私钥和API密钥向微信支付API请求平台证书
2. **证书缓存**：下载的平台证书会被缓存在内存中
3. **自动更新**：SDK会定期检查证书有效期并自动更新
4. **签名验证**：使用下载的平台证书验证微信支付API响应签名

### 修复关键点

- **使用 `WithWechatPayAutoAuthCipher`**：启用自动证书管理
- **配置 `use_auto_cert_mode: true`**：在应用层启用自动证书模式
- **正确的证书文件路径**：确保私钥文件可访问
- **有效的API密钥和序列号**：用于身份验证

通过这种方式，系统不再需要手动管理平台证书文件，完全由SDK自动处理证书的下载、验证和更新。