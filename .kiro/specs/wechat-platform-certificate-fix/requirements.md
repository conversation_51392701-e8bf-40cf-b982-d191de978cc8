# 微信支付平台证书问题修复需求文档

## 1. 问题概述

### 1.1 错误现象
- **错误信息**: `无可用的平台证书，请在商户平台-API安全申请使用微信支付公钥`
- **错误代码**: `RESOURCE_NOT_EXISTS (404)`
- **发生场景**: 点击"确认打款"时，微信转账API调用失败
- **影响范围**: 所有微信企业付款功能无法正常使用

### 1.2 根本原因分析
根据wechatpay-go官方文档分析，问题的根本原因是：
1. **平台证书缺失**: 微信支付API v3需要平台证书来验证响应签名
2. **证书配置错误**: 当前配置可能未正确设置平台证书获取方式
3. **商户平台配置**: 可能需要在微信商户平台申请使用微信支付公钥

## 2. 功能目标

### 2.1 主要目标
- **修复转账功能**: 确保微信企业付款API能够正常调用
- **自动证书管理**: 实现平台证书的自动下载和更新
- **错误处理优化**: 提供更详细的错误信息和处理逻辑
- **配置标准化**: 统一证书配置方式，提高可维护性

### 2.2 次要目标
- **监控和日志**: 增强证书相关的监控和日志记录
- **故障排除**: 提供证书问题的诊断工具
- **文档完善**: 更新部署和维护文档

## 3. 验收标准

### 3.1 功能验收
- [ ] 微信转账API调用成功，无"平台证书"相关错误
- [ ] 能够正常发起批量转账
- [ ] 能够正常查询转账状态
- [ ] 能够正常查询转账明细
- [ ] 转账回调通知正常处理

### 3.2 技术验收
- [ ] 使用官方推荐的`WithWechatPayAutoAuthCipher`方式初始化客户端
- [ ] 平台证书能够自动下载和更新
- [ ] 证书相关错误有详细的日志记录
- [ ] 配置文件中证书配置项标准化
- [ ] 环境变量配置完整且正确

### 3.3 运维验收
- [ ] 生产环境部署成功
- [ ] 证书文件权限设置正确
- [ ] 微信商户平台配置正确
- [ ] 提供证书状态检查工具
- [ ] 更新部署文档

## 4. 技术要求

### 4.1 SDK要求
- 使用官方`wechatpay-apiv3/wechatpay-go` SDK
- 采用`WithWechatPayAutoAuthCipher`自动证书模式
- 确保SDK版本兼容性

### 4.2 配置要求
- 统一使用环境变量管理敏感信息
- 证书文件路径标准化
- 支持多环境配置（开发/生产）

### 4.3 安全要求
- 证书文件权限设置为600
- 私钥文件不得暴露在代码中
- API密钥通过环境变量管理

## 5. 约束条件

### 5.1 技术约束
- 必须保持现有API接口不变
- 不能影响其他微信支付功能
- 需要向后兼容现有配置

### 5.2 业务约束
- 修复期间不能影响正常业务
- 需要提供回滚方案
- 必须在生产环境验证

### 5.3 时间约束
- 需要尽快修复，影响用户体验
- 修复后需要充分测试

## 6. 风险评估

### 6.1 技术风险
- **证书配置错误**: 可能导致所有微信支付功能异常
- **SDK版本兼容**: 可能需要更新相关依赖
- **环境差异**: 开发和生产环境配置可能不一致

### 6.2 业务风险
- **用户体验**: 转账功能异常影响用户满意度
- **资金安全**: 转账失败可能影响资金流转
- **合规风险**: 支付功能异常可能影响业务合规

### 6.3 风险缓解
- 在测试环境充分验证
- 准备回滚方案
- 分步骤部署和验证
- 加强监控和告警

## 7. 成功指标

### 7.1 技术指标
- 微信转账API调用成功率 > 99%
- 平台证书自动更新成功率 100%
- 证书相关错误数量 = 0

### 7.2 业务指标
- 用户转账成功率恢复到正常水平
- 转账处理时间在可接受范围内
- 用户投诉数量显著减少

## 8. 后续计划

### 8.1 短期计划
- 完成核心问题修复
- 验证功能正常性
- 更新相关文档

### 8.2 长期计划
- 建立证书监控机制
- 优化错误处理流程
- 完善自动化测试

## 9. 相关资源

### 9.1 技术文档
- [微信支付API v3文档](https://pay.weixin.qq.com/doc/v3/)
- [wechatpay-go SDK文档](https://github.com/wechatpay-apiv3/wechatpay-go)
- [微信商户平台API安全设置](https://pay.weixin.qq.com/index.php/core/cert/api_cert)

### 9.2 相关文件
- `backend/pkg/wechat/official_transfer_client_v2.go`
- `backend/config/conf/config.prod.yaml`
- `production.env`
- 现有修复文档：`WECHAT_PLATFORM_CERTIFICATE_FIX_SUMMARY.md`

### 9.3 环境信息
- 商户号：1717184423
- 证书序列号：3B2F1BB6FBF9CD4D2448AB6310720C15CD668247
- 公钥ID：PUB_KEY_ID_0117171844232025052600452088000602