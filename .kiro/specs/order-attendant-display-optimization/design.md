# 订单陪诊师显示优化设计文档

## 概述

本设计文档详细说明了订单列表中陪诊师信息显示优化的技术实现方案，通过条件渲染和数据处理优化，确保未匹配陪诊师的订单不显示任何陪诊师相关信息，提供更清晰的用户体验。

## 架构

### 前端架构优化
- **模板层**: `frontend/pages/order/index.wxml` - 优化条件渲染逻辑
- **逻辑层**: `frontend/pages/order/index.js` - 清理默认数据生成逻辑
- **样式层**: `frontend/pages/order/index.wxss` - 优化布局和视觉效果
- **工具层**: `frontend/utils/attendant-helper.js` - 陪诊师信息判断工具

### 后端数据处理
- **API响应**: 确保未分配陪诊师时attendant字段为null
- **数据验证**: 验证陪诊师信息完整性
- **状态管理**: 准确反映订单匹配状态

## 组件和接口

### 1. 陪诊师信息显示逻辑重构

#### 陪诊师信息判断函数
```javascript
// frontend/utils/attendant-helper.js

/**
 * 判断是否有有效的陪诊师信息
 * @param {Object} attendant - 陪诊师对象
 * @returns {boolean} 是否有有效陪诊师信息
 */
export function hasValidAttendant(attendant) {
  // 检查陪诊师对象是否存在
  if (!attendant) {
    return false;
  }
  
  // 检查必要字段是否存在且不为默认值
  if (!attendant.name || 
      attendant.name === '未指定陪诊师' || 
      attendant.name.trim() === '') {
    return false;
  }
  
  // 检查是否为系统生成的默认数据
  if (attendant.name === '未指定陪诊师' || 
      attendant.title === '等待系统匹配专业陪诊师') {
    return false;
  }
  
  return true;
}

/**
 * 判断陪诊师信息是否完整
 * @param {Object} attendant - 陪诊师对象
 * @returns {Object} 完整性检查结果
 */
export function checkAttendantCompleteness(attendant) {
  if (!hasValidAttendant(attendant)) {
    return {
      isComplete: false,
      isValid: false,
      issues: ['陪诊师未分配']
    };
  }
  
  const issues = [];
  
  // 检查电话号码
  if (!attendant.phone || attendant.phone.trim() === '') {
    issues.push('缺少联系电话');
  } else {
    // 验证电话号码格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(attendant.phone)) {
      issues.push('电话号码格式不正确');
    }
  }
  
  // 检查头像
  if (!attendant.avatar || attendant.avatar === '/assets/images/default-avatar.png') {
    issues.push('使用默认头像');
  }
  
  // 检查评分
  if (!attendant.rating || attendant.rating === '5.0') {
    issues.push('评分信息待完善');
  }
  
  return {
    isComplete: issues.length === 0,
    isValid: true,
    issues: issues,
    canContact: issues.length === 0 || !issues.some(issue => issue.includes('电话'))
  };
}

/**
 * 判断是否应该显示联系按钮
 * @param {Object} order - 订单对象
 * @returns {boolean} 是否显示联系按钮
 */
export function shouldShowContactButton(order) {
  // 必须有有效的陪诊师信息
  if (!hasValidAttendant(order.attendant)) {
    return false;
  }
  
  // 检查陪诊师信息完整性
  const completeness = checkAttendantCompleteness(order.attendant);
  if (!completeness.canContact) {
    return false;
  }
  
  // 检查订单状态是否允许联系
  const contactableStatuses = [2, 4, 5, 6]; // 已支付、已匹配、已确认、服务开始
  return contactableStatuses.includes(order.status);
}
```

#### 数据处理逻辑优化
```javascript
// frontend/pages/order/index.js - formatRecords函数优化

formatRecords(records) {
  return records.map(record => {
    // 字段映射
    record.record_no = record.order_no || record.appointment_no || ('ORD' + record.id);
    record.status_text = record.status_text || this.getStatusText(record.status, record.matching_status);
    
    // 陪诊师信息处理 - 不再生成默认数据
    if (record.attendant && hasValidAttendant(record.attendant)) {
      // 有有效陪诊师信息，检查完整性
      const completeness = checkAttendantCompleteness(record.attendant);
      
      // 确保必要字段存在
      record.attendant.avatar = record.attendant.avatar || '/assets/images/default-avatar.png';
      record.attendant.rating = record.attendant.rating || '5.0';
      record.attendant.title = record.attendant.title || '专业陪诊师';
      
      // 设置完整性标记
      record.attendantInfoIncomplete = !completeness.isComplete;
      record.attendantIssues = completeness.issues;
      
      // 如果信息不完整，更新标题提示
      if (!completeness.isComplete) {
        if (completeness.issues.includes('缺少联系电话')) {
          record.attendant.title = '陪诊师联系方式待完善';
        } else if (completeness.issues.length > 1) {
          record.attendant.title = '陪诊师信息待完善';
        }
      }
    } else {
      // 没有有效陪诊师信息，设置为null
      record.attendant = null;
      record.attendantInfoIncomplete = false;
    }
    
    // 处理时间字段
    const serviceTimeInfo = formatServiceTimeInfo(record);
    if (serviceTimeInfo) {
      record.serviceTimeInfo = serviceTimeInfo;
      if (serviceTimeInfo.appointmentTime) {
        record.date = serviceTimeInfo.appointmentDate || serviceTimeInfo.appointmentTime.split(' ')[0];
        record.time = serviceTimeInfo.appointmentTimeOnly || serviceTimeInfo.appointmentTime.split(' ')[1];
      }
      
      if (serviceTimeInfo.showActualTimes && serviceTimeInfo.actualServiceStartTime) {
        record.actualDate = serviceTimeInfo.actualServiceStartDate;
        record.actualTime = serviceTimeInfo.actualServiceStartTimeOnly;
      }
    } else {
      // 兼容旧的service_time字段
      if (record.service_time) {
        try {
          const date = new Date(record.service_time);
          record.date = date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
          record.time = date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } catch (e) {
          console.error('[Order] 日期格式化失败:', e);
          record.date = record.service_time;
          record.time = '';
        }
      }
    }

    // 添加类型字段
    record.type = record.order_no ? 'order' : 'appointment';
    
    // 设置联系按钮显示逻辑
    record.shouldShowContactButton = shouldShowContactButton(record);
    
    // 陪诊师状态组件显示逻辑
    record.shouldShowAttendantStatus = shouldShowAttendantStatus(record);
    if (record.shouldShowAttendantStatus) {
      const statusConfig = getAttendantStatusConfig(record);
      if (statusConfig) {
        record.attendantStatusConfig = statusConfig;
        record.showMatchingTime = statusConfig.showMatchingTime;
        
        const timeoutInfo = checkMatchingTimeout(record);
        record.matchingTimeoutInfo = timeoutInfo;
        
        if (timeoutInfo.isTimeout) {
          record.attendantStatusConfig.showQuickActions = true;
          record.attendantStatusConfig.shouldShowContactService = true;
        }
      }
    }
    
    return record;
  });
}
```

### 2. 模板条件渲染优化

#### 陪诊师信息区域条件显示
```xml
<!-- frontend/pages/order/index.wxml - 陪诊师信息区域优化 -->

<view class="order-item" wx:for="{{orderList}}" wx:key="id" data-id="{{item.id}}" data-type="{{item.type}}">
  <view class="order-header">
    <view class="record-info">
      <text class="record-type {{item.type === 'order' ? 'order-type' : 'appt-type'}}">{{item.type === 'order' ? '订单' : '预约'}}</text>
      <view class="order-number">{{item.record_no}}</view>
    </view>
    <view class="status-tag status-{{item.status}} {{!item.attendant ? 'no-attendant' : ''}}">{{item.status_text}}</view>
  </view>

  <!-- 陪诊师信息区域 - 只有在有陪诊师信息或需要显示状态组件时才显示 -->
  <view class="attendant-info" wx:if="{{item.attendant || item.shouldShowAttendantStatus}}">
    <!-- 使用陪诊师状态组件显示未指定陪诊师的状态 -->
    <block wx:if="{{item.shouldShowAttendantStatus && item.type === 'order'}}">
      <attendant-status 
        order="{{item}}"
        order-id="{{item.id}}"
        show-matching-time="{{item.showMatchingTime}}"
        bind:statusTap="onAttendantStatusTap"
        bind:contactService="onContactService"
        bind:cancelOrder="onCancelOrderFromStatus"
        bind:retryMatching="onRetryMatching"
      />
    </block>
    <!-- 显示已分配陪诊师的信息 -->
    <block wx:elif="{{item.attendant}}">
      <image class="avatar" src="{{item.attendant.avatar}}" mode="aspectFill"></image>
      <view class="attendant-details">
        <view class="name-rating">
          <text class="name">{{item.attendant.name}}</text>
          <view class="rating" wx:if="{{!item.attendantInfoIncomplete}}">
            <text class="rating-score">{{item.attendant.rating}}</text>
            <text class="rating-unit">分</text>
          </view>
        </view>
        <view class="attendant-title {{item.attendantInfoIncomplete ? 'incomplete-info' : ''}}">
          {{item.attendant.title}}
        </view>
        <view class="attendant-status" wx:if="{{item.attendantInfoIncomplete && item.status >= 4}}">
          <text class="status-warning">联系方式待完善，如需联系请咨询客服</text>
        </view>
        <!-- 显示具体的信息缺失提示 -->
        <view class="attendant-issues" wx:if="{{item.attendantIssues && item.attendantIssues.length > 0}}">
          <text class="issues-text">信息待完善：{{item.attendantIssues.join('、')}}</text>
        </view>
      </view>
    </block>
  </view>

  <!-- 分隔线 - 只有在显示陪诊师信息时才显示 -->
  <view class="divider" wx:if="{{item.attendant || item.shouldShowAttendantStatus}}"></view>

  <!-- 服务详情区域 - 当没有陪诊师信息时，添加特殊样式类 -->
  <view class="service-details {{!item.attendant && !item.shouldShowAttendantStatus ? 'no-attendant-spacing' : ''}}">
    <!-- 服务详情内容保持不变 -->
    <view class="service-row" wx:if="{{item.type === 'order'}}">
      <view class="service-icon service-type-icon"></view>
      <text class="service-label">服务类型</text>
      <text class="service-value">{{item.service_name || '陪诊服务'}}</text>
    </view>
    <view class="service-row">
      <view class="service-icon hospital-icon"></view>
      <text class="service-label">就医医院</text>
      <text class="service-value">{{item.hospital || '未指定'}}</text>
    </view>
    <view class="service-row">
      <view class="service-icon department-icon"></view>
      <text class="service-label">就诊科室</text>
      <text class="service-value">{{item.department || '未指定'}}</text>
    </view>
    <view class="service-row">
      <view class="service-icon time-icon"></view>
      <text class="service-label">预约时间</text>
      <text class="service-value">{{item.date || '未确定'}} {{item.time || ''}}</text>
    </view>
    <view class="service-row" wx:if="{{item.serviceTimeInfo && item.serviceTimeInfo.showActualTimes}}">
      <view class="service-icon actual-time-icon"></view>
      <text class="service-label">实际服务</text>
      <view class="service-value">
        <view wx:if="{{item.actualDate && item.actualTime}}">
          {{item.actualDate}} {{item.actualTime}}
        </view>
        <view wx:elif="{{item.serviceTimeInfo.serviceStatus === 'in_progress'}}">
          <text class="status-tag in-progress">进行中</text>
        </view>
        <view wx:else>
          <text class="status-tag completed">已完成</text>
        </view>
      </view>
    </view>
    <view class="service-row" wx:if="{{item.type === 'order'}}">
      <view class="service-icon price-icon"></view>
      <text class="service-label">服务费用</text>
      <text class="service-value price">¥{{item.amount || '0.00'}}</text>
    </view>
  </view>

  <view class="divider"></view>

  <!-- 操作按钮区域保持不变 -->
  <view class="order-actions">
    <!-- 操作按钮逻辑保持不变 -->
    <!-- ... -->
  </view>
  
  <view class="detail-btn" catchtap="goToDetail" data-id="{{item.id}}" data-type="{{item.type}}"></view>
</view>
```

### 3. 样式优化设计

#### 无陪诊师信息时的布局优化
```css
/* frontend/pages/order/index.wxss - 样式优化 */

/* 无陪诊师信息时的状态标签样式 */
.status-tag.no-attendant {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  position: relative;
  overflow: hidden;
}

/* 匹配中状态的动态效果 */
.status-tag.no-attendant.status-3 {
  animation: matchingPulse 2s infinite;
}

@keyframes matchingPulse {
  0%, 100% { 
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    transform: scale(1);
  }
  50% { 
    background: linear-gradient(135deg, #40a9ff, #69c0ff);
    transform: scale(1.02);
  }
}

/* 待支付状态的突出显示 */
.status-tag.no-attendant.status-1 {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  animation: paymentBlink 3s infinite;
}

@keyframes paymentBlink {
  0%, 90%, 100% { opacity: 1; }
  95% { opacity: 0.7; }
}

/* 无陪诊师信息时的服务详情区域间距调整 */
.service-details.no-attendant-spacing {
  margin-top: 0;
  padding-top: 24rpx;
}

/* 陪诊师信息不完整时的提示样式 */
.attendant-issues {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
}

.issues-text {
  font-size: 22rpx;
  color: #d48806;
  line-height: 1.4;
}

/* 状态警告文字样式优化 */
.status-warning {
  font-size: 22rpx;
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  display: inline-block;
}

/* 订单项整体布局优化 */
.order-item {
  transition: all 0.3s ease;
}

/* 无陪诊师信息时的订单项样式 */
.order-item.no-attendant {
  border-left: 4rpx solid #1890ff;
}

.order-item.no-attendant .order-header {
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 0;
}

/* 响应式设计优化 */
@media (max-width: 375px) {
  .status-tag.no-attendant {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
  }
  
  .service-details.no-attendant-spacing {
    padding-top: 20rpx;
  }
}
```

### 4. 状态判断逻辑优化

#### 陪诊师状态组件显示条件
```javascript
// frontend/utils/attendant-status-helper.js - 优化显示条件判断

/**
 * 判断是否应该显示陪诊师状态组件
 * @param {Object} order - 订单对象
 * @returns {boolean} 是否显示状态组件
 */
export function shouldShowAttendantStatus(order) {
  // 只有订单类型才显示状态组件
  if (order.type !== 'order') {
    return false;
  }
  
  // 如果已经有有效的陪诊师信息，不显示状态组件
  if (hasValidAttendant(order.attendant)) {
    return false;
  }
  
  // 根据订单状态判断是否显示状态组件
  const statusesForStatusComponent = [1, 2, 3]; // 待支付、已支付、匹配中
  return statusesForStatusComponent.includes(order.status);
}

/**
 * 获取陪诊师状态配置
 * @param {Object} order - 订单对象
 * @returns {Object|null} 状态配置对象
 */
export function getAttendantStatusConfig(order) {
  if (!shouldShowAttendantStatus(order)) {
    return null;
  }
  
  const { status, created_at } = order;
  const matchingDuration = getMatchingDuration(created_at);
  
  // 待支付状态
  if (status === 1) {
    return {
      ...ATTENDANT_STATUS_CONFIG.WAITING_PAYMENT,
      isClickable: true,
      showProgress: false,
      allowCancel: true
    };
  }
  
  // 已支付或匹配中状态
  if (status === 2 || status === 3) {
    // 检查是否匹配超时
    if (matchingDuration > 10 * 60 * 1000) { // 10分钟
      return {
        ...ATTENDANT_STATUS_CONFIG.MATCHING_TIMEOUT,
        isClickable: true,
        showQuickActions: true,
        shouldShowContactService: true,
        matchingDuration: formatDuration(matchingDuration),
        allowCancel: true
      };
    }
    
    return {
      ...ATTENDANT_STATUS_CONFIG.MATCHING,
      isClickable: true,
      showProgress: true,
      showMatchingTime: true,
      matchingDuration: formatDuration(matchingDuration),
      allowCancel: true
    };
  }
  
  return null;
}
```

## 数据模型

### 优化后的订单数据结构
```javascript
const OptimizedOrderData = {
  // 基础订单信息
  id: Number,
  record_no: String,
  status: Number,
  status_text: String,
  type: String, // 'order' | 'appointment'
  
  // 陪诊师信息 - 可能为null
  attendant: {
    name: String,
    avatar: String,
    rating: String,
    phone: String,
    title: String
  } || null,
  
  // 陪诊师信息状态标记
  attendantInfoIncomplete: Boolean,
  attendantIssues: Array<String>, // 信息缺失问题列表
  
  // 显示控制标记
  shouldShowContactButton: Boolean,
  shouldShowAttendantStatus: Boolean,
  
  // 状态组件配置（仅在shouldShowAttendantStatus为true时存在）
  attendantStatusConfig: {
    icon: String,
    title: String,
    subtitle: String,
    actionText: String,
    showAnimation: Boolean,
    showQuickActions: Boolean,
    isClickable: Boolean,
    allowCancel: Boolean
  } || null,
  
  // 服务信息
  service_name: String,
  hospital: String,
  department: String,
  date: String,
  time: String,
  amount: String,
  
  // 时间信息
  serviceTimeInfo: Object,
  actualDate: String,
  actualTime: String
};
```

### 陪诊师信息完整性检查结果
```javascript
const AttendantCompletenessResult = {
  isComplete: Boolean,      // 信息是否完整
  isValid: Boolean,         // 陪诊师信息是否有效
  canContact: Boolean,      // 是否可以联系
  issues: Array<String>     // 具体问题列表
};
```

## 错误处理

### 1. 数据异常处理
```javascript
// 处理后端返回的异常陪诊师数据
function sanitizeAttendantData(attendant) {
  if (!attendant) {
    return null;
  }
  
  // 检查是否为无效的默认数据
  const invalidNames = ['未指定陪诊师', '', null, undefined];
  if (invalidNames.includes(attendant.name)) {
    return null;
  }
  
  // 检查是否为系统生成的测试数据
  if (attendant.name && attendant.name.includes('测试') || 
      attendant.phone === '00000000000') {
    console.warn('[Order] 检测到测试数据，不显示陪诊师信息');
    return null;
  }
  
  return attendant;
}
```

### 2. 渲染异常处理
```javascript
// 模板渲染时的容错处理
function safeRenderAttendant(order) {
  try {
    // 确保订单对象存在
    if (!order) {
      return { shouldRender: false, error: '订单数据不存在' };
    }
    
    // 检查陪诊师信息
    const sanitizedAttendant = sanitizeAttendantData(order.attendant);
    
    return {
      shouldRender: !!sanitizedAttendant,
      attendant: sanitizedAttendant,
      error: null
    };
  } catch (error) {
    console.error('[Order] 陪诊师信息渲染异常:', error);
    return { shouldRender: false, error: error.message };
  }
}
```

### 3. 状态组件异常处理
```javascript
// 状态组件显示异常处理
function safeShowAttendantStatus(order) {
  try {
    // 基础检查
    if (!order || order.type !== 'order') {
      return false;
    }
    
    // 如果有有效陪诊师信息，不显示状态组件
    if (hasValidAttendant(order.attendant)) {
      return false;
    }
    
    // 检查订单状态是否支持
    const supportedStatuses = [1, 2, 3];
    if (!supportedStatuses.includes(order.status)) {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('[Order] 状态组件显示判断异常:', error);
    return false;
  }
}
```

## 测试策略

### 1. 单元测试用例
```javascript
// 陪诊师信息判断函数测试
describe('hasValidAttendant', () => {
  test('应该正确识别无效的陪诊师信息', () => {
    expect(hasValidAttendant(null)).toBe(false);
    expect(hasValidAttendant(undefined)).toBe(false);
    expect(hasValidAttendant({})).toBe(false);
    expect(hasValidAttendant({ name: '' })).toBe(false);
    expect(hasValidAttendant({ name: '未指定陪诊师' })).toBe(false);
  });
  
  test('应该正确识别有效的陪诊师信息', () => {
    expect(hasValidAttendant({ name: '张医生' })).toBe(true);
    expect(hasValidAttendant({ name: '李护士', phone: '13800138000' })).toBe(true);
  });
});

// 联系按钮显示逻辑测试
describe('shouldShowContactButton', () => {
  test('无陪诊师信息时不显示联系按钮', () => {
    const order = { status: 2, attendant: null };
    expect(shouldShowContactButton(order)).toBe(false);
  });
  
  test('陪诊师信息不完整时不显示联系按钮', () => {
    const order = { 
      status: 2, 
      attendant: { name: '张医生', phone: '' } 
    };
    expect(shouldShowContactButton(order)).toBe(false);
  });
  
  test('陪诊师信息完整且状态允许时显示联系按钮', () => {
    const order = { 
      status: 2, 
      attendant: { name: '张医生', phone: '13800138000' } 
    };
    expect(shouldShowContactButton(order)).toBe(true);
  });
});
```

### 2. 集成测试用例
```javascript
// 订单列表渲染测试
describe('Order List Rendering', () => {
  test('未匹配陪诊师的订单不显示陪诊师信息区域', async () => {
    const orders = [
      { id: 1, status: 1, attendant: null, type: 'order' },
      { id: 2, status: 2, attendant: null, type: 'order' }
    ];
    
    const component = render(<OrderList orders={orders} />);
    
    // 验证不显示陪诊师信息区域
    expect(component.find('.attendant-info')).toHaveLength(0);
    
    // 验证状态标签有特殊样式
    expect(component.find('.status-tag.no-attendant')).toHaveLength(2);
  });
  
  test('已匹配陪诊师的订单正常显示陪诊师信息', async () => {
    const orders = [
      { 
        id: 1, 
        status: 4, 
        attendant: { name: '张医生', phone: '13800138000' }, 
        type: 'order' 
      }
    ];
    
    const component = render(<OrderList orders={orders} />);
    
    // 验证显示陪诊师信息区域
    expect(component.find('.attendant-info')).toHaveLength(1);
    expect(component.find('.attendant-details .name')).toHaveText('张医生');
  });
});
```

### 3. 视觉回归测试
- 验证无陪诊师信息时的订单布局
- 测试状态标签的动态效果
- 检查不同设备尺寸下的显示效果
- 验证深色模式下的样式适配

## 性能考虑

### 1. 渲染性能优化
```javascript
// 使用计算属性缓存陪诊师信息判断结果
const attendantDisplayInfo = useMemo(() => {
  return orders.map(order => ({
    id: order.id,
    hasValidAttendant: hasValidAttendant(order.attendant),
    shouldShowStatus: shouldShowAttendantStatus(order),
    canContact: shouldShowContactButton(order)
  }));
}, [orders]);
```

### 2. 数据处理优化
```javascript
// 批量处理订单数据，避免重复计算
function batchProcessOrders(orders) {
  return orders.map(order => {
    const processedOrder = { ...order };
    
    // 一次性完成所有陪诊师相关的判断
    const attendantInfo = processAttendantInfo(order.attendant);
    processedOrder.attendant = attendantInfo.attendant;
    processedOrder.shouldShowContactButton = attendantInfo.canContact;
    processedOrder.shouldShowAttendantStatus = attendantInfo.shouldShowStatus;
    
    return processedOrder;
  });
}
```

### 3. 内存使用优化
- 避免创建不必要的默认陪诊师对象
- 及时清理无用的状态组件配置
- 使用对象池复用陪诊师信息检查结果

## 可访问性

### 1. 语义化改进
```xml
<!-- 改进的语义化标记 -->
<view class="order-item" role="article" aria-label="订单信息">
  <view class="order-header" role="banner">
    <view class="status-tag" role="status" aria-live="polite">
      {{item.status_text}}
    </view>
  </view>
  
  <view class="attendant-info" wx:if="{{item.attendant}}" role="section" aria-label="陪诊师信息">
    <!-- 陪诊师信息内容 -->
  </view>
  
  <view class="service-details" role="main" aria-label="服务详情">
    <!-- 服务详情内容 -->
  </view>
</view>
```

### 2. 屏幕阅读器支持
- 为状态标签添加详细的aria-label
- 为缺失的陪诊师信息提供替代文本
- 确保动态状态变化能被屏幕阅读器感知

### 3. 键盘导航优化
- 确保所有交互元素支持键盘访问
- 提供清晰的焦点指示器
- 优化Tab键导航顺序