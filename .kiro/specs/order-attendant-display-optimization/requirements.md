# 订单陪诊师显示优化需求文档

## 介绍

本文档定义了陪诊服务订单列表中陪诊师信息显示的优化需求，确保未匹配到陪诊师的订单不显示任何陪诊师相关数据，提供更清晰的用户体验。

## 需求

### 需求 1: 未匹配陪诊师时隐藏陪诊师信息区域

**用户故事:** 作为患者，我希望在订单未匹配到陪诊师时不看到任何陪诊师相关信息，以便更清楚地了解订单当前状态

#### 验证标准
1. WHEN 订单状态为"待支付"(status=1) THEN 系统 SHALL 完全隐藏陪诊师信息区域
2. WHEN 订单状态为"已支付"(status=2) AND 未分配陪诊师 THEN 系统 SHALL 完全隐藏陪诊师信息区域
3. WHEN 订单状态为"匹配中"(status=3) AND 未分配陪诊师 THEN 系统 SHALL 完全隐藏陪诊师信息区域
4. WHEN 订单已分配陪诊师 THEN 系统 SHALL 正常显示陪诊师信息区域
5. WHEN 陪诊师信息不完整 THEN 系统 SHALL 显示陪诊师信息但标记为不完整状态

### 需求 2: 优化订单布局和视觉层次

**用户故事:** 作为患者，我希望未匹配陪诊师的订单布局更紧凑，重点突出服务信息和订单状态

#### 验证标准
1. WHEN 隐藏陪诊师信息区域 THEN 订单头部 SHALL 直接连接到服务详情区域
2. WHEN 隐藏陪诊师信息区域 THEN 订单状态标签 SHALL 更加突出显示
3. WHEN 隐藏陪诊师信息区域 THEN 服务详情区域 SHALL 保持完整显示
4. WHEN 隐藏陪诊师信息区域 THEN 操作按钮区域 SHALL 保持正常功能
5. WHEN 显示陪诊师信息 THEN 布局 SHALL 与现有设计保持一致

### 需求 3: 清理默认陪诊师数据生成逻辑

**用户故事:** 作为开发者，我希望系统不再为未匹配的订单生成默认陪诊师数据，以便前端能够正确判断显示逻辑

#### 验证标准
1. WHEN 订单未分配陪诊师 THEN 系统 SHALL 不生成默认陪诊师对象
2. WHEN 订单未分配陪诊师 THEN attendant字段 SHALL 为null或undefined
3. WHEN 订单已分配陪诊师但信息不完整 THEN 系统 SHALL 保留陪诊师对象并标记不完整
4. WHEN 前端判断显示逻辑 THEN 系统 SHALL 基于attendant字段的真实存在性进行判断
5. WHEN 陪诊师信息完整 THEN 系统 SHALL 正常显示所有陪诊师相关信息

### 需求 4: 保持联系按钮显示逻辑的准确性

**用户故事:** 作为患者，我希望只有在陪诊师信息完整且可联系时才显示联系按钮

#### 验证标准
1. WHEN 订单未分配陪诊师 THEN 系统 SHALL 不显示联系陪诊师按钮
2. WHEN 陪诊师信息不完整 THEN 系统 SHALL 不显示联系陪诊师按钮
3. WHEN 陪诊师电话号码无效 THEN 系统 SHALL 不显示联系陪诊师按钮
4. WHEN 陪诊师信息完整且订单状态允许联系 THEN 系统 SHALL 显示联系陪诊师按钮
5. WHEN 点击联系按钮 THEN 系统 SHALL 验证电话号码有效性后执行拨打操作

### 需求 5: 改进订单状态的视觉反馈

**用户故事:** 作为患者，我希望在没有陪诊师信息的情况下，订单状态能够更清晰地传达当前进展

#### 验收标准
1. WHEN 隐藏陪诊师信息区域 THEN 订单状态标签 SHALL 使用更显眼的样式
2. WHEN 订单处于匹配状态 THEN 状态标签 SHALL 显示动态效果提示匹配进行中
3. WHEN 订单待支付 THEN 状态标签 SHALL 突出显示并引导用户支付
4. WHEN 订单已完成匹配 THEN 状态标签 SHALL 恢复正常样式
5. WHEN 订单状态发生变化 THEN 视觉反馈 SHALL 平滑过渡更新