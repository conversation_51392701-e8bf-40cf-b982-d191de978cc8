# 陪诊师推荐人字段功能设计文档

## 概述

本设计文档描述了在小程序成为陪诊师页面中添加推荐人字段功能的技术实现方案。该功能允许新注册的陪诊师填写推荐人手机号，系统将验证推荐人是否为已注册的陪诊师，并建立推荐关系。

## 架构

### 整体架构图

```mermaid
graph TB
    A[小程序前端] --> B[后端API]
    B --> C[陪诊师服务层]
    C --> D[陪诊师仓库层]
    D --> E[MySQL数据库]
    
    subgraph "前端组件"
        F[推荐人输入组件]
        G[手机号验证组件]
        H[表单提交组件]
    end
    
    subgraph "后端服务"
        I[推荐人验证服务]
        J[陪诊师注册服务]
        K[推荐关系管理服务]
    end
    
    A --> F
    F --> G
    G --> H
    H --> B
    B --> I
    I --> J
    J --> K
```

### 数据流程图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端页面
    participant A as 后端API
    participant S as 陪诊师服务
    participant D as 数据库
    
    U->>F: 输入推荐人手机号
    F->>F: 客户端格式验证
    F->>A: 调用推荐人验证API
    A->>S: 验证推荐人存在性
    S->>D: 查询陪诊师表
    D-->>S: 返回查询结果
    S-->>A: 返回验证结果
    A-->>F: 返回验证状态
    F-->>U: 显示验证反馈
    
    U->>F: 提交注册表单
    F->>A: 提交陪诊师认证申请
    A->>S: 处理认证申请
    S->>D: 保存认证信息和推荐关系
    D-->>S: 确认保存成功
    S-->>A: 返回处理结果
    A-->>F: 返回提交结果
    F-->>U: 显示提交状态
```

## 组件和接口

### 前端组件

#### 1. 推荐人输入组件 (ReferrerInput)

**位置**: `frontend/pages/attendant/register/index.wxml`

**功能**:
- 提供推荐人手机号输入框
- 实时格式验证
- 显示验证状态和错误信息

**属性**:
```javascript
{
  value: String,           // 推荐人手机号
  placeholder: String,     // 占位符文本
  disabled: Boolean,       // 是否禁用
  validating: Boolean,     // 是否正在验证
  validationResult: Object // 验证结果
}
```

#### 2. 手机号验证器 (PhoneValidator)

**位置**: `frontend/utils/validator.js`

**功能**:
- 验证手机号格式
- 防抖处理避免频繁请求
- 缓存验证结果

### 后端API接口

#### 1. 推荐人验证接口

**路径**: `POST /api/attendants/validate-referrer`

**请求参数**:
```json
{
  "phone": "13800138000"
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "valid": true,
    "referrer_id": 123,
    "referrer_name": "张三"
  }
}
```

**错误响应**:
```json
{
  "code": 1001,
  "message": "未发现该手机号陪诊师",
  "data": null
}
```

#### 2. 陪诊师认证提交接口（扩展）

**路径**: `POST /api/attendants/verification`

**扩展请求参数**:
```json
{
  "name": "李四",
  "gender": 1,
  "age": 30,
  "phone": "13900139000",
  "referrer_phone": "13800138000",
  // ... 其他现有字段
}
```

### 后端服务层

#### 1. 推荐人验证服务

**接口定义**:
```go
type IReferrerService interface {
    ValidateReferrer(ctx context.Context, phone string) (*ReferrerValidationResult, error)
    CreateReferralRelation(ctx context.Context, referrerID, refereeID uint) error
}

type ReferrerValidationResult struct {
    Valid       bool   `json:"valid"`
    ReferrerID  uint   `json:"referrer_id"`
    ReferrerName string `json:"referrer_name"`
}
```

#### 2. 陪诊师服务扩展

**扩展现有接口**:
```go
type SubmitVerificationReq struct {
    // ... 现有字段
    ReferrerPhone string `json:"referrer_phone,omitempty"`
}
```

## 数据模型

### 1. 陪诊师表扩展 (attendants)

**新增字段**:
```sql
ALTER TABLE attendants ADD COLUMN referrer_id INT UNSIGNED NULL COMMENT '推荐人ID';
ALTER TABLE attendants ADD INDEX idx_referrer_id (referrer_id);
```

### 2. 陪诊师认证表扩展 (attendant_verifications)

**新增字段**:
```sql
ALTER TABLE attendant_verifications ADD COLUMN referrer_phone VARCHAR(11) NULL COMMENT '推荐人手机号';
ALTER TABLE attendant_verifications ADD COLUMN referrer_id INT UNSIGNED NULL COMMENT '推荐人ID';
```

### 3. 推荐关系表 (attendant_referrals) - 新建

```sql
CREATE TABLE attendant_referrals (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT UNSIGNED NOT NULL COMMENT '推荐人ID',
    referee_id INT UNSIGNED NOT NULL COMMENT '被推荐人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referee_id (referee_id),
    UNIQUE KEY uk_referrer_referee (referrer_id, referee_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='陪诊师推荐关系表';
```

### 4. Go模型定义

```go
// AttendantReferral 陪诊师推荐关系
type AttendantReferral struct {
    BaseModel
    ReferrerID uint `gorm:"not null" json:"referrer_id"` // 推荐人ID
    RefereeID  uint `gorm:"not null" json:"referee_id"`  // 被推荐人ID
}

// 扩展现有模型
type Attendant struct {
    // ... 现有字段
    ReferrerID *uint `gorm:"" json:"referrer_id,omitempty"` // 推荐人ID
}

type AttendantVerification struct {
    // ... 现有字段
    ReferrerPhone string `gorm:"size:11" json:"referrer_phone,omitempty"` // 推荐人手机号
    ReferrerID    *uint  `gorm:"" json:"referrer_id,omitempty"`           // 推荐人ID
}
```

## 错误处理

### 错误码定义

```go
const (
    ErrReferrerNotFound     = 1001 // 推荐人不存在
    ErrReferrerInvalid      = 1002 // 推荐人无效（未认证等）
    ErrSelfReferral         = 1003 // 不能推荐自己
    ErrPhoneFormatInvalid   = 1004 // 手机号格式错误
    ErrReferralExists       = 1005 // 推荐关系已存在
)
```

### 错误处理策略

1. **前端错误处理**:
   - 格式错误：实时显示错误提示
   - 网络错误：显示重试按钮
   - 验证失败：显示具体错误信息

2. **后端错误处理**:
   - 参数验证：返回具体的参数错误信息
   - 业务逻辑错误：返回业务相关的错误码和消息
   - 系统错误：记录日志并返回通用错误信息

## 测试策略

### 单元测试

1. **前端测试**:
   - 手机号格式验证测试
   - 推荐人验证API调用测试
   - 表单提交逻辑测试

2. **后端测试**:
   - 推荐人验证服务测试
   - 推荐关系创建测试
   - 错误场景测试

### 集成测试

1. **API集成测试**:
   - 推荐人验证接口测试
   - 陪诊师认证提交接口测试
   - 数据库操作测试

2. **端到端测试**:
   - 完整的推荐人填写和验证流程
   - 表单提交和数据存储验证
   - 错误场景的用户体验测试

### 性能测试

1. **响应时间测试**:
   - 推荐人验证接口响应时间 < 500ms
   - 表单提交响应时间 < 1000ms

2. **并发测试**:
   - 支持100个并发推荐人验证请求
   - 数据库连接池优化

## 安全考虑

### 数据验证

1. **输入验证**:
   - 手机号格式严格验证
   - SQL注入防护
   - XSS攻击防护

2. **业务逻辑验证**:
   - 防止自我推荐
   - 防止重复推荐关系
   - 推荐人状态验证

### 隐私保护

1. **数据脱敏**:
   - 推荐人信息只返回必要字段
   - 手机号部分隐藏显示

2. **访问控制**:
   - 只有认证用户可以验证推荐人
   - 推荐关系数据访问权限控制

## 部署和监控

### 部署策略

1. **数据库迁移**:
   - 创建数据库迁移脚本
   - 分阶段执行表结构变更

2. **代码部署**:
   - 后端API优先部署
   - 前端功能灰度发布

### 监控指标

1. **业务指标**:
   - 推荐人验证成功率
   - 推荐关系创建数量
   - 错误率统计

2. **技术指标**:
   - API响应时间
   - 数据库查询性能
   - 错误日志监控