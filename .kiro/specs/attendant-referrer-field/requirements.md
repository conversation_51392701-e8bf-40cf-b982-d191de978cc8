# 陪诊师推荐人字段功能需求文档

## 介绍

在小程序的成为陪诊师页面中添加推荐人字段功能，允许新注册的陪诊师填写推荐人的手机号码。该功能为选填项，需要对输入的手机号进行验证，确保推荐人是系统中已存在的陪诊师用户。

## 需求

### 需求 1：推荐人字段添加

**用户故事：** 作为一个想要成为陪诊师的用户，我希望能够在注册页面填写推荐人的手机号，以便建立推荐关系。

#### 验收标准

1. WHEN 用户访问成为陪诊师页面 THEN 系统 SHALL 显示推荐人手机号输入字段
2. WHEN 用户查看推荐人字段 THEN 系统 SHALL 明确标识该字段为选填项
3. WHEN 用户不填写推荐人字段 THEN 系统 SHALL 允许用户正常提交注册申请

### 需求 2：手机号格式验证

**用户故事：** 作为一个填写推荐人信息的用户，我希望系统能够验证我输入的手机号格式是否正确，以避免输入错误。

#### 验收标准

1. WHEN 用户输入推荐人手机号 THEN 系统 SHALL 验证手机号格式符合中国大陆手机号规范
2. IF 用户输入的手机号格式不正确 THEN 系统 SHALL 显示格式错误提示信息
3. WHEN 用户输入正确格式的手机号 THEN 系统 SHALL 清除之前的错误提示

### 需求 3：推荐人存在性验证

**用户故事：** 作为一个填写推荐人信息的用户，我希望系统能够验证推荐人是否为已注册的陪诊师，确保推荐关系的有效性。

#### 验收标准

1. WHEN 用户输入推荐人手机号并触发验证 THEN 系统 SHALL 查询该手机号是否对应已注册的陪诊师
2. IF 推荐人手机号在陪诊师数据库中不存在 THEN 系统 SHALL 显示"未发现该手机号陪诊师"的错误提示
3. IF 推荐人手机号在陪诊师数据库中存在 THEN 系统 SHALL 显示验证通过的提示信息
4. WHEN 推荐人验证失败 THEN 系统 SHALL 阻止用户提交注册申请直到修正或清空推荐人字段

### 需求 4：用户体验优化

**用户故事：** 作为一个使用推荐人功能的用户，我希望获得流畅的交互体验和清晰的反馈信息。

#### 验收标准

1. WHEN 用户开始输入推荐人手机号 THEN 系统 SHALL 提供实时的输入格式提示
2. WHEN 用户完成手机号输入 THEN 系统 SHALL 在适当的时机（如失去焦点时）自动触发验证
3. WHEN 验证过程进行中 THEN 系统 SHALL 显示加载状态指示器
4. WHEN 验证完成 THEN 系统 SHALL 显示明确的成功或失败状态反馈

### 需求 5：数据存储和关联

**用户故事：** 作为系统管理员，我希望推荐关系能够被正确记录和存储，以便后续的业务分析和奖励机制。

#### 验收标准

1. WHEN 用户成功提交包含有效推荐人的注册申请 THEN 系统 SHALL 在数据库中记录推荐关系
2. WHEN 存储推荐关系 THEN 系统 SHALL 记录推荐人ID、被推荐人ID和推荐时间
3. IF 用户未填写推荐人或推荐人验证失败 THEN 系统 SHALL 将推荐人字段存储为空值
4. WHEN 推荐关系建立后 THEN 系统 SHALL 确保数据的完整性和一致性