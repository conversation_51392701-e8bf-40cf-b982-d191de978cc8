# 陪诊师推荐人字段功能实现任务列表

- [x] 1. 数据库结构设计和迁移
  - 创建数据库迁移脚本，为现有表添加推荐人相关字段
  - 创建新的推荐关系表用于记录推荐关系
  - 添加必要的索引以优化查询性能
  - _需求: 需求5.1, 需求5.2, 需求5.4_

- [x] 2. 后端数据模型扩展
  - 扩展Attendant模型，添加ReferrerID字段
  - 扩展AttendantVerification模型，添加推荐人相关字段
  - 创建AttendantReferral模型用于管理推荐关系
  - _需求: 需求5.1, 需求5.2_

- [x] 3. 推荐人验证服务实现
  - 创建IReferrerService接口定义推荐人验证方法
  - 实现推荐人手机号存在性验证逻辑
  - 实现推荐人状态验证（确保推荐人已认证）
  - 添加防止自我推荐的业务逻辑验证
  - _需求: 需求3.1, 需求3.2, 需求3.4_

- [x] 4. 推荐人验证API接口开发
  - 创建POST /api/attendants/validate-referrer接口
  - 实现手机号格式验证中间件
  - 实现推荐人查询和验证逻辑
  - 添加适当的错误处理和响应格式
  - _需求: 需求3.1, 需求3.2, 需求3.4_

- [x] 5. 陪诊师认证服务扩展
  - 扩展SubmitVerificationReq DTO，添加ReferrerPhone字段
  - 修改陪诊师认证提交逻辑，处理推荐人信息
  - 实现推荐关系创建逻辑
  - 添加推荐人验证到认证流程中
  - _需求: 需求5.1, 需求5.2, 需求5.3, 需求5.4_

- [x] 6. 前端推荐人输入组件开发
  - 在成为陪诊师页面添加推荐人手机号输入框
  - 实现手机号格式的客户端验证
  - 添加选填字段的UI标识
  - 实现输入框的样式和交互效果
  - _需求: 需求1.1, 需求1.2, 需求1.3, 需求2.1, 需求2.2_

- [x] 7. 前端推荐人验证逻辑实现
  - 实现推荐人验证API调用逻辑
  - 添加防抖机制避免频繁API请求
  - 实现验证状态的UI反馈（加载、成功、失败）
  - 添加验证结果的缓存机制
  - _需求: 需求3.1, 需求3.2, 需求3.4, 需求4.1, 需求4.2, 需求4.3_

- [x] 8. 前端表单提交逻辑扩展
  - 修改表单验证逻辑，包含推荐人字段验证
  - 扩展表单提交数据结构，包含推荐人手机号
  - 实现推荐人验证失败时的提交阻止逻辑
  - 添加推荐人字段的错误提示显示
  - _需求: 需求3.4, 需求4.4, 需求5.3_

- [x] 9. 错误处理和用户反馈优化
  - 定义推荐人相关的错误码和错误消息
  - 实现前端错误信息的本地化显示
  - 添加网络错误的重试机制
  - 优化用户体验，提供清晰的操作指引
  - _需求: 需求3.2, 需求4.1, 需求4.2, 需求4.3, 需求4.4_

- [x] 10. 单元测试编写
  - 编写推荐人验证服务的单元测试
  - 编写推荐人验证API的单元测试
  - 编写前端验证逻辑的单元测试
  - 编写数据模型和数据库操作的测试
  - _需求: 需求2.1, 需求2.2, 需求3.1, 需求3.2, 需求3.4_

- [x] 11. 集成测试和端到端测试
  - 编写推荐人验证的集成测试
  - 编写完整注册流程的端到端测试
  - 测试各种错误场景的处理
  - 验证数据库数据的正确性
  - _需求: 需求1.1, 需求1.2, 需求1.3, 需求5.1, 需求5.2, 需求5.4_

- [x] 12. 性能优化和安全加固
  - 优化推荐人查询的数据库性能
  - 添加API请求频率限制
  - 实现推荐人信息的数据脱敏
  - 添加SQL注入和XSS攻击防护
  - _需求: 需求2.1, 需求3.1, 需求4.2_