# Implementation Plan

- [x] 1. 清理现有代码和移除不必要功能
  - 移除订单恢复相关服务和API文件
  - 移除订单延迟统计功能
  - 清理不再使用的状态常量和方法
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 8.2, 8.3_

- [x] 1.1 删除订单恢复相关文件
  - 删除 `backend/internal/service/order_status_recovery.go` 文件
  - 删除 `backend/internal/service/impl/order_status_recovery.go` 文件
  - 删除 `backend/internal/handler/order_recovery_handler.go` 文件
  - _Requirements: 5.3_

- [x] 1.2 简化订单状态常量定义
  - 修改 `backend/internal/model/const.go` 移除状态8"服务延迟"和状态9"待确认完成"
  - 保持状态1-7不变，确保向后兼容
  - 更新状态文本映射方法
  - _Requirements: 5.1, 5.2, 5.5_

- [x] 1.3 更新订单模型状态文本方法
  - 修改 `backend/internal/model/order.go` 中的 `GetStatusText()` 方法
  - 移除对状态8和状态9的处理
  - 确保状态4返回"服务完成"文本
  - _Requirements: 5.5_

- [x] 2. 实现数据库结构调整
  - 创建简化的数据库迁移文件
  - 添加自动开始服务相关字段
  - 添加服务完成确认相关字段
  - 添加提醒记录相关字段
  - _Requirements: 8.1, 8.4_

- [x] 2.1 创建数据库迁移文件
  - 修改 `backend/migrations/20250729000001_add_order_status_delayed.sql` 文件
  - 添加 auto_started_at, expected_end_time, service_summary, service_photos, special_notes, last_reminder_at, reminder_count 字段
  - 添加 is_abnormal, abnormal_reason, abnormal_marked_at 字段支持异常订单标记
  - 更新订单状态字段注释
  - 创建必要的索引
  - _Requirements: 8.1, 8.4_

- [x] 2.2 创建回滚迁移文件
  - 修改 `backend/migrations/20250729000001_add_order_status_delayed_rollback.sql` 文件
  - 确保能够安全回滚新增字段
  - 不影响现有数据完整性
  - _Requirements: 8.5_

- [x] 2.3 更新订单模型结构
  - 修改 `backend/internal/model/order.go` 添加新字段定义
  - 添加 AutoStartedAt, ExpectedEndTime, ServiceSummary, ServicePhotos, SpecialNotes, LastReminderAt, ReminderCount 字段
  - 添加 IsAbnormal, AbnormalReason, AbnormalMarkedAt 字段支持异常订单标记
  - 确保字段类型和约束正确
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 3. 实现免打扰时间管理器
  - 创建免打扰时间管理服务
  - 实现时间判断逻辑
  - 实现延迟计算功能
  - 添加配置支持
  - _Requirements: 4.4, 4.5_

- [x] 3.1 创建免打扰时间管理器接口
  - 创建 `backend/internal/service/quiet_time_manager.go` 文件
  - 定义 IQuietTimeManager 接口
  - 定义 QuietTimeConfig 配置结构
  - 定义相关常量和类型
  - _Requirements: 4.4_

- [x] 3.2 实现免打扰时间管理器
  - 创建 `backend/internal/service/impl/quiet_time_manager.go` 文件
  - 实现 IsQuietTime 方法判断是否在免打扰时间内(22:00-7:00)
  - 实现 GetNextAllowedTime 方法计算下一个允许时间
  - 实现时区处理逻辑
  - _Requirements: 4.4, 4.5_

- [x] 3.3 添加免打扰时间配置
  - 在配置文件中添加免打扰时间配置项
  - 支持开始时间、结束时间和时区配置
  - 添加配置验证逻辑
  - _Requirements: 4.4_

- [x] 4. 重构自动开始服务调度器
  - 修改现有的自动开始调度器
  - 优化查询逻辑和性能
  - 添加通知功能
  - 完善错误处理
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 4.1 修改自动开始服务调度器查询逻辑
  - 修改 `backend/internal/scheduler/auto_start_scheduler.go` 文件
  - 优化 findOrdersReadyForAutoStart 方法的查询条件
  - 确保只处理已支付且已匹配的订单
  - 添加批量处理支持
  - _Requirements: 1.1, 1.2_

- [x] 4.2 完善自动开始服务时间设置
  - 修改 autoStartService 方法
  - 设置 auto_started_at 为当前时间
  - 设置 actual_service_start_time 为预约时间
  - 计算并设置 expected_end_time
  - _Requirements: 1.4, 7.1, 7.2, 7.3_

- [x] 4.3 添加自动开始服务通知
  - 集成通知服务
  - 向陪诊师发送服务开始通知
  - 记录通知发送状态
  - 处理通知发送失败情况
  - _Requirements: 1.3_

- [x] 5. 实现服务完成确认功能
  - 重构现有的订单完成服务
  - 添加严格的验证逻辑
  - 实现服务总结必填验证
  - 确保只有完成的订单才能结算
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 5.1 重构订单完成服务接口
  - 修改 `backend/internal/service/order_completion.go` 文件
  - 简化接口定义，移除不必要的方法
  - 更新 CompleteServiceRequest 结构，确保服务总结必填
  - 添加服务总结长度验证(最少50字)
  - _Requirements: 2.1, 2.2_

- [x] 5.2 重构订单完成服务实现
  - 修改 `backend/internal/service/impl/order_completion.go` 文件
  - 实现严格的服务总结验证逻辑
  - 确保只有提交服务成果才能标记为完成(状态4)
  - 移除自动完成相关逻辑
  - _Requirements: 2.3, 2.4, 2.5_

- [x] 5.3 更新订单完成API处理器
  - 修改 `backend/internal/handler/order_completion_handler.go` 文件
  - 简化API接口，移除不必要的端点
  - 加强输入验证和错误处理
  - 确保API响应格式一致性
  - _Requirements: 2.1, 2.2_

- [x] 6. 实现完成提醒调度器
  - 创建新的完成提醒调度器
  - 实现简化的提醒逻辑(1小时短信提醒，10小时异常标记)
  - 集成免打扰时间管理
  - 添加异常订单标记功能
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 6.1 创建完成提醒调度器
  - 重构 `backend/internal/scheduler/completion_scheduler.go` 文件
  - 移除自动完成订单逻辑
  - 实现简化的提醒策略(1小时短信提醒，10小时异常标记)
  - 集成免打扰时间管理器
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 6.2 实现短信提醒逻辑
  - 创建 sendSMSReminder 方法处理短信提醒
  - 实现提醒延迟逻辑，避开22:00-7:00时间段
  - 记录提醒发送历史和次数
  - 处理提醒发送失败情况
  - _Requirements: 4.1, 4.3_

- [x] 6.3 实现异常订单标记
  - 添加异常订单标记逻辑(超时10小时)
  - 在订单模型中添加异常状态字段
  - 添加管理员通知机制
  - 记录异常标记日志
  - _Requirements: 4.2, 4.4, 4.5_

- [x] 7. 优化过期订单处理逻辑
  - 修改匹配调度器的过期处理
  - 移除已匹配订单的自动退款
  - 保留未匹配订单的退款逻辑
  - 添加异常情况人工处理
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [x] 7.1 修改匹配调度器过期处理逻辑
  - 修改 `backend/internal/scheduler/matching_scheduler.go` 文件
  - 在 handleExpiredOrder 方法中添加匹配状态判断
  - 已匹配订单不再自动创建退款申请
  - 未匹配订单继续执行退款逻辑
  - _Requirements: 6.1, 6.2_

- [x] 7.2 添加已匹配订单的提醒处理
  - 对已匹配但过期的订单启动提醒流程
  - 集成完成提醒调度器的处理逻辑
  - 避免重复处理同一订单
  - _Requirements: 6.3_

- [x] 7.3 完善异常订单人工处理机制
  - 添加异常订单标记和通知
  - 创建人工处理工作流
  - 记录处理历史和结果
  - _Requirements: 6.4_

- [x] 8. 更新调度器管理器
  - 修改主调度器管理器
  - 集成新的调度器组件
  - 确保调度器协调工作
  - 添加监控和日志
  - _Requirements: 1.1, 4.1, 6.1_

- [x] 8.1 更新调度器管理器结构
  - 修改 `backend/internal/scheduler/scheduler.go` 文件
  - 移除不需要的调度器引用
  - 确保自动开始调度器和完成提醒调度器正确集成
  - 添加调度器状态监控
  - _Requirements: 1.1_

- [x] 8.2 完善调度器启动和停止逻辑
  - 确保所有调度器能够正确启动和停止
  - 添加调度器健康检查
  - 实现优雅关闭机制
  - _Requirements: 4.1, 6.1_

- [ ] 9. 实现用户评价独立化
  - 确保用户评价不影响订单状态
  - 用户评价不影响结算流程
  - 评价作为陪诊师信用评级依据
  - 添加评价时间限制
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 9.1 验证现有评价系统独立性
  - 检查现有的订单评价相关代码
  - 确保评价提交不会改变订单状态
  - 确保评价不影响结算流程
  - _Requirements: 3.2, 3.3_

- [ ] 9.2 完善评价功能
  - 确保用户可以在订单完成后评价
  - 添加评价时间窗口限制
  - 将评价纳入陪诊师信用评级计算
  - _Requirements: 3.1, 3.4, 3.5_

- [ ] 9.3 实现管理后台异常订单显示
  - 在管理后台订单列表中添加异常状态显示
  - 添加异常订单筛选功能
  - 实现异常订单详情查看
  - 添加异常订单处理记录功能
  - _Requirements: 4.2, 4.5_

- [ ] 10. 编写单元测试和集成测试
  - 为新功能编写全面的测试用例
  - 测试自动开始服务逻辑
  - 测试免打扰时间管理
  - 测试服务完成确认流程
  - 测试超时提醒机制
  - _Requirements: 1.1, 2.1, 4.1, 4.4_

- [ ] 10.1 编写自动开始服务测试
  - 创建 `backend/internal/scheduler/auto_start_scheduler_test.go` 文件
  - 测试自动开始的触发条件和时间设置
  - 测试批量处理和并发安全性
  - 测试异常情况处理
  - _Requirements: 1.1, 1.2, 1.4_

- [ ] 10.2 编写免打扰时间管理测试
  - 创建 `backend/internal/service/impl/quiet_time_manager_test.go` 文件
  - 测试时间判断逻辑的准确性
  - 测试延迟计算的正确性
  - 测试时区处理
  - _Requirements: 4.4, 4.5_

- [ ] 10.3 编写服务完成确认测试
  - 创建 `backend/internal/service/impl/order_completion_test.go` 文件
  - 测试服务总结验证逻辑
  - 测试状态转换的正确性
  - 测试权限验证和错误处理
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 10.4 编写完成提醒调度器测试
  - 创建 `backend/internal/scheduler/completion_scheduler_test.go` 文件
  - 测试短信提醒逻辑(1小时超时)
  - 测试异常订单标记逻辑(10小时超时)
  - 测试免打扰时间集成
  - 测试异常订单处理
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 11. 更新配置文件和文档
  - 添加新功能的配置项
  - 更新系统文档
  - 创建部署指南
  - 添加监控和告警配置
  - _Requirements: 4.4, 7.5_

- [ ] 11.1 添加系统配置
  - 在配置文件中添加自动开始服务配置
  - 添加简化的完成提醒配置(1小时短信，10小时异常标记)
  - 添加免打扰时间配置
  - 添加通知服务配置
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 11.2 更新系统文档
  - 更新API文档
  - 更新数据库文档
  - 创建功能使用说明
  - 添加故障排除指南
  - _Requirements: 7.5_

- [ ] 12. 执行数据库迁移和部署验证
  - 在测试环境执行数据库迁移
  - 验证新功能的正确性
  - 进行性能测试
  - 准备生产环境部署
  - _Requirements: 8.1, 8.4, 8.5_

- [ ] 12.1 执行测试环境迁移
  - 在测试环境执行数据库迁移脚本
  - 验证新字段和索引创建正确
  - 测试数据完整性
  - _Requirements: 8.1, 8.4_

- [ ] 12.2 进行功能验证测试
  - 测试完整的订单流程
  - 验证自动开始服务功能
  - 验证服务完成确认功能
  - 验证超时提醒功能
  - _Requirements: 1.1, 2.1, 4.1_

- [ ] 12.3 进行性能和压力测试
  - 测试调度器在大量订单下的性能
  - 测试数据库查询性能
  - 验证系统稳定性
  - _Requirements: 8.1_