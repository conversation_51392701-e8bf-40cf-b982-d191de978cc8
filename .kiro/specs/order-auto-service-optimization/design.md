# Design Document

## Overview

本设计文档描述了订单自动服务优化系统的技术架构和实现方案。系统将通过自动开始服务机制、严格的完成确认流程和人性化的超时提醒来优化陪诊订单的服务流程，确保服务质量和合规性。

## Architecture

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   订单创建      │───▶│   支付完成      │───▶│   匹配成功      │
│   (状态1)       │    │   (状态2)       │    │   (状态2)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   服务完成      │◀───│   进行中        │◀───│   自动开始      │
│   (状态4)       │    │   (状态3)       │    │   (定时触发)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   用户评价      │    │   超时提醒      │
│   (独立功能)    │    │   (人性化)      │
└─────────────────┘    └─────────────────┘
```

### 核心组件

1. **AutoStartScheduler**: 自动开始服务调度器
2. **CompletionReminderScheduler**: 完成提醒调度器  
3. **OrderCompletionService**: 订单完成服务
4. **QuietTimeManager**: 免打扰时间管理器
5. **NotificationService**: 通知服务

## Components and Interfaces

### 1. AutoStartScheduler (自动开始服务调度器)

```go
type AutoStartScheduler struct {
    orderRepo repository.IOrderRepository
    notificationService service.INotificationService
    stopCh    chan struct{}
    interval  time.Duration
}

// 核心方法
func (s *AutoStartScheduler) Start()
func (s *AutoStartScheduler) Stop()
func (s *AutoStartScheduler) processAutoStartOrders(ctx context.Context)
func (s *AutoStartScheduler) autoStartService(ctx context.Context, order *model.Order) error
```

**职责:**
- 定期扫描已匹配且到达预约时间的订单
- 自动将订单状态从"已支付"更新为"进行中"
- 设置自动开始时间和预计结束时间
- 发送服务开始通知

### 2. CompletionReminderScheduler (完成提醒调度器)

```go
type CompletionReminderScheduler struct {
    orderRepo repository.IOrderRepository
    notificationService service.INotificationService
    quietTimeManager service.IQuietTimeManager
    stopCh    chan struct{}
    interval  time.Duration
}

// 核心方法
func (s *CompletionReminderScheduler) Start()
func (s *CompletionReminderScheduler) Stop()
func (s *CompletionReminderScheduler) processOverdueOrders(ctx context.Context)
func (s *CompletionReminderScheduler) sendSMSReminder(ctx context.Context, order *model.Order) error
func (s *CompletionReminderScheduler) markOrderAsAbnormal(ctx context.Context, order *model.Order) error
```

**职责:**
- 定期扫描超时未完成的订单
- 超时1小时发送短信提醒
- 超时10小时标记为异常状态
- 遵守免打扰时间规则(22:00-7:00)
- 记录提醒历史和异常处理

### 3. OrderCompletionService (订单完成服务)

```go
type IOrderCompletionService interface {
    CompleteServiceWithSummary(ctx context.Context, req *CompleteServiceRequest) (*CompleteServiceResponse, error)
    GetPendingCompletionOrders(ctx context.Context, attendantID uint) ([]*PendingCompletionOrder, error)
    ValidateServiceSummary(summary string) error
}

type CompleteServiceRequest struct {
    OrderID        uint     `json:"order_id" binding:"required"`
    AttendantID    uint     `json:"attendant_id" binding:"required"`
    ServiceSummary string   `json:"service_summary" binding:"required,min=50,max=1000"`
    ServicePhotos  []string `json:"service_photos"`
    SpecialNotes   string   `json:"special_notes" binding:"max=500"`
}
```

**职责:**
- 处理陪诊师的服务完成确认
- 验证服务总结的完整性(最少50字)
- 更新订单状态为"服务完成"
- 触发结算流程

### 4. QuietTimeManager (免打扰时间管理器)

```go
type IQuietTimeManager interface {
    IsQuietTime(t time.Time) bool
    GetNextAllowedTime(t time.Time) time.Time
    ShouldDelayNotification(t time.Time, notificationType NotificationType) bool
}

type QuietTimeConfig struct {
    StartHour int `yaml:"start_hour"` // 22
    EndHour   int `yaml:"end_hour"`   // 7
    Timezone  string `yaml:"timezone"` // "Asia/Shanghai"
}
```

**职责:**
- 判断当前时间是否在免打扰时间内(22:00-7:00)
- 计算下一个允许发送通知的时间
- 支持不同类型通知的时间策略

### 5. NotificationService (通知服务)

```go
type INotificationService interface {
    SendSMSNotification(ctx context.Context, phone string, message string) error
    NotifyAdminAbnormalOrder(ctx context.Context, orderID uint, reason string) error
    ScheduleDelayedNotification(ctx context.Context, notification *DelayedNotification) error
}

type NotificationType int
const (
    NotificationTypeSMS NotificationType = iota
    NotificationTypeAdminAlert
)
```

**职责:**
- 发送短信提醒通知
- 发送管理员异常订单通知
- 处理延迟通知的调度
- 记录通知发送历史

## Data Models

### 订单模型扩展

```go
type Order struct {
    // 现有字段...
    
    // 自动开始服务相关字段
    AutoStartedAt        *time.Time `json:"auto_started_at" gorm:"comment:系统自动开始服务时间"`
    ExpectedEndTime      *time.Time `json:"expected_end_time" gorm:"comment:预计服务结束时间"`
    
    // 服务完成相关字段
    ServiceSummary       string     `json:"service_summary" gorm:"type:text;comment:服务总结"`
    ServicePhotos        JSON       `json:"service_photos" gorm:"type:json;comment:服务照片"`
    SpecialNotes         string     `json:"special_notes" gorm:"type:text;comment:特殊情况说明"`
    
    // 提醒和异常处理相关字段
    LastReminderAt       *time.Time `json:"last_reminder_at" gorm:"comment:最后提醒时间"`
    ReminderCount        int        `json:"reminder_count" gorm:"default:0;comment:提醒次数"`
    IsAbnormal           bool       `json:"is_abnormal" gorm:"default:false;comment:是否异常订单"`
    AbnormalReason       string     `json:"abnormal_reason" gorm:"size:255;comment:异常原因"`
    AbnormalMarkedAt     *time.Time `json:"abnormal_marked_at" gorm:"comment:异常标记时间"`
}
```

### 简化的订单状态

```go
const (
    OrderStatusCreated       = 1 // 待支付
    OrderStatusPaid          = 2 // 已支付
    OrderStatusInProgress    = 3 // 进行中(自动开始)
    OrderStatusCompleted     = 4 // 服务完成(陪诊师已提交成果)
    OrderStatusCancelled     = 5 // 已取消
    OrderStatusRefunded      = 6 // 已退款
    OrderStatusPendingRefund = 7 // 待退款
)
```

### 提醒记录模型

```go
type OrderReminder struct {
    BaseModel
    OrderID       uint                 `gorm:"not null;index"`
    ReminderType  NotificationType     `gorm:"not null"`
    ScheduledAt   time.Time           `gorm:"not null"`
    SentAt        *time.Time          `gorm:"index"`
    DelayedReason string              `gorm:"size:100"`
    Status        ReminderStatus      `gorm:"not null;default:1"`
    Message       string              `gorm:"type:text"`
}

// 提醒类型常量
const (
    ReminderTypeSMS         = 1 // 短信提醒
    ReminderTypeAbnormalMark = 2 // 异常标记
)

// 提醒状态常量
const (
    ReminderStatusPending   = 1 // 待发送
    ReminderStatusSent      = 2 // 已发送
    ReminderStatusFailed    = 3 // 发送失败
    ReminderStatusDelayed   = 4 // 延迟发送
)
```

## Error Handling

### 错误分类和处理策略

1. **业务逻辑错误**
   - 订单状态不正确: 返回明确的错误信息
   - 服务总结不符合要求: 提示具体的格式要求
   - 权限验证失败: 返回权限错误

2. **系统错误**
   - 数据库连接失败: 重试机制
   - 通知服务失败: 记录失败日志，稍后重试
   - 调度器异常: 自动重启机制

3. **外部服务错误**
   - 短信服务失败: 降级到推送通知
   - 推送服务失败: 记录失败，人工处理

### 错误恢复机制

```go
type ErrorRecoveryConfig struct {
    MaxRetries    int           `yaml:"max_retries"`
    RetryInterval time.Duration `yaml:"retry_interval"`
    BackoffFactor float64       `yaml:"backoff_factor"`
}

// 重试逻辑
func (s *Service) executeWithRetry(ctx context.Context, operation func() error) error {
    var lastErr error
    for i := 0; i < s.config.MaxRetries; i++ {
        if err := operation(); err == nil {
            return nil
        } else {
            lastErr = err
            time.Sleep(s.config.RetryInterval * time.Duration(math.Pow(s.config.BackoffFactor, float64(i))))
        }
    }
    return lastErr
}
```

## Testing Strategy

### 单元测试

1. **AutoStartScheduler测试**
   - 测试自动开始服务的触发条件
   - 测试时间计算的准确性
   - 测试异常情况的处理

2. **CompletionReminderScheduler测试**
   - 测试免打扰时间逻辑
   - 测试不同类型提醒的触发
   - 测试提醒延迟机制

3. **OrderCompletionService测试**
   - 测试服务总结验证逻辑
   - 测试状态转换的正确性
   - 测试权限验证

### 集成测试

1. **端到端流程测试**
   - 从订单创建到服务完成的完整流程
   - 超时提醒的完整流程
   - 异常情况的处理流程

2. **并发测试**
   - 多个订单同时自动开始
   - 大量超时订单的处理
   - 调度器的并发安全性

### 性能测试

1. **调度器性能**
   - 大量订单的处理能力
   - 内存使用情况
   - CPU使用率

2. **数据库性能**
   - 查询优化验证
   - 索引效果测试
   - 并发写入性能

### 测试数据准备

```go
// 测试用例数据
type TestOrderData struct {
    OrderID         uint
    Status          int
    MatchingStatus  int
    AppointmentTime time.Time
    AttendantID     uint
    ServiceHours    int
}

// 测试场景
var testScenarios = []TestScenario{
    {
        Name: "正常自动开始服务",
        Orders: []TestOrderData{
            {Status: OrderStatusPaid, MatchingStatus: MatchingStatusMatched, AppointmentTime: time.Now().Add(-1 * time.Minute)},
        },
        ExpectedResult: "订单状态应该变为进行中",
    },
    {
        Name: "免打扰时间延迟提醒",
        Orders: []TestOrderData{
            {Status: OrderStatusInProgress, ExpectedEndTime: time.Now().Add(-2 * time.Hour)},
        },
        CurrentTime: time.Date(2024, 1, 1, 23, 0, 0, 0, time.Local), // 23:00
        ExpectedResult: "提醒应该延迟到次日7:00",
    },
}
```

## 配置管理

### 系统配置

```yaml
# config/order_auto_service.yaml
order_auto_service:
  # 自动开始服务配置
  auto_start:
    enabled: true
    check_interval: "1m"
    batch_size: 100
    
  # 完成提醒配置
  completion_reminder:
    enabled: true
    check_interval: "5m"
    quiet_time:
      start_hour: 22
      end_hour: 7
      timezone: "Asia/Shanghai"
    reminder_schedule:
      sms_after: "1h"       # 1小时后短信提醒
      abnormal_after: "10h" # 10小时后标记异常
      
  # 服务完成配置
  completion:
    min_summary_length: 50
    max_summary_length: 1000
    max_photos: 10
    allowed_photo_formats: ["jpg", "jpeg", "png"]
    max_photo_size: "5MB"
    
  # 通知配置
  notification:
    retry_count: 3
    retry_interval: "30s"
    timeout: "10s"
```

## 部署考虑

### 数据库迁移策略

1. **渐进式迁移**
   - 先添加新字段，保持向后兼容
   - 逐步启用新功能
   - 最后清理旧代码和字段

2. **回滚计划**
   - 保留回滚脚本
   - 数据备份策略
   - 快速回滚机制

### 监控和告警

1. **业务指标监控**
   - 自动开始成功率
   - 服务完成及时率
   - 超时订单数量
   - 提醒发送成功率

2. **系统指标监控**
   - 调度器运行状态
   - 数据库连接池状态
   - 内存和CPU使用率
   - 错误日志统计

3. **告警规则**
   - 自动开始失败率超过5%
   - 超时订单数量异常增长
   - 调度器停止运行
   - 数据库连接异常

### 灰度发布策略

1. **功能开关**
   - 自动开始服务开关
   - 完成提醒开关
   - 免打扰时间开关

2. **分批发布**
   - 先在测试环境验证
   - 小批量用户试点
   - 逐步扩大范围
   - 全量发布