# Requirements Document

## Introduction

本需求文档旨在优化陪诊订单的服务流程，解决当前系统中陪诊师忘记点击"开始服务"导致订单被误退款的问题。通过实现自动开始服务机制，简化操作流程，提升用户体验，同时确保服务质量和合规性。

## Requirements

### Requirement 1

**User Story:** 作为系统管理员，我希望订单能在预约时间到达时自动开始服务，这样陪诊师就不需要手动点击"开始服务"按钮，避免因忘记操作导致的订单退款问题。

#### Acceptance Criteria

1. WHEN 订单状态为已支付且匹配状态为已匹配 AND 当前时间达到预约时间 THEN 系统 SHALL 自动将订单状态更新为进行中
2. WHEN 系统自动开始服务时 THEN 系统 SHALL 记录自动开始时间(auto_started_at)和预计结束时间(expected_end_time)
3. WHEN 自动开始服务后 THEN 系统 SHALL 向陪诊师发送服务开始通知
4. WHEN 自动开始服务时 THEN 系统 SHALL 将实际服务开始时间设置为预约时间而非当前时间

### Requirement 2

**User Story:** 作为陪诊师，我希望在服务完成后能够提交服务总结和照片，这样系统就能确认服务已完成，我也能获得相应的报酬。

#### Acceptance Criteria

1. WHEN 陪诊师提交服务完成确认时 THEN 系统 SHALL 要求必填服务总结(最少50字)
2. WHEN 陪诊师提交服务完成确认时 THEN 系统 SHALL 允许可选上传服务照片
3. WHEN 陪诊师成功提交服务成果时 THEN 系统 SHALL 将订单状态更新为服务完成(状态4)
4. WHEN 订单状态为服务完成时 THEN 系统 SHALL 允许进行结算流程
5. IF 陪诊师未点击完成服务且未提交服务成果 THEN 系统 SHALL NOT 允许订单进入结算流程
6. WHEN 订单处于异常状态时 THEN 系统 SHALL NOT 允许自动结算，需要人工审核后处理

### Requirement 3

**User Story:** 作为用户，我希望能够对已完成的服务进行评价，但评价不应该影响订单的完成状态和陪诊师的结算。

#### Acceptance Criteria

1. WHEN 订单状态为服务完成时 THEN 用户 SHALL 能够对服务进行评价
2. WHEN 用户提交评价时 THEN 系统 SHALL NOT 改变订单状态
3. WHEN 用户提交评价时 THEN 系统 SHALL NOT 影响结算流程
4. WHEN 用户未评价时 THEN 系统 SHALL 仍然允许订单正常结算
5. WHEN 用户评价后 THEN 系统 SHALL 将评价纳入陪诊师信用评级计算

### Requirement 4

**User Story:** 作为系统管理员，我希望对超时未提交服务成果的订单进行简化的提醒策略，通过短信提醒和异常状态标记来确保服务质量。

#### Acceptance Criteria

1. WHEN 订单超过预计结束时间1小时且未提交服务成果 THEN 系统 SHALL 发送短信提醒
2. WHEN 订单超过预计结束时间10小时且未提交服务成果 THEN 系统 SHALL 在陪诊管理系统订单列表中标记订单状态为异常状态
3. WHEN 提醒时间在22:00-7:00之间 THEN 系统 SHALL 延迟到7:00后发送提醒
4. WHEN 订单被标记为异常状态时 THEN 系统 SHALL 通知管理人员进行人工处理
5. WHEN 订单处于异常状态时 THEN 系统 SHALL 在管理后台显示明显的异常标识

### Requirement 5

**User Story:** 作为系统管理员，我希望简化订单状态，移除不必要的状态和功能，使系统更加清晰易维护。

#### Acceptance Criteria

1. WHEN 系统重构时 THEN 系统 SHALL 移除"服务延迟"状态(原状态8)
2. WHEN 系统重构时 THEN 系统 SHALL 移除"待确认完成"状态(原状态9)
3. WHEN 系统重构时 THEN 系统 SHALL 移除订单恢复相关功能和API
4. WHEN 系统重构时 THEN 系统 SHALL 移除订单延迟统计视图
5. WHEN 系统重构时 THEN 系统 SHALL 保持订单状态为：1待支付、2已支付、3进行中、4服务完成、5已取消、6已退款、7待退款

### Requirement 6

**User Story:** 作为系统管理员，我希望调整过期订单处理逻辑，避免已匹配订单被误退款。

#### Acceptance Criteria

1. WHEN 订单已匹配且预约时间过期时 THEN 系统 SHALL NOT 自动创建退款申请
2. WHEN 订单未匹配且预约时间过期时 THEN 系统 SHALL 继续执行自动退款逻辑
3. WHEN 已匹配订单超时时 THEN 系统 SHALL 执行人性化提醒流程而非退款流程
4. WHEN 订单出现异常情况时 THEN 系统 SHALL 标记为需要人工处理而非自动操作

### Requirement 7

**User Story:** 作为开发人员，我希望系统能够记录详细的服务时间信息，便于后续的数据分析和问题排查。

#### Acceptance Criteria

1. WHEN 系统自动开始服务时 THEN 系统 SHALL 记录auto_started_at字段为当前时间
2. WHEN 系统自动开始服务时 THEN 系统 SHALL 记录actual_service_start_time字段为预约时间
3. WHEN 系统计算预计结束时间时 THEN 系统 SHALL 基于预约时间加上服务时长
4. WHEN 陪诊师提交服务成果时 THEN 系统 SHALL 记录actual_service_end_time字段
5. WHEN 需要提醒时 THEN 系统 SHALL 记录last_reminder_at和reminder_count字段

### Requirement 8

**User Story:** 作为系统管理员，我希望确保数据库结构的简洁性，移除不必要的表和字段。

#### Acceptance Criteria

1. WHEN 数据库迁移时 THEN 系统 SHALL 添加auto_started_at、expected_end_time、service_summary、service_photos、special_notes、last_reminder_at、reminder_count字段
2. WHEN 数据库迁移时 THEN 系统 SHALL NOT 创建order_recovery_logs表
3. WHEN 数据库迁移时 THEN 系统 SHALL NOT 创建order_delay_stats视图
4. WHEN 数据库迁移时 THEN 系统 SHALL 更新订单状态字段注释为最新的状态说明
5. WHEN 回滚迁移时 THEN 系统 SHALL 能够安全地移除新增字段而不影响现有数据