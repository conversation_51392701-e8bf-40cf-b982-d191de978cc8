# 订单状态流转优化设计文档

## 设计概述

本设计旨在完全移除 `status = 4` (OrderStatusCompleted) 的使用，统一所有订单完成流程为T+2审核机制，确保状态流转的唯一性和业务逻辑的一致性。

## 架构设计

### 当前状态流转问题分析

#### 现有的多路径问题
```mermaid
graph TD
    A[订单进行中 status=3] --> B{完成方式}
    B -->|路径1: 旧接口| C[直接完成 status=4]
    B -->|路径2: 管理员强制| D[直接完成 status=4]
    B -->|路径3: 异常处理| E[直接完成 status=4]
    B -->|路径4: 模型方法| F[直接完成 status=4]
    B -->|路径5: 新接口| G[T+2审核 status=8]
    
    C --> H[直接结算 status=13]
    D --> H
    E --> H
    F --> H
    G --> I[审核中 status=9]
    I --> J[审核通过 status=10]
    J --> H
    
    style C fill:#ffcccc
    style D fill:#ffcccc
    style E fill:#ffcccc
    style F fill:#ffcccc
    style G fill:#ccffcc
```

#### 优化后的统一流程
```mermaid
graph TD
    A[订单进行中 status=3] --> B{完成触发}
    B -->|陪诊师完成| C[进入T+2审核 status=8]
    B -->|管理员强制完成| D[进入T+2审核 status=8]
    B -->|异常处理完成| E[进入T+2审核 status=8]
    B -->|系统自动完成| F[进入T+2审核 status=8]
    
    C --> G[审核中 status=9]
    D --> G
    E --> G
    F --> G
    
    G --> H{审核结果}
    H -->|通过| I[审核通过 status=10]
    H -->|不通过| J[审核不通过 status=11]
    
    I --> K[结算 status=13]
    
    style C fill:#ccffcc
    style D fill:#ccffcc
    style E fill:#ccffcc
    style F fill:#ccffcc
```

## 组件设计

### 1. 订单完成服务重构 (OrderCompletionService)

```go
type IOrderCompletionService interface {
    // 统一的订单完成入口
    CompleteOrder(ctx context.Context, req *OrderCompletionRequest) error
    
    // 陪诊师完成服务
    CompleteByAttendant(ctx context.Context, orderID uint, attendantID uint, evidence *ServiceEvidence) error
    
    // 管理员强制完成
    ForceCompleteByAdmin(ctx context.Context, orderID uint, adminID uint, reason string) error
    
    // 异常处理系统完成
    CompleteByExceptionHandler(ctx context.Context, orderID uint, handlerID string, reason string) error
    
    // 系统自动完成
    AutoComplete(ctx context.Context, orderID uint, reason string) error
}

type OrderCompletionRequest struct {
    OrderID      uint                 `json:"order_id"`
    CompletionType CompletionType     `json:"completion_type"`
    OperatorID   uint                 `json:"operator_id"`
    Reason       string               `json:"reason"`
    Evidence     *ServiceEvidence     `json:"evidence,omitempty"`
    AutoReview   bool                 `json:"auto_review"` // 是否自动审核通过
}

type CompletionType int

const (
    CompletionTypeAttendant         CompletionType = 1 // 陪诊师完成
    CompletionTypeAdminForce        CompletionType = 2 // 管理员强制
    CompletionTypeExceptionHandler  CompletionType = 3 // 异常处理
    CompletionTypeAutoSystem        CompletionType = 4 // 系统自动
)
```

### 2. T+2审核服务扩展 (ReviewService)

```go
type IReviewService interface {
    // 创建审核记录（支持不同完成类型）
    CreateReview(ctx context.Context, req *CreateReviewRequest) error
    
    // 自动审核（用于特殊情况）
    AutoApproveReview(ctx context.Context, orderID uint, reason string) error
    
    // 手动审核
    ManualReview(ctx context.Context, req *ManualReviewRequest) error
    
    // 获取待审核订单
    GetPendingReviews(ctx context.Context, filter *ReviewFilter) ([]*OrderReview, error)
}

type CreateReviewRequest struct {
    OrderID        uint           `json:"order_id"`
    CompletionType CompletionType `json:"completion_type"`
    OperatorID     uint           `json:"operator_id"`
    Reason         string         `json:"reason"`
    AutoApprove    bool           `json:"auto_approve"` // 特殊情况自动审核
    ReviewDeadline time.Time      `json:"review_deadline"`
}
```

### 3. 旧接口废弃处理

```go
// 在 AttendantHandler 中废弃旧接口
func (h *AttendantHandler) EndService(c *gin.Context) {
    // 返回废弃提示并重定向到新接口
    response.Error(c, 410, "此接口已废弃，请使用新的订单完成接口 POST /api/v1/order-actions/:order_id/complete")
    return
}

// 或者提供兼容性包装
func (h *AttendantHandler) EndServiceCompat(c *gin.Context) {
    var req EndServiceRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.Error(c, 400, "参数错误")
        return
    }
    
    // 转换为新的完成请求
    completionReq := &OrderCompletionRequest{
        OrderID:        req.OrderID,
        CompletionType: CompletionTypeAttendant,
        OperatorID:     req.AttendantID,
        Evidence:       req.Evidence,
        AutoReview:     false, // 正常走审核流程
    }
    
    if err := h.orderCompletionService.CompleteOrder(c, completionReq); err != nil {
        response.Error(c, 500, err.Error())
        return
    }
    
    response.Success(c, "订单已提交审核，请等待T+2审核完成")
}
```

### 4. 结算服务简化 (SettlementService)

```go
// 移除对 status = 4 的支持，只处理 status = 10
func (s *SettlementServiceImpl) ProcessOrderSettlement(ctx context.Context, orderID uint) error {
    order, err := s.orderRepo.GetByID(ctx, orderID)
    if err != nil {
        return err
    }
    
    // 只处理审核通过的订单
    if order.Status != OrderStatusReviewApproved { // status = 10
        return fmt.Errorf("订单状态不符合结算条件，当前状态: %d", order.Status)
    }
    
    // 执行结算逻辑
    return s.executeSettlement(ctx, order)
}

// 批量处理待结算订单
func (s *SettlementServiceImpl) ProcessPendingSettlements(ctx context.Context) error {
    // 只查询 status = 10 的订单
    orders, err := s.orderRepo.GetOrdersByStatus(ctx, OrderStatusReviewApproved)
    if err != nil {
        return err
    }
    
    for _, order := range orders {
        if err := s.ProcessOrderSettlement(ctx, order.ID); err != nil {
            log.Errorf("处理订单结算失败: orderID=%d, error=%v", order.ID, err)
            continue
        }
    }
    
    return nil
}
```

### 5. 数据迁移设计

#### 历史数据迁移脚本
```sql
-- 1. 迁移现有 status = 4 的订单到新流程
-- 创建临时表记录迁移信息
CREATE TABLE order_status_migration_log (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    order_id BIGINT UNSIGNED NOT NULL,
    old_status TINYINT NOT NULL,
    new_status TINYINT NOT NULL,
    migration_reason VARCHAR(255) NOT NULL,
    migrated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_order_id (order_id)
);

-- 2. 将 status = 4 的订单迁移到 status = 10 (审核通过)
UPDATE orders 
SET status = 10, -- OrderStatusReviewApproved
    updated_at = NOW()
WHERE status = 4;

-- 3. 记录迁移日志
INSERT INTO order_status_migration_log (order_id, old_status, new_status, migration_reason)
SELECT id, 4, 10, '历史数据迁移：status=4 -> status=10'
FROM orders 
WHERE status = 10 AND updated_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE);

-- 4. 为迁移的订单创建审核记录（标记为自动审核通过）
INSERT INTO order_reviews (
    order_id, 
    completion_type, 
    operator_id, 
    review_status, 
    review_result, 
    review_reason,
    auto_approved,
    created_at,
    updated_at
)
SELECT 
    id,
    1, -- CompletionTypeAttendant (默认)
    attendant_id,
    3, -- ReviewStatusApproved
    1, -- ReviewResultApproved
    '历史数据迁移自动审核通过',
    1, -- auto_approved = true
    completed_at,
    completed_at
FROM orders 
WHERE id IN (
    SELECT order_id FROM order_status_migration_log 
    WHERE old_status = 4 AND new_status = 10
);
```

#### 回滚脚本
```sql
-- 回滚迁移（如果需要）
UPDATE orders 
SET status = 4,
    updated_at = NOW()
WHERE id IN (
    SELECT order_id FROM order_status_migration_log 
    WHERE old_status = 4 AND new_status = 10
);

-- 删除迁移创建的审核记录
DELETE FROM order_reviews 
WHERE order_id IN (
    SELECT order_id FROM order_status_migration_log 
    WHERE old_status = 4 AND new_status = 10
) AND auto_approved = 1;
```

### 6. 状态常量重构

```go
// 移除 OrderStatusCompleted = 4 的使用
const (
    OrderStatusCreated         = 1  // 待支付
    OrderStatusPaid           = 2  // 已支付
    OrderStatusInProgress     = 3  // 进行中
    // OrderStatusCompleted   = 4  // 已完成 - 废弃
    OrderStatusCancelled      = 5  // 已取消
    OrderStatusRefunded       = 6  // 已退款
    OrderStatusPendingRefund  = 7  // 待退款
    OrderStatusPendingReview  = 8  // 待审核 (T+2)
    OrderStatusUnderReview    = 9  // 审核中
    OrderStatusReviewApproved = 10 // 审核通过
    OrderStatusReviewRejected = 11 // 审核不通过
    OrderStatusDisputed       = 12 // 争议中
    OrderStatusSettled        = 13 // 已结算
)

// 状态文本映射更新
func GetOrderStatusText(status int) string {
    switch status {
    case OrderStatusCreated:
        return "待支付"
    case OrderStatusPaid:
        return "已支付"
    case OrderStatusInProgress:
        return "进行中"
    case OrderStatusCancelled:
        return "已取消"
    case OrderStatusRefunded:
        return "已退款"
    case OrderStatusPendingRefund:
        return "待退款"
    case OrderStatusPendingReview:
        return "待审核"
    case OrderStatusUnderReview:
        return "审核中"
    case OrderStatusReviewApproved:
        return "审核通过"
    case OrderStatusReviewRejected:
        return "审核不通过"
    case OrderStatusDisputed:
        return "争议中"
    case OrderStatusSettled:
        return "已结算"
    default:
        return "未知状态"
    }
}
```

## API设计

### 1. 统一订单完成API

```go
// POST /api/v1/order-actions/:order_id/complete
type OrderCompleteRequest struct {
    CompletionType CompletionType    `json:"completion_type" binding:"required"`
    Reason         string            `json:"reason" binding:"max=500"`
    Evidence       *ServiceEvidence  `json:"evidence,omitempty"`
    AutoReview     bool              `json:"auto_review"` // 特殊情况自动审核
}

type ServiceEvidence struct {
    Photos      []string `json:"photos"`
    Description string   `json:"description"`
    Location    string   `json:"location"`
    CompletedAt string   `json:"completed_at"`
}
```

### 2. 管理员强制完成API

```go
// POST /api/v1/admin/orders/:order_id/force-complete
type ForceCompleteRequest struct {
    Reason     string `json:"reason" binding:"required,max=500"`
    AutoReview bool   `json:"auto_review"` // 是否自动审核通过
}
```

### 3. 异常处理完成API

```go
// POST /api/v1/system/orders/:order_id/exception-complete
type ExceptionCompleteRequest struct {
    HandlerID  string `json:"handler_id" binding:"required"`
    Reason     string `json:"reason" binding:"required,max=500"`
    AutoReview bool   `json:"auto_review"` // 异常情况通常自动审核
}
```

## 业务规则

### 核心业务规则
1. **统一完成流程**：所有订单完成都必须进入T+2审核流程
2. **特殊情况自动审核**：管理员强制完成和异常处理可以设置自动审核通过
3. **审核时间控制**：正常完成48小时审核期，特殊情况可立即审核
4. **状态流转唯一性**：移除所有直接跳到status=4的路径

### 自动审核规则
```go
func (s *OrderCompletionService) shouldAutoApprove(completionType CompletionType, reason string) bool {
    switch completionType {
    case CompletionTypeAttendant:
        return false // 陪诊师完成需要人工审核
    case CompletionTypeAdminForce:
        return true  // 管理员强制完成自动审核
    case CompletionTypeExceptionHandler:
        return true  // 异常处理自动审核
    case CompletionTypeAutoSystem:
        return true  // 系统自动完成自动审核
    default:
        return false
    }
}
```

## 监控和告警设计

### 1. 关键指标监控
- 订单完成方式分布统计
- T+2审核通过率和时效性
- 自动审核订单比例
- 状态迁移成功率
- 旧接口调用次数（监控废弃进度）

### 2. 告警规则
```go
type StatusFlowMonitor struct {
    // 监控旧接口调用
    oldAPICallCount int64
    
    // 监控状态异常
    invalidStatusTransitions int64
    
    // 监控审核积压
    pendingReviewCount int64
}

func (m *StatusFlowMonitor) CheckAlerts() {
    // 旧接口调用告警
    if m.oldAPICallCount > 0 {
        alert.Send("检测到旧接口调用，需要尽快迁移")
    }
    
    // 审核积压告警
    if m.pendingReviewCount > 100 {
        alert.Send("T+2审核订单积压过多，需要处理")
    }
}
```

## 实施策略

### 1. 分阶段实施
```mermaid
graph TD
    A[阶段1: 后端服务重构] --> B[阶段2: 数据迁移]
    B --> C[阶段3: API接口更新]
    C --> D[阶段4: 前端代码迁移]
    D --> E[阶段5: 旧接口废弃]
    E --> F[阶段6: 监控和优化]
```

### 2. 兼容性策略
- 保持API响应格式兼容
- 提供旧接口的兼容性包装
- 逐步迁移前端代码
- 监控迁移进度

### 3. 回滚策略
- 保留数据迁移日志
- 提供完整的回滚脚本
- 监控系统稳定性
- 准备应急预案

## 优势分析

### 1. 业务逻辑统一
- 所有订单完成都走相同流程
- 状态流转逻辑清晰一致
- 减少业务理解成本

### 2. 风险控制加强
- 所有订单都经过审核
- 特殊情况有明确标记
- 操作日志完整可追溯

### 3. 系统维护性提升
- 代码逻辑简化
- 状态判断统一
- 测试覆盖更容易

### 4. 合规性保证
- 财务分账更规范
- 审核流程更完整
- 监管要求更易满足

## 风险评估

### 1. 数据迁移风险
- **风险**：历史数据迁移可能影响现有订单
- **缓解**：充分测试迁移脚本，提供回滚方案

### 2. 业务中断风险
- **风险**：接口变更可能影响前端功能
- **缓解**：提供兼容性包装，分阶段迁移

### 3. 性能影响风险
- **风险**：统一审核流程可能增加系统负载
- **缓解**：优化审核服务性能，增加监控

### 4. 用户体验风险
- **风险**：所有订单都需要审核可能影响体验
- **缓解**：特殊情况自动审核，优化审核时效