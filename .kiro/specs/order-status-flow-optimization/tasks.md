# 订单状态流转优化实施任务列表

## 实施概述

本实施计划将完全移除 `status = 4` (OrderStatusCompleted) 的使用，统一所有订单完成流程为T+2审核机制，确保状态流转的唯一性和业务逻辑的一致性。

## 任务列表

### 阶段一：后端核心服务重构

- [x] 1. 重构订单完成服务
  - 创建统一的 IOrderCompletionService 接口
  - 实现 OrderCompletionServiceImpl 支持多种完成类型
  - 定义 CompletionType 枚举和相关数据结构
  - 实现统一的订单完成入口 CompleteOrder 方法
  - 实现陪诊师完成、管理员强制完成、异常处理完成、系统自动完成等方法
  - _需求: 1.1, 2.1_

- [x] 1.1 创建订单完成服务接口
  - 在 `backend/internal/service/` 目录创建 IOrderCompletionService 接口
  - 定义 OrderCompletionRequest、CompletionType 等数据结构
  - 定义 ServiceEvidence 结构体用于服务凭证
  - _需求: 1.1_

- [x] 1.2 实现订单完成服务
  - 在 `backend/internal/service/impl/` 创建 OrderCompletionServiceImpl
  - 实现 CompleteOrder 统一完成方法
  - 实现 CompleteByAttendant 陪诊师完成方法
  - 实现 ForceCompleteByAdmin 管理员强制完成方法
  - 实现 CompleteByExceptionHandler 异常处理完成方法
  - 实现 AutoComplete 系统自动完成方法
  - _需求: 1.1, 1.2_

- [x] 2. 扩展T+2审核服务
  - 扩展现有的 IReviewService 接口支持不同完成类型
  - 实现 CreateReview 方法支持 CompletionType 参数
  - 实现 AutoApproveReview 方法用于特殊情况自动审核
  - 更新审核记录数据结构包含完成类型信息
  - 实现自动审核规则判断逻辑
  - _需求: 1.1, 3.1_

- [x] 2.1 扩展审核服务接口
  - 更新 IReviewService 接口添加 CreateReview 和 AutoApproveReview 方法
  - 创建 CreateReviewRequest 结构体包含完成类型
  - 更新 OrderReview 模型添加 completion_type 字段
  - _需求: 1.1_

- [x] 2.2 实现扩展的审核服务
  - 在 ReviewServiceImpl 中实现新的审核创建逻辑
  - 实现自动审核规则：管理员强制、异常处理、系统自动完成自动审核通过
  - 实现审核记录创建时的完成类型记录
  - _需求: 1.1, 3.1_

- [x] 3. 简化结算服务
  - 修改 SettlementServiceImpl 移除对 status = 4 的支持
  - 更新 ProcessOrderSettlement 方法只处理 status = 10 的订单
  - 更新 ProcessPendingSettlements 方法只查询 status = 10 的订单
  - 添加状态检查逻辑，拒绝处理非审核通过的订单
  - _需求: 4.1, 4.2, 4.3_

- [x] 3.1 修改结算服务状态检查
  - 在 ProcessOrderSettlement 方法中添加状态检查
  - 只允许处理 OrderStatusReviewApproved (status = 10) 的订单
  - 移除所有对 OrderStatusCompleted (status = 4) 的处理逻辑
  - _需求: 4.1, 4.2_

- [x] 3.2 更新批量结算逻辑
  - 修改 ProcessPendingSettlements 方法的查询条件
  - 只查询和处理 status = 10 的订单
  - 更新相关的日志和错误处理
  - _需求: 4.3_

### 阶段二：数据库迁移和状态常量更新

- [x] 4. 执行数据库迁移
  - 创建订单状态迁移日志表 order_status_migration_log
  - 将现有 status = 4 的订单迁移到 status = 10
  - 为迁移的订单创建对应的审核记录
  - 记录详细的迁移日志用于追踪和回滚
  - 验证迁移结果的正确性
  - _需求: 5.1, 5.2_

- [x] 4.1 创建迁移脚本
  - 编写 SQL 迁移脚本创建 order_status_migration_log 表
  - 编写订单状态迁移脚本 (status 4 -> 10)
  - 编写审核记录创建脚本为迁移订单补充审核信息
  - 编写迁移验证脚本检查数据完整性
  - _需求: 5.1_

- [x] 4.2 执行数据迁移
  - 在开发环境执行迁移脚本并验证结果
  - 备份生产环境数据
  - 在生产环境执行迁移脚本
  - 验证迁移后的数据完整性和业务功能
  - _需求: 5.2_

- [x] 4.3 创建回滚方案
  - 编写回滚脚本将 status = 10 恢复为 status = 4
  - 编写审核记录清理脚本
  - 测试回滚脚本的正确性
  - 准备应急回滚预案
  - _需求: 5.2_

- [x] 5. 更新状态常量和映射
  - 更新订单状态常量定义，标记 OrderStatusCompleted = 4 为废弃
  - 更新 GetOrderStatusText 函数移除对 status = 4 的支持
  - 更新所有使用 status = 4 的代码逻辑
  - 添加状态迁移的兼容性处理
  - _需求: 3.1, 3.2_

- [x] 5.1 更新状态常量定义
  - 在订单模型中注释或移除 OrderStatusCompleted = 4
  - 更新状态文本映射函数
  - 添加状态验证函数检查无效状态
  - _需求: 3.1_

- [x] 5.2 清理相关代码逻辑
  - 搜索并更新所有使用 status = 4 的代码
  - 将相关逻辑改为使用 status = 10
  - 更新状态判断条件和查询逻辑
  - _需求: 3.2_

### 阶段三：API接口更新和旧接口处理

- [x] 6. 更新订单完成API接口
  - 更新现有的 POST /api/v1/order-actions/:order_id/complete 接口
  - 支持 CompletionType 参数区分不同完成类型
  - 集成新的 OrderCompletionService 服务
  - 更新接口响应格式和错误处理
  - _需求: 1.1, 2.1_

- [x] 6.1 更新订单操作接口
  - 修改 OrderActionHandler 中的 Complete 方法
  - 更新请求参数结构体支持 completion_type
  - 集成 OrderCompletionService 替换原有逻辑
  - _需求: 1.1_

- [x] 6.2 添加管理员强制完成接口
  - 创建 POST /api/v1/admin/orders/:order_id/force-complete 接口
  - 实现 ForceCompleteRequest 参数验证
  - 集成管理员权限验证
  - _需求: 2.1_

- [x] 6.3 添加异常处理完成接口
  - 创建 POST /api/v1/system/orders/:order_id/exception-complete 接口
  - 实现 ExceptionCompleteRequest 参数处理
  - 添加系统级别的权限验证
  - _需求: 2.1_

- [x] 7. 处理旧的结束服务接口
  - 废弃 POST /api/v1/attendants/end-service 接口
  - 提供兼容性包装或返回废弃提示
  - 监控旧接口的调用情况
  - 逐步引导前端迁移到新接口
  - _需求: 2.2, 2.3, 2.4_

- [x] 7.1 废弃旧接口实现
  - 修改 AttendantHandler 中的 EndService 方法
  - 返回 410 Gone 状态码和废弃提示信息
  - 或者提供兼容性包装调用新的完成服务
  - _需求: 2.2_

- [x] 7.2 添加接口调用监控
  - 在旧接口中添加调用统计和日志记录
  - 监控旧接口的使用频率和来源
  - 设置告警机制提醒尽快迁移
  - _需求: 2.3_

### 阶段四：前端代码适配和迁移

- [x] 8. 更新陪诊师工作台
  - 修改 `frontend/pages/attendant/workbench/index.js` 中的订单完成逻辑
  - 将旧的 end-service 接口调用改为新的 complete 接口
  - 更新请求参数格式包含 completion_type
  - 更新成功提示信息说明需要等待审核
  - _需求: 2.4, 5.3_

- [x] 8.1 修改接口调用逻辑
  - 找到工作台中的订单完成相关代码
  - 将 POST /api/v1/attendants/end-service 改为 POST /api/v1/order-actions/:order_id/complete
  - 更新请求参数添加 completion_type: 1 (陪诊师完成)
  - _需求: 2.4_

- [x] 8.2 更新用户界面提示
  - 修改订单完成后的提示信息
  - 说明订单已提交审核，需要等待T+2审核完成
  - 更新相关的状态显示和用户引导
  - _需求: 5.3_

- [x] 9. 更新订单状态显示
  - 修改前端订单列表和详情页面的状态显示逻辑
  - 移除对 status = 4 的显示支持
  - 更新状态文本映射，确保 status = 10 显示为"已完成"
  - 处理状态迁移期间的兼容性问题
  - _需求: 5.3_

- [x] 9.1 更新状态映射函数
  - 修改前端的订单状态文本映射
  - 移除 status = 4 的映射
  - 确保 status = 10 正确显示为"已完成"
  - _需求: 5.3_

- [x] 9.2 更新订单列表和详情页
  - 检查所有显示订单状态的页面
  - 确保状态显示逻辑正确
  - 处理可能的状态显示异常
  - _需求: 5.3_

- [x] 10. 更新管理后台界面
  - 修改管理后台的订单管理页面
  - 添加强制完成订单的操作按钮和界面
  - 更新订单状态筛选和显示逻辑
  - 添加订单完成类型的显示
  - _需求: 2.1, 5.3_

- [x] 10.1 添加管理员强制完成功能
  - 在订单详情页添加"强制完成"按钮
  - 实现强制完成的确认对话框
  - 集成 force-complete 接口调用
  - _需求: 2.1_

- [x] 10.2 更新订单状态管理
  - 更新管理后台的状态筛选器
  - 移除对 status = 4 的筛选支持
  - 添加完成类型的显示列
  - _需求: 5.3_

### 阶段五：监控告警和日志完善

- [x] 11. 实现状态流转监控
  - 创建 StatusFlowMonitor 监控服务
  - 监控订单完成方式的分布统计
  - 监控T+2审核的通过率和时效性
  - 监控旧接口的调用次数
  - 实现异常状态流转的告警机制
  - _需求: 5.1, 5.4_

- [x] 11.1 创建监控服务
  - 实现 StatusFlowMonitor 结构体
  - 添加各种监控指标的统计方法
  - 实现监控数据的收集和存储
  - _需求: 5.1_

- [x] 11.2 实现告警机制
  - 设置旧接口调用告警
  - 设置异常状态流转告警
  - 设置审核积压告警
  - 集成现有的告警通知系统
  - _需求: 5.4_

- [x] 12. 完善操作日志记录
  - 在所有订单完成操作中添加详细日志
  - 记录完成类型、操作人、操作原因等信息
  - 实现日志的结构化存储和查询
  - 添加敏感操作的审计日志
  - _需求: 5.1, 5.2_

- [x] 12.1 添加操作日志
  - 在 OrderCompletionService 中添加日志记录
  - 记录每次订单完成的详细信息
  - 包含操作人、时间、原因、结果等
  - _需求: 5.1_

- [x] 12.2 实现审计日志
  - 为管理员强制完成添加审计日志
  - 为异常处理完成添加系统日志
  - 实现日志的查询和导出功能
  - _需求: 5.2_

### 阶段六：测试验证和性能优化

- [ ] 13. 编写单元测试
  - 为 OrderCompletionService 编写完整的单元测试
  - 测试不同完成类型的处理逻辑
  - 测试自动审核规则的正确性
  - 测试状态流转的一致性
  - 测试异常情况的处理
  - _需求: 1.1, 1.2, 3.1_

- [ ] 13.1 测试订单完成服务
  - 测试 CompleteOrder 方法的各种场景
  - 测试不同 CompletionType 的处理逻辑
  - 测试自动审核和手动审核的分支
  - _需求: 1.1, 1.2_

- [ ] 13.2 测试状态流转逻辑
  - 测试状态从 3 -> 8 -> 9 -> 10 的完整流程
  - 测试特殊情况的自动审核流程
  - 测试状态迁移的正确性
  - _需求: 3.1_

- [ ] 14. 进行集成测试
  - 测试完整的订单完成到结算流程
  - 测试前端接口调用的正确性
  - 测试数据迁移的完整性
  - 验证旧接口废弃的影响
  - 测试监控告警的有效性
  - _需求: 1.1-5.4_

- [ ] 14.1 测试端到端流程
  - 测试陪诊师完成订单的完整流程
  - 测试管理员强制完成的流程
  - 测试异常处理完成的流程
  - _需求: 1.1, 2.1_

- [ ] 14.2 测试数据一致性
  - 验证迁移后的订单数据正确性
  - 测试结算服务只处理正确状态的订单
  - 验证前端状态显示的准确性
  - _需求: 4.1, 5.2_

- [ ] 15. 性能测试和优化
  - 测试统一审核流程对系统性能的影响
  - 优化订单完成服务的执行效率
  - 优化数据库查询和索引设计
  - 监控系统资源使用情况
  - _需求: 系统性能_

- [ ] 15.1 性能基准测试
  - 测试订单完成操作的响应时间
  - 测试批量审核处理的性能
  - 测试数据库查询的执行效率
  - _需求: 系统性能_

- [ ] 15.2 性能优化实施
  - 优化订单完成服务的数据库操作
  - 添加必要的数据库索引
  - 优化审核服务的批量处理逻辑
  - _需求: 系统性能_

### 阶段七：部署上线和监控

- [ ] 16. 准备生产环境部署
  - 准备生产环境的数据迁移脚本
  - 配置生产环境的监控告警
  - 准备部署检查清单
  - 制定回滚预案和应急处理流程
  - _需求: 5.4_

- [ ] 16.1 准备部署脚本
  - 整理所有数据库迁移脚本
  - 准备应用程序部署脚本
  - 配置环境变量和配置文件
  - _需求: 5.4_

- [ ] 16.2 制定部署计划
  - 制定分阶段部署计划
  - 准备部署验证检查项
  - 制定回滚触发条件和流程
  - _需求: 5.4_

- [ ] 17. 执行生产环境部署
  - 执行数据库迁移脚本
  - 部署后端服务更新
  - 部署前端代码更新
  - 验证部署结果和功能正常性
  - 监控系统稳定性和性能指标
  - _需求: 5.4_

- [ ] 17.1 执行部署操作
  - 按计划执行数据库迁移
  - 部署后端和前端代码更新
  - 验证关键功能的正常运行
  - _需求: 5.4_

- [ ] 17.2 部署后验证
  - 验证订单完成流程正常工作
  - 验证状态迁移的正确性
  - 验证前端界面显示正常
  - 监控系统错误率和性能指标
  - _需求: 5.4_

- [ ] 18. 上线后监控和优化
  - 持续监控系统运行状态
  - 收集用户反馈和问题报告
  - 优化系统性能和用户体验
  - 处理上线后发现的问题
  - 完善监控告警规则
  - _需求: 5.5_

- [ ] 18.1 持续监控
  - 监控订单完成成功率
  - 监控审核处理时效性
  - 监控系统错误和异常
  - _需求: 5.5_

- [ ] 18.2 问题处理和优化
  - 及时处理用户反馈的问题
  - 优化发现的性能瓶颈
  - 完善监控告警机制
  - _需求: 5.5_

## 实施里程碑

### 里程碑1：核心服务重构完成 (预计2周)
- 订单完成服务重构完成
- T+2审核服务扩展完成
- 结算服务简化完成
- 单元测试通过

### 里程碑2：数据迁移和接口更新完成 (预计1.5周)
- 数据库迁移脚本完成并验证
- API接口更新完成
- 旧接口废弃处理完成
- 集成测试通过

### 里程碑3：前端适配和监控完善 (预计1.5周)
- 前端代码迁移完成
- 监控告警系统完善
- 操作日志记录完整
- 端到端测试通过

### 里程碑4：生产部署和上线 (预计1周)
- 生产环境部署完成
- 系统稳定运行
- 监控指标正常
- 用户反馈良好

## 总工期估算
**预计总工期：6周**

## 风险评估

### 高风险项
- 数据迁移可能影响现有订单业务
- 前端接口调用变更可能导致功能异常
- 统一审核流程可能增加系统负载

### 中风险项
- 旧接口废弃可能影响部分功能
- 状态显示逻辑变更可能导致界面异常
- 监控告警配置可能不够完善

### 低风险项
- 单元测试覆盖相对独立
- 性能优化可以逐步进行
- 日志记录为辅助功能

## 成功标准

1. **功能完整性**：所有订单完成都走统一的T+2审核流程
2. **数据一致性**：历史数据成功迁移，无数据丢失或错误
3. **接口兼容性**：前端功能正常，用户体验无明显变化
4. **性能指标**：系统响应时间无明显增加，错误率<0.1%
5. **监控有效性**：监控告警正常工作，异常能及时发现
6. **业务连续性**：部署过程中业务不中断，回滚方案可用
7. **代码质量**：单元测试覆盖率>80%，代码可维护性提升