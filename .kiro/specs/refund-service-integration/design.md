# 退款服务集成设计文档 - 完善自动退款功能

## 概述

本设计文档描述了退款服务与人工分配系统集成的技术架构和实现方案，重点解决当人工处理失败时的自动退款流程，确保用户能够及时获得退款。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[人工分配失败] --> B[自动退款触发器]
    B --> C[退款服务扩展]
    C --> D[退款金额计算]
    C --> E[支付系统集成]
    C --> F[状态管理]
    
    D --> G[退款策略引擎]
    E --> H[微信支付退款API]
    F --> I[订单状态更新]
    
    G --> J[退款记录]
    H --> K[退款结果处理]
    I --> L[通知系统]
    
    J --> M[审计日志]
    K --> N[异常处理]
    L --> O[用户通知]
```

### 服务依赖关系

```mermaid
graph LR
    A[ManualAssignmentService] --> B[RefundService]
    B --> C[PaymentService]
    B --> D[OrderRepository]
    B --> E[RefundRepository]
    
    C --> F[WechatPayment]
    D --> G[Database]
    E --> G
    
    B --> H[NotificationService]
    B --> I[AuditService]
    
    H --> J[SMS/WeChat/Email]
    I --> K[AuditLog]
```

## 核心组件设计

### 1. 退款服务接口扩展

```go
// IRefundService 退款服务接口扩展
type IRefundService interface {
    // 现有方法...
    
    // 匹配失败自动退款
    TriggerAutoRefundForMatchingFailure(ctx context.Context, req *AutoRefundRequest) error
    
    // 处理匹配失败退款
    ProcessMatchingFailureRefund(ctx context.Context, orderID uint, reason string) (*RefundResult, error)
    
    // 计算退款金额
    CalculateRefundAmount(ctx context.Context, orderID uint, refundType RefundType) (*RefundCalculation, error)
    
    // 获取退款状态
    GetRefundStatus(ctx context.Context, orderID uint) (*RefundStatus, error)
    
    // 重试失败的退款
    RetryFailedRefund(ctx context.Context, refundID uint) error
}

// AutoRefundRequest 自动退款请求
type AutoRefundRequest struct {
    OrderID     uint   `json:"order_id" validate:"required"`
    Reason      string `json:"reason" validate:"required"`
    AdminID     uint   `json:"admin_id"`
    TriggerType string `json:"trigger_type"` // manual, timeout, system
}

// RefundResult 退款结果
type RefundResult struct {
    RefundID        uint                   `json:"refund_id"`
    OrderID         uint                   `json:"order_id"`
    RefundAmount    decimal.Decimal        `json:"refund_amount"`
    RefundStatus    RefundStatus           `json:"refund_status"`
    TransactionID   string                 `json:"transaction_id"`
    ProcessedAt     time.Time              `json:"processed_at"`
    Details         *RefundCalculation     `json:"details"`
}

// RefundCalculation 退款金额计算详情
type RefundCalculation struct {
    OriginalAmount    decimal.Decimal `json:"original_amount"`
    RefundAmount      decimal.Decimal `json:"refund_amount"`
    DeductedFees      decimal.Decimal `json:"deducted_fees"`
    CouponRefund      decimal.Decimal `json:"coupon_refund"`
    CalculationRules  []string        `json:"calculation_rules"`
    PolicyApplied     string          `json:"policy_applied"`
}
```

### 2. 退款服务实现

```go
// RefundServiceImpl 退款服务实现
type RefundServiceImpl struct {
    db                *gorm.DB
    orderRepo         repository.IOrderRepository
    refundRepo        repository.IRefundRepository
    paymentService    service.IPaymentService
    notificationService service.INotificationService
    auditService      service.IAuditService
    logger            *zap.Logger
    config            *RefundConfig
}

// TriggerAutoRefundForMatchingFailure 触发匹配失败自动退款
func (s *RefundServiceImpl) TriggerAutoRefundForMatchingFailure(
    ctx context.Context, 
    req *AutoRefundRequest,
) error {
    // 验证请求参数
    if err := s.validateAutoRefundRequest(req); err != nil {
        return fmt.Errorf("请求参数验证失败: %w", err)
    }
    
    // 获取订单信息
    order, err := s.orderRepo.GetByID(ctx, req.OrderID)
    if err != nil {
        return fmt.Errorf("获取订单失败: %w", err)
    }
    
    // 验证订单状态
    if !s.canTriggerAutoRefund(order) {
        return fmt.Errorf("订单状态不允许自动退款: status=%d, matching_status=%d", 
            order.Status, order.MatchingStatus)
    }
    
    // 开始事务处理
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 更新订单状态为退款处理中
        if err := s.updateOrderRefundStatus(ctx, tx, req.OrderID, RefundStatusProcessing); err != nil {
            return err
        }
        
        // 异步处理退款
        go s.processRefundAsync(ctx, req)
        
        // 记录审计日志
        s.auditService.LogRefundTrigger(ctx, req.OrderID, req.AdminID, req.Reason)
        
        return nil
    })
}

// ProcessMatchingFailureRefund 处理匹配失败退款
func (s *RefundServiceImpl) ProcessMatchingFailureRefund(
    ctx context.Context, 
    orderID uint, 
    reason string,
) (*RefundResult, error) {
    // 计算退款金额
    calculation, err := s.CalculateRefundAmount(ctx, orderID, RefundTypeMatchingFailure)
    if err != nil {
        return nil, fmt.Errorf("计算退款金额失败: %w", err)
    }
    
    // 创建退款记录
    refund := &model.Refund{
        OrderID:      orderID,
        RefundAmount: calculation.RefundAmount,
        RefundReason: reason,
        RefundType:   RefundTypeMatchingFailure,
        Status:       RefundStatusProcessing,
        CreatedAt:    time.Now(),
    }
    
    if err := s.refundRepo.Create(ctx, refund); err != nil {
        return nil, fmt.Errorf("创建退款记录失败: %w", err)
    }
    
    // 调用支付系统退款
    paymentResult, err := s.processPaymentRefund(ctx, refund)
    if err != nil {
        // 更新退款状态为失败
        s.updateRefundStatus(ctx, refund.ID, RefundStatusFailed, err.Error())
        return nil, fmt.Errorf("支付系统退款失败: %w", err)
    }
    
    // 更新退款状态为成功
    refund.Status = RefundStatusSuccess
    refund.TransactionID = paymentResult.TransactionID
    refund.ProcessedAt = time.Now()
    
    if err := s.refundRepo.Update(ctx, refund); err != nil {
        s.logger.Error("更新退款记录失败", zap.Error(err))
    }
    
    // 更新订单状态
    s.updateOrderRefundStatus(ctx, s.db, orderID, RefundStatusSuccess)
    
    // 发送通知
    s.sendRefundNotification(ctx, refund, RefundNotificationSuccess)
    
    return &RefundResult{
        RefundID:      refund.ID,
        OrderID:       refund.OrderID,
        RefundAmount:  refund.RefundAmount,
        RefundStatus:  RefundStatusSuccess,
        TransactionID: refund.TransactionID,
        ProcessedAt:   refund.ProcessedAt,
        Details:       calculation,
    }, nil
}
```

### 3. 退款金额计算引擎

```go
// RefundCalculationEngine 退款金额计算引擎
type RefundCalculationEngine struct {
    config *RefundPolicyConfig
    logger *zap.Logger
}

// CalculateRefundAmount 计算退款金额
func (e *RefundCalculationEngine) CalculateRefundAmount(
    ctx context.Context,
    order *model.Order,
    refundType RefundType,
) (*RefundCalculation, error) {
    calculation := &RefundCalculation{
        OriginalAmount: order.TotalAmount,
        RefundAmount:   decimal.Zero,
        DeductedFees:   decimal.Zero,
        CouponRefund:   decimal.Zero,
        CalculationRules: []string{},
    }
    
    // 根据退款类型应用不同策略
    switch refundType {
    case RefundTypeMatchingFailure:
        return e.calculateMatchingFailureRefund(order, calculation)
    case RefundTypeUserCancel:
        return e.calculateUserCancelRefund(order, calculation)
    case RefundTypeSystemError:
        return e.calculateSystemErrorRefund(order, calculation)
    default:
        return nil, fmt.Errorf("不支持的退款类型: %v", refundType)
    }
}

// calculateMatchingFailureRefund 计算匹配失败退款
func (e *RefundCalculationEngine) calculateMatchingFailureRefund(
    order *model.Order,
    calculation *RefundCalculation,
) (*RefundCalculation, error) {
    // 匹配失败通常全额退款
    calculation.RefundAmount = order.TotalAmount
    calculation.PolicyApplied = "匹配失败全额退款政策"
    calculation.CalculationRules = append(calculation.CalculationRules, 
        "匹配失败，退还全部支付金额")
    
    // 检查是否有优惠券
    if order.CouponAmount.GreaterThan(decimal.Zero) {
        calculation.CouponRefund = order.CouponAmount
        calculation.CalculationRules = append(calculation.CalculationRules,
            fmt.Sprintf("退还优惠券金额: %s", order.CouponAmount.String()))
    }
    
    // 检查是否需要扣除手续费
    if e.config.DeductProcessingFee && order.ProcessingFee.GreaterThan(decimal.Zero) {
        if e.shouldDeductFee(order) {
            calculation.DeductedFees = order.ProcessingFee
            calculation.RefundAmount = calculation.RefundAmount.Sub(order.ProcessingFee)
            calculation.CalculationRules = append(calculation.CalculationRules,
                fmt.Sprintf("扣除处理手续费: %s", order.ProcessingFee.String()))
        }
    }
    
    return calculation, nil
}
```

### 4. 支付系统集成

```go
// PaymentRefundProcessor 支付退款处理器
type PaymentRefundProcessor struct {
    wechatPayment *payment.WechatMiniAppPayment
    logger        *zap.Logger
    config        *PaymentConfig
}

// ProcessRefund 处理支付退款
func (p *PaymentRefundProcessor) ProcessRefund(
    ctx context.Context,
    refund *model.Refund,
    order *model.Order,
) (*PaymentRefundResult, error) {
    // 构建退款请求
    refundReq := &payment.RefundRequest{
        OutTradeNo:   order.OrderNo,
        OutRefundNo:  p.generateRefundNo(refund.ID),
        TotalAmount:  order.TotalAmount.IntPart(),
        RefundAmount: refund.RefundAmount.IntPart(),
        Reason:       refund.RefundReason,
        NotifyURL:    p.config.RefundNotifyURL,
    }
    
    // 调用微信支付退款API
    result, err := p.wechatPayment.Refund(ctx, refundReq)
    if err != nil {
        return nil, fmt.Errorf("微信支付退款失败: %w", err)
    }
    
    return &PaymentRefundResult{
        TransactionID: result.RefundID,
        Status:        result.Status,
        RefundTime:    result.RefundTime,
    }, nil
}

// 退款回调处理
func (p *PaymentRefundProcessor) HandleRefundCallback(
    ctx context.Context,
    callbackData *payment.RefundCallbackData,
) error {
    // 验证回调签名
    if !p.wechatPayment.VerifyRefundCallback(callbackData) {
        return fmt.Errorf("退款回调签名验证失败")
    }
    
    // 更新退款状态
    refund, err := p.refundRepo.GetByRefundNo(ctx, callbackData.OutRefundNo)
    if err != nil {
        return fmt.Errorf("获取退款记录失败: %w", err)
    }
    
    // 根据回调结果更新状态
    switch callbackData.RefundStatus {
    case "SUCCESS":
        refund.Status = RefundStatusSuccess
        refund.ProcessedAt = callbackData.RefundTime
    case "FAIL":
        refund.Status = RefundStatusFailed
        refund.FailureReason = callbackData.FailureReason
    }
    
    return p.refundRepo.Update(ctx, refund)
}
```

### 5. 异常处理和重试机制

```go
// RefundRetryManager 退款重试管理器
type RefundRetryManager struct {
    refundService service.IRefundService
    scheduler     *scheduler.Scheduler
    logger        *zap.Logger
    config        *RetryConfig
}

// RetryFailedRefunds 重试失败的退款
func (r *RefundRetryManager) RetryFailedRefunds(ctx context.Context) error {
    // 获取需要重试的退款记录
    failedRefunds, err := r.refundRepo.GetFailedRefunds(ctx, r.config.MaxRetryAge)
    if err != nil {
        return fmt.Errorf("获取失败退款记录失败: %w", err)
    }
    
    for _, refund := range failedRefunds {
        if r.shouldRetry(refund) {
            go r.retryRefund(ctx, refund)
        }
    }
    
    return nil
}

// retryRefund 重试单个退款
func (r *RefundRetryManager) retryRefund(ctx context.Context, refund *model.Refund) {
    // 检查重试次数
    if refund.RetryCount >= r.config.MaxRetryCount {
        r.logger.Warn("退款重试次数超限，转为人工处理",
            zap.Uint("refund_id", refund.ID),
            zap.Int("retry_count", refund.RetryCount))
        
        r.markForManualProcessing(ctx, refund)
        return
    }
    
    // 计算重试延迟
    delay := r.calculateRetryDelay(refund.RetryCount)
    time.Sleep(delay)
    
    // 更新重试次数
    refund.RetryCount++
    r.refundRepo.Update(ctx, refund)
    
    // 重新处理退款
    result, err := r.refundService.ProcessMatchingFailureRefund(
        ctx, refund.OrderID, refund.RefundReason)
    
    if err != nil {
        r.logger.Error("退款重试失败",
            zap.Uint("refund_id", refund.ID),
            zap.Int("retry_count", refund.RetryCount),
            zap.Error(err))
        
        // 记录重试失败
        r.recordRetryFailure(ctx, refund, err)
    } else {
        r.logger.Info("退款重试成功",
            zap.Uint("refund_id", refund.ID),
            zap.Int("retry_count", refund.RetryCount))
    }
}

// calculateRetryDelay 计算重试延迟（指数退避）
func (r *RefundRetryManager) calculateRetryDelay(retryCount int) time.Duration {
    baseDelay := time.Duration(r.config.BaseRetryDelay) * time.Second
    maxDelay := time.Duration(r.config.MaxRetryDelay) * time.Second
    
    delay := baseDelay * time.Duration(math.Pow(2, float64(retryCount)))
    if delay > maxDelay {
        delay = maxDelay
    }
    
    // 添加随机抖动
    jitter := time.Duration(rand.Intn(1000)) * time.Millisecond
    return delay + jitter
}
```

### 6. 通知系统集成

```go
// RefundNotificationService 退款通知服务
type RefundNotificationService struct {
    smsService    service.ISMSService
    wechatService service.IWechatService
    emailService  service.IEmailService
    logger        *zap.Logger
}

// SendRefundNotification 发送退款通知
func (n *RefundNotificationService) SendRefundNotification(
    ctx context.Context,
    refund *model.Refund,
    notificationType RefundNotificationType,
) error {
    // 获取用户信息
    user, err := n.userRepo.GetByOrderID(ctx, refund.OrderID)
    if err != nil {
        return fmt.Errorf("获取用户信息失败: %w", err)
    }
    
    // 构建通知内容
    notification := n.buildNotificationContent(refund, notificationType)
    
    // 发送多渠道通知
    var errors []error
    
    // 发送短信通知
    if user.Phone != "" {
        if err := n.sendSMSNotification(ctx, user.Phone, notification); err != nil {
            errors = append(errors, fmt.Errorf("短信通知失败: %w", err))
        }
    }
    
    // 发送微信通知
    if user.WechatOpenID != "" {
        if err := n.sendWechatNotification(ctx, user.WechatOpenID, notification); err != nil {
            errors = append(errors, fmt.Errorf("微信通知失败: %w", err))
        }
    }
    
    // 发送邮件通知（如果有邮箱）
    if user.Email != "" {
        if err := n.sendEmailNotification(ctx, user.Email, notification); err != nil {
            errors = append(errors, fmt.Errorf("邮件通知失败: %w", err))
        }
    }
    
    // 记录通知结果
    n.recordNotificationResult(ctx, refund.ID, errors)
    
    if len(errors) > 0 {
        return fmt.Errorf("部分通知发送失败: %v", errors)
    }
    
    return nil
}

// buildNotificationContent 构建通知内容
func (n *RefundNotificationService) buildNotificationContent(
    refund *model.Refund,
    notificationType RefundNotificationType,
) *NotificationContent {
    switch notificationType {
    case RefundNotificationProcessing:
        return &NotificationContent{
            Title:   "退款处理中",
            Content: fmt.Sprintf("您的订单退款正在处理中，退款金额：¥%.2f", refund.RefundAmount),
            Type:    "refund_processing",
        }
    case RefundNotificationSuccess:
        return &NotificationContent{
            Title:   "退款成功",
            Content: fmt.Sprintf("您的订单退款已成功，退款金额：¥%.2f，预计1-3个工作日到账", refund.RefundAmount),
            Type:    "refund_success",
        }
    case RefundNotificationFailed:
        return &NotificationContent{
            Title:   "退款异常",
            Content: "您的订单退款处理异常，请联系客服处理",
            Type:    "refund_failed",
        }
    default:
        return &NotificationContent{
            Title:   "退款通知",
            Content: "您的订单退款状态已更新",
            Type:    "refund_update",
        }
    }
}
```

## 配置管理设计

### 1. 退款配置结构

```go
// RefundConfig 退款配置
type RefundConfig struct {
    // 自动退款开关
    AutoRefundEnabled bool `yaml:"auto_refund_enabled" mapstructure:"auto_refund_enabled"`
    
    // 退款策略配置
    Policy RefundPolicyConfig `yaml:"policy" mapstructure:"policy"`
    
    // 重试配置
    Retry RetryConfig `yaml:"retry" mapstructure:"retry"`
    
    // 通知配置
    Notification NotificationConfig `yaml:"notification" mapstructure:"notification"`
    
    // 超时配置
    Timeout TimeoutConfig `yaml:"timeout" mapstructure:"timeout"`
}

// RefundPolicyConfig 退款策略配置
type RefundPolicyConfig struct {
    // 是否扣除处理手续费
    DeductProcessingFee bool `yaml:"deduct_processing_fee"`
    
    // 手续费扣除规则
    FeeDeductionRules []FeeRule `yaml:"fee_deduction_rules"`
    
    // 优惠券退还策略
    CouponRefundPolicy string `yaml:"coupon_refund_policy"`
    
    // 不同场景的退款比例
    RefundRatios map[string]float64 `yaml:"refund_ratios"`
}

// RetryConfig 重试配置
type RetryConfig struct {
    MaxRetryCount   int `yaml:"max_retry_count"`
    BaseRetryDelay  int `yaml:"base_retry_delay"`  // 秒
    MaxRetryDelay   int `yaml:"max_retry_delay"`   // 秒
    MaxRetryAge     int `yaml:"max_retry_age"`     // 小时
}
```

### 2. 配置文件示例

```yaml
# config/conf/config.dev.yaml
refund:
  auto_refund_enabled: true
  policy:
    deduct_processing_fee: false
    coupon_refund_policy: "full_refund"
    refund_ratios:
      matching_failure: 1.0
      user_cancel: 0.9
      system_error: 1.0
  retry:
    max_retry_count: 3
    base_retry_delay: 30
    max_retry_delay: 300
    max_retry_age: 24
  notification:
    enabled: true
    channels: ["sms", "wechat"]
  timeout:
    processing_timeout: 300  # 5分钟
    callback_timeout: 1800   # 30分钟
```

## 数据库设计

### 1. 退款记录表扩展

```sql
-- 扩展退款记录表
ALTER TABLE refunds 
ADD COLUMN refund_type TINYINT NOT NULL DEFAULT 1 COMMENT '退款类型：1用户取消 2匹配失败 3系统错误',
ADD COLUMN trigger_type TINYINT NOT NULL DEFAULT 1 COMMENT '触发类型：1手动 2自动 3超时',
ADD COLUMN retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
ADD COLUMN last_retry_at TIMESTAMP NULL COMMENT '最后重试时间',
ADD COLUMN failure_reason VARCHAR(500) NULL COMMENT '失败原因',
ADD COLUMN calculation_details JSON NULL COMMENT '计算详情',
ADD COLUMN notification_status TINYINT NOT NULL DEFAULT 0 COMMENT '通知状态：0未发送 1已发送 2发送失败';

-- 添加索引
CREATE INDEX idx_refunds_type_status ON refunds(refund_type, status);
CREATE INDEX idx_refunds_retry ON refunds(retry_count, last_retry_at);
CREATE INDEX idx_refunds_notification ON refunds(notification_status);
```

### 2. 退款策略配置表

```sql
-- 创建退款策略配置表
CREATE TABLE refund_policies (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    policy_name VARCHAR(100) NOT NULL COMMENT '策略名称',
    refund_type TINYINT NOT NULL COMMENT '退款类型',
    refund_ratio DECIMAL(5,4) NOT NULL DEFAULT 1.0000 COMMENT '退款比例',
    deduct_processing_fee BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否扣除手续费',
    fee_calculation_rule JSON NULL COMMENT '手续费计算规则',
    effective_from TIMESTAMP NOT NULL COMMENT '生效时间',
    effective_to TIMESTAMP NULL COMMENT '失效时间',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id),
    KEY idx_policy_type_active (refund_type, is_active),
    KEY idx_policy_effective (effective_from, effective_to)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款策略配置表';
```

## 监控和告警设计

### 1. 关键指标监控

```go
// RefundMetrics 退款指标
type RefundMetrics struct {
    // 退款统计
    TotalRefunds        int64   `json:"total_refunds"`
    SuccessfulRefunds   int64   `json:"successful_refunds"`
    FailedRefunds       int64   `json:"failed_refunds"`
    PendingRefunds      int64   `json:"pending_refunds"`
    
    // 金额统计
    TotalRefundAmount   decimal.Decimal `json:"total_refund_amount"`
    AverageRefundAmount decimal.Decimal `json:"average_refund_amount"`
    
    // 性能指标
    AverageProcessingTime time.Duration `json:"average_processing_time"`
    SuccessRate          float64       `json:"success_rate"`
    
    // 重试统计
    TotalRetries         int64 `json:"total_retries"`
    AverageRetryCount    float64 `json:"average_retry_count"`
}

// 监控指标收集
func (s *RefundServiceImpl) CollectMetrics(ctx context.Context) (*RefundMetrics, error) {
    metrics := &RefundMetrics{}
    
    // 查询退款统计
    var stats struct {
        Total     int64
        Success   int64
        Failed    int64
        Pending   int64
        TotalAmount decimal.Decimal
    }
    
    err := s.db.Model(&model.Refund{}).
        Select(`
            COUNT(*) as total,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as success,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed,
            SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pending,
            SUM(refund_amount) as total_amount
        `, RefundStatusSuccess, RefundStatusFailed, RefundStatusProcessing).
        Where("created_at >= ?", time.Now().Add(-24*time.Hour)).
        Scan(&stats).Error
    
    if err != nil {
        return nil, err
    }
    
    metrics.TotalRefunds = stats.Total
    metrics.SuccessfulRefunds = stats.Success
    metrics.FailedRefunds = stats.Failed
    metrics.PendingRefunds = stats.Pending
    metrics.TotalRefundAmount = stats.TotalAmount
    
    if stats.Total > 0 {
        metrics.AverageRefundAmount = stats.TotalAmount.Div(decimal.NewFromInt(stats.Total))
        metrics.SuccessRate = float64(stats.Success) / float64(stats.Total)
    }
    
    return metrics, nil
}
```

### 2. 告警规则

```go
// RefundAlertManager 退款告警管理器
type RefundAlertManager struct {
    alertService service.IAlertService
    logger       *zap.Logger
    config       *AlertConfig
}

// CheckAlerts 检查告警条件
func (a *RefundAlertManager) CheckAlerts(ctx context.Context, metrics *RefundMetrics) error {
    alerts := []Alert{}
    
    // 检查成功率告警
    if metrics.SuccessRate < a.config.MinSuccessRate {
        alerts = append(alerts, Alert{
            Type:     "refund_success_rate_low",
            Level:    AlertLevelWarning,
            Message:  fmt.Sprintf("退款成功率过低: %.2f%%", metrics.SuccessRate*100),
            Value:    metrics.SuccessRate,
            Threshold: a.config.MinSuccessRate,
        })
    }
    
    // 检查失败数量告警
    if metrics.FailedRefunds > a.config.MaxFailedRefunds {
        alerts = append(alerts, Alert{
            Type:     "refund_failures_high",
            Level:    AlertLevelCritical,
            Message:  fmt.Sprintf("退款失败数量过多: %d", metrics.FailedRefunds),
            Value:    float64(metrics.FailedRefunds),
            Threshold: float64(a.config.MaxFailedRefunds),
        })
    }
    
    // 检查处理时间告警
    if metrics.AverageProcessingTime > a.config.MaxProcessingTime {
        alerts = append(alerts, Alert{
            Type:     "refund_processing_slow",
            Level:    AlertLevelWarning,
            Message:  fmt.Sprintf("退款处理时间过长: %v", metrics.AverageProcessingTime),
            Value:    float64(metrics.AverageProcessingTime.Seconds()),
            Threshold: float64(a.config.MaxProcessingTime.Seconds()),
        })
    }
    
    // 发送告警
    for _, alert := range alerts {
        if err := a.alertService.SendAlert(ctx, &alert); err != nil {
            a.logger.Error("发送告警失败", zap.Error(err), zap.Any("alert", alert))
        }
    }
    
    return nil
}
```

## 测试策略

### 1. 单元测试

```go
// 退款服务单元测试
func TestRefundService_TriggerAutoRefundForMatchingFailure(t *testing.T) {
    // 设置测试环境
    db := setupTestDB(t)
    defer cleanupTestDB(db)
    
    service := setupRefundService(db)
    
    // 测试用例
    tests := []struct {
        name    string
        request *AutoRefundRequest
        setup   func()
        wantErr bool
    }{
        {
            name: "正常匹配失败退款",
            request: &AutoRefundRequest{
                OrderID: 1,
                Reason:  "匹配失败",
                AdminID: 1,
                TriggerType: "manual",
            },
            setup: func() {
                createTestOrder(db, 1, OrderStatusPaid, MatchingStatusManualFailed)
            },
            wantErr: false,
        },
        {
            name: "订单状态不允许退款",
            request: &AutoRefundRequest{
                OrderID: 2,
                Reason:  "匹配失败",
                AdminID: 1,
                TriggerType: "manual",
            },
            setup: func() {
                createTestOrder(db, 2, OrderStatusCompleted, MatchingStatusServiceStarted)
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            tt.setup()
            
            err := service.TriggerAutoRefundForMatchingFailure(context.Background(), tt.request)
            
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

### 2. 集成测试

```go
// 退款流程集成测试
func TestRefundIntegration(t *testing.T) {
    // 启动测试服务
    server := startTestServer(t)
    defer server.Close()
    
    // 创建测试订单
    order := createTestOrder(t, server)
    
    // 模拟匹配失败
    simulateMatchingFailure(t, server, order.ID)
    
    // 触发自动退款
    refundReq := &AutoRefundRequest{
        OrderID:     order.ID,
        Reason:      "匹配失败自动退款",
        TriggerType: "system",
    }
    
    resp := triggerAutoRefund(t, server, refundReq)
    assert.Equal(t, 200, resp.StatusCode)
    
    // 验证退款状态
    time.Sleep(2 * time.Second) // 等待异步处理
    
    refundStatus := getRefundStatus(t, server, order.ID)
    assert.Equal(t, RefundStatusSuccess, refundStatus.Status)
    assert.Equal(t, order.TotalAmount, refundStatus.RefundAmount)
}
```

## 总结

本设计文档提供了完整的退款服务集成解决方案，包括：

1. **完整的服务架构**：自动退款触发、金额计算、支付集成、状态管理
2. **健壮的异常处理**：重试机制、降级处理、错误恢复
3. **灵活的配置管理**：支持多种退款策略和业务规则
4. **完善的监控告警**：关键指标监控和实时告警
5. **全面的测试覆盖**：单元测试、集成测试、端到端测试

通过这个设计方案，可以确保退款服务与人工分配系统的完美集成，提供可靠、高效的自动退款功能。