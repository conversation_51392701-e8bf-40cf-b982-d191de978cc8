# 陪诊师活跃时间优化需求文档

## 介绍

本需求旨在解决陪诊师活跃时间(`last_active`)字段更新不及时的问题，并优化当前的自动禁用不活跃陪诊师功能。当前系统存在活跃时间只在创建时设置一次的问题，导致正常活跃的陪诊师被误判为长时间离线。同时，考虑到陪诊师资源不足的现状，需要暂时移除自动禁用功能。

## 需求

### 需求1：完善陪诊师活跃时间更新机制

**用户故事：** 作为系统管理员，我希望陪诊师的活跃时间能够准确反映其真实的在线状态，以便正确评估陪诊师的活跃度。

#### 验收标准

1. WHEN 陪诊师通过微信小程序登录 THEN 系统应更新其last_active字段为当前时间
2. WHEN 陪诊师通过手机号密码登录 THEN 系统应更新其last_active字段为当前时间
3. WHEN 陪诊师访问需要身份验证的API接口 THEN 系统应通过中间件更新其last_active字段
4. WHEN 陪诊师进行订单相关操作（接单、确认服务等） THEN 系统应更新其last_active字段
5. WHEN 陪诊师更新个人资料或服务配置 THEN 系统应更新其last_active字段
6. WHEN 系统更新陪诊师活跃时间 THEN 应记录操作日志便于调试和监控

### 需求2：移除自动禁用不活跃陪诊师功能

**用户故事：** 作为产品经理，我希望暂时移除自动禁用不活跃陪诊师的功能，以避免在陪诊师资源不足的情况下进一步减少可用陪诊师数量。

#### 验收标准

1. WHEN 系统运行异常处理任务 THEN 不应执行自动禁用陪诊师的逻辑
2. WHEN 管理员调用离线陪诊师处理API THEN 系统应返回功能已禁用的提示信息
3. WHEN 系统进行订单匹配 THEN 不应因为陪诊师长时间未活跃而排除其参与匹配
4. WHEN 查看异常处理统计 THEN 不应包含离线陪诊师处理的相关数据
5. WHEN 系统文档更新 THEN 应明确说明该功能已暂时禁用及原因

### 需求3：保留活跃时间显示和统计功能

**用户故事：** 作为管理员，我希望仍然能够查看陪诊师的活跃时间信息，用于运营分析和决策支持。

#### 验收标准

1. WHEN 管理员查看陪诊师列表 THEN 应显示准确的最后活跃时间
2. WHEN 管理员查看陪诊师详情 THEN 应显示活跃时间相关统计信息
3. WHEN 进行数据分析 THEN 活跃时间数据应准确可用于统计报表
4. WHEN 系统进行订单匹配 THEN 可以将活跃时间作为排序参考因素（但不作为排除条件）

### 需求4：为未来功能恢复预留接口

**用户故事：** 作为技术负责人，我希望为未来可能恢复的自动禁用功能预留清晰的接口和配置选项。

#### 验收标准

1. WHEN 需要恢复自动禁用功能 THEN 应通过配置开关轻松启用
2. WHEN 系统配置更新 THEN 应支持设置不同的离线时间阈值
3. WHEN 功能重新启用 THEN 应支持渐进式处理策略（提醒->降级->禁用）
4. WHEN 处理离线陪诊师 THEN 应提供多种处理选项而非直接禁用
5. WHEN 陪诊师被误判 THEN 应提供便捷的恢复和申诉机制

## 技术约束

1. 活跃时间更新不应显著影响系统性能
2. 数据库更新操作应考虑并发安全性
3. 日志记录应适度，避免产生过多日志
4. 配置变更应支持热更新，无需重启服务
5. 代码修改应保持向后兼容性

## 验收测试场景

### 场景1：陪诊师登录活跃时间更新
- 给定：陪诊师账号存在且状态正常
- 当：陪诊师通过微信小程序登录
- 那么：数据库中该陪诊师的last_active字段应更新为当前时间

### 场景2：API调用活跃时间更新
- 给定：陪诊师已登录并获得有效token
- 当：陪诊师调用需要身份验证的API接口
- 那么：系统应通过中间件自动更新其活跃时间

### 场景3：自动禁用功能已禁用
- 给定：系统中存在长时间未活跃的陪诊师
- 当：运行异常处理任务或调用相关API
- 那么：系统不应自动禁用任何陪诊师，并返回功能禁用提示

### 场景4：活跃时间显示正常
- 给定：陪诊师最近有登录或API调用活动
- 当：管理员查看陪诊师列表
- 那么：应显示准确的最后活跃时间信息

## 成功标准

1. 陪诊师活跃时间能够准确反映真实的在线活动状态
2. 不再出现活跃陪诊师被误判为长时间离线的情况
3. 自动禁用功能完全停用，不影响现有陪诊师的正常服务
4. 系统性能不受活跃时间更新机制影响
5. 为未来功能优化预留了清晰的扩展接口