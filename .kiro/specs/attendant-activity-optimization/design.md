# 陪诊师活跃时间优化设计文档

## 概述

本设计文档描述了如何解决陪诊师活跃时间更新不及时的问题，并安全地移除自动禁用不活跃陪诊师的功能。设计重点关注性能优化、数据一致性和系统稳定性。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[用户登录] --> B[活跃时间更新服务]
    C[陪诊师中间件] --> B
    D[订单操作] --> B
    E[资料更新] --> B
    
    B --> F[陪诊师仓库层]
    F --> G[数据库]
    
    H[异常处理服务] --> I[配置管理器]
    I --> J{自动禁用开关}
    J -->|关闭| K[返回功能禁用提示]
    J -->|开启| L[执行禁用逻辑]
    
    M[管理后台] --> N[活跃时间显示]
    N --> F
```

### 核心组件

#### 1. 活跃时间更新服务 (AttendantActivityService)
- 负责统一管理陪诊师活跃时间的更新逻辑
- 提供防重复更新机制，避免频繁数据库写入
- 支持批量更新和异步更新

#### 2. 活跃时间中间件 (ActivityMiddleware)
- 在陪诊师访问API时自动更新活跃时间
- 集成到现有的陪诊师认证中间件中
- 支持配置化的更新频率控制

#### 3. 配置管理器 (ConfigManager)
- 管理自动禁用功能的开关状态
- 支持热更新配置，无需重启服务
- 提供功能降级和恢复机制

## 数据模型

### 陪诊师活跃时间字段
```go
type Attendant struct {
    // ... 其他字段
    LastActive   time.Time `json:"last_active" gorm:"type:timestamp"`
    // ... 其他字段
}
```

### 配置模型
```go
type AttendantActivityConfig struct {
    EnableAutoDisable     bool          `json:"enable_auto_disable"`     // 是否启用自动禁用
    OfflineThresholdHours int           `json:"offline_threshold_hours"` // 离线阈值（小时）
    UpdateIntervalMinutes int           `json:"update_interval_minutes"` // 更新间隔（分钟）
    EnableBatchUpdate     bool          `json:"enable_batch_update"`     // 是否启用批量更新
    LogLevel              string        `json:"log_level"`               // 日志级别
}
```

### 活跃时间更新记录（用于调试）
```go
type AttendantActivityLog struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    AttendantID  uint      `json:"attendant_id" gorm:"index"`
    UserID       uint      `json:"user_id" gorm:"index"`
    UpdateSource string    `json:"update_source"` // login, api_call, order_action, profile_update
    UpdateTime   time.Time `json:"update_time"`
    IPAddress    string    `json:"ip_address"`
    UserAgent    string    `json:"user_agent"`
    CreatedAt    time.Time `json:"created_at"`
}
```

## 组件设计

### 1. AttendantActivityService

```go
type IAttendantActivityService interface {
    // 更新陪诊师活跃时间
    UpdateLastActive(ctx context.Context, userID uint, source string) error
    
    // 批量更新活跃时间
    BatchUpdateLastActive(ctx context.Context, updates []ActivityUpdate) error
    
    // 获取陪诊师活跃状态
    GetActivityStatus(ctx context.Context, attendantID uint) (*ActivityStatus, error)
    
    // 检查是否需要更新（防重复更新）
    ShouldUpdate(ctx context.Context, userID uint) (bool, error)
}

type ActivityUpdate struct {
    UserID    uint
    Source    string
    Timestamp time.Time
    IPAddress string
    UserAgent string
}

type ActivityStatus struct {
    AttendantID    uint      `json:"attendant_id"`
    LastActive     time.Time `json:"last_active"`
    IsOnline       bool      `json:"is_online"`
    OfflineDuration string   `json:"offline_duration"`
}
```

### 2. 活跃时间更新中间件

```go
func AttendantActivityMiddleware(activityService IAttendantActivityService) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取用户ID
        userID, exists := c.Get("userID")
        if !exists {
            c.Next()
            return
        }
        
        // 异步更新活跃时间，不阻塞请求
        go func() {
            ctx := context.Background()
            err := activityService.UpdateLastActive(ctx, userID.(uint), "api_call")
            if err != nil {
                log.Printf("更新陪诊师活跃时间失败: %v", err)
            }
        }()
        
        c.Next()
    }
}
```

### 3. 配置管理器

```go
type IConfigManager interface {
    // 获取活跃时间配置
    GetActivityConfig() *AttendantActivityConfig
    
    // 更新配置
    UpdateConfig(config *AttendantActivityConfig) error
    
    // 检查自动禁用功能是否启用
    IsAutoDisableEnabled() bool
    
    // 热更新配置
    ReloadConfig() error
}
```

## 接口设计

### 1. 用户服务更新

#### 微信登录接口更新
```go
func (s *UserService) WxLogin(ctx context.Context, code string) (*model.User, string, error) {
    // ... 现有登录逻辑
    
    // 更新陪诊师活跃时间
    if user.Role == "attendant" {
        if err := s.activityService.UpdateLastActive(ctx, user.ID, "wx_login"); err != nil {
            s.logger.Warn("更新陪诊师活跃时间失败", zap.Error(err))
        }
    }
    
    return user, token, nil
}
```

#### 手机号登录接口更新
```go
func (s *UserService) Login(ctx context.Context, phone, password string) (*model.User, string, error) {
    // ... 现有登录逻辑
    
    // 更新陪诊师活跃时间
    if user.Role == "attendant" {
        if err := s.activityService.UpdateLastActive(ctx, user.ID, "phone_login"); err != nil {
            s.logger.Warn("更新陪诊师活跃时间失败", zap.Error(err))
        }
    }
    
    return user, token, nil
}
```

### 2. 异常处理服务更新

```go
func (s *exceptionHandlerService) HandleOfflineAttendants(ctx context.Context, offlineHours int) (*service.ExceptionHandleResult, error) {
    // 检查功能是否启用
    if !s.configManager.IsAutoDisableEnabled() {
        return &service.ExceptionHandleResult{
            ProcessedCount: 0,
            SuccessCount:   0,
            FailedCount:    0,
            Errors:         []string{"自动禁用不活跃陪诊师功能已暂时关闭"},
            ProcessDuration: 0,
        }, nil
    }
    
    // ... 原有逻辑（当功能启用时）
}
```

### 3. 新增活跃时间管理接口

```go
// GET /api/v1/attendants/activity/status
func (h *AttendantHandler) GetActivityStatus(c *gin.Context) {
    attendantID := c.Param("id")
    // ... 实现逻辑
}

// POST /api/v1/attendants/activity/config
func (h *AttendantHandler) UpdateActivityConfig(c *gin.Context) {
    // ... 实现逻辑
}

// GET /api/v1/attendants/activity/logs
func (h *AttendantHandler) GetActivityLogs(c *gin.Context) {
    // ... 实现逻辑
}
```

## 错误处理

### 1. 活跃时间更新失败处理
- 记录错误日志但不影响主业务流程
- 提供重试机制，最多重试3次
- 支持降级处理，在数据库压力大时暂停更新

### 2. 配置更新失败处理
- 保持现有配置不变
- 记录配置更新失败的详细日志
- 提供配置回滚机制

### 3. 数据一致性保证
- 使用数据库事务确保数据一致性
- 提供数据修复工具，处理异常情况
- 定期检查数据完整性

## 测试策略

### 1. 单元测试
- 活跃时间更新服务的各个方法
- 配置管理器的配置读取和更新
- 中间件的活跃时间更新逻辑

### 2. 集成测试
- 用户登录后活跃时间更新
- API调用时活跃时间更新
- 异常处理服务的功能禁用

### 3. 性能测试
- 高并发情况下的活跃时间更新性能
- 数据库写入压力测试
- 内存使用情况监控

### 4. 端到端测试
- 完整的用户登录到活跃时间显示流程
- 配置更新的热生效测试
- 功能降级和恢复测试

## 部署策略

### 1. 分阶段部署
1. **第一阶段**：部署活跃时间更新功能，不影响现有业务
2. **第二阶段**：禁用自动禁用功能，确保陪诊师不被误禁
3. **第三阶段**：优化性能，添加监控和告警

### 2. 配置管理
- 使用配置中心管理功能开关
- 支持不同环境的配置隔离
- 提供配置变更审计日志

### 3. 监控和告警
- 监控活跃时间更新的成功率
- 监控数据库写入性能
- 设置异常情况告警

## 性能优化

### 1. 防重复更新机制
```go
// 使用Redis缓存最近更新时间，避免频繁数据库写入
func (s *attendantActivityService) ShouldUpdate(ctx context.Context, userID uint) (bool, error) {
    key := fmt.Sprintf("attendant_activity:%d", userID)
    lastUpdate, err := s.redis.Get(ctx, key).Time()
    if err == redis.Nil {
        return true, nil
    }
    if err != nil {
        return true, err // 出错时允许更新
    }
    
    // 5分钟内不重复更新
    return time.Since(lastUpdate) > 5*time.Minute, nil
}
```

### 2. 批量更新机制
- 收集一定时间窗口内的更新请求
- 批量执行数据库更新操作
- 减少数据库连接和事务开销

### 3. 异步处理
- 活跃时间更新不阻塞主业务流程
- 使用消息队列处理大量更新请求
- 提供更新失败的补偿机制

## 安全考虑

### 1. 数据访问控制
- 只有陪诊师本人和管理员可以查看活跃时间
- API访问需要适当的权限验证
- 敏感操作需要审计日志

### 2. 防止恶意攻击
- 限制活跃时间更新的频率
- 防止通过频繁API调用刷活跃时间
- 监控异常的活跃时间更新模式

### 3. 数据隐私保护
- 活跃时间日志定期清理
- 敏感信息脱敏处理
- 符合数据保护法规要求

## 向后兼容性

### 1. API兼容性
- 现有API接口保持不变
- 新增功能通过新的API端点提供
- 支持API版本管理

### 2. 数据库兼容性
- 不修改现有数据库表结构
- 新增字段使用默认值
- 提供数据迁移脚本

### 3. 配置兼容性
- 新配置项使用合理的默认值
- 支持配置的平滑升级
- 保持配置文件格式稳定

## 未来扩展

### 1. 智能活跃度分析
- 基于用户行为模式分析真实活跃度
- 支持多维度活跃度评估
- 提供活跃度趋势分析

### 2. 渐进式处理策略
- 支持多级处理策略（提醒->降级->禁用）
- 可配置的处理策略
- 智能恢复机制

### 3. 实时监控面板
- 实时显示陪诊师活跃状态
- 提供活跃度统计报表
- 支持自定义监控指标

这个设计确保了系统的稳定性和可扩展性，同时解决了当前的活跃时间更新问题，并为未来的功能优化预留了空间。