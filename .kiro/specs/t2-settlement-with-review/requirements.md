# T+2分账审核机制需求文档

## 功能概述

将现有的实时分账机制改为T+2延迟分账，增加订单内容审核环节，包括服务凭证审核、投诉处理、退款处理等风控机制，确保服务质量和资金安全。

## 核心需求

### 需求1：T+2分账周期机制

**用户故事：** 作为平台，我需要将分账周期从实时改为T+2，以便有充足时间进行订单审核和处理潜在问题。

#### 验收标准

1. WHEN 订单完成后 THEN 系统应将订单标记为"待审核"状态，不立即分账
2. WHEN 订单完成48小时后 THEN 系统应自动检查是否可以进入分账流程
3. WHEN 自动分账开关开启且无异常情况 THEN 系统应自动执行分账
4. WHEN 自动分账开关关闭 THEN 系统应等待管理员手动审核后分账

### 需求2：订单内容审核机制

**用户故事：** 作为管理员，我需要在分账前审核订单内容，包括服务凭证验证，确保服务真实发生。

#### 验收标准

1. WHEN 订单进入审核期 THEN 系统应展示待审核订单列表
2. WHEN 管理员审核订单时 THEN 系统应显示服务凭证、服务总结等关键信息
3. WHEN 审核通过时 THEN 系统应允许订单进入分账流程
4. WHEN 审核不通过时 THEN 系统应冻结订单，阻止分账并记录原因

### 需求3：投诉和退款优先处理机制

**用户故事：** 作为平台，当48小时内收到用户投诉或退款申请时，我需要优先处理这些问题，暂停相关订单的分账。

#### 验收标准

1. WHEN 48小时内收到投诉或退款申请 THEN 系统应立即冻结对应订单的分账
2. WHEN 订单被冻结时 THEN 系统应启动调查流程，通知相关人员
3. WHEN 调查完成后 THEN 系统应根据责任判定结果处理分账
4. WHEN 判定陪诊师有责任时 THEN 系统应支持扣减分成或全额退款

### 需求4：自动分账开关控制

**用户故事：** 作为管理员，我需要能够控制自动分账功能的开启和关闭，以应对特殊情况如节假日。

#### 验收标准

1. WHEN 管理员开启自动分账开关 THEN T+2后系统应自动处理符合条件的订单分账
2. WHEN 管理员关闭自动分账开关 THEN 系统应停止自动分账，等待手动处理
3. WHEN 开关状态变更时 THEN 系统应记录操作日志和变更原因
4. WHEN 查看开关状态时 THEN 系统应显示当前状态和最后变更信息

### 需求5：订单状态管理优化

**用户故事：** 作为系统，我需要增加新的订单状态来支持T+2分账流程的各个环节。

#### 验收标准

1. WHEN 订单完成时 THEN 系统应将状态设置为"已完成-待审核"
2. WHEN 订单进入审核期时 THEN 系统应将状态设置为"审核中"
3. WHEN 订单被冻结时 THEN 系统应将状态设置为"已冻结"
4. WHEN 订单分账完成时 THEN 系统应将状态设置为"已结算"

### 需求6：调查和责任判定流程

**用户故事：** 作为管理员，当出现投诉或争议时，我需要有完整的调查流程来判定责任并处理分账。

#### 验收标准

1. WHEN 启动调查时 THEN 系统应创建调查记录并分配处理人员
2. WHEN 联系相关方时 THEN 系统应记录沟通过程和结果
3. WHEN 收集证据时 THEN 系统应支持上传和管理相关证据材料
4. WHEN 做出判定时 THEN 系统应记录判定结果和处理方案

### 需求7：分账冻结和解冻机制

**用户故事：** 作为系统，我需要能够冻结和解冻订单的分账，以支持争议处理流程。

#### 验收标准

1. WHEN 订单需要冻结时 THEN 系统应立即停止该订单的分账流程
2. WHEN 冻结订单时 THEN 系统应记录冻结原因和操作人员
3. WHEN 解冻订单时 THEN 系统应恢复正常的分账流程
4. WHEN 查询冻结订单时 THEN 系统应提供冻结状态和处理进度

### 需求8：定时任务和自动化处理

**用户故事：** 作为系统，我需要定时检查T+2到期的订单，并根据配置自动处理分账。

#### 验收标准

1. WHEN 定时任务运行时 THEN 系统应检查所有T+2到期的订单
2. WHEN 订单符合自动分账条件时 THEN 系统应自动执行分账流程
3. WHEN 遇到异常订单时 THEN 系统应跳过并记录异常信息
4. WHEN 任务完成时 THEN 系统应生成处理报告和统计信息

### 需求9：通知和提醒机制

**用户故事：** 作为相关人员，我需要及时收到关于订单审核、投诉处理等重要事件的通知。

#### 验收标准

1. WHEN 订单进入审核期时 THEN 系统应通知审核人员
2. WHEN 收到投诉或退款申请时 THEN 系统应立即通知相关管理员
3. WHEN 调查需要联系用户时 THEN 系统应发送通知给客服人员
4. WHEN 分账被冻结或解冻时 THEN 系统应通知财务人员

### 需求10：数据统计和报表

**用户故事：** 作为管理员，我需要查看T+2分账机制的运行统计和效果分析。

#### 验收标准

1. WHEN 查看分账统计时 THEN 系统应显示自动分账和手动分账的数量
2. WHEN 查看审核统计时 THEN 系统应显示审核通过率和平均审核时间
3. WHEN 查看争议统计时 THEN 系统应显示投诉处理情况和责任判定结果
4. WHEN 导出报表时 THEN 系统应支持按时间段导出详细数据