# T+2分账审核机制设计文档

## 系统架构

### 整体架构设计
T+2分账审核机制在现有收入管理系统基础上，增加审核层和延迟处理机制：

```
订单完成 → 审核期(48h) → 自动/手动审核 → 分账执行
    ↓           ↓              ↓
  状态变更    投诉处理        责任判定
    ↓           ↓              ↓
  通知提醒    冻结分账        扣减/退款
```

### 核心组件架构

#### 1. 审核管理组件
- **OrderReviewService**: 订单审核服务
- **ReviewScheduler**: 审核定时任务调度器
- **AutoSettlementController**: 自动分账控制器

#### 2. 争议处理组件
- **DisputeService**: 争议处理服务
- **InvestigationService**: 调查流程服务
- **FreezeService**: 分账冻结服务

#### 3. 通知提醒组件
- **NotificationService**: 通知服务
- **AlertService**: 告警服务

## 数据模型设计

### 新增数据表

#### order_reviews (订单审核表)
```sql
CREATE TABLE `order_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `review_status` int NOT NULL DEFAULT '1' COMMENT '审核状态：1待审核 2审核中 3审核通过 4审核不通过',
  `review_type` int NOT NULL DEFAULT '1' COMMENT '审核类型：1常规审核 2争议审核',
  `reviewer_id` bigint unsigned DEFAULT NULL COMMENT '审核员ID',
  `review_notes` text COMMENT '审核备注',
  `evidence_files` json COMMENT '证据文件列表',
  `review_deadline` timestamp NOT NULL COMMENT '审核截止时间',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '审核完成时间',
  `auto_review_eligible` tinyint(1) DEFAULT '1' COMMENT '是否符合自动审核条件',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_review_status` (`review_status`),
  KEY `idx_review_deadline` (`review_deadline`),
  KEY `idx_reviewer_id` (`reviewer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单审核表';
```

#### order_disputes (订单争议表)
```sql
CREATE TABLE `order_disputes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `dispute_type` int NOT NULL COMMENT '争议类型：1投诉 2退款申请 3服务质量问题',
  `complainant_type` int NOT NULL COMMENT '申请人类型：1患者 2陪诊师',
  `complainant_id` bigint unsigned NOT NULL COMMENT '申请人ID',
  `dispute_reason` varchar(500) NOT NULL COMMENT '争议原因',
  `evidence_files` json COMMENT '证据文件',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1待处理 2调查中 3已解决 4已关闭',
  `priority` int NOT NULL DEFAULT '2' COMMENT '优先级：1高 2中 3低',
  `handler_id` bigint unsigned DEFAULT NULL COMMENT '处理人ID',
  `resolution` text COMMENT '处理结果',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_dispute_type` (`dispute_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单争议表';
```

#### settlement_freeze_records (分账冻结记录表)
```sql
CREATE TABLE `settlement_freeze_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `freeze_type` int NOT NULL COMMENT '冻结类型：1争议冻结 2人工冻结 3系统冻结',
  `freeze_reason` varchar(500) NOT NULL COMMENT '冻结原因',
  `frozen_amount` decimal(10,2) NOT NULL COMMENT '冻结金额',
  `freeze_status` int NOT NULL DEFAULT '1' COMMENT '冻结状态：1已冻结 2已解冻',
  `freezer_id` bigint unsigned NOT NULL COMMENT '冻结操作人ID',
  `unfreezer_id` bigint unsigned DEFAULT NULL COMMENT '解冻操作人ID',
  `frozen_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '冻结时间',
  `unfrozen_at` timestamp NULL DEFAULT NULL COMMENT '解冻时间',
  `related_dispute_id` bigint unsigned DEFAULT NULL COMMENT '关联争议ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_freeze_status` (`freeze_status`),
  KEY `idx_frozen_at` (`frozen_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分账冻结记录表';
```

#### auto_settlement_configs (自动分账配置表)
```sql
CREATE TABLE `auto_settlement_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) COMMENT '配置描述',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `updated_by` bigint unsigned NOT NULL COMMENT '更新人ID',
  `update_reason` varchar(255) COMMENT '更新原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自动分账配置表';
```

#### investigation_records (调查记录表)
```sql
CREATE TABLE `investigation_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `dispute_id` bigint unsigned NOT NULL COMMENT '争议ID',
  `investigator_id` bigint unsigned NOT NULL COMMENT '调查员ID',
  `investigation_type` int NOT NULL COMMENT '调查类型：1电话调查 2实地调查 3证据收集',
  `target_type` int NOT NULL COMMENT '调查对象：1患者 2陪诊师 3第三方',
  `target_id` bigint unsigned NOT NULL COMMENT '调查对象ID',
  `investigation_content` text NOT NULL COMMENT '调查内容',
  `investigation_result` text COMMENT '调查结果',
  `evidence_collected` json COMMENT '收集的证据',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1进行中 2已完成',
  `investigated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调查时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_dispute_id` (`dispute_id`),
  KEY `idx_investigator_id` (`investigator_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='调查记录表';
```

### 订单状态扩展

在现有订单状态基础上，增加审核相关状态：

```go
const (
    OrderStatusCompleted        = 4  // 已完成（原有）
    OrderStatusPendingReview    = 5  // 已完成-待审核
    OrderStatusUnderReview      = 6  // 审核中
    OrderStatusReviewPassed     = 7  // 审核通过
    OrderStatusReviewFailed     = 8  // 审核不通过
    OrderStatusFrozen          = 9  // 已冻结
    OrderStatusSettled         = 10 // 已结算
)
```

## 业务流程设计

### 1. T+2分账主流程

```mermaid
sequenceDiagram
    participant Order as 订单系统
    participant Review as 审核服务
    participant Scheduler as 定时任务
    participant Settlement as 分账服务
    participant Config as 配置服务

    Order->>Review: 订单完成，创建审核记录
    Review->>Review: 设置48小时审核期
    Note over Review: 状态：待审核
    
    loop 48小时内
        alt 收到投诉/退款
            Review->>Review: 冻结分账
            Review->>Review: 启动争议处理
        else 人工审核
            Review->>Review: 审核通过/不通过
        end
    end
    
    Scheduler->>Review: 检查T+2到期订单
    Review->>Config: 检查自动分账开关
    
    alt 自动分账开启 && 无异常
        Review->>Settlement: 自动执行分账
    else 自动分账关闭 || 有异常
        Review->>Review: 等待人工处理
    end
```

### 2. 争议处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Dispute as 争议服务
    participant Investigation as 调查服务
    participant Settlement as 分账服务
    participant Notification as 通知服务

    User->>Dispute: 提交投诉/退款申请
    Dispute->>Settlement: 立即冻结分账
    Dispute->>Investigation: 启动调查流程
    Dispute->>Notification: 通知相关人员
    
    Investigation->>Investigation: 联系患者核实
    Investigation->>Investigation: 联系陪诊师核实
    Investigation->>Investigation: 收集证据材料
    
    Investigation->>Dispute: 提交调查结果
    Dispute->>Dispute: 责任判定
    
    alt 陪诊师有责任
        Dispute->>Settlement: 扣减分成/全额退款
    else 陪诊师无责任
        Dispute->>Settlement: 解冻分账，正常结算
    end
    
    Dispute->>Notification: 通知处理结果
```

### 3. 自动分账控制流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Config as 配置服务
    participant Scheduler as 定时任务
    participant Review as 审核服务

    Admin->>Config: 设置自动分账开关
    Config->>Config: 记录操作日志
    
    loop 定时检查
        Scheduler->>Review: 获取T+2到期订单
        Review->>Config: 检查自动分账开关状态
        
        alt 开关开启
            Review->>Review: 检查订单审核状态
            alt 无异常情况
                Review->>Review: 自动执行分账
            else 有异常情况
                Review->>Review: 跳过，等待人工处理
            end
        else 开关关闭
            Review->>Review: 跳过所有订单
        end
    end
```

## 接口设计

### 审核管理接口

#### 1. 获取待审核订单列表
```
GET /api/admin/reviews/pending
Query Parameters:
- page: 页码
- page_size: 每页数量
- review_type: 审核类型
- priority: 优先级

Response:
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "order_id": 12345,
        "order_no": "PZ202501290001",
        "review_status": 1,
        "review_deadline": "2025-01-31T10:00:00Z",
        "patient_name": "张三",
        "attendant_name": "李四",
        "service_amount": 150.00,
        "evidence_files": ["file1.jpg", "file2.pdf"]
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20
  }
}
```

#### 2. 审核订单
```
POST /api/admin/reviews/{id}/review
Request:
{
  "action": "approve", // approve/reject
  "review_notes": "服务凭证完整，审核通过",
  "reviewer_id": 1001
}

Response:
{
  "code": 0,
  "message": "审核完成",
  "data": {
    "review_id": 1,
    "new_status": "approved",
    "reviewed_at": "2025-01-29T15:30:00Z"
  }
}
```

### 争议处理接口

#### 1. 创建争议
```
POST /api/disputes/create
Request:
{
  "order_id": 12345,
  "dispute_type": 1,
  "complainant_type": 1,
  "complainant_id": 2001,
  "dispute_reason": "服务质量不满意",
  "evidence_files": ["complaint1.jpg", "complaint2.pdf"]
}

Response:
{
  "code": 0,
  "message": "争议已提交",
  "data": {
    "dispute_id": 501,
    "status": "pending",
    "freeze_applied": true
  }
}
```

#### 2. 处理争议
```
POST /api/admin/disputes/{id}/resolve
Request:
{
  "resolution": "经调查，陪诊师服务存在问题，给予患者全额退款",
  "responsibility": "attendant", // attendant/patient/platform
  "penalty_amount": 150.00,
  "handler_id": 1002
}

Response:
{
  "code": 0,
  "message": "争议处理完成",
  "data": {
    "dispute_id": 501,
    "resolution_type": "refund",
    "penalty_applied": true
  }
}
```

### 配置管理接口

#### 1. 获取自动分账配置
```
GET /api/admin/settlement/config

Response:
{
  "code": 0,
  "data": {
    "auto_settlement_enabled": true,
    "review_period_hours": 48,
    "auto_review_threshold": 100.00,
    "last_updated_by": "admin",
    "last_updated_at": "2025-01-29T10:00:00Z",
    "update_reason": "正常运营期间开启自动分账"
  }
}
```

#### 2. 更新自动分账开关
```
POST /api/admin/settlement/config/toggle
Request:
{
  "enabled": false,
  "reason": "春节假期，关闭自动分账",
  "operator_id": 1001
}

Response:
{
  "code": 0,
  "message": "配置更新成功",
  "data": {
    "auto_settlement_enabled": false,
    "updated_at": "2025-01-29T16:00:00Z"
  }
}
```

## 定时任务设计

### 1. T+2审核检查任务
```go
// 每小时执行一次，检查T+2到期的订单
func (s *ReviewScheduler) CheckT2Reviews() {
    // 1. 查询48小时前完成的订单
    deadline := time.Now().Add(-48 * time.Hour)
    orders := s.getOrdersCompletedBefore(deadline)
    
    // 2. 检查自动分账开关
    if !s.configService.IsAutoSettlementEnabled() {
        s.logger.Info("自动分账已关闭，跳过处理")
        return
    }
    
    // 3. 处理符合条件的订单
    for _, order := range orders {
        if s.canAutoSettle(order) {
            s.processAutoSettlement(order)
        }
    }
}
```

### 2. 争议超时检查任务
```go
// 每天执行一次，检查超时未处理的争议
func (s *DisputeScheduler) CheckOverdueDisputes() {
    // 1. 查询超过处理时限的争议
    overdueDisputes := s.getOverdueDisputes()
    
    // 2. 发送告警通知
    for _, dispute := range overdueDisputes {
        s.notificationService.SendOverdueAlert(dispute)
    }
    
    // 3. 自动升级优先级
    s.escalateDisputePriority(overdueDisputes)
}
```

## 安全设计

### 1. 权限控制
- 审核权限：只有指定角色可以进行订单审核
- 争议处理权限：客服和管理员可以处理争议
- 配置修改权限：只有高级管理员可以修改自动分账配置
- 冻结/解冻权限：财务和管理员可以操作分账冻结

### 2. 数据安全
- 审核记录不可删除，只能软删除
- 争议处理过程全程记录，不可篡改
- 分账冻结操作需要双重确认
- 敏感操作需要操作日志记录

### 3. 业务安全
- 防止重复提交争议
- 防止恶意冻结分账
- 审核时限控制，防止无限期拖延
- 自动分账开关操作需要审批流程

## 监控与告警

### 1. 业务监控
- T+2审核完成率监控
- 争议处理时效监控
- 自动分账成功率监控
- 冻结订单数量监控

### 2. 系统监控
- 定时任务执行状态监控
- 数据库性能监控
- 接口响应时间监控
- 错误率监控

### 3. 告警机制
- 争议超时未处理告警
- 大量订单审核积压告警
- 自动分账异常告警
- 系统异常自动告警

## 性能优化

### 1. 数据库优化
- 为审核截止时间添加索引
- 为争议状态和创建时间添加复合索引
- 定期清理历史数据
- 使用读写分离提升查询性能

### 2. 缓存策略
- 缓存自动分账配置
- 缓存审核员信息
- 缓存争议处理模板
- 使用Redis缓存热点数据

### 3. 异步处理
- 争议创建后异步发送通知
- 审核完成后异步更新相关数据
- 分账冻结后异步记录日志
- 使用消息队列处理耗时操作