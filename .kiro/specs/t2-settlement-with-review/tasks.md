# T+2分账审核机制实施任务列表 (优化版)

## 实施计划概述

将现有的实时分账机制升级为T+2延迟分账，增加订单审核、争议处理、自动化控制等功能。基于重复功能分析，大量复用现有收入管理功能，重点开发全新的审核和争议处理机制。

## 功能复用策略

### ✅ 直接复用现有功能 (无需开发)
- **SettlementService**: 分账核心逻辑 (100%完成) - 仅需增加冻结检查
- **CommissionService**: 费率管理服务 (100%完成) - 完全复用
- **AccountService**: 账户管理服务 (95%完成) - 完全复用
- **基础数据表**: accounts、attendant_income、withdrawals等 - 完全复用

### 🔄 扩展现有功能 (修改开发)
- **订单完成流程**: 从立即分账改为创建审核记录
- **管理后台界面**: 复用提现审核界面组件和交互模式
- **统计报表功能**: 扩展现有统计，增加T+2相关指标

### 🆕 全新开发功能 (重点任务)
- **订单审核机制**: 审核流程、状态管理、定时检查
- **争议处理系统**: 投诉处理、调查流程、责任判定
- **分账冻结机制**: 冻结/解冻操作、状态检查
- **自动分账控制**: 开关管理、配置控制

## 任务列表

### 阶段一：数据库设计和迁移

- [-] 1. 创建订单审核相关数据表
  - 创建 order_reviews 表，支持订单审核流程管理
  - 创建 order_disputes 表，支持投诉和争议处理
  - 创建 settlement_freeze_records 表，支持分账冻结管理
  - 创建 auto_settlement_configs 表，支持自动分账配置
  - 创建 investigation_records 表，支持调查流程记录
  - _需求: 1.1, 2.1, 3.1, 4.1, 6.1_

- [x] 1.1 设计数据库迁移脚本
  - 编写创建新表的SQL迁移脚本
  - 编写回滚脚本确保可逆性
  - 添加必要的索引优化查询性能
  - _需求: 1.1, 2.1_

- [x] 1.2 扩展订单状态枚举
  - 在订单模型中增加新的审核相关状态
  - 更新订单状态转换逻辑
  - 修改相关的状态查询和过滤逻辑
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [x] 1.3 执行数据库迁移
  - 在开发环境执行迁移脚本
  - 验证表结构和索引创建正确
  - 测试数据插入和查询功能
  - _需求: 1.1, 2.1_

### 阶段二：核心服务层开发 (重点：全新功能)

- [x] 2. 修改现有订单完成流程 **[高优先级-修改现有]**
  - 修改 OrderCompletionService 的完成逻辑
  - 订单完成后创建审核记录，不立即分账
  - 订单状态从 OrderStatusCompleted 改为 OrderStatusPendingReview
  - 集成新的审核服务调用
  - _需求: 1.1, 5.1_

- [x] 3. 实现订单审核服务 **[高优先级-全新功能]**
  - 创建 OrderReviewService 处理审核业务逻辑
  - 实现审核记录的创建、更新、查询功能
  - 实现审核截止时间计算和检查逻辑
  - 集成现有的订单服务和用户服务
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 3.1 实现审核记录管理
  - 订单完成时自动创建审核记录
  - 设置48小时审核期限
  - 支持审核状态变更和记录更新
  - _需求: 2.1, 2.2_

- [x] 3.2 实现审核业务逻辑
  - 审核通过/不通过的处理逻辑
  - 审核备注和证据文件管理
  - 审核员权限验证和操作记录
  - _需求: 2.2, 2.3_

- [x] 4. 实现争议处理服务 **[高优先级-全新功能]**
  - 创建 DisputeService 处理投诉和争议
  - 实现争议创建、分配、处理流程
  - 实现与分账冻结服务的集成
  - 支持争议优先级和状态管理
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [x] 4.1 实现争议创建和管理
  - 支持患者和陪诊师提交争议
  - 争议类型分类和优先级设置
  - 证据文件上传和管理功能
  - _需求: 3.1, 3.2_

- [x] 4.2 实现调查流程服务
  - 创建 InvestigationService 管理调查过程
  - 支持调查任务分配和进度跟踪
  - 实现调查结果记录和证据收集
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [x] 5. 实现分账冻结服务 **[高优先级-全新功能]**
  - 创建 FreezeService 管理分账冻结
  - 实现冻结/解冻操作和状态管理
  - 修改现有 SettlementService 增加冻结检查
  - 实现冻结原因记录和操作日志
  - _需求: 7.1, 7.2, 7.3, 7.4_

- [x] 5.1 实现冻结操作逻辑
  - 争议提交时自动冻结分账
  - 支持手动冻结和解冻操作
  - 冻结状态检查和验证逻辑
  - _需求: 7.1, 7.2_

- [x] 5.2 集成现有分账服务 **[中优先级-修改现有]**
  - 在现有 ProcessOrderSettlement 方法开头增加冻结检查
  - 冻结订单跳过分账处理并记录日志
  - 解冻后恢复正常分账流程
  - **复用**: 现有分账核心逻辑、费率管理、账户管理
  - _需求: 7.3, 7.4_

- [x] 6. 实现自动分账控制服务 **[高优先级-全新功能]**
  - 创建 AutoSettlementController 管理自动分账
  - 实现配置管理和开关控制功能
  - 支持配置变更记录和权限控制
  - 集成定时任务调度器
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 6.1 实现配置管理功能
  - 自动分账开关的读取和设置
  - 配置变更历史记录
  - 操作权限验证和审批流程
  - _需求: 4.1, 4.2, 4.3_

- [x] 6.2 实现定时任务调度
  - T+2订单检查定时任务
  - 争议超时检查定时任务
  - 任务执行状态监控和日志记录
  - _需求: 8.1, 8.2, 8.3, 8.4_

- [ ] 7. 完善现有账户流水功能 **[低优先级-技术债务]**
  - 实现 AccountService 中的 GetAccountLogs 方法
  - 创建 AccountLogRepository 支持流水查询
  - 优化前端收入明细页面的流水显示
  - **说明**: 解决现有功能的待完善项目
  - _技术债务处理_

### 阶段三：API接口层开发 (重点：全新接口)

- [x] 8. 实现审核管理API **[高优先级-全新功能]**
  - 创建 ReviewHandler 处理审核相关请求
  - 实现待审核订单列表查询接口
  - 实现订单审核操作接口
  - 添加接口权限控制和参数验证
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 8.1 实现审核列表查询接口
  - GET /api/admin/reviews/pending 获取待审核订单
  - 支持分页、筛选、排序功能
  - 返回订单详情和审核状态信息
  - _需求: 2.1, 2.2_

- [x] 8.2 实现审核操作接口
  - POST /api/admin/reviews/{id}/review 执行审核
  - 支持审核通过/不通过操作
  - 记录审核备注和操作人信息
  - _需求: 2.3, 2.4_

- [ ] 9. 实现争议处理API **[高优先级-全新功能]**
  - 创建 DisputeHandler 处理争议相关请求
  - 实现争议创建和查询接口
  - 实现争议处理和解决接口
  - 支持调查记录的管理接口
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 9.1 实现争议创建接口
  - POST /api/disputes/create 创建争议
  - 支持文件上传和证据提交
  - 自动触发分账冻结逻辑
  - _需求: 3.1, 3.2_

- [ ] 9.2 实现争议管理接口
  - GET /api/admin/disputes 获取争议列表
  - POST /api/admin/disputes/{id}/resolve 处理争议
  - 支持责任判定和处理结果记录
  - _需求: 3.3, 3.4_

- [ ] 10. 实现配置管理API **[高优先级-全新功能]**
  - 创建 SettlementConfigHandler 处理配置请求
  - 实现自动分账配置查询接口
  - 实现配置更新和开关控制接口
  - 添加配置变更审批和日志记录
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [x] 10.1 实现配置查询接口
  - GET /api/admin/settlement/config 获取当前配置
  - 显示开关状态和最后更新信息
  - 支持配置历史查询
  - _需求: 4.1, 4.2_

- [x] 10.2 实现配置更新接口
  - POST /api/admin/settlement/config/toggle 切换开关
  - 记录操作原因和操作人信息
  - 发送配置变更通知
  - _需求: 4.3, 4.4_

### 阶段四：管理后台界面开发 (重点：复用现有组件)

- [ ] 11. 开发订单审核管理界面 **[中优先级-扩展现有]**
  - **复用**: 现有提现审核界面的列表组件、详情弹窗、操作按钮等
  - 创建待审核订单列表页面 (复用 WithdrawalHandler 的列表逻辑)
  - 实现订单详情查看和审核操作界面 (复用审核操作组件)
  - 添加审核历史记录查看功能
  - 集成文件查看和下载功能
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [ ] 11.1 实现审核列表页面
  - 显示待审核订单的关键信息
  - 支持按状态、时间等条件筛选 (复用现有筛选组件)
  - 提供快速审核操作按钮 (复用现有操作按钮样式)
  - _需求: 2.1, 2.2_

- [ ] 11.2 实现审核详情页面
  - 显示订单完整信息和服务凭证
  - 提供审核通过/不通过操作界面 (复用提现审核的操作界面)
  - 支持审核备注输入和文件查看
  - _需求: 2.3, 2.4_

- [ ] 12. 开发争议处理管理界面 **[高优先级-全新功能]**
  - 创建争议列表和详情页面
  - 实现调查流程管理界面
  - 添加争议处理和解决功能
  - 支持证据文件管理和查看
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 12.1 实现争议列表页面
  - 显示争议的基本信息和状态
  - 支持按类型、优先级筛选
  - 提供快速分配和处理入口
  - _需求: 3.1, 3.2_

- [ ] 12.2 实现争议处理页面
  - 显示争议详情和相关证据
  - 提供调查记录输入界面
  - 支持责任判定和处理结果录入
  - _需求: 3.3, 3.4_

- [ ] 13. 开发配置管理界面 **[中优先级-全新功能]**
  - 创建自动分账配置管理页面
  - 实现开关控制和配置修改界面
  - 添加配置变更历史查看功能
  - 支持批量操作和紧急控制
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 13.1 实现配置控制页面
  - 显示当前自动分账开关状态
  - 提供开关切换和原因输入界面
  - 显示配置变更历史记录
  - _需求: 4.1, 4.2, 4.3_

- [ ] 13.2 扩展现有统计页面 **[低优先级-扩展现有]**
  - **复用**: 现有收入统计和提现统计的图表组件
  - 在现有统计页面增加T+2分账处理统计
  - 展示争议处理效率数据
  - 提供异常订单监控面板
  - **说明**: 扩展现有统计功能，而非重新开发
  - _需求: 10.1, 10.2, 10.3, 10.4_

### 阶段五：通知和监控系统 (重点：扩展现有通知)

- [ ] 14. 扩展现有通知系统 **[中优先级-扩展现有]**
  - **复用**: 现有的通知基础设施和渠道
  - 在现有 NotificationService 基础上增加审核和争议通知
  - 实现审核提醒、争议告警等通知功能
  - 复用现有的邮件、短信、站内信等通知渠道
  - 扩展现有通知模板管理功能
  - _需求: 9.1, 9.2, 9.3, 9.4_

- [ ] 14.1 实现审核提醒通知
  - 订单进入审核期时通知审核员
  - 审核截止时间临近时发送提醒
  - 审核完成后通知相关人员
  - **复用**: 现有通知模板和发送机制
  - _需求: 9.1, 9.2_

- [ ] 14.2 实现争议处理通知
  - 争议提交时立即通知管理员
  - 调查过程中通知相关人员配合
  - 争议解决后通知申请人和相关方
  - **复用**: 现有紧急通知机制
  - _需求: 9.3, 9.4_

- [ ] 15. 扩展现有监控系统 **[低优先级-扩展现有]**
  - **复用**: 现有的监控基础设施
  - 在现有监控系统基础上增加T+2相关指标
  - 实现业务指标监控和异常检测
  - 复用现有的告警规则配置和通知策略
  - _需求: 10.1, 10.2, 10.3, 10.4_

- [ ] 15.1 实现业务监控告警
  - 监控T+2审核完成率和及时性
  - 监控争议处理时效和积压情况
  - 监控自动分账成功率和异常
  - **复用**: 现有业务监控面板和告警机制
  - _需求: 10.1, 10.2_

- [ ] 15.2 扩展系统监控告警
  - 监控定时任务执行状态
  - 监控数据库性能和接口响应
  - 监控错误率和异常情况
  - **复用**: 现有系统监控基础设施
  - _需求: 10.3, 10.4_

### 阶段六：测试和优化

- [ ] 16. 编写单元测试 **[标准优先级]**
  - 为所有新增服务编写单元测试
  - 重点测试审核、争议、冻结等核心业务逻辑
  - 测试配置管理和定时任务功能
  - 确保新增功能测试覆盖率达到80%以上
  - **复用**: 现有测试框架和工具链
  - _需求: 1.1-10.4_

- [ ] 16.1 测试审核服务功能
  - 测试审核记录创建和状态变更
  - 测试审核截止时间计算逻辑
  - 测试审核权限控制和操作记录
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [ ] 16.2 测试争议处理功能
  - 测试争议创建和分账冻结逻辑
  - 测试调查流程和责任判定
  - 测试争议解决和分账恢复
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 16.3 测试现有功能集成
  - 测试修改后的订单完成流程
  - 测试分账服务的冻结检查集成
  - 验证现有账户管理和费率管理功能不受影响
  - **重点**: 确保现有功能稳定性

- [ ] 17. 进行集成测试 **[高优先级]**
  - 测试T+2分账完整流程
  - 测试争议处理和分账冻结集成
  - 测试自动分账开关控制功能
  - 验证与现有收入管理功能的集成
  - _需求: 1.1-10.4_

- [ ] 17.1 测试T+2分账流程
  - 测试订单完成到分账的完整流程
  - 测试自动分账和手动分账场景
  - 测试异常情况的处理逻辑
  - **重点**: 验证与现有分账服务的无缝集成
  - _需求: 1.1, 8.1, 8.2_

- [ ] 17.2 测试争议处理流程
  - 测试从争议提交到解决的完整流程
  - 测试分账冻结和解冻的时机
  - 测试不同责任判定结果的处理
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [ ] 18. 性能测试和优化 **[标准优先级]**
  - 测试大量订单的审核处理性能
  - 优化新增数据库查询和索引设计
  - 测试定时任务的执行效率
  - 优化新增接口响应时间和并发处理
  - **复用**: 现有性能测试工具和基准
  - _需求: 8.1, 8.2, 8.3, 8.4_

- [ ] 18.1 数据库性能优化
  - 优化审核和争议相关查询
  - 添加必要的复合索引
  - 优化大数据量的分页查询
  - **保持**: 现有数据库性能不受影响
  - _需求: 2.1, 3.1_

- [ ] 18.2 接口性能优化
  - 优化审核列表查询接口
  - 优化争议处理相关接口
  - 添加适当的缓存机制
  - **复用**: 现有缓存基础设施
  - _需求: 2.1, 3.1_

### 阶段七：部署和上线 (重点：平滑迁移)

- [ ] 19. 准备生产环境部署 **[高优先级-风险控制]**
  - 准备生产环境数据库迁移脚本 (仅新增表，不影响现有表)
  - 配置生产环境的定时任务
  - 扩展现有监控告警和日志收集
  - 准备回滚方案和应急预案
  - **重点**: 确保现有收入管理功能不受影响
  - _需求: 1.1-10.4_

- [ ] 19.1 执行数据库迁移
  - 在生产环境执行新表结构迁移 (5个新表)
  - 扩展订单状态枚举 (向后兼容)
  - 验证数据完整性和索引效果
  - 备份现有财务数据确保安全
  - **安全策略**: 只新增，不修改现有表结构
  - _需求: 1.1, 1.2_

- [ ] 19.2 配置生产环境服务
  - 部署新增的服务和接口
  - 配置定时任务和监控告警
  - 设置自动分账开关初始状态为关闭 (手动控制)
  - **复用**: 现有的部署流水线和配置管理
  - _需求: 4.1, 8.1, 10.1_

- [ ] 20. 灰度发布和验证 **[高优先级-风险控制]**
  - 选择少量订单进行灰度测试 (1%流量)
  - 验证T+2分账流程正常运行
  - 监控系统性能和错误率
  - 确保现有实时分账功能正常运行
  - 收集用户反馈和优化建议
  - _需求: 1.1-10.4_

- [ ] 20.1 灰度测试验证
  - 测试小批量订单的T+2分账 (与现有分账并行运行)
  - 验证审核和争议处理功能
  - 监控系统稳定性和性能
  - **对比验证**: T+2分账结果与现有分账结果一致性
  - _需求: 1.1, 2.1, 3.1_

- [ ] 20.2 全量上线部署
  - 逐步扩大T+2分账覆盖范围 (10% → 50% → 100%)
  - 开启自动分账功能 (经过充分验证后)
  - 持续监控和优化系统性能
  - **平滑迁移**: 保持现有用户体验不变
  - _需求: 4.1, 8.1, 10.1_

## 实施里程碑 (优化后)

### 里程碑1：数据库和核心服务完成 (预计3周) **[减少1周]**
- 完成5个新数据表创建和迁移
- 完成审核、争议、冻结等核心服务开发
- 修改现有订单完成流程和分账服务集成
- 通过单元测试验证
- **优化**: 复用现有分账、费率、账户管理服务

### 里程碑2：API接口和管理界面完成 (预计2.5周) **[减少0.5周]**
- 完成审核、争议、配置管理API接口开发
- 完成管理后台界面开发 (复用现有审核界面组件)
- 扩展现有统计页面功能
- 通过集成测试验证
- **优化**: 复用现有管理界面组件和交互模式

### 里程碑3：通知监控和测试完成 (预计1.5周) **[减少0.5周]**
- 扩展现有通知提醒和监控告警系统
- 完成性能测试和优化
- 完善现有账户流水功能 (技术债务处理)
- 准备生产环境部署
- **优化**: 复用现有通知和监控基础设施

### 里程碑4：生产部署和上线 (预计1周)
- 完成生产环境平滑部署 (不影响现有功能)
- 完成灰度测试和全量上线
- 系统稳定运行，现有功能不受影响
- **重点**: 确保向后兼容和平滑迁移

## 总工期优化
- **原估算**: 10周
- **优化后**: 8周 **[减少20%工作量]**
- **优化原因**: 大量复用现有功能，减少重复开发

## 风险评估 (优化后)

### 高风险项 **[风险降低]**
- ~~数据库迁移可能影响现有分账功能~~ → **已优化**: 只新增表，不修改现有表结构
- T+2延迟可能影响陪诊师收入预期 → **缓解**: 灰度发布，逐步迁移
- 争议处理流程可能增加运营成本 → **保持**: 需要运营团队配合

### 中风险项
- 定时任务可能存在性能瓶颈 → **缓解**: 复用现有定时任务基础设施
- 自动分账开关控制需要严格权限管理 → **保持**: 需要严格的权限设计
- ~~通知系统可能存在延迟或失败~~ → **已优化**: 复用现有稳定的通知系统

### 低风险项 **[风险进一步降低]**
- ~~管理界面开发相对独立~~ → **已优化**: 复用现有界面组件，降低开发风险
- ~~监控告警系统为辅助功能~~ → **已优化**: 扩展现有监控系统
- 性能优化可以逐步进行 → **保持**: 基于现有性能基准优化

### 新增风险控制
- **向后兼容风险**: 通过并行运行和对比验证控制
- **功能回归风险**: 通过充分的集成测试控制
- **数据一致性风险**: 复用现有成熟的财务数据处理逻辑

## 成功标准 (优化后)

1. **功能完整性**: 所有需求功能正常运行，通过测试验证
2. **性能指标**: T+2审核处理时间<2小时，争议处理时间<24小时
3. **稳定性指标**: 系统可用性>99.9%，错误率<0.1%
4. **用户满意度**: 管理员操作便捷，陪诊师接受度高
5. **业务指标**: 争议率<5%，自动分账率>90%
6. **兼容性指标**: 现有收入管理功能100%正常运行，无功能回归
7. **迁移指标**: T+2分账结果与现有分账结果一致性>99.9%
8. **开发效率**: 通过功能复用，实际开发工作量比原计划减少20%

## 复用效果评估

### 直接复用功能 (节省100%开发工作量)
- ✅ SettlementService 分账核心逻辑
- ✅ CommissionService 费率管理服务  
- ✅ AccountService 账户管理服务
- ✅ 基础财务数据表结构

### 扩展现有功能 (节省60%开发工作量)
- 🔄 管理后台审核界面组件
- 🔄 统计报表和图表组件
- 🔄 通知提醒基础设施
- 🔄 监控告警系统

### 全新开发功能 (100%开发工作量)
- 🆕 订单审核流程和数据表
- 🆕 争议处理系统
- 🆕 分账冻结机制
- 🆕 自动分账控制

**总体复用率**: 约40%的功能可以直接复用或扩展现有功能，显著降低开发成本和风险。