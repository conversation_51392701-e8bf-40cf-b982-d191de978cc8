# 设计文档

## 概述

订单管理系统采用面向微服务的架构设计，处理陪诊服务订单的完整生命周期。系统遵循领域驱动设计（DDD）原则，在表示层、业务逻辑层和数据访问层之间实现清晰的关注点分离。

架构支持三个主要用户流程：
1. **患者流程**：服务发现 → 订单创建 → 支付 → 服务交付 → 评价
2. **陪诊师流程**：订单通知 → 接受分配 → 服务交付 → 完成
3. **管理员流程**：订单监控 → 异常处理 → 人工干预

系统集成外部服务，包括微信支付用于支付处理、通知服务用于实时更新，以及自动匹配算法用于陪诊师分配。

## 架构设计

### 高层架构

```mermaid
graph TB
    subgraph "Client Layer"
        WX[WeChat Mini Program]
        ADMIN[Admin Dashboard]
        API[External APIs]
    end
    
    subgraph "API Gateway Layer"
        GW[API Gateway/Router]
        AUTH[Authentication Middleware]
        RATE[Rate Limiting]
    end
    
    subgraph "Application Layer"
        OH[Order Handler]
        PH[Payment Handler]
        NH[Notification Handler]
        MH[Matching Handler]
    end
    
    subgraph "Business Logic Layer"
        OS[Order Service]
        PS[Payment Service]
        MS[Matching Service]
        NS[Notification Service]
        TS[Timeout Service]
    end
    
    subgraph "Data Access Layer"
        OR[Order Repository]
        AR[Attendant Repository]
        UR[User Repository]
        PR[Patient Repository]
    end
    
    subgraph "External Services"
        WP[WeChat Pay]
        MQ[Message Queue]
        REDIS[Redis Cache]
    end
    
    subgraph "Data Storage"
        DB[(MySQL Database)]
    end
    
    WX --> GW
    ADMIN --> GW
    API --> GW
    
    GW --> AUTH
    AUTH --> RATE
    RATE --> OH
    RATE --> PH
    RATE --> NH
    RATE --> MH
    
    OH --> OS
    PH --> PS
    NH --> NS
    MH --> MS
    
    OS --> OR
    OS --> AR
    OS --> UR
    OS --> PR
    PS --> WP
    MS --> MQ
    NS --> MQ
    TS --> OR
    
    OR --> DB
    AR --> DB
    UR --> DB
    PR --> DB
    
    OS --> REDIS
    MS --> REDIS
```

### 服务架构模式

系统遵循依赖注入的分层架构：

- **处理器层**：HTTP请求/响应处理、输入验证、身份认证
- **服务层**：业务逻辑实现、事务管理、外部服务集成
- **仓储层**：数据访问抽象、数据库操作、缓存
- **模型层**：领域实体、值对象、业务规则

### 数据流架构

```mermaid
sequenceDiagram
    participant P as Patient
    participant API as API Gateway
    participant OS as Order Service
    participant PS as Payment Service
    participant MS as Matching Service
    participant A as Attendant
    participant DB as Database
    participant WP as WeChat Pay
    participant MQ as Message Queue
    
    P->>API: Create Order Request
    API->>OS: Process Order Creation
    OS->>DB: Save Order (Status: Created)
    OS->>API: Return Order Details
    API->>P: Order Created Response
    
    P->>API: Initiate Payment
    API->>PS: Process Payment
    PS->>WP: WeChat Pay Request
    WP->>PS: Payment Success
    PS->>OS: Update Order Status (Paid)
    OS->>DB: Update Order
    OS->>MS: Trigger Matching
    MS->>MQ: Send Matching Request
    MQ->>A: Notify Available Attendants
    
    A->>API: Accept Order
    API->>MS: Process Acceptance
    MS->>OS: Update Order (Matched)
    OS->>DB: Update Order Status
    OS->>MQ: Notify Patient
    MQ->>P: Order Matched Notification
```

## 组件和接口

### 核心服务接口

#### 订单服务接口
```go
type IOrderService interface {
    // Order Lifecycle Management
    CreateOrder(ctx context.Context, userID uint, input *CreateOrderInput) (*model.Order, error)
    GetOrder(ctx context.Context, orderID uint) (*model.Order, error)
    UpdateOrderStatus(ctx context.Context, orderID uint, status int, operatorID uint, operatorType int, remark string) error
    CancelOrder(ctx context.Context, orderID uint, userID uint, reason string) error
    CompleteOrder(ctx context.Context, orderID uint, attendantID uint) error
    
    // Order Queries
    ListUserOrders(ctx context.Context, userID uint, status int, offset, limit int) ([]*model.Order, int64, error)
    ListAttendantOrders(ctx context.Context, attendantID uint, status int, offset, limit int) ([]*model.Order, int64, error)
    GetOrderStatistics(ctx context.Context, startTime, endTime time.Time) (*model.OrderDailyStats, error)
    
    // Service Management
    StartOrderService(ctx context.Context, orderID uint, attendantID uint) error
    CompleteOrderWithEvidence(ctx context.Context, orderID uint, attendantID uint, req *dto.CompleteOrderRequest) (*dto.CompleteOrderResponse, error)
    
    // Review System
    CreateOrderReview(ctx context.Context, orderID uint, userID uint, input *CreateOrderReviewInput) error
    ReplyOrderReview(ctx context.Context, orderID uint, attendantID uint, reply string) error
}
```

#### IPaymentService Interface
```go
type IPaymentService interface {
    // Payment Processing
    CreatePayment(ctx context.Context, orderID uint, amount float64, paymentMethod string) (*model.Payment, error)
    ProcessPayment(ctx context.Context, paymentID uint) error
    HandlePaymentCallback(ctx context.Context, callbackData map[string]interface{}) error
    
    // Refund Management
    CreateRefund(ctx context.Context, orderID uint, amount float64, reason string) (*model.Refund, error)
    ProcessRefund(ctx context.Context, refundID uint) error
    HandleRefundCallback(ctx context.Context, callbackData map[string]interface{}) error
    
    // Payment Queries
    GetPaymentByOrderID(ctx context.Context, orderID uint) (*model.Payment, error)
    ListRefunds(ctx context.Context, orderID uint) ([]*model.Refund, error)
}
```

#### IMatchingService Interface
```go
type IMatchingService interface {
    // Attendant Matching
    MatchAttendantsForOrder(ctx context.Context, orderID uint) ([]*model.Attendant, error)
    AssignOrderToAttendant(ctx context.Context, orderID uint, attendantID uint, matchType int) error
    ProcessAttendantResponse(ctx context.Context, matchingID uint, response int, message string) error
    
    // Matching Queries
    GetOrderMatchings(ctx context.Context, orderID uint) ([]*model.OrderMatching, error)
    GetAttendantPendingOrders(ctx context.Context, attendantID uint) ([]*model.Order, error)
    
    // Timeout Handling
    ProcessMatchingTimeouts(ctx context.Context) error
    ReassignTimedOutOrders(ctx context.Context) error
}
```

#### INotificationService Interface
```go
type INotificationService interface {
    // Order Notifications
    NotifyOrderCreated(ctx context.Context, order *model.Order) error
    NotifyPaymentSuccess(ctx context.Context, order *model.Order) error
    NotifyOrderMatched(ctx context.Context, order *model.Order, attendant *model.Attendant) error
    NotifyServiceStarted(ctx context.Context, order *model.Order) error
    NotifyOrderCompleted(ctx context.Context, order *model.Order) error
    NotifyOrderCancelled(ctx context.Context, order *model.Order, reason string) error
    
    // Attendant Notifications
    NotifyAttendantAssignment(ctx context.Context, attendant *model.Attendant, order *model.Order) error
    NotifyAttendantTimeout(ctx context.Context, attendant *model.Attendant, order *model.Order) error
    
    // Admin Notifications
    NotifyAdminException(ctx context.Context, orderID uint, exception string) error
    NotifyManualIntervention(ctx context.Context, orderID uint, reason string) error
}
```

### Repository Interfaces

#### IOrderRepository Interface
```go
type IOrderRepository interface {
    // Basic CRUD Operations
    Create(ctx context.Context, order *model.Order) error
    FindByID(ctx context.Context, id uint) (*model.Order, error)
    Update(ctx context.Context, order *model.Order) error
    Delete(ctx context.Context, id uint) error
    
    // Query Operations
    List(ctx context.Context, filter model.OrderFilter) ([]*model.Order, int64, error)
    ListByUserID(ctx context.Context, userID uint, offset, limit int) ([]*model.Order, int64, error)
    ListByAttendantID(ctx context.Context, attendantID uint, offset, limit int) ([]*model.Order, int64, error)
    CountByStatus(ctx context.Context, status int) (int64, error)
    
    // Status Management
    UpdateStatus(ctx context.Context, orderID uint, status int) error
    UpdateMatchingStatus(ctx context.Context, orderID uint, matchingStatus int, attendantID uint) error
    
    // Timeout Queries
    FindTimeoutOrders(ctx context.Context, timeoutMinutes int, statuses []int) ([]*model.Order, error)
    FindUnmatchedOrders(ctx context.Context, timeoutMinutes int) ([]*model.Order, error)
    
    // Transaction Support
    Transaction(fn func(tx *gorm.DB) error) error
}
```

### Data Transfer Objects (DTOs)

#### Order Creation DTO
```go
type CreateOrderInput struct {
    PatientName     string    `json:"patient_name" binding:"required"`
    PatientPhone    string    `json:"patient_phone" binding:"required"`
    PatientGender   int       `json:"patient_gender" binding:"required"`
    IDCard          string    `json:"id_card"`
    Relation        string    `json:"relation" binding:"required"`
    ServiceID       uint      `json:"service_id" binding:"required"`
    AttendantID     uint      `json:"attendant_id"`
    HospitalID      uint      `json:"hospital_id" binding:"required"`
    Department      string    `json:"department" binding:"required"`
    ServiceTime     time.Time `json:"service_time" binding:"required"`
    ServiceHours    int       `json:"service_hours" binding:"required,min=1"`
    Amount          float64   `json:"amount"`
    PricingType     int       `json:"pricing_type"`
    PricingDuration string    `json:"pricing_duration"`
    ServiceItems    []uint    `json:"service_items"`
    Remark          string    `json:"remark"`
}
```

#### Order Response DTO
```go
type OrderDetailResponse struct {
    ID                  uint                    `json:"id"`
    OrderNo             string                  `json:"order_no"`
    Status              int                     `json:"status"`
    StatusText          string                  `json:"status_text"`
    MatchingStatus      int                     `json:"matching_status"`
    MatchingStatusText  string                  `json:"matching_status_text"`
    Amount              float64                 `json:"amount"`
    ServiceFee          float64                 `json:"service_fee"`
    ServiceTime         time.Time               `json:"service_time"`
    ServiceHours        int                     `json:"service_hours"`
    PaymentMethod       string                  `json:"payment_method"`
    PaymentTime         *time.Time              `json:"payment_time"`
    EmergencyLevel      int                     `json:"emergency_level"`
    EmergencyLevelText  string                  `json:"emergency_level_text"`
    CancelReason        string                  `json:"cancel_reason"`
    CreatedAt           time.Time               `json:"created_at"`
    UpdatedAt           time.Time               `json:"updated_at"`
    
    // Related Entities
    Patient             *PatientResponse        `json:"patient"`
    Attendant           *AttendantBriefInfo     `json:"attendant"`
    Service             *ServiceResponse        `json:"service"`
    Appointment         *AppointmentResponse    `json:"appointment"`
    Reviews             []*OrderReviewResponse  `json:"reviews"`
}
```

## Data Models

### Core Domain Models

#### Order Entity
```go
type Order struct {
    ID                  uint       `json:"id" gorm:"primaryKey"`
    OrderNo             string     `json:"order_no" gorm:"uniqueIndex;not null"`
    AppointmentID       uint       `json:"appointment_id" gorm:"not null"`
    UserID              uint       `json:"user_id" gorm:"not null"`
    AttendantID         uint       `json:"attendant_id"`
    PatientID           uint       `json:"patient_id" gorm:"not null"`
    ServiceID           *uint      `json:"service_id"`
    ServiceMode         int        `json:"service_mode" gorm:"default:1"`
    Amount              float64    `json:"amount" gorm:"type:decimal(10,2);not null"`
    ServiceFee          float64    `json:"service_fee" gorm:"type:decimal(10,2);not null"`
    ServiceTime         time.Time  `json:"service_time" gorm:"not null"`
    ServiceHours        int        `json:"service_hours" gorm:"not null"`
    PaymentMethod       string     `json:"payment_method"`
    PaymentTime         *time.Time `json:"payment_time"`
    PaymentNo           string     `json:"payment_no"`
    Status              int        `json:"status" gorm:"default:1"`
    MatchingStatus      int        `json:"matching_status" gorm:"default:0"`
    MatchingStartedAt   *time.Time `json:"matching_started_at"`
    MatchingCompletedAt *time.Time `json:"matching_completed_at"`
    EmergencyLevel      int        `json:"emergency_level" gorm:"default:1"`
    CancelReason        string     `json:"cancel_reason"`
    
    // Settlement Fields
    PlatformCommission  float64    `json:"platform_commission" gorm:"type:decimal(10,2);default:0"`
    AttendantAmount     float64    `json:"attendant_amount" gorm:"type:decimal(10,2);default:0"`
    CommissionRate      float64    `json:"commission_rate" gorm:"type:decimal(5,4);default:0.1"`
    SettlementStatus    int        `json:"settlement_status" gorm:"default:0"`
    
    CreatedAt           time.Time  `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt           time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
    
    // Relationships
    Appointment         *Appointment `json:"appointment,omitempty" gorm:"foreignKey:AppointmentID"`
    User                *User        `json:"user,omitempty" gorm:"foreignKey:UserID"`
    Attendant           *Attendant   `json:"attendant,omitempty" gorm:"foreignKey:AttendantID"`
    Patient             *Patient     `json:"patient,omitempty" gorm:"foreignKey:PatientID"`
    Service             *Service     `json:"service,omitempty" gorm:"foreignKey:ServiceID"`
}
```

#### 订单状态常量
```go
// 订单主状态 (orders.status)
const (
    OrderStatusCreated        = 1  // 待支付 - 订单已创建，等待用户支付
    OrderStatusPaid          = 2  // 已支付 - 用户已支付，等待匹配陪诊师
    OrderStatusInProgress    = 3  // 进行中 - 已匹配陪诊师，服务进行中
    OrderStatusCompleted     = 4  // 已完成 - 服务已完成
    OrderStatusCancelled     = 5  // 已取消 - 订单被取消
    OrderStatusRefunded      = 6  // 已退款 - 订单已退款
    OrderStatusPendingRefund = 7  // 待退款 - 申请退款中
    // OrderStatusManualHandling = 8  // 已移除 - 改用 matching_status 管理人工处理
)

// 匹配状态 (orders.matching_status)
const (
    MatchingStatusNone           = 0  // 未开始匹配 - 订单未开始匹配流程
    MatchingStatusMatching       = 1  // 匹配中 - 正在进行匹配
    MatchingStatusMatched        = 2  // 已匹配 - 成功匹配到陪诊师
    MatchingStatusFailed         = 3  // 匹配失败 - 匹配失败，需要人工处理
    MatchingStatusServiceStarted = 4  // 服务进行中 - 陪诊师已开始服务
)

// 匹配记录状态 (order_matching.status)
const (
    OrderMatchingStatusPending  = 1  // 待确认 - 已推送给陪诊师，等待响应
    OrderMatchingStatusAccepted = 2  // 已接受 - 陪诊师接受订单
    OrderMatchingStatusRejected = 3  // 已拒绝 - 陪诊师拒绝订单
    OrderMatchingStatusTimeout  = 4  // 已超时 - 陪诊师未在规定时间内响应
    OrderMatchingStatusSent     = 5  // 已发送 - 匹配记录已创建并推送
    OrderMatchingStatusFailed   = 6  // 发送失败 - 推送失败
)

// 匹配类型 (order_matching.match_type)
const (
    MatchTypeAlgorithm = 1  // 算法匹配 - 系统自动匹配
    MatchTypeManual    = 2  // 人工分配 - 管理员手动分配
    MatchTypeReassign  = 3  // 自动转派 - 系统自动转派给其他陪诊师
)

// 紧急程度 (orders.emergency_level)
const (
    EmergencyLevelNormal  = 1  // 普通
    EmergencyLevelLow     = 2  // 低
    EmergencyLevelMedium  = 3  // 中
    EmergencyLevelHigh    = 4  // 高
    EmergencyLevelUrgent  = 5  // 特急
)
```

#### 订单匹配实体
```go
type OrderMatching struct {
    ID              uint       `json:"id" gorm:"primaryKey"`
    OrderID         uint       `json:"order_id" gorm:"not null"`
    AttendantID     uint       `json:"attendant_id" gorm:"not null"`
    Status          int        `json:"status" gorm:"not null;default:5"`  // 默认为已发送状态
    MatchType       int        `json:"match_type" gorm:"not null;default:1"`  // 默认为算法匹配
    MatchScore      float64    `json:"match_score" gorm:"type:decimal(5,2);default:0"`
    PushTime        time.Time  `json:"push_time" gorm:"not null"`
    ResponseTime    *time.Time `json:"response_time"`
    ResponseMessage string     `json:"response_message"`
    TimeoutAt       time.Time  `json:"timeout_at" gorm:"not null"`  // 30分钟后超时
    CreatedAt       time.Time  `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt       time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
    
    // 关联关系
    Order           *Order     `json:"order,omitempty" gorm:"foreignKey:OrderID"`
    Attendant       *Attendant `json:"attendant,omitempty" gorm:"foreignKey:AttendantID"`
}
```

### 订单匹配流程设计

#### 匹配流程状态机
```mermaid
stateDiagram-v2
    [*] --> 待支付: 创建订单
    待支付 --> 已支付: 支付成功
    已支付 --> 匹配中: 启动匹配
    匹配中 --> 已匹配: 陪诊师接受
    匹配中 --> 人工处理: 匹配失败
    匹配中 --> 匹配中: 重新匹配
    人工处理 --> 已匹配: 人工分配成功
    人工处理 --> 待退款: 分配失败且超时
    已匹配 --> 进行中: 开始服务
    进行中 --> 已完成: 服务完成
    待退款 --> 已退款: 退款完成
    
    state 匹配中 {
        [*] --> 算法匹配
        算法匹配 --> 创建匹配记录
        创建匹配记录 --> 推送通知
        推送通知 --> 等待响应
        等待响应 --> 接受: 陪诊师接受
        等待响应 --> 拒绝: 陪诊师拒绝
        等待响应 --> 超时: 30分钟无响应
        拒绝 --> 检查其他匹配
        超时 --> 检查其他匹配
        检查其他匹配 --> 等待响应: 还有待处理
        检查其他匹配 --> 匹配失败: 全部失败
        匹配失败 --> 重新匹配: 距离服务时间>2小时
        匹配失败 --> [*]: 距离服务时间<2小时
    }
```

#### 匹配算法设计
```go
type MatchingAlgorithm struct {
    // 匹配评分权重
    DistanceWeight    float64  // 距离权重 (40%)
    RatingWeight      float64  // 评分权重 (30%)
    ExperienceWeight  float64  // 经验权重 (20%)
    AvailabilityWeight float64 // 可用性权重 (10%)
}

type MatchingCriteria struct {
    ServiceTime     time.Time  // 服务时间
    ServiceLocation Point      // 服务位置
    ServiceType     uint       // 服务类型
    EmergencyLevel  int        // 紧急程度
    MaxDistance     float64    // 最大距离(公里)
    MinRating       float64    // 最低评分
    MaxResults      int        // 最大结果数
}

func (m *MatchingAlgorithm) CalculateScore(attendant *Attendant, criteria *MatchingCriteria) float64 {
    // 距离评分 (越近分数越高)
    distanceScore := m.calculateDistanceScore(attendant.Location, criteria.ServiceLocation)
    
    // 评分评分 (评分越高分数越高)
    ratingScore := attendant.Rating / 5.0
    
    // 经验评分 (经验越多分数越高)
    experienceScore := math.Min(float64(attendant.Experience)/10.0, 1.0)
    
    // 可用性评分 (当前时间段可用性)
    availabilityScore := m.calculateAvailabilityScore(attendant, criteria.ServiceTime)
    
    // 加权总分
    totalScore := distanceScore*m.DistanceWeight +
                  ratingScore*m.RatingWeight +
                  experienceScore*m.ExperienceWeight +
                  availabilityScore*m.AvailabilityWeight
    
    return totalScore
}
```

### 数据库架构设计

#### 主要数据表
- **orders**: 核心订单信息和状态跟踪
- **appointments**: 与订单关联的医疗预约详情
- **order_matchings**: 陪诊师分配和匹配记录
- **order_status_logs**: 完整的状态变更审计跟踪
- **order_reviews**: 患者评价和陪诊师回复
- **order_service_records**: 服务交付跟踪和证据记录
- **payments**: 支付交易记录
- **refunds**: 退款处理记录

#### 索引策略
```sql
-- Performance Indexes
CREATE INDEX idx_orders_user_id_status ON orders(user_id, status);
CREATE INDEX idx_orders_attendant_id_status ON orders(attendant_id, status);
CREATE INDEX idx_orders_service_time ON orders(service_time);
CREATE INDEX idx_orders_matching_status ON orders(matching_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);

-- Timeout Processing Indexes
CREATE INDEX idx_orders_timeout_check ON orders(status, matching_status, created_at);
CREATE INDEX idx_order_matchings_timeout ON order_matchings(status, timeout_at);

-- Business Logic Indexes
CREATE INDEX idx_order_matchings_order_attendant ON order_matchings(order_id, attendant_id);
CREATE INDEX idx_order_status_logs_order_id ON order_status_logs(order_id, created_at);
```

## 错误处理

### 错误分类

#### 业务逻辑错误
```go
var (
    ErrOrderNotFound           = errors.New("订单不存在")
    ErrOrderStatusInvalid      = errors.New("订单状态不允许此操作")
    ErrAttendantNotAvailable   = errors.New("陪诊师当前不可用")
    ErrPaymentFailed          = errors.New("支付处理失败")
    ErrMatchingTimeout        = errors.New("匹配超时")
    ErrInsufficientBalance    = errors.New("余额不足")
    ErrOrderAlreadyCompleted  = errors.New("订单已完成")
    ErrUnauthorizedOperation  = errors.New("无权执行此操作")
)
```

#### 错误响应结构
```go
type ErrorResponse struct {
    Code      int    `json:"code"`
    Message   string `json:"message"`
    Details   string `json:"details,omitempty"`
    RequestID string `json:"request_id,omitempty"`
    Timestamp int64  `json:"timestamp"`
}
```

### 错误处理策略

1. **验证错误**：返回400 Bad Request，包含具体的字段验证消息
2. **认证错误**：返回401 Unauthorized，包含清晰的认证要求
3. **授权错误**：返回403 Forbidden，包含权限详情
4. **资源未找到**：返回404 Not Found，包含资源标识
5. **业务逻辑错误**：返回422 Unprocessable Entity，包含业务规则说明
6. **外部服务错误**：返回502 Bad Gateway，包含重试建议
7. **内部错误**：返回500 Internal Server Error，包含错误跟踪ID

### Retry and Circuit Breaker Patterns

```go
type CircuitBreakerConfig struct {
    MaxFailures     int           `json:"max_failures"`
    ResetTimeout    time.Duration `json:"reset_timeout"`
    RetryAttempts   int           `json:"retry_attempts"`
    RetryDelay      time.Duration `json:"retry_delay"`
}

// Applied to external service calls:
// - WeChat Pay API calls
// - Notification service calls
// - Message queue operations
```

## Testing Strategy

### Unit Testing

#### Service Layer Testing
```go
func TestOrderService_CreateOrder(t *testing.T) {
    tests := []struct {
        name        string
        input       *service.CreateOrderInput
        mockSetup   func(*mocks.MockOrderRepository)
        expectedErr error
    }{
        {
            name: "successful order creation",
            input: &service.CreateOrderInput{
                PatientName:  "张三",
                ServiceID:    1,
                HospitalID:   1,
                Department:   "内科",
                ServiceTime:  time.Now().Add(24 * time.Hour),
                ServiceHours: 4,
            },
            mockSetup: func(repo *mocks.MockOrderRepository) {
                repo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
            },
            expectedErr: nil,
        },
        {
            name: "invalid service time",
            input: &service.CreateOrderInput{
                ServiceTime: time.Now().Add(-1 * time.Hour), // Past time
            },
            expectedErr: service.ErrInvalidServiceTime,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

#### Repository Layer Testing
```go
func TestOrderRepository_Create(t *testing.T) {
    db := setupTestDB(t)
    repo := repository.NewOrderRepository(db)
    
    order := &model.Order{
        OrderNo:      "TEST001",
        UserID:       1,
        PatientID:    1,
        ServiceTime:  time.Now().Add(24 * time.Hour),
        ServiceHours: 4,
        Amount:       200.00,
        Status:       model.OrderStatusCreated,
    }
    
    err := repo.Create(context.Background(), order)
    assert.NoError(t, err)
    assert.NotZero(t, order.ID)
}
```

### Integration Testing

#### API Endpoint Testing
```go
func TestOrderHandler_CreateOrder(t *testing.T) {
    router := setupTestRouter()
    
    payload := map[string]interface{}{
        "patient_name":   "张三",
        "service_id":     1,
        "hospital_id":    1,
        "department":     "内科",
        "service_time":   time.Now().Add(24 * time.Hour).Format(time.RFC3339),
        "service_hours":  4,
    }
    
    body, _ := json.Marshal(payload)
    req := httptest.NewRequest("POST", "/api/v1/orders", bytes.NewBuffer(body))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+testToken)
    
    w := httptest.NewRecorder()
    router.ServeHTTP(w, req)
    
    assert.Equal(t, http.StatusOK, w.Code)
    
    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    assert.Equal(t, 0, int(response["code"].(float64)))
}
```

### End-to-End Testing

#### Order Lifecycle Testing
```go
func TestOrderLifecycle(t *testing.T) {
    // 1. Create order
    order := createTestOrder(t)
    assert.Equal(t, model.OrderStatusCreated, order.Status)
    
    // 2. Process payment
    payment := processTestPayment(t, order.ID)
    assert.Equal(t, "success", payment.Status)
    
    // 3. Verify order status updated
    updatedOrder := getOrder(t, order.ID)
    assert.Equal(t, model.OrderStatusPaid, updatedOrder.Status)
    
    // 4. Trigger matching
    attendants := triggerMatching(t, order.ID)
    assert.NotEmpty(t, attendants)
    
    // 5. Accept order
    acceptOrder(t, order.ID, attendants[0].ID)
    
    // 6. Verify matching completed
    finalOrder := getOrder(t, order.ID)
    assert.Equal(t, model.MatchingStatusMatched, finalOrder.MatchingStatus)
}
```

### Performance Testing

#### Load Testing Scenarios
1. **Order Creation Load**: 1000 concurrent order creations per minute
2. **Payment Processing Load**: 500 concurrent payment requests per minute  
3. **Matching Algorithm Load**: 100 concurrent matching requests per minute
4. **Database Query Performance**: Complex order queries under load
5. **Cache Performance**: Redis cache hit rates under high load

#### Performance Benchmarks
```go
func BenchmarkOrderService_CreateOrder(b *testing.B) {
    service := setupOrderService()
    input := &service.CreateOrderInput{
        // Test data
    }
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, err := service.CreateOrder(context.Background(), 1, input)
        if err != nil {
            b.Fatal(err)
        }
    }
}
```

### Test Data Management

#### Test Database Setup
```go
func setupTestDB(t *testing.T) *gorm.DB {
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    require.NoError(t, err)
    
    // Auto-migrate test schema
    err = db.AutoMigrate(
        &model.Order{},
        &model.Appointment{},
        &model.OrderMatching{},
        &model.OrderStatusLog{},
        &model.OrderReview{},
    )
    require.NoError(t, err)
    
    // Seed test data
    seedTestData(db)
    
    return db
}
```

#### Mock Data Factories
```go
func CreateTestOrder(overrides ...func(*model.Order)) *model.Order {
    order := &model.Order{
        OrderNo:      fmt.Sprintf("TEST%d", time.Now().Unix()),
        UserID:       1,
        PatientID:    1,
        ServiceTime:  time.Now().Add(24 * time.Hour),
        ServiceHours: 4,
        Amount:       200.00,
        Status:       model.OrderStatusCreated,
    }
    
    for _, override := range overrides {
        override(order)
    }
    
    return order
}
```