# 需求文档

## 项目介绍

订单管理系统是陪诊服务平台的核心功能，连接需要陪诊服务的患者和提供专业陪护服务的陪诊师。该系统管理服务订单从创建到完成的完整生命周期，包括支付处理、陪诊师匹配、服务交付和服务后评价。

系统服务三类主要用户：需要陪诊服务的患者、提供服务的陪诊师，以及管理平台的管理员。系统处理多种服务模式（常规、紧急、灵活），并集成支付系统、通知服务和匹配算法。

## 需求列表

### 需求 1

**用户故事：** 作为患者，我希望创建医疗陪诊服务订单，以便在医院就诊过程中获得专业的陪护服务。

#### 验收标准

1. 当患者选择服务并提供预约详情时，系统应创建一个具有唯一订单号的新订单
2. 当创建订单时，系统应验证所选医院和科室是否存在
3. 当创建订单时，系统应根据服务类型和计费模式（按次、半天、全天）计算总金额
4. 当创建订单时，系统应创建或更新患者信息记录
5. 当创建订单时，系统应创建包含医院和科室详情的关联预约记录
6. 当创建订单时，系统应将初始订单状态设置为"待支付"
7. 如果选择了特定陪诊师，系统应验证陪诊师可用性并将其分配给订单
8. 如果未选择特定陪诊师，系统应将陪诊师字段留空以便后续匹配

### 需求 2

**用户故事：** 作为患者，我希望为服务订单付款，以便确认预订并获得陪诊服务。

#### 验收标准

1. 当患者发起订单支付时，系统应集成微信支付网关
2. 当支付成功时，系统应将订单状态更新为"已支付"
3. 当支付成功时，系统应记录支付时间、支付单号和支付方式
4. 当支付成功时，如果未预选陪诊师，系统应触发陪诊师匹配流程
5. 当支付失败时，系统应保持订单状态为"待支付"并允许重试
6. 当支付完成时，系统应向患者发送确认通知

### 需求 3

**用户故事：** 作为患者，我希望实时跟踪订单状态，以便了解服务进展情况。

#### 验收标准

1. 当患者查看订单时，系统应显示当前订单状态及描述性文本
2. 当患者查看订单时，如果陪诊师分配正在进行中，系统应显示匹配状态
3. 当患者查看订单时，一旦匹配成功，系统应显示陪诊师信息
4. 当患者查看订单时，系统应显示预约详情，包括医院、科室和服务时间
5. 当患者查看订单时，系统应显示来自陪诊师的服务进度更新
6. 当订单状态发生变化时，系统应向患者发送推送通知

### 需求 4

**用户故事：** 作为陪诊师，我希望接收并响应订单分配，以便接受合适的服务请求并管理我的工作量。

#### 验收标准

1. 当订单分配给陪诊师时，系统应发送包含订单详情的通知
2. 当陪诊师收到分配时，系统应提供订单信息，包括患者详情、服务时间和位置
3. 当陪诊师接受订单时，系统应将匹配状态更新为"已匹配"
4. 当陪诊师接受订单时，系统应将订单状态更新为"进行中"
5. 当陪诊师拒绝订单时，系统应触发重新分配给其他可用陪诊师
6. 当分配超时无响应时，系统应自动重新分配订单
7. 当陪诊师接受订单时，系统应通知患者匹配成功

### 需求 5

**用户故事：** 作为陪诊师，我希望管理服务交付并更新订单进度，以便让患者了解情况并记录服务完成情况。

#### 验收标准

1. 当陪诊师开始服务时，系统应将匹配状态更新为"服务已开始"
2. 当陪诊师开始服务时，系统应记录服务开始时间和位置
3. 当陪诊师提供服务更新时，系统应允许添加进度备注和照片
4. 当陪诊师完成服务时，系统应将订单状态更新为"已完成"
5. 当陪诊师完成服务时，系统应触发自动结算处理
6. 当服务完成时，系统应启用患者评价功能
7. 当服务完成时，系统应记录完成时间和最终服务摘要

### 需求 6

**用户故事：** 作为患者，我希望在需要时取消订单，以便避免不必要的费用并释放陪诊师的可用性。

#### 验收标准

1. 当患者请求取消订单时，系统应验证订单状态是否允许取消
2. 当订单处于"待支付"状态时，系统应允许免费取消
3. 当订单处于"已支付"状态时，系统应允许取消并处理退款
4. 当订单处于"进行中"状态时，系统应要求提供取消原因并可能收取取消费用
5. 当订单被取消时，系统应将状态更新为"已取消"
6. 当订单被取消时，如果适用，系统应通知已分配的陪诊师
7. 当订单被取消时，系统应根据取消政策处理退款
8. 当订单被取消时，系统应记录取消原因和时间戳

### 需求 7

**用户故事：** 作为患者，我希望在服务完成后评价服务质量，以便提供反馈并帮助其他患者做出明智的决定。

#### 验收标准

1. 当订单完成时，系统应启用患者评价功能
2. 当患者提交评价时，系统应接受1-5星的评分
3. 当患者提交评价时，系统应接受书面反馈评论
4. 当患者提交评价时，系统应允许上传服务照片作为证据
5. 当患者提交评价时，系统应允许选择服务质量标签
6. 当提交评价时，系统应更新陪诊师的总体评分和评价数量
7. 当提交评价时，系统应通知陪诊师有新评价
8. 当陪诊师回复评价时，系统应记录回复内容和时间戳

### 需求 8

**用户故事：** 作为管理员，我希望监控订单操作并处理异常，以便确保平台质量并及时解决问题。

#### 验收标准

1. 当管理员访问系统时，系统应提供订单统计仪表板
2. 当管理员查看订单时，系统应显示按状态、日期范围和其他条件筛选的订单
3. 当管理员查看订单时，系统应显示包括状态变更在内的完整订单历史
4. 当匹配反复失败时，系统应标记订单以进行人工干预
5. 当出现支付问题时，系统应提供人工支付验证和处理工具
6. 当出现争议时，系统应提供订单详情和沟通历史以便解决
7. 当发生系统错误时，系统应记录详细的错误信息以便故障排除
8. 当管理员更新订单状态时，系统应记录操作员和变更原因

### 需求 9

**用户故事：** 作为系统，我希望自动匹配订单与合适的陪诊师，以便基于可用性、位置和能力高效分配服务请求。

#### 验收标准

1. 当订单支付成功时，系统应启动匹配流程并将匹配状态设置为"匹配中"
2. 当进行自动匹配时，系统应根据距离、评分、经验等因素创建多个匹配记录
3. 当创建匹配记录时，系统应设置30分钟的响应超时时间
4. 当匹配记录创建后，系统应推送通知给符合条件的陪诊师
5. 当陪诊师接受订单时，系统应更新订单状态为"进行中"并拒绝其他匹配记录
6. 当陪诊师拒绝订单时，系统应检查是否还有其他待响应的匹配记录
7. 当匹配超时30分钟时，系统应将匹配记录状态设置为"已超时"
8. 当所有匹配都被拒绝或超时时，系统应检查服务时间决定重新匹配或人工处理
9. 当距离服务时间少于2小时且匹配失败时，系统应转入人工处理状态
10. 当距离服务时间超过2小时且匹配失败时，系统应扩大匹配范围重新匹配

### 需求 10

**用户故事：** 作为系统，我希望自动处理订单超时和异常，以便订单不会无限期地保持在待处理状态。

#### 验收标准

1. 当订单未支付超过30分钟时，系统应自动取消订单
2. 当陪诊师匹配记录超时30分钟无响应时，系统应将匹配记录状态设置为"已超时"
3. 当所有匹配记录都被拒绝或超时时，系统应检查服务时间决定后续处理
4. 当距离服务时间少于2小时且匹配失败时，系统应将订单状态设置为"人工处理"
5. 当服务时间过去1小时且订单仍未匹配时，系统应自动申请退款
6. 当系统错误阻止订单处理时，系统应记录错误并通知管理员
7. 当支付处理失败时，系统应重试支付验证并相应更新状态
8. 当检测到数据不一致时，系统应触发数据一致性检查和修复

### 需求 11

**用户故事：** 作为管理员，我希望通过人工干预处理匹配失败的订单，以便确保患者能够获得服务。

#### 验收标准

1. 当订单进入人工处理状态时，系统应在管理员界面显示待处理订单
2. 当管理员手动分配陪诊师时，系统应创建人工匹配记录并设置30分钟超时
3. 当创建人工匹配记录时，系统应在陪诊师工作台显示待确认订单
4. 当陪诊师接受人工分配时，系统应更新订单状态为"进行中"并完成匹配
5. 当陪诊师拒绝人工分配时，系统应允许管理员重新选择其他陪诊师
6. 当人工分配超时30分钟无响应时，系统应允许管理员重新分配
7. 当人工分配多次失败且服务时间已过时，系统应自动申请退款
8. 当人工干预完成时，系统应记录操作日志包括操作员和处理结果