# 订单匹配管理API数据格式规范

## 概述

本文档定义了订单匹配管理功能的前后端数据格式规范，确保前端页面能正确显示后端返回的数据。

## API响应格式标准

### 基础响应结构

所有API响应都应遵循以下标准格式：

```json
{
  "code": 0,                    // 状态码：0表示成功，非0表示错误
  "message": "success",         // 响应消息
  "data": {                     // 响应数据
    // 具体数据内容
  }
}
```

### 分页响应格式

对于分页数据，使用以下格式：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [],                 // 数据列表
    "total": 100,               // 总记录数
    "page": 1,                  // 当前页码
    "size": 10                  // 每页大小
  }
}
```

## 订单匹配管理API规范

### 1. 获取可手动分配的订单列表

**接口地址**: `GET /api/admin/order-matching`

**请求参数**:
```json
{
  "page": 1,                    // 页码，默认1
  "limit": 10                   // 每页数量，默认10
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 10011,                           // 订单ID（前端主要使用）
        "order_id": 10011,                     // 订单ID（兼容性）
        "order_no": "ORD202507171649579207",   // 订单编号
        "patient_name": "葛大头",               // 患者姓名
        "hospital": "北京儿童医院",             // 医院名称（原始字段）
        "hospital_name": "北京儿童医院",        // 医院名称（前端期望字段）
        "department": "急诊",                   // 科室
        "service_time": "2025-07-21T14:04:29Z", // 服务时间
        "amount": 0.01,                        // 订单金额
        "created_at": "2025-07-17T08:49:58Z",  // 创建时间
        "status": 8,                           // 订单状态码
        "status_text": "人工处理",             // 状态描述
        "attendant_name": "",                  // 陪诊师姓名（未分配时为空）
        "failure_reason": "匹配超时，进入人工处理" // 失败原因
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10
  }
}
```

### 2. 获取可分配的陪诊师列表

**接口地址**: `GET /api/admin/order-matching/available-attendants`

**请求参数**:
```json
{
  "order_id": 10011             // 订单ID
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "attendant_id": 1,        // 陪诊师ID
      "name": "张三",           // 姓名
      "phone": "13800138000",   // 电话
      "rating": 4.8,            // 评分
      "experience": 5,          // 经验年数
      "service_count": 100,     // 服务次数
      "status": 1,              // 状态
      "last_active": "2025-07-17T10:00:00Z", // 最后活跃时间
      "distance": 2.5           // 距离（公里）
    }
  ]
}
```

### 3. 手动分配订单

**接口地址**: `POST /api/admin/order-matching/manual-assign`

**请求参数**:
```json
{
  "order_id": 10011,            // 订单ID
  "attendant_id": 1,            // 陪诊师ID
  "remark": "紧急分配"          // 备注
}
```

**响应格式**:
```json
{
  "code": 0,
  "message": "分配成功"
}
```

## 前端表格字段映射

### 订单匹配管理页面表格列

| 表格列头 | 对应字段 | 数据类型 | 说明 |
|---------|---------|---------|------|
| 订单编号 | `order_no` | string | 订单编号 |
| 患者 | `patient_name` | string | 患者姓名 |
| 科室 | `department` | string | 医院科室 |
| 时间时间 | `service_time` | string | 服务时间，需格式化显示 |
| 正常状态 | `status_text` | string | 订单状态描述 |
| 陪诊师 | `attendant_name` | string | 陪诊师姓名，未分配时显示"未分配" |
| 操作 | - | - | 操作按钮（分配、查看等） |

### 时间格式化

前端需要将时间字段格式化为用户友好的格式：

```javascript
// 服务时间格式化
function formatServiceTime(serviceTime) {
  const date = new Date(serviceTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}
```

### 状态显示

根据订单状态显示不同的标签样式：

```javascript
function getStatusTag(status, statusText) {
  const statusMap = {
    2: { color: 'blue', text: '已支付' },
    3: { color: 'orange', text: '匹配中' },
    8: { color: 'red', text: '人工处理' }
  };
  
  return statusMap[status] || { color: 'gray', text: statusText };
}
```

## 错误处理规范

### 错误响应格式

```json
{
  "code": 400,                  // 错误码
  "message": "请求参数错误",     // 错误消息
  "data": null                  // 错误时data为null
}
```

### 常见错误码

| 错误码 | 说明 | 处理方式 |
|-------|------|---------|
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 重新登录 |
| 403 | 权限不足 | 提示权限不足 |
| 404 | 资源不存在 | 提示资源不存在 |
| 500 | 服务器内部错误 | 提示系统错误，稍后重试 |

## 前端实现建议

### 1. 数据获取

```javascript
async function fetchOrderMatchingList(page = 1, limit = 10) {
  try {
    const response = await fetch(`/api/admin/order-matching?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw error;
  }
}
```

### 2. 表格渲染

```javascript
function renderOrderTable(orders) {
  return orders.map(order => ({
    id: order.id,
    orderNo: order.order_no,
    patientName: order.patient_name,
    hospitalName: order.hospital_name,
    department: order.department,
    serviceTime: formatServiceTime(order.service_time),
    statusText: order.status_text,
    attendantName: order.attendant_name || '未分配',
    amount: order.amount,
    actions: renderActions(order)
  }));
}
```

### 3. 错误处理

```javascript
function handleApiError(error) {
  if (error.code) {
    switch (error.code) {
      case 401:
        // 重定向到登录页
        window.location.href = '/login';
        break;
      case 403:
        message.error('权限不足');
        break;
      case 500:
        message.error('系统错误，请稍后重试');
        break;
      default:
        message.error(error.message || '操作失败');
    }
  } else {
    message.error('网络错误，请检查网络连接');
  }
}
```

## 测试验证

### 1. API响应验证

使用提供的测试脚本验证API响应格式：

```bash
./admin/server/scripts/test_api_format.sh
```

### 2. 前端集成测试

确保前端能正确解析和显示后端返回的数据：

1. 检查表格是否正确显示所有字段
2. 验证时间格式化是否正确
3. 确认状态标签显示是否正常
4. 测试分页功能是否正常

### 3. 数据一致性检查

定期检查前后端数据格式是否保持一致：

1. 新增字段时同步更新前后端
2. 修改字段名时保持向后兼容
3. 删除字段时提前通知前端

## 版本控制

### 版本号规则

API版本号格式：`v{major}.{minor}.{patch}`

- major: 不兼容的API修改
- minor: 向后兼容的功能性新增
- patch: 向后兼容的问题修正

### 兼容性策略

1. **向后兼容**: 新增字段不影响现有功能
2. **废弃通知**: 删除字段前提前通知并提供替代方案
3. **版本并存**: 重大变更时保持多版本并存

## 更新日志

### v1.0.0 (2025-07-17)
- 初始版本
- 定义基础API响应格式
- 规范订单匹配管理接口
- 建立前后端字段映射关系