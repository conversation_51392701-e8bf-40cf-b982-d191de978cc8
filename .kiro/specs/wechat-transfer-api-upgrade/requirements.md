# 微信转账API升级需求文档

## 介绍

当前系统使用的微信企业付款接口已过时，商户号已升级到新版本，导致转账失败并返回"NO_AUTH"错误。根据微信支付官方文档，需要将现有的企业付款接口升级为新的"商家转账到零钱"API。

错误信息：
```
当前商户号接入升级版本功能，暂不支持使用升级前功能，请在产品中心-商家转账-前往功能查看接口文档
```

## 需求

### 需求 1：API接口升级

**用户故事：** 作为系统管理员，我希望微信转账功能能够正常工作，以便陪诊师能够成功提现。

#### 验收标准

1. WHEN 系统发起转账请求 THEN 系统应使用新的"商家转账到零钱"API接口
2. WHEN 调用新API接口 THEN 系统应能成功发起转账而不返回NO_AUTH错误
3. WHEN 转账成功 THEN 系统应正确处理微信返回的批次号和状态信息
4. WHEN 转账失败 THEN 系统应正确解析新API的错误码和错误信息

### 需求 2：配置参数适配

**用户故事：** 作为开发人员，我希望系统配置能够支持新API的参数要求，以便正确调用微信接口。

#### 验收标准

1. WHEN 系统初始化微信客户端 THEN 系统应使用新API要求的配置参数
2. WHEN 配置转账场景ID THEN 系统应使用正确的场景ID（如1000-普通商家转账）
3. WHEN 处理证书认证 THEN 系统应支持新API的证书验证方式
4. IF 配置参数缺失 THEN 系统应提供明确的错误提示

### 需求 3：数据结构兼容

**用户故事：** 作为系统用户，我希望转账功能升级后现有数据仍能正常使用，不影响历史记录查询。

#### 验收标准

1. WHEN 使用新API发起转账 THEN 系统应保持现有的数据库结构不变
2. WHEN 查询转账记录 THEN 系统应能正确显示新旧API的转账记录
3. WHEN 处理回调通知 THEN 系统应能正确处理新API的回调数据格式
4. WHEN 转账状态更新 THEN 系统应正确映射新API的状态到现有状态枚举

### 需求 4：错误处理优化

**用户故事：** 作为系统管理员，我希望当转账出现问题时能够获得清晰的错误信息，以便快速定位和解决问题。

#### 验收标准

1. WHEN 新API返回错误 THEN 系统应提供中文化的错误信息
2. WHEN 权限配置错误 THEN 系统应提示具体的配置步骤
3. WHEN 参数验证失败 THEN 系统应明确指出哪个参数有问题
4. WHEN 网络请求失败 THEN 系统应区分网络错误和业务错误

### 需求 5：向后兼容性

**用户故事：** 作为开发人员，我希望API升级过程中系统能够平滑过渡，不影响现有功能。

#### 验收标准

1. WHEN 升级新API THEN 现有的服务接口签名应保持不变
2. WHEN 处理转账请求 THEN 系统应能自动选择合适的API版本
3. WHEN 查询历史记录 THEN 系统应能正确处理新旧API的数据
4. IF 新API不可用 THEN 系统应提供降级机制或明确的错误提示

### 需求 6：测试验证

**用户故事：** 作为质量保证人员，我希望能够充分测试新API的功能，确保转账功能的稳定性。

#### 验收标准

1. WHEN 执行单元测试 THEN 所有转账相关的测试用例应通过
2. WHEN 执行集成测试 THEN 新API应能与微信支付平台正常交互
3. WHEN 执行压力测试 THEN 新API应能处理高并发转账请求
4. WHEN 执行异常测试 THEN 系统应能正确处理各种异常情况

### 需求 7：监控和日志

**用户故事：** 作为运维人员，我希望能够监控新API的调用情况，及时发现和处理问题。

#### 验收标准

1. WHEN 调用新API THEN 系统应记录详细的请求和响应日志
2. WHEN 转账状态变化 THEN 系统应记录状态变更的时间和原因
3. WHEN 出现错误 THEN 系统应记录错误的详细信息和上下文
4. WHEN 性能异常 THEN 系统应能及时告警并记录性能指标