# 未指定陪诊师状态UI优化需求文档

## 介绍

本文档定义了陪诊服务订单页面中未指定陪诊师状态下的UI显示优化需求，旨在提供更好的用户体验和清晰的状态反馈。

## 需求

### 需求 1: 未指定陪诊师状态的视觉设计优化

**用户故事:** 作为患者，我希望在订单未指定陪诊师时能够看到清晰友好的状态提示，以便了解当前订单进展

#### 验证标准
1. WHEN 订单状态为"待支付"(status=1) THEN 系统 SHALL 显示"等待支付后匹配"状态和相应图标
2. WHEN 订单状态为"已支付"(status=2) AND 未匹配陪诊师 THEN 系统 SHALL 显示"正在为您匹配陪诊师"状态
3. WHEN 订单状态为"匹配中"(status=3) THEN 系统 SHALL 显示动态匹配图标和"匹配中，请稍候"提示
4. WHEN 未指定陪诊师时 THEN 头像位置 SHALL 显示专门的匹配状态图标而非默认头像
5. WHEN 未指定陪诊师时 THEN 陪诊师姓名位置 SHALL 显示状态相关的友好提示文字

### 需求 2: 匹配状态的交互反馈优化

**用户故事:** 作为患者，我希望在匹配过程中能够获得清晰的进度反馈和操作指引，以便了解下一步该做什么

#### 验证标准
1. WHEN 订单处于匹配状态 THEN 系统 SHALL 显示预计匹配时间提示
2. WHEN 匹配超时 THEN 系统 SHALL 显示联系客服的友好提示
3. WHEN 用户点击匹配状态区域 THEN 系统 SHALL 显示匹配进度详情弹窗
4. WHEN 匹配中状态 THEN 系统 SHALL 显示微动画效果增强视觉反馈
5. IF 匹配时间较长 THEN 系统 SHALL 提供手动联系客服的快捷入口

### 需求 3: 状态图标和文案的一致性设计

**用户故事:** 作为患者，我希望不同匹配状态下的图标和文案保持一致的设计风格，以便更好地理解系统状态

#### 验证标准
1. WHEN 显示匹配状态图标 THEN 图标 SHALL 与整体设计风格保持一致
2. WHEN 显示状态文案 THEN 文案 SHALL 使用友好、积极的语调
3. WHEN 状态发生变化 THEN 图标和文案 SHALL 平滑过渡更新
4. WHEN 不同设备尺寸 THEN 图标和文案 SHALL 保持良好的显示效果
5. WHEN 深色模式 THEN 图标和文案 SHALL 适配深色主题显示

### 需求 4: 用户操作引导优化

**用户故事:** 作为患者，我希望在等待匹配期间能够获得清晰的操作指引，以便知道可以进行哪些操作

#### 验证标准
1. WHEN 订单待支付 THEN 系统 SHALL 突出显示"去支付"按钮并提示支付后开始匹配
2. WHEN 匹配中状态 THEN 系统 SHALL 显示"取消订单"选项和预计匹配时间
3. WHEN 匹配时间较长 THEN 系统 SHALL 提供"联系客服"快捷按钮
4. WHEN 用户尝试联系陪诊师 THEN 系统 SHALL 友好提示当前状态不支持联系
5. IF 系统检测到匹配异常 THEN 系统 SHALL 主动提供解决方案选项

### 需求 5: 信息层次和可读性优化

**用户故事:** 作为患者，我希望未指定陪诊师状态下的信息层次清晰，重要信息突出显示

#### 验证标准
1. WHEN 显示匹配状态 THEN 状态信息 SHALL 在视觉层次上最为突出
2. WHEN 显示辅助信息 THEN 辅助信息 SHALL 使用较小字号和较淡颜色
3. WHEN 显示操作按钮 THEN 主要操作 SHALL 使用品牌色突出显示
4. WHEN 显示时间信息 THEN 预约时间 SHALL 保持清晰可读
5. WHEN 显示服务信息 THEN 服务详情 SHALL 保持完整显示不受匹配状态影响