# 未指定陪诊师状态UI优化设计文档

## 概述

本设计文档详细说明了未指定陪诊师状态下的UI优化技术实现方案，通过改进视觉设计、交互反馈和用户引导来提升用户体验。

## 架构

### 前端架构
- **页面层**: `frontend/pages/order/index.wxml` - 订单列表页面模板
- **样式层**: `frontend/pages/order/index.wxss` + `frontend/styles/unassigned-attendant.wxss` - 样式定义
- **逻辑层**: `frontend/pages/order/index.js` - 页面交互逻辑
- **组件层**: `frontend/components/attendant-status/` - 陪诊师状态组件
- **工具层**: `frontend/utils/attendant-status-helper.js` - 状态处理工具函数

## 组件和接口

### 1. 陪诊师状态显示组件

#### 状态映射逻辑
```javascript
// 陪诊师状态配置
const ATTENDANT_STATUS_CONFIG = {
  // 待支付状态
  WAITING_PAYMENT: {
    status: 1,
    icon: '/assets/images/payment-waiting.png',
    title: '等待支付后匹配',
    subtitle: '支付完成后系统将自动为您匹配专业陪诊师',
    showAnimation: false,
    actionText: '立即支付开始匹配'
  },
  
  // 匹配中状态
  MATCHING: {
    status: [2, 3], // 已支付但未匹配 或 匹配中
    icon: '/assets/images/matching-animation.gif',
    title: '正在为您匹配陪诊师',
    subtitle: '系统正在根据您的需求匹配最合适的陪诊师',
    showAnimation: true,
    actionText: '预计3-5分钟完成匹配'
  },
  
  // 匹配超时状态
  MATCHING_TIMEOUT: {
    status: 3,
    condition: 'timeout',
    icon: '/assets/images/matching-timeout.png',
    title: '匹配时间较长',
    subtitle: '当前陪诊师较忙，您可以继续等待或联系客服',
    showAnimation: false,
    actionText: '联系客服获取帮助'
  }
};
```

#### 组件模板结构
```xml
<!-- 陪诊师状态组件 -->
<view class="attendant-status-container">
  <!-- 状态图标区域 -->
  <view class="status-icon-wrapper">
    <image 
      class="status-icon {{statusConfig.showAnimation ? 'animated' : ''}}" 
      src="{{statusConfig.icon}}" 
      mode="aspectFit"
    />
    <!-- 匹配进度环 -->
    <view class="progress-ring" wx:if="{{statusConfig.showAnimation}}">
      <view class="progress-circle"></view>
    </view>
  </view>
  
  <!-- 状态信息区域 -->
  <view class="status-info">
    <view class="status-title">{{statusConfig.title}}</view>
    <view class="status-subtitle">{{statusConfig.subtitle}}</view>
    
    <!-- 匹配时间提示 -->
    <view class="matching-time" wx:if="{{showMatchingTime}}">
      <text class="time-label">匹配时长：</text>
      <text class="time-value">{{matchingDuration}}</text>
    </view>
    
    <!-- 操作提示 -->
    <view class="action-hint">
      <text class="action-text">{{statusConfig.actionText}}</text>
    </view>
  </view>
  
  <!-- 快捷操作按钮 -->
  <view class="quick-actions" wx:if="{{statusConfig.showQuickActions}}">
    <button class="quick-btn contact-service" bindtap="contactService">
      联系客服
    </button>
  </view>
</view>
```

### 2. 状态样式设计

#### 基础容器样式
```css
.attendant-status-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-radius: 16rpx;
  border: 2rpx solid #e6f0ff;
  margin: 8rpx 0;
  position: relative;
  overflow: hidden;
}

.attendant-status-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1890ff, #40a9ff, #1890ff);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

#### 状态图标样式
```css
.status-icon-wrapper {
  position: relative;
  margin-right: 20rpx;
}

.status-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
  border: 3rpx solid #e6f0ff;
}

.status-icon.animated {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 匹配进度环 */
.progress-ring {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  width: 108rpx;
  height: 108rpx;
  border-radius: 50%;
  background: conic-gradient(#1890ff 0deg, #40a9ff 180deg, #1890ff 360deg);
  animation: rotate 3s linear infinite;
  padding: 3rpx;
}

.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #ffffff;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
```

#### 状态信息样式
```css
.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.status-subtitle {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 12rpx;
}

.matching-time {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.time-label {
  font-size: 22rpx;
  color: #999999;
}

.time-value {
  font-size: 22rpx;
  color: #1890ff;
  font-weight: 500;
  margin-left: 8rpx;
}

.action-hint {
  background: rgba(24, 144, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  align-self: flex-start;
}

.action-text {
  font-size: 22rpx;
  color: #1890ff;
  font-weight: 500;
}
```

### 3. 交互反馈设计

#### 点击反馈效果
```css
.attendant-status-container:active {
  transform: scale(0.98);
  opacity: 0.9;
  transition: all 0.2s;
}

.attendant-status-container {
  transition: all 0.3s ease;
}

.attendant-status-container:hover {
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.2);
  transform: translateY(-2rpx);
}
```

#### 状态变化动画
```css
.status-transition {
  animation: statusChange 0.5s ease-in-out;
}

@keyframes statusChange {
  0% { opacity: 0; transform: translateY(20rpx); }
  100% { opacity: 1; transform: translateY(0); }
}
```

### 4. 快捷操作按钮设计

#### 按钮组布局
```css
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-left: 16rpx;
}

.quick-btn {
  min-width: 120rpx;
  height: 56rpx;
  border-radius: 28rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-weight: 500;
  transition: all 0.3s;
}

.contact-service {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.contact-service:active {
  transform: scale(0.95);
  opacity: 0.9;
}
```

## 数据模型

### 陪诊师状态数据结构
```javascript
const AttendantStatusData = {
  // 基础状态信息
  status: Number,           // 订单状态
  matchingStatus: Number,   // 匹配状态
  hasAttendant: Boolean,    // 是否已分配陪诊师
  
  // 匹配相关信息
  matchingStartTime: Date,  // 匹配开始时间
  matchingDuration: String, // 匹配持续时间
  estimatedTime: String,    // 预计匹配时间
  
  // 显示配置
  statusConfig: {
    icon: String,           // 状态图标
    title: String,          // 主标题
    subtitle: String,       // 副标题
    actionText: String,     // 操作提示文字
    showAnimation: Boolean, // 是否显示动画
    showQuickActions: Boolean // 是否显示快捷操作
  },
  
  // 交互状态
  isClickable: Boolean,     // 是否可点击
  showProgress: Boolean,    // 是否显示进度
  allowCancel: Boolean      // 是否允许取消
};
```

### 状态判断逻辑
```javascript
// 获取陪诊师状态配置
function getAttendantStatusConfig(order) {
  const { status, attendant, created_at } = order;
  const matchingDuration = getMatchingDuration(created_at);
  
  // 未分配陪诊师的情况
  if (!attendant || !attendant.name || attendant.name === '未指定陪诊师') {
    // 待支付状态
    if (status === 1) {
      return {
        ...ATTENDANT_STATUS_CONFIG.WAITING_PAYMENT,
        isClickable: true,
        showProgress: false
      };
    }
    
    // 匹配中状态
    if (status === 2 || status === 3) {
      // 检查是否匹配超时
      if (matchingDuration > 10 * 60 * 1000) { // 10分钟
        return {
          ...ATTENDANT_STATUS_CONFIG.MATCHING_TIMEOUT,
          isClickable: true,
          showQuickActions: true,
          matchingDuration: formatDuration(matchingDuration)
        };
      }
      
      return {
        ...ATTENDANT_STATUS_CONFIG.MATCHING,
        isClickable: true,
        showProgress: true,
        matchingDuration: formatDuration(matchingDuration)
      };
    }
  }
  
  // 已分配陪诊师的情况
  return null;
}
```

## 错误处理

### 1. 状态异常处理
```javascript
// 处理未知状态
function handleUnknownStatus(status) {
  console.warn('Unknown attendant status:', status);
  return {
    icon: '/assets/images/unknown-status.png',
    title: '状态异常',
    subtitle: '请联系客服处理',
    actionText: '联系客服',
    showAnimation: false,
    showQuickActions: true
  };
}

// 处理网络异常
function handleNetworkError() {
  return {
    icon: '/assets/images/network-error.png',
    title: '网络异常',
    subtitle: '无法获取匹配状态，请检查网络连接',
    actionText: '点击重试',
    showAnimation: false,
    isClickable: true
  };
}
```

### 2. 匹配超时处理
```javascript
// 匹配超时检测
function checkMatchingTimeout(order) {
  const matchingTime = Date.now() - new Date(order.created_at).getTime();
  const timeoutThreshold = 15 * 60 * 1000; // 15分钟
  
  if (matchingTime > timeoutThreshold && (order.status === 2 || order.status === 3)) {
    // 触发超时处理
    return {
      isTimeout: true,
      duration: matchingTime,
      shouldShowContactService: true
    };
  }
  
  return { isTimeout: false };
}
```

## 测试策略

### 1. 视觉测试用例
- 验证不同状态下的图标和文案显示
- 测试动画效果的流畅性
- 检查不同设备尺寸下的适配效果
- 验证深色模式下的显示效果

### 2. 交互测试用例
- 测试点击状态区域的反馈效果
- 验证快捷操作按钮的功能
- 测试状态变化时的过渡动画
- 验证匹配超时时的处理逻辑

### 3. 边界条件测试
- 测试网络异常时的显示效果
- 验证数据异常时的容错处理
- 测试极长匹配时间的显示
- 验证状态快速变化时的处理

## 性能考虑

### 1. 动画性能优化
```css
/* 使用transform和opacity进行动画，避免重排重绘 */
.status-icon.animated {
  will-change: transform;
  animation: pulse 2s infinite;
}

/* 使用GPU加速 */
.progress-ring {
  transform: translateZ(0);
  will-change: transform;
}
```

### 2. 图片资源优化
- 使用WebP格式的状态图标
- 实现图片懒加载
- 缓存常用状态图标
- 压缩动画GIF文件大小

### 3. 状态更新优化
```javascript
// 防抖处理状态更新
const updateAttendantStatus = debounce((order) => {
  const newConfig = getAttendantStatusConfig(order);
  if (JSON.stringify(newConfig) !== JSON.stringify(currentConfig)) {
    setData({ statusConfig: newConfig });
  }
}, 300);
```

## 可访问性

### 1. 语义化标记
```xml
<!-- 添加ARIA标签 -->
<view 
  class="attendant-status-container" 
  role="status" 
  aria-label="陪诊师匹配状态"
  aria-live="polite"
>
  <image 
    class="status-icon" 
    src="{{statusConfig.icon}}" 
    alt="{{statusConfig.title}}"
    role="img"
  />
  <view class="status-info" role="group">
    <view class="status-title" role="heading" aria-level="3">
      {{statusConfig.title}}
    </view>
    <view class="status-subtitle" role="text">
      {{statusConfig.subtitle}}
    </view>
  </view>
</view>
```

### 2. 颜色对比度
- 确保所有文字颜色符合WCAG 2.1 AA标准
- 提供高对比度模式支持
- 使用图标+文字的组合传达信息

### 3. 交互反馈
- 提供清晰的按钮状态反馈
- 确保触摸目标大小不小于44rpx
- 支持键盘导航（如适用）