# 陪诊师工作时间页面优化设计文档

## 概述

本设计文档基于需求文档，详细描述了陪诊师工作时间页面优化的技术实现方案。优化重点包括默认时间段选择、今日日期视觉强调、多日期选择功能等用户体验改进。

## 架构

### 现有架构分析

当前的工作时间页面（`frontend/pages/attendant/schedule/index.js`）采用以下架构：

- **数据层**：使用Page的data对象管理状态
- **交互层**：通过事件处理函数响应用户操作
- **视图层**：WXML模板渲染日历和时间段选择界面
- **样式层**：WXSS提供视觉样式

### 优化后的架构设计

保持现有架构不变，在现有基础上进行功能增强：

```
┌─────────────────────────────────────────┐
│                视图层                    │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │   日历组件   │  │   时间段选择组件     ││
│  │             │  │                     ││
│  │ - 多选支持   │  │ - 默认选中逻辑       ││
│  │ - 今日标记   │  │ - 状态同步          ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                交互层                    │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │ 日期选择逻辑 │  │   时间段管理逻辑     ││
│  │             │  │                     ││
│  │ - 多选管理   │  │ - 默认选择          ││
│  │ - 状态切换   │  │ - 批量应用          ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│                数据层                    │
│  ┌─────────────┐  ┌─────────────────────┐│
│  │ 选择状态管理 │  │   时间段状态管理     ││
│  │             │  │                     ││
│  │ - 多选映射   │  │ - 默认配置          ││
│  │ - 持久化     │  │ - 同步机制          ││
│  └─────────────┘  └─────────────────────┘│
└─────────────────────────────────────────┘
```

## 组件和接口

### 1. 日历组件优化

#### 数据结构扩展

```javascript
// 扩展现有的days数组结构
days: [
  {
    day: 1,
    date: "2025-08-01",
    currentMonth: true,
    today: false,
    isToday: true,        // 新增：标记今日
    selected: false,
    hasSchedule: false,
    expired: false,
    multiSelected: false  // 新增：多选状态标记
  }
]

// 新增多选状态管理
multiSelectMode: false,           // 是否启用多选模式
selectedDatesSet: new Set(),      // 使用Set管理选中日期（性能优化）
maxSelectableDates: 31           // 最大可选日期数限制
```

#### 接口设计

```javascript
// 日期选择处理函数优化
selectDate(e) {
  const { date } = e.currentTarget.dataset;
  
  // 检查日期有效性
  if (this.isDateExpired(date)) return;
  
  // 多选逻辑处理
  this.handleMultiDateSelection(date);
  
  // 默认时间段选择
  this.applyDefaultTimeSlots();
  
  // 更新视图状态
  this.updateCalendarView();
}

// 新增：多日期选择处理
handleMultiDateSelection(date) {
  const selectedDatesSet = new Set(this.data.selectedDates);
  
  if (selectedDatesSet.has(date)) {
    selectedDatesSet.delete(date);
  } else {
    if (selectedDatesSet.size >= this.data.maxSelectableDates) {
      wx.showToast({
        title: `最多只能选择${this.data.maxSelectableDates}个日期`,
        icon: 'none'
      });
      return;
    }
    selectedDatesSet.add(date);
  }
  
  this.setData({
    selectedDates: Array.from(selectedDatesSet),
    selectedDatesMap: this.arrayToMap(Array.from(selectedDatesSet))
  });
}

// 新增：默认时间段应用
applyDefaultTimeSlots() {
  const timeSlots = this.data.timeSlots.map(slot => ({
    ...slot,
    available: true  // 默认选中所有时段
  }));
  
  this.setData({ timeSlots });
}
```

### 2. 时间段组件优化

#### 默认选择逻辑

```javascript
// 时间段默认配置
const DEFAULT_TIME_SLOTS_CONFIG = {
  morning: { type: 1, period: "上午", time: "08:00-12:00", defaultSelected: true },
  afternoon: { type: 2, period: "下午", time: "13:00-17:00", defaultSelected: true }
};

// 初始化时间段状态
initializeTimeSlots() {
  const timeSlots = [
    { 
      period: "上午", 
      time: "08:00-12:00", 
      available: true,  // 默认选中
      type: 1 
    },
    { 
      period: "下午", 
      time: "13:00-17:00", 
      available: true,  // 默认选中
      type: 2 
    }
  ];
  
  this.setData({ timeSlots });
}
```

### 3. 视觉样式组件

#### 今日日期样式

```css
/* 今日日期特殊样式 */
.day.today {
  color: #ff4d4f !important;
  font-weight: bold !important;
  position: relative;
}

.day.today::before {
  content: "";
  position: absolute;
  bottom: 4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 8rpx;
  height: 8rpx;
  background: #ff4d4f;
  border-radius: 50%;
}

/* 多选状态样式 */
.day.multi-selected {
  background: #e6f7e6;
  color: #07c160;
  transform: scale(1.05);
  box-shadow: 0 0 0 2rpx #07c160 inset;
  font-weight: bold;
}

.day.multi-selected::after {
  content: "✓";
  position: absolute;
  right: 4rpx;
  top: 4rpx;
  font-size: 20rpx;
  color: #07c160;
  font-weight: bold;
}
```

## 数据模型

### 状态管理模型

```javascript
// 页面数据模型扩展
data: {
  // 现有数据保持不变...
  
  // 新增优化相关数据
  todayDate: "",                    // 今日日期字符串
  multiSelectEnabled: true,         // 是否启用多选功能
  selectedDatesSet: new Set(),      // 选中日期集合（内存优化）
  defaultTimeSlotsEnabled: true,    // 是否启用默认时间段选择
  
  // 用户体验配置
  maxSelectableDates: 31,          // 最大可选日期数
  autoSaveEnabled: false,          // 是否启用自动保存
  
  // 动画和交互状态
  isAnimating: false,              // 是否正在执行动画
  lastSelectedDate: "",            // 最后选择的日期
  
  // 性能优化标记
  shouldUpdateCalendar: true,      // 是否需要更新日历视图
  batchUpdateMode: false           // 批量更新模式
}
```

### 日期选择状态模型

```javascript
// 日期选择状态管理
const DateSelectionState = {
  // 选中日期管理
  selectedDates: [],               // 选中日期数组
  selectedDatesMap: {},            // 选中日期映射（快速查找）
  
  // 时间段状态
  timeSlotsByDate: {},             // 每个日期的时间段配置
  defaultTimeSlots: [              // 默认时间段配置
    { type: 1, available: true },
    { type: 2, available: true }
  ],
  
  // 操作历史（用于撤销功能）
  operationHistory: [],
  currentHistoryIndex: -1
};
```

## 错误处理

### 1. 日期选择错误处理

```javascript
// 日期有效性检查
validateDateSelection(date) {
  // 检查是否为过期日期
  if (this.isDateExpired(date)) {
    throw new Error('不能选择过去的日期');
  }
  
  // 检查选择数量限制
  if (this.data.selectedDates.length >= this.data.maxSelectableDates) {
    throw new Error(`最多只能选择${this.data.maxSelectableDates}个日期`);
  }
  
  // 检查日期格式
  if (!this.isValidDateFormat(date)) {
    throw new Error('日期格式无效');
  }
  
  return true;
}

// 错误处理包装器
handleDateSelectionError(error, date) {
  console.error('日期选择错误:', error, '日期:', date);
  
  wx.showToast({
    title: error.message || '日期选择失败',
    icon: 'none',
    duration: 2000
  });
  
  // 恢复到上一个有效状态
  this.restoreLastValidState();
}
```

### 2. 时间段设置错误处理

```javascript
// 时间段验证
validateTimeSlotConfiguration(timeSlots) {
  if (!Array.isArray(timeSlots) || timeSlots.length === 0) {
    throw new Error('时间段配置无效');
  }
  
  const hasAvailableSlot = timeSlots.some(slot => slot.available);
  if (!hasAvailableSlot) {
    throw new Error('至少需要选择一个时间段');
  }
  
  return true;
}
```

### 3. 网络请求错误处理

```javascript
// 保存操作错误处理
async handleSaveError(error) {
  console.error('保存工作时间失败:', error);
  
  // 根据错误类型提供不同的处理方案
  if (error.code === 'NETWORK_ERROR') {
    wx.showModal({
      title: '网络错误',
      content: '网络连接失败，是否重试？',
      success: (res) => {
        if (res.confirm) {
          this.saveSchedules();
        }
      }
    });
  } else if (error.code === 'VALIDATION_ERROR') {
    wx.showToast({
      title: error.message || '数据验证失败',
      icon: 'none'
    });
  } else {
    wx.showToast({
      title: '保存失败，请稍后重试',
      icon: 'none'
    });
  }
}
```

## 测试策略

### 1. 单元测试

```javascript
// 日期选择逻辑测试
describe('日期选择功能', () => {
  test('应该正确处理单日期选择', () => {
    const page = createPageInstance();
    page.selectDate({ currentTarget: { dataset: { date: '2025-08-15' } } });
    
    expect(page.data.selectedDates).toContain('2025-08-15');
    expect(page.data.timeSlots.every(slot => slot.available)).toBe(true);
  });
  
  test('应该正确处理多日期选择', () => {
    const page = createPageInstance();
    page.selectDate({ currentTarget: { dataset: { date: '2025-08-15' } } });
    page.selectDate({ currentTarget: { dataset: { date: '2025-08-16' } } });
    
    expect(page.data.selectedDates).toHaveLength(2);
    expect(page.data.selectedDates).toContain('2025-08-15');
    expect(page.data.selectedDates).toContain('2025-08-16');
  });
  
  test('应该拒绝过期日期选择', () => {
    const page = createPageInstance();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    
    expect(() => {
      page.selectDate({ currentTarget: { dataset: { date: yesterdayStr } } });
    }).toThrow('不能选择过去的日期');
  });
});
```

### 2. 集成测试

```javascript
// 完整工作流程测试
describe('工作时间设置完整流程', () => {
  test('应该完成从选择日期到保存的完整流程', async () => {
    const page = createPageInstance();
    
    // 1. 选择日期
    page.selectDate({ currentTarget: { dataset: { date: '2025-08-15' } } });
    
    // 2. 验证默认时间段选择
    expect(page.data.timeSlots.every(slot => slot.available)).toBe(true);
    
    // 3. 保存设置
    const saveResult = await page.saveSchedules();
    
    // 4. 验证保存结果
    expect(saveResult.success).toBe(true);
    expect(page.data.selectedDates).toHaveLength(0); // 保存后应清空选择
  });
});
```

### 3. 用户体验测试

```javascript
// 视觉反馈测试
describe('用户体验测试', () => {
  test('今日日期应该有特殊样式', () => {
    const page = createPageInstance();
    const today = new Date().toISOString().split('T')[0];
    
    const todayElement = page.data.days.find(day => day.date === today);
    expect(todayElement.today).toBe(true);
    expect(todayElement.isToday).toBe(true);
  });
  
  test('多选状态应该正确显示', () => {
    const page = createPageInstance();
    page.selectDate({ currentTarget: { dataset: { date: '2025-08-15' } } });
    page.selectDate({ currentTarget: { dataset: { date: '2025-08-16' } } });
    
    const selectedDays = page.data.days.filter(day => day.multiSelected);
    expect(selectedDays).toHaveLength(2);
  });
});
```

## 性能优化

### 1. 渲染优化

```javascript
// 使用防抖优化频繁更新
const debounceUpdateCalendar = debounce(() => {
  this.updateCalendarView();
}, 100);

// 批量更新模式
enableBatchUpdateMode() {
  this.setData({ batchUpdateMode: true });
}

disableBatchUpdateMode() {
  this.setData({ batchUpdateMode: false });
  this.updateCalendarView();
}
```

### 2. 内存优化

```javascript
// 使用Set管理选中状态（提高查找性能）
const selectedDatesSet = new Set(this.data.selectedDates);

// 懒加载月份数据
loadMonthData(year, month) {
  if (this.monthDataCache[`${year}-${month}`]) {
    return this.monthDataCache[`${year}-${month}`];
  }
  
  const monthData = this.generateMonthData(year, month);
  this.monthDataCache[`${year}-${month}`] = monthData;
  return monthData;
}
```

### 3. 交互优化

```javascript
// 触摸反馈优化
addTouchFeedback(element) {
  element.addEventListener('touchstart', () => {
    element.classList.add('touch-active');
  });
  
  element.addEventListener('touchend', () => {
    setTimeout(() => {
      element.classList.remove('touch-active');
    }, 150);
  });
}
```

## 兼容性考虑

### 1. 微信小程序版本兼容

- 支持微信小程序基础库 2.0.0 及以上版本
- 使用条件编译处理新特性的兼容性
- 提供降级方案处理不支持的功能

### 2. 设备兼容性

- 适配不同屏幕尺寸的手机设备
- 优化触摸交互体验
- 考虑低性能设备的流畅性

### 3. 数据兼容性

- 保持与现有API接口的兼容性
- 提供数据迁移方案
- 支持渐进式功能升级