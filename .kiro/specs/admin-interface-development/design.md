# 管理后台界面开发设计文档 - Vue.js管理界面

## 概述

本设计文档描述了基于Vue.js技术栈的管理后台界面开发方案，为管理员提供直观、高效的人工分配管理功能，包括界面设计、组件架构、数据流管理等关键技术方案。

## 技术架构

### 整体架构

```mermaid
graph TB
    A[Vue.js 应用] --> B[路由管理 Vue Router]
    A --> C[状态管理 Vuex/Pinia]
    A --> D[UI组件库 Element Plus]
    
    B --> E[页面组件]
    C --> F[数据状态]
    D --> G[基础组件]
    
    E --> H[订单管理页面]
    E --> I[人工分配页面]
    E --> J[统计报表页面]
    
    F --> K[订单状态]
    F --> L[用户状态]
    F --> M[系统配置]
    
    G --> N[表格组件]
    G --> O[表单组件]
    G --> P[对话框组件]
    
    H --> Q[后端API]
    I --> Q
    J --> Q
```

### 组件层次结构

```mermaid
graph TD
    A[App.vue] --> B[Layout布局]
    B --> C[Header头部]
    B --> D[Sidebar侧边栏]
    B --> E[Main主内容区]
    
    E --> F[ManualPendingOrders待处理订单]
    E --> G[ManualProcessingOrders处理中订单]
    E --> H[RefundOrders退款订单]
    E --> I[StatisticsReport统计报表]
    
    F --> J[OrderList订单列表]
    F --> K[OrderFilter订单筛选]
    F --> L[AssignmentDialog分配对话框]
    
    G --> M[ProcessingList处理列表]
    G --> N[ProcessingActions处理操作]
    
    H --> O[RefundList退款列表]
    H --> P[RefundDetails退款详情]
    
    I --> Q[Charts图表组件]
    I --> R[DataTable数据表格]
```

## 核心页面设计

### 1. 待人工处理订单列表页面

**文件路径**: `admin/web/src/views/orders/ManualPending.vue`

```vue
<template>
  <div class="manual-pending-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>待人工处理订单</h2>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showBatchAssign" :disabled="!selectedOrders.length">
          批量分配
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <OrderFilter 
      v-model="filterParams"
      @filter="handleFilter"
      @reset="handleFilterReset"
    />

    <!-- 订单列表 -->
    <OrderList
      :data="orderList"
      :loading="loading"
      :pagination="pagination"
      @selection-change="handleSelectionChange"
      @assign="handleAssign"
      @view-detail="handleViewDetail"
      @page-change="handlePageChange"
    />

    <!-- 分配对话框 -->
    <AssignmentDialog
      v-model="assignDialogVisible"
      :order="currentOrder"
      @confirm="handleAssignConfirm"
    />

    <!-- 批量分配对话框 -->
    <BatchAssignmentDialog
      v-model="batchAssignDialogVisible"
      :orders="selectedOrders"
      @confirm="handleBatchAssignConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import OrderFilter from '@/components/orders/OrderFilter.vue'
import OrderList from '@/components/orders/OrderList.vue'
import AssignmentDialog from '@/components/orders/AssignmentDialog.vue'
import BatchAssignmentDialog from '@/components/orders/BatchAssignmentDialog.vue'
import { useOrderStore } from '@/stores/order'
import { useAttendantStore } from '@/stores/attendant'

// 状态管理
const orderStore = useOrderStore()
const attendantStore = useAttendantStore()

// 响应式数据
const loading = ref(false)
const orderList = ref([])
const selectedOrders = ref([])
const currentOrder = ref(null)
const assignDialogVisible = ref(false)
const batchAssignDialogVisible = ref(false)

// 筛选参数
const filterParams = reactive({
  emergencyLevel: '',
  serviceMode: '',
  hospitalId: '',
  department: '',
  dateRange: [],
  keyword: ''
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const hasSelectedOrders = computed(() => selectedOrders.value.length > 0)

// 方法定义
const fetchOrderList = async () => {
  loading.value = true
  try {
    const params = {
      ...filterParams,
      offset: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }
    
    const response = await orderStore.fetchManualPendingOrders(params)
    orderList.value = response.list
    pagination.total = response.total
  } catch (error) {
    ElMessage.error('获取订单列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

const handleFilter = () => {
  pagination.current = 1
  fetchOrderList()
}

const handleFilterReset = () => {
  Object.keys(filterParams).forEach(key => {
    filterParams[key] = Array.isArray(filterParams[key]) ? [] : ''
  })
  pagination.current = 1
  fetchOrderList()
}

const handleSelectionChange = (selection) => {
  selectedOrders.value = selection
}

const handleAssign = (order) => {
  currentOrder.value = order
  assignDialogVisible.value = true
}

const handleAssignConfirm = async (assignmentData) => {
  try {
    await orderStore.assignAttendant({
      orderId: currentOrder.value.id,
      ...assignmentData
    })
    
    ElMessage.success('分配成功')
    assignDialogVisible.value = false
    fetchOrderList()
  } catch (error) {
    ElMessage.error('分配失败：' + error.message)
  }
}

const showBatchAssign = () => {
  if (!hasSelectedOrders.value) {
    ElMessage.warning('请先选择要分配的订单')
    return
  }
  batchAssignDialogVisible.value = true
}

const handleBatchAssignConfirm = async (assignmentData) => {
  try {
    const promises = selectedOrders.value.map(order => 
      orderStore.assignAttendant({
        orderId: order.id,
        ...assignmentData
      })
    )
    
    await Promise.all(promises)
    ElMessage.success(`成功分配 ${selectedOrders.value.length} 个订单`)
    batchAssignDialogVisible.value = false
    selectedOrders.value = []
    fetchOrderList()
  } catch (error) {
    ElMessage.error('批量分配失败：' + error.message)
  }
}

const refreshData = () => {
  fetchOrderList()
}

const handlePageChange = (page) => {
  pagination.current = page
  fetchOrderList()
}

// 生命周期
onMounted(() => {
  fetchOrderList()
  attendantStore.fetchAvailableAttendants()
})
</script>

<style scoped>
.manual-pending-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
}
</style>
```

### 2. 订单筛选组件

**文件路径**: `admin/web/src/components/orders/OrderFilter.vue`

```vue
<template>
  <div class="order-filter">
    <el-form :model="filterForm" inline>
      <el-form-item label="紧急程度">
        <el-select v-model="filterForm.emergencyLevel" placeholder="请选择" clearable>
          <el-option label="低" :value="1" />
          <el-option label="中" :value="2" />
          <el-option label="高" :value="3" />
          <el-option label="紧急" :value="4" />
        </el-select>
      </el-form-item>

      <el-form-item label="服务模式">
        <el-select v-model="filterForm.serviceMode" placeholder="请选择" clearable>
          <el-option label="陪同就诊" :value="1" />
          <el-option label="代为就诊" :value="2" />
          <el-option label="专家陪诊" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="医院">
        <el-select 
          v-model="filterForm.hospitalId" 
          placeholder="请选择医院" 
          clearable
          filterable
        >
          <el-option 
            v-for="hospital in hospitalList"
            :key="hospital.id"
            :label="hospital.name"
            :value="hospital.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="科室">
        <el-input 
          v-model="filterForm.department" 
          placeholder="请输入科室名称" 
          clearable
        />
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker
          v-model="filterForm.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="关键词">
        <el-input 
          v-model="filterForm.keyword" 
          placeholder="订单号/用户姓名/手机号" 
          clearable
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleFilter">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, watch, onMounted } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'
import { useHospitalStore } from '@/stores/hospital'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'filter', 'reset'])

// 状态管理
const hospitalStore = useHospitalStore()

// 响应式数据
const filterForm = reactive({ ...props.modelValue })
const hospitalList = ref([])

// 监听表单变化
watch(filterForm, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 方法
const handleFilter = () => {
  emit('filter')
}

const handleReset = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = Array.isArray(filterForm[key]) ? [] : ''
  })
  emit('reset')
}

// 生命周期
onMounted(async () => {
  try {
    hospitalList.value = await hospitalStore.fetchHospitalList()
  } catch (error) {
    console.error('获取医院列表失败:', error)
  }
})
</script>

<style scoped>
.order-filter {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
```

### 3. 人工分配对话框组件

**文件路径**: `admin/web/src/components/orders/AssignmentDialog.vue`

```vue
<template>
  <el-dialog
    v-model="dialogVisible"
    title="人工分配陪诊师"
    width="800px"
    :before-close="handleClose"
  >
    <div class="assignment-dialog">
      <!-- 订单信息 -->
      <div class="order-info">
        <h4>订单信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">
            {{ order?.orderNo }}
          </el-descriptions-item>
          <el-descriptions-item label="用户姓名">
            {{ order?.user?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="服务时间">
            {{ formatDateTime(order?.appointmentTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            <el-tag :type="getEmergencyType(order?.emergencyLevel)">
              {{ getEmergencyText(order?.emergencyLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="医院">
            {{ order?.hospital?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="科室">
            {{ order?.department }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 陪诊师选择 -->
      <div class="attendant-selection">
        <h4>选择陪诊师</h4>
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索陪诊师姓名、技能"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="attendant-list">
          <el-radio-group v-model="selectedAttendantId">
            <div 
              v-for="attendant in filteredAttendants"
              :key="attendant.id"
              class="attendant-item"
            >
              <el-radio :label="attendant.id">
                <div class="attendant-card">
                  <div class="attendant-avatar">
                    <el-avatar :src="attendant.avatar" :size="50">
                      {{ attendant.name.charAt(0) }}
                    </el-avatar>
                  </div>
                  <div class="attendant-info">
                    <div class="attendant-name">{{ attendant.name }}</div>
                    <div class="attendant-skills">
                      <el-tag 
                        v-for="skill in attendant.skills" 
                        :key="skill"
                        size="small"
                        type="info"
                      >
                        {{ skill }}
                      </el-tag>
                    </div>
                    <div class="attendant-stats">
                      <span>评分: {{ attendant.rating }}/5</span>
                      <span>服务次数: {{ attendant.serviceCount }}</span>
                    </div>
                  </div>
                  <div class="attendant-status">
                    <el-tag 
                      :type="attendant.available ? 'success' : 'warning'"
                      size="small"
                    >
                      {{ attendant.available ? '可用' : '忙碌' }}
                    </el-tag>
                  </div>
                </div>
              </el-radio>
            </div>
          </el-radio-group>
        </div>
      </div>

      <!-- 分配原因 -->
      <div class="assignment-reason">
        <h4>分配原因</h4>
        <el-input
          v-model="assignmentReason"
          type="textarea"
          :rows="3"
          placeholder="请填写分配原因（必填）"
          maxlength="500"
          show-word-limit
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="confirming"
          :disabled="!canConfirm"
        >
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useAttendantStore } from '@/stores/attendant'
import { formatDateTime } from '@/utils/date'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  order: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm'])

// 状态管理
const attendantStore = useAttendantStore()

// 响应式数据
const dialogVisible = ref(props.modelValue)
const selectedAttendantId = ref(null)
const assignmentReason = ref('')
const searchKeyword = ref('')
const confirming = ref(false)

// 计算属性
const availableAttendants = computed(() => attendantStore.availableAttendants)

const filteredAttendants = computed(() => {
  if (!searchKeyword.value) return availableAttendants.value
  
  return availableAttendants.value.filter(attendant => 
    attendant.name.includes(searchKeyword.value) ||
    attendant.skills.some(skill => skill.includes(searchKeyword.value))
  )
})

const canConfirm = computed(() => 
  selectedAttendantId.value && assignmentReason.value.trim()
)

// 监听器
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 方法
const resetForm = () => {
  selectedAttendantId.value = null
  assignmentReason.value = ''
  searchKeyword.value = ''
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleConfirm = async () => {
  if (!canConfirm.value) {
    ElMessage.warning('请选择陪诊师并填写分配原因')
    return
  }

  confirming.value = true
  try {
    const assignmentData = {
      attendantId: selectedAttendantId.value,
      reason: assignmentReason.value.trim(),
      adminId: getCurrentAdminId() // 获取当前管理员ID
    }

    emit('confirm', assignmentData)
  } finally {
    confirming.value = false
  }
}

const getEmergencyType = (level) => {
  const types = { 1: '', 2: 'warning', 3: 'danger', 4: 'danger' }
  return types[level] || ''
}

const getEmergencyText = (level) => {
  const texts = { 1: '低', 2: '中', 3: '高', 4: '紧急' }
  return texts[level] || '未知'
}

const getCurrentAdminId = () => {
  // 从用户状态或token中获取当前管理员ID
  return 1 // 临时返回
}
</script>

<style scoped>
.assignment-dialog {
  max-height: 600px;
  overflow-y: auto;
}

.order-info,
.attendant-selection,
.assignment-reason {
  margin-bottom: 24px;
}

.order-info h4,
.attendant-selection h4,
.assignment-reason h4 {
  margin-bottom: 12px;
  color: #303133;
}

.search-bar {
  margin-bottom: 16px;
}

.attendant-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.attendant-item {
  border-bottom: 1px solid #f0f0f0;
}

.attendant-item:last-child {
  border-bottom: none;
}

.attendant-card {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 16px;
}

.attendant-info {
  flex: 1;
}

.attendant-name {
  font-weight: 500;
  margin-bottom: 8px;
}

.attendant-skills {
  margin-bottom: 8px;
}

.attendant-skills .el-tag {
  margin-right: 8px;
}

.attendant-stats {
  font-size: 12px;
  color: #909399;
}

.attendant-stats span {
  margin-right: 16px;
}

.attendant-status {
  text-align: right;
}
</style>
```

## 状态管理设计

### 1. 订单状态管理 (Pinia Store)

**文件路径**: `admin/web/src/stores/order.js`

```javascript
import { defineStore } from 'pinia'
import { orderApi } from '@/api/order'
import { OrderStatusManager } from '@/utils/order-status-manager'

export const useOrderStore = defineStore('order', {
  state: () => ({
    // 订单列表
    manualPendingOrders: [],
    manualProcessingOrders: [],
    refundOrders: [],
    
    // 加载状态
    loading: {
      pending: false,
      processing: false,
      refund: false
    },
    
    // 分页信息
    pagination: {
      pending: { current: 1, pageSize: 20, total: 0 },
      processing: { current: 1, pageSize: 20, total: 0 },
      refund: { current: 1, pageSize: 20, total: 0 }
    },
    
    // 筛选参数
    filters: {
      pending: {},
      processing: {},
      refund: {}
    }
  }),

  getters: {
    // 获取格式化的订单列表
    formattedPendingOrders: (state) => {
      return state.manualPendingOrders.map(order => ({
        ...order,
        statusInfo: OrderStatusManager.getDisplayStatus(order),
        canAssign: OrderStatusManager.canAssignAttendant(order),
        waitingTime: calculateWaitingTime(order.createdAt)
      }))
    },

    formattedProcessingOrders: (state) => {
      return state.manualProcessingOrders.map(order => ({
        ...order,
        statusInfo: OrderStatusManager.getDisplayStatus(order),
        canReassign: OrderStatusManager.canReassignAttendant(order),
        processingTime: calculateProcessingTime(order.manualAssignmentTime)
      }))
    },

    // 统计信息
    pendingOrdersCount: (state) => state.manualPendingOrders.length,
    processingOrdersCount: (state) => state.manualProcessingOrders.length,
    urgentOrdersCount: (state) => {
      return state.manualPendingOrders.filter(order => 
        order.emergencyLevel >= 3
      ).length
    }
  },

  actions: {
    // 获取待人工处理订单
    async fetchManualPendingOrders(params = {}) {
      this.loading.pending = true
      try {
        const response = await orderApi.getManualPendingOrders(params)
        this.manualPendingOrders = response.data.list
        this.pagination.pending = {
          current: params.offset / params.limit + 1,
          pageSize: params.limit,
          total: response.data.total
        }
        return response.data
      } catch (error) {
        console.error('获取待处理订单失败:', error)
        throw error
      } finally {
        this.loading.pending = false
      }
    },

    // 获取人工处理中订单
    async fetchManualProcessingOrders(params = {}) {
      this.loading.processing = true
      try {
        const response = await orderApi.getManualProcessingOrders(params)
        this.manualProcessingOrders = response.data.list
        this.pagination.processing = {
          current: params.offset / params.limit + 1,
          pageSize: params.limit,
          total: response.data.total
        }
        return response.data
      } catch (error) {
        console.error('获取处理中订单失败:', error)
        throw error
      } finally {
        this.loading.processing = false
      }
    },

    // 分配陪诊师
    async assignAttendant(assignmentData) {
      try {
        const response = await orderApi.assignAttendant(assignmentData)
        
        // 更新本地状态
        this.updateOrderStatus(assignmentData.orderId, {
          status: 2, // 已支付
          matchingStatus: 2, // 已匹配
          attendantId: assignmentData.attendantId,
          manualAssignmentTime: new Date().toISOString(),
          manualAssignmentAdminId: assignmentData.adminId
        })
        
        return response.data
      } catch (error) {
        console.error('分配陪诊师失败:', error)
        throw error
      }
    },

    // 标记处理失败
    async markProcessingFailed(orderId, reason, adminId) {
      try {
        const response = await orderApi.markManualProcessingFailed({
          orderId,
          reason,
          adminId
        })
        
        // 更新本地状态
        this.updateOrderStatus(orderId, {
          matchingStatus: 5, // 人工处理失败
          status: 7 // 待退款
        })
        
        return response.data
      } catch (error) {
        console.error('标记处理失败:', error)
        throw error
      }
    },

    // 获取订单分配历史
    async fetchAssignmentHistory(orderId) {
      try {
        const response = await orderApi.getManualAssignmentHistory(orderId)
        return response.data
      } catch (error) {
        console.error('获取分配历史失败:', error)
        throw error
      }
    },

    // 更新订单状态（本地状态更新）
    updateOrderStatus(orderId, updates) {
      // 更新待处理订单列表
      const pendingIndex = this.manualPendingOrders.findIndex(
        order => order.id === orderId
      )
      if (pendingIndex !== -1) {
        Object.assign(this.manualPendingOrders[pendingIndex], updates)
      }

      // 更新处理中订单列表
      const processingIndex = this.manualProcessingOrders.findIndex(
        order => order.id === orderId
      )
      if (processingIndex !== -1) {
        Object.assign(this.manualProcessingOrders[processingIndex], updates)
      }
    },

    // 从列表中移除订单
    removeOrderFromList(orderId, listType = 'pending') {
      const listMap = {
        pending: 'manualPendingOrders',
        processing: 'manualProcessingOrders',
        refund: 'refundOrders'
      }
      
      const listName = listMap[listType]
      if (listName) {
        const index = this[listName].findIndex(order => order.id === orderId)
        if (index !== -1) {
          this[listName].splice(index, 1)
        }
      }
    }
  }
})

// 辅助函数
function calculateWaitingTime(createdAt) {
  const now = new Date()
  const created = new Date(createdAt)
  const diffMs = now - created
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (diffHours > 0) {
    return `${diffHours}小时${diffMinutes}分钟`
  } else {
    return `${diffMinutes}分钟`
  }
}

function calculateProcessingTime(assignmentTime) {
  if (!assignmentTime) return '未知'
  
  const now = new Date()
  const assigned = new Date(assignmentTime)
  const diffMs = now - assigned
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟`
  } else {
    const hours = Math.floor(diffMinutes / 60)
    const minutes = diffMinutes % 60
    return `${hours}小时${minutes}分钟`
  }
}
```

### 2. 陪诊师状态管理

**文件路径**: `admin/web/src/stores/attendant.js`

```javascript
import { defineStore } from 'pinia'
import { attendantApi } from '@/api/attendant'

export const useAttendantStore = defineStore('attendant', {
  state: () => ({
    availableAttendants: [],
    allAttendants: [],
    loading: false,
    lastFetchTime: null
  }),

  getters: {
    // 按评分排序的陪诊师
    attendantsByRating: (state) => {
      return [...state.availableAttendants].sort((a, b) => b.rating - a.rating)
    },

    // 按服务次数排序的陪诊师
    attendantsByExperience: (state) => {
      return [...state.availableAttendants].sort((a, b) => b.serviceCount - a.serviceCount)
    },

    // 可用陪诊师数量
    availableCount: (state) => state.availableAttendants.length,

    // 按技能分组的陪诊师
    attendantsBySkill: (state) => {
      const grouped = {}
      state.availableAttendants.forEach(attendant => {
        attendant.skills.forEach(skill => {
          if (!grouped[skill]) {
            grouped[skill] = []
          }
          grouped[skill].push(attendant)
        })
      })
      return grouped
    }
  },

  actions: {
    // 获取可用陪诊师列表
    async fetchAvailableAttendants(forceRefresh = false) {
      // 缓存5分钟
      const cacheTime = 5 * 60 * 1000
      const now = Date.now()
      
      if (!forceRefresh && 
          this.lastFetchTime && 
          (now - this.lastFetchTime) < cacheTime) {
        return this.availableAttendants
      }

      this.loading = true
      try {
        const response = await attendantApi.getAvailableAttendants()
        this.availableAttendants = response.data.map(attendant => ({
          ...attendant,
          available: this.checkAttendantAvailability(attendant)
        }))
        this.lastFetchTime = now
        return this.availableAttendants
      } catch (error) {
        console.error('获取可用陪诊师失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 获取陪诊师详细信息
    async fetchAttendantDetail(attendantId) {
      try {
        const response = await attendantApi.getAttendantDetail(attendantId)
        return response.data
      } catch (error) {
        console.error('获取陪诊师详情失败:', error)
        throw error
      }
    },

    // 检查陪诊师可用性
    checkAttendantAvailability(attendant) {
      // 检查是否在工作时间
      const now = new Date()
      const currentHour = now.getHours()
      
      // 简单的可用性检查逻辑
      if (attendant.workingHours) {
        const { start, end } = attendant.workingHours
        if (currentHour < start || currentHour > end) {
          return false
        }
      }

      // 检查是否有正在进行的订单
      if (attendant.currentOrderCount >= attendant.maxConcurrentOrders) {
        return false
      }

      return true
    },

    // 搜索陪诊师
    searchAttendants(keyword) {
      if (!keyword) return this.availableAttendants
      
      return this.availableAttendants.filter(attendant => 
        attendant.name.includes(keyword) ||
        attendant.skills.some(skill => skill.includes(keyword)) ||
        attendant.specialties.some(specialty => specialty.includes(keyword))
      )
    },

    // 根据订单需求推荐陪诊师
    recommendAttendants(order) {
      let candidates = [...this.availableAttendants]
      
      // 按医院经验筛选
      if (order.hospitalId) {
        candidates = candidates.filter(attendant => 
          attendant.hospitalExperience.includes(order.hospitalId)
        )
      }

      // 按科室经验筛选
      if (order.department) {
        candidates = candidates.filter(attendant => 
          attendant.departmentExperience.includes(order.department)
        )
      }

      // 按紧急程度排序
      if (order.emergencyLevel >= 3) {
        candidates = candidates.filter(attendant => 
          attendant.canHandleEmergency
        )
      }

      // 按评分和经验排序
      return candidates.sort((a, b) => {
        const scoreA = a.rating * 0.6 + (a.serviceCount / 100) * 0.4
        const scoreB = b.rating * 0.6 + (b.serviceCount / 100) * 0.4
        return scoreB - scoreA
      })
    }
  }
})
```

## API接口设计

### 1. 订单API

**文件路径**: `admin/web/src/api/order.js`

```javascript
import request from '@/utils/request'

export const orderApi = {
  // 获取待人工处理订单
  getManualPendingOrders(params) {
    return request({
      url: '/api/v1/admin/orders/manual-pending',
      method: 'get',
      params
    })
  },

  // 获取人工处理中订单
  getManualProcessingOrders(params) {
    return request({
      url: '/api/v1/admin/orders/manual-processing',
      method: 'get',
      params
    })
  },

  // 获取自动退款订单
  getAutoRefundOrders(params) {
    return request({
      url: '/api/v1/admin/orders/auto-refund-pending',
      method: 'get',
      params
    })
  },

  // 分配陪诊师
  assignAttendant(data) {
    return request({
      url: `/api/v1/admin/orders/${data.orderId}/manual-assign`,
      method: 'post',
      data: {
        attendant_id: data.attendantId,
        admin_id: data.adminId,
        reason: data.reason
      }
    })
  },

  // 标记人工处理失败
  markManualProcessingFailed(data) {
    return request({
      url: `/api/v1/admin/orders/${data.orderId}/manual-processing-failed`,
      method: 'post',
      data: {
        admin_id: data.adminId,
        reason: data.reason
      }
    })
  },

  // 获取人工分配历史
  getManualAssignmentHistory(orderId) {
    return request({
      url: `/api/v1/admin/orders/${orderId}/manual-assignment-history`,
      method: 'get'
    })
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/api/v1/admin/orders/${orderId}`,
      method: 'get'
    })
  },

  // 批量操作
  batchAssignAttendant(data) {
    return request({
      url: '/api/v1/admin/orders/batch-assign',
      method: 'post',
      data
    })
  },

  // 导出订单数据
  exportOrders(params) {
    return request({
      url: '/api/v1/admin/orders/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}
```

## 路由配置

**文件路径**: `admin/web/src/router/modules/orders.js`

```javascript
export default {
  path: '/orders',
  component: () => import('@/layout/index.vue'),
  redirect: '/orders/manual-pending',
  name: 'Orders',
  meta: {
    title: '订单管理',
    icon: 'order',
    roles: ['admin', 'operator']
  },
  children: [
    {
      path: 'manual-pending',
      component: () => import('@/views/orders/ManualPending.vue'),
      name: 'ManualPendingOrders',
      meta: {
        title: '待人工处理',
        icon: 'pending',
        roles: ['admin', 'operator']
      }
    },
    {
      path: 'manual-processing',
      component: () => import('@/views/orders/ManualProcessing.vue'),
      name: 'ManualProcessingOrders',
      meta: {
        title: '人工处理中',
        icon: 'processing',
        roles: ['admin', 'operator']
      }
    },
    {
      path: 'auto-refund',
      component: () => import('@/views/orders/AutoRefund.vue'),
      name: 'AutoRefundOrders',
      meta: {
        title: '自动退款',
        icon: 'refund',
        roles: ['admin', 'operator']
      }
    },
    {
      path: 'statistics',
      component: () => import('@/views/orders/Statistics.vue'),
      name: 'OrderStatistics',
      meta: {
        title: '统计报表',
        icon: 'chart',
        roles: ['admin']
      }
    },
    {
      path: 'detail/:id',
      component: () => import('@/views/orders/OrderDetail.vue'),
      name: 'OrderDetail',
      meta: {
        title: '订单详情',
        hidden: true,
        roles: ['admin', 'operator']
      }
    }
  ]
}
```

## 实时通知设计

### 1. WebSocket连接管理

**文件路径**: `admin/web/src/utils/websocket.js`

```javascript
class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.listeners = new Map()
  }

  connect(url) {
    try {
      this.ws = new WebSocket(url)
      this.setupEventListeners()
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleReconnect()
    }
  }

  setupEventListeners() {
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立')
      this.reconnectAttempts = 0
      this.emit('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭')
      this.emit('disconnected')
      this.handleReconnect()
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error)
      this.emit('error', error)
    }
  }

  handleMessage(data) {
    const { type, payload } = data
    
    switch (type) {
      case 'new_manual_order':
        this.emit('newManualOrder', payload)
        break
      case 'order_assigned':
        this.emit('orderAssigned', payload)
        break
      case 'order_timeout':
        this.emit('orderTimeout', payload)
        break
      case 'system_alert':
        this.emit('systemAlert', payload)
        break
      default:
        console.warn('未知的消息类型:', type)
    }
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.connect(this.url)
      }, this.reconnectInterval)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
      this.emit('reconnectFailed')
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('WebSocket事件处理错误:', error)
        }
      })
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  close() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

export default new WebSocketManager()
```

### 2. 通知组件

**文件路径**: `admin/web/src/components/common/NotificationCenter.vue`

```vue
<template>
  <div class="notification-center">
    <!-- 通知图标 -->
    <el-badge :value="unreadCount" :hidden="unreadCount === 0">
      <el-button 
        circle 
        @click="togglePanel"
        :class="{ 'has-notification': unreadCount > 0 }"
      >
        <el-icon><Bell /></el-icon>
      </el-button>
    </el-badge>

    <!-- 通知面板 -->
    <el-drawer
      v-model="panelVisible"
      title="通知中心"
      direction="rtl"
      size="400px"
    >
      <div class="notification-panel">
        <!-- 通知列表 -->
        <div class="notification-list">
          <div 
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon">
              <el-icon :color="getNotificationColor(notification.type)">
                <component :is="getNotificationIcon(notification.type)" />
              </el-icon>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
            </div>
            <div class="notification-actions">
              <el-button 
                v-if="!notification.read"
                size="small" 
                type="primary" 
                text
                @click.stop="markAsRead(notification.id)"
              >
                标记已读
              </el-button>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="notification-footer">
          <el-button @click="markAllAsRead" :disabled="unreadCount === 0">
            全部标记已读
          </el-button>
          <el-button @click="clearAll">
            清空通知
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElNotification } from 'element-plus'
import { Bell, Warning, InfoFilled, SuccessFilled } from '@element-plus/icons-vue'
import websocket from '@/utils/websocket'
import { useNotificationStore } from '@/stores/notification'

// 状态管理
const notificationStore = useNotificationStore()

// 响应式数据
const panelVisible = ref(false)

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const unreadCount = computed(() => notificationStore.unreadCount)

// 方法
const togglePanel = () => {
  panelVisible.value = !panelVisible.value
}

const handleNotificationClick = (notification) => {
  if (!notification.read) {
    markAsRead(notification.id)
  }
  
  // 根据通知类型执行相应操作
  if (notification.action) {
    executeNotificationAction(notification.action)
  }
}

const markAsRead = (notificationId) => {
  notificationStore.markAsRead(notificationId)
}

const markAllAsRead = () => {
  notificationStore.markAllAsRead()
}

const clearAll = () => {
  notificationStore.clearAll()
}

const getNotificationIcon = (type) => {
  const icons = {
    info: InfoFilled,
    warning: Warning,
    success: SuccessFilled,
    error: Warning
  }
  return icons[type] || InfoFilled
}

const getNotificationColor = (type) => {
  const colors = {
    info: '#409EFF',
    warning: '#E6A23C',
    success: '#67C23A',
    error: '#F56C6C'
  }
  return colors[type] || '#409EFF'
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return time.toLocaleDateString()
}

const executeNotificationAction = (action) => {
  // 根据action类型执行相应操作
  switch (action.type) {
    case 'navigate':
      // 导航到指定页面
      break
    case 'refresh':
      // 刷新数据
      break
    default:
      console.warn('未知的通知操作类型:', action.type)
  }
}

// WebSocket事件处理
const handleNewManualOrder = (data) => {
  notificationStore.addNotification({
    type: 'warning',
    title: '新的待处理订单',
    message: `订单 ${data.orderNo} 需要人工分配`,
    action: {
      type: 'navigate',
      path: '/orders/manual-pending'
    }
  })
  
  // 显示桌面通知
  showDesktopNotification('新的待处理订单', `订单 ${data.orderNo} 需要人工分配`)
}

const handleOrderTimeout = (data) => {
  notificationStore.addNotification({
    type: 'error',
    title: '订单处理超时',
    message: `订单 ${data.orderNo} 等待时间过长`,
    action: {
      type: 'navigate',
      path: `/orders/detail/${data.orderId}`
    }
  })
  
  // 显示紧急通知
  ElNotification({
    title: '订单处理超时',
    message: `订单 ${data.orderNo} 等待时间过长，请及时处理`,
    type: 'error',
    duration: 0 // 不自动关闭
  })
}

const showDesktopNotification = (title, body) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(title, {
      body,
      icon: '/favicon.ico'
    })
  }
}

// 生命周期
onMounted(() => {
  // 请求桌面通知权限
  if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission()
  }
  
  // 连接WebSocket
  websocket.connect(process.env.VUE_APP_WS_URL)
  
  // 注册WebSocket事件监听
  websocket.on('newManualOrder', handleNewManualOrder)
  websocket.on('orderTimeout', handleOrderTimeout)
  
  // 加载历史通知
  notificationStore.fetchNotifications()
})

onUnmounted(() => {
  // 清理WebSocket事件监听
  websocket.off('newManualOrder', handleNewManualOrder)
  websocket.off('orderTimeout', handleOrderTimeout)
})
</script>

<style scoped>
.notification-center {
  position: relative;
}

.has-notification {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f5f5f5;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-left: 3px solid #409EFF;
}

.notification-icon {
  margin-right: 12px;
  font-size: 20px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.notification-message {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.notification-time {
  color: #999;
  font-size: 12px;
}

.notification-actions {
  margin-left: 12px;
}

.notification-footer {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}
</style>
```

## 总结

本设计文档提供了完整的管理后台界面开发解决方案，包括：

1. **完整的技术架构**：基于Vue.js + Element Plus的现代化前端架构
2. **丰富的页面组件**：待处理订单、人工分配、统计报表等核心页面
3. **高效的状态管理**：使用Pinia进行集中式状态管理
4. **实时通知系统**：WebSocket + 桌面通知的完整通知方案
5. **优秀的用户体验**：响应式设计、加载状态、错误处理等
6. **可维护的代码结构**：模块化组件、统一的API管理、清晰的路由配置

通过这个设计方案，可以为管理员提供高效、直观的人工分配管理界面，显著提升工作效率和用户体验。