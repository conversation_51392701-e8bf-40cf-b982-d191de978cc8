# 前端状态适配需求文档 - 移除status=8依赖，适配新状态

## 项目背景

订单状态字段优化项目的后端功能已经完成，移除了 `orders.status = 8`（人工处理）状态。现在需要更新前端代码以适应新的状态体系，确保用户界面正确显示订单状态，并且所有相关功能正常工作。

## 当前问题

根据前端适配进度报告，目前存在以下关键问题：

1. **小程序前端**：大量 `status === 8` 的硬编码引用未清理
2. **管理后台前端**：部分接口已适配，但界面逻辑仍依赖旧状态
3. **状态映射逻辑**：从单一status=8到status+matching_status组合判断的复杂性
4. **测试覆盖不足**：缺乏针对新状态逻辑的测试用例

## 需求概述

完成前端代码的状态适配工作，移除所有对 `status = 8` 的依赖，实现基于新状态体系的用户界面显示和交互逻辑，确保用户体验的连续性和功能的正确性。

## 用户故事和需求

### 需求1：清理status=8硬编码引用

**用户故事：** 作为系统维护者，我希望前端代码中不再有任何对 `status = 8` 的硬编码引用，以便系统状态逻辑保持一致性。

#### 验收标准
1. WHEN 搜索前端代码时 THEN 不应找到任何 `status === 8` 或 `status == 8` 的引用
2. WHEN 搜索前端代码时 THEN 不应找到任何 `item.status === 8` 的引用
3. WHEN 搜索前端代码时 THEN 不应找到任何 `detail.status === 8` 的引用
4. WHEN 搜索前端代码时 THEN 不应找到任何 `orderData.status === 8` 的引用
5. WHEN 搜索CSS文件时 THEN 不应找到任何 `.status-8` 的样式定义（除非重新定义用途）
6. 所有原本使用 `status === 8` 的逻辑都应替换为基于新状态体系的判断

### 需求2：实现新状态显示逻辑

**用户故事：** 作为用户，我希望在小程序和管理后台中能够准确看到订单的当前状态，包括匹配中、匹配失败、人工处理中等状态。

#### 验收标准
1. WHEN 订单状态为已支付且匹配状态为0时 THEN 应显示"等待匹配"
2. WHEN 订单状态为已支付且匹配状态为1时 THEN 应显示"匹配中"
3. WHEN 订单状态为已支付且匹配状态为2时 THEN 应显示"已匹配"
4. WHEN 订单状态为已支付且匹配状态为3时 THEN 应显示"匹配失败"或"等待人工处理"
5. WHEN 订单状态为已支付且匹配状态为4时 THEN 应显示"人工处理中"
6. WHEN 订单状态为已支付且匹配状态为5时 THEN 应显示"人工处理失败"
7. WHEN 订单状态为已支付且匹配状态为6时 THEN 应显示"服务已开始"
8. 状态显示应在所有相关页面保持一致

### 需求3：更新操作按钮显示逻辑

**用户故事：** 作为用户，我希望根据订单的实际状态看到正确的操作按钮，能够执行相应的操作。

#### 验收标准
1. WHEN 订单匹配失败时 THEN 用户应能看到"联系客服"或相关提示按钮
2. WHEN 订单人工处理中时 THEN 用户应能看到处理进度提示
3. WHEN 订单已匹配时 THEN 用户应能看到"联系陪诊师"按钮
4. WHEN 订单服务已开始时 THEN 用户应能看到服务相关操作按钮
5. IF 订单状态不支持某操作 THEN 对应按钮应被隐藏或禁用
6. 按钮显示逻辑应与后端状态保持同步

### 需求4：管理后台界面适配

**用户故事：** 作为管理员，我希望在管理后台能够正确筛选和管理需要人工处理的订单，并能执行相应的管理操作。

#### 验收标准
1. WHEN 管理员查看订单列表时 THEN 应能正确筛选匹配失败的订单
2. WHEN 管理员查看订单列表时 THEN 应能正确筛选人工处理中的订单
3. WHEN 管理员点击人工分配按钮时 THEN 应能正常执行分配操作
4. WHEN 管理员查看订单详情时 THEN 应能看到完整的状态变更历史
5. WHEN 管理员执行批量操作时 THEN 应基于新的状态逻辑进行筛选
6. 管理后台的API调用应使用新的接口端点

### 需求5：创建统一状态管理工具

**用户故事：** 作为前端开发者，我希望有统一的状态管理工具来处理复杂的状态判断逻辑，避免在多个地方重复编写相同的逻辑。

#### 验收标准
1. WHEN 需要获取订单显示状态时 THEN 应使用统一的状态管理工具
2. WHEN 需要判断按钮显示逻辑时 THEN 应使用统一的工具函数
3. WHEN 需要获取状态样式时 THEN 应使用统一的样式映射函数
4. WHEN 状态逻辑需要修改时 THEN 只需要在一个地方进行修改
5. 状态管理工具应支持向后兼容和错误处理
6. 工具函数应有完整的文档和使用示例

### 需求6：样式系统更新

**用户故事：** 作为用户，我希望不同状态的订单有清晰的视觉区分，并且样式风格保持一致。

#### 验收标准
1. WHEN 订单处于不同状态时 THEN 应有对应的颜色和样式标识
2. WHEN 状态发生变更时 THEN 样式应平滑过渡
3. WHEN 在不同设备上查看时 THEN 样式应保持响应式适配
4. IF 状态为匹配失败 THEN 应使用警告色系（橙色/红色）
5. IF 状态为人工处理中 THEN 应使用处理中色系（蓝色/紫色）
6. 样式命名应语义化，便于维护

### 需求7：测试用例完善

**用户故事：** 作为质量保证人员，我希望有完整的测试用例来验证状态适配的正确性，确保功能稳定可靠。

#### 验收标准
1. WHEN 运行测试用例时 THEN 所有状态显示逻辑应通过测试
2. WHEN 运行测试用例时 THEN 所有按钮显示逻辑应通过测试
3. WHEN 运行测试用例时 THEN 所有样式映射应通过测试
4. WHEN 运行测试用例时 THEN 边界情况和异常情况应被覆盖
5. WHEN 状态数据异常时 THEN 应有合适的降级处理
6. 测试覆盖率应达到80%以上

### 需求8：向后兼容性保证

**用户故事：** 作为系统运维人员，我希望前端适配不会影响现有功能的正常使用，并能平滑过渡到新状态体系。

#### 验收标准
1. WHEN 后端返回旧格式数据时 THEN 前端应能正确处理
2. WHEN 后端返回新格式数据时 THEN 前端应能正确显示
3. WHEN 数据格式混合时 THEN 前端应优先使用新格式
4. IF 状态数据缺失 THEN 应有合理的默认显示
5. IF 状态数据异常 THEN 应有错误提示和降级方案
6. 适配过程中不应影响用户的正常操作

## 前置条件 - 数据库迁移

在进行前端适配之前，必须先完成以下数据库迁移脚本的部署，这些是订单状态优化项目中尚未部署的SQL文件：

### 必须执行的迁移脚本（按顺序）

1. **`backend/migrations/20241221_add_manual_assignment_fields_to_orders.sql`**
   - 为orders表添加人工分配相关字段
   - 添加manual_assignment_admin_id、manual_assignment_time、manual_assignment_reason字段
   - 创建相关索引和外键约束

2. **`backend/migrations/20241221_create_order_manual_assignments_table.sql`**
   - 创建order_manual_assignments表
   - 用于记录管理员对订单的人工分配操作历史
   - 包含完整的索引和外键约束

3. **`backend/migrations/20241222_migrate_order_status_8_to_new_system.sql`**
   - 迁移历史订单状态数据
   - 处理现有的status = 8的订单，转换为新的状态体系
   - 创建迁移日志表记录变更历史

4. **`backend/migrations/20250121000001_order_matching_status_optimization.sql`**
   - 订单匹配状态优化相关数据库迁移
   - 添加匹配状态变更日志表和接单验证缓存表
   - 优化order_matching表的索引结构

### 迁移验证要求

1. WHEN 执行迁移脚本后 THEN 不应存在任何status = 8的订单记录
2. WHEN 查询orders表时 THEN 应包含manual_assignment_*相关字段
3. WHEN 查询数据库时 THEN order_manual_assignments表应存在且结构正确
4. WHEN 检查迁移日志时 THEN 应有完整的状态变更记录
5. 所有外键约束和索引应正确创建

## 技术约束

1. 必须保持小程序的性能和用户体验
2. 管理后台的Vue.js组件需要保持响应式特性
3. 状态管理工具应轻量级，不增加显著的包体积
4. 样式更新应保持设计系统的一致性
5. 需要支持微信小程序的兼容性要求
6. **数据库迁移必须在前端适配开始前完成**

## 验收标准

1. 所有 `status === 8` 的引用已清理完毕
2. 新状态显示逻辑在所有页面正确工作
3. 操作按钮显示逻辑与后端状态同步
4. 管理后台功能完全适配新状态体系
5. 统一状态管理工具已实现并投入使用
6. 样式系统更新完成，视觉效果良好
7. 测试用例覆盖率达到要求
8. 向后兼容性验证通过
9. 用户体验无明显下降
10. 代码质量和可维护性提升

## 优先级

- P0: 清理status=8硬编码引用，实现基础状态显示
- P0: 更新操作按钮显示逻辑，确保核心功能正常
- P1: 管理后台界面适配，创建统一状态管理工具
- P1: 样式系统更新，提升用户体验
- P2: 测试用例完善，向后兼容性保证

## 风险评估

- **高风险**: 状态判断逻辑错误可能导致用户操作异常
- **中风险**: 样式更新可能影响用户体验的一致性
- **低风险**: 测试用例不完善可能导致未发现的边界问题

## 成功标准

1. 前端代码完全适配新状态体系
2. 用户界面状态显示准确无误
3. 所有相关功能正常工作
4. 代码质量和可维护性提升
5. 用户体验保持或改善