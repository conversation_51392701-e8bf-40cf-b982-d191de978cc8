# 预约成功短信通知功能需求文档

## 功能概述
在陪诊师确认接单后，系统需要自动向预约用户发送预约成功的短信通知，告知用户预约详情和陪诊人员信息。

## 功能目标
1. 提升用户体验，及时通知用户预约状态
2. 减少用户焦虑，提供详细的预约信息
3. 建立用户与陪诊师的联系渠道
4. 规范化短信通知流程

## 业务场景
- **触发时机**：陪诊师确认接单后
- **接收对象**：预约的用户
- **通知内容**：预约详情、陪诊师信息、医院信息等

## 功能需求

### 核心功能
1. **短信发送触发**
   - 在陪诊师确认接单后自动触发
   - 获取订单相关信息
   - 调用短信服务发送通知

2. **短信模板配置**
   - 短信模板CODE：SMS_491470219
   - 模板参数：
     - `username`：用户姓名
     - `appointment_time`：预约时间
     - `hospital`：医院名称
     - `apartment`：科室名称
     - `attendant_name`：陪诊人员姓名
     - `attendant_mobile`：陪诊人员电话
     - `hospital_address`：医院地址

3. **短信内容**
   ```
   尊敬的${username}您好，已成功为您预约${appointment_time} 医院：${hospital} ，科室：${apartment}的陪诊服务，陪诊人员姓名：${attendant_name}，电话：${attendant_mobile}，医院地址：${hospital_address}，请您携带相关证件及病历资料按时前往医院就诊，陪诊人员会在就诊前一日与您电话联系确认碰面时间，请注意接听电话。
   ```

### 技术需求
1. **数据获取**
   - 从订单表获取预约信息
   - 从用户表获取用户信息
   - 从陪诊师表获取陪诊师信息
   - 从医院表获取医院信息

2. **短信发送**
   - 集成现有短信服务
   - 支持模板参数替换
   - 错误处理和重试机制

3. **日志记录**
   - 记录短信发送状态
   - 记录发送时间和结果
   - 便于问题排查

## 验收标准

### 功能验收
1. ✅ 陪诊师确认接单后能自动触发短信发送
2. ✅ 短信内容包含所有必要的预约信息
3. ✅ 短信模板参数正确替换
4. ✅ 用户能正常接收到短信
5. ✅ 短信发送失败时有适当的错误处理

### 技术验收
1. ✅ 代码符合项目规范
2. ✅ 包含完整的错误处理
3. ✅ 添加必要的日志记录
4. ✅ 通过单元测试
5. ✅ 通过集成测试

### 性能验收
1. ✅ 短信发送响应时间 < 5秒
2. ✅ 支持并发处理多个订单
3. ✅ 短信发送成功率 > 95%

## 约束条件
1. 使用现有的短信服务提供商
2. 遵循短信发送频率限制
3. 保护用户隐私信息
4. 符合相关法规要求

## 风险评估
1. **短信服务商故障**：准备备用方案
2. **模板参数缺失**：添加数据验证
3. **发送频率限制**：实现队列机制
4. **用户投诉**：提供退订机制

## 后续优化
1. 支持短信模板动态配置
2. 添加短信发送统计报表
3. 支持多种通知方式（微信、邮件等）
4. 个性化短信内容