# 微信提现打款功能需求文档

## 功能概述

当前陪诊系统的提现功能只实现了状态管理，缺少实际的微信企业付款API集成。需要完善微信提现打款功能，确保管理员确认打款后能够真正调用微信支付API将资金转账到陪诊师的微信零钱。

## 问题描述

**现状**：ID为13的陪诊师申请微信提现，提现单号为WD20250803000001，管理后台已审核并标记为"已打款"，但陪诊师实际未收到款项。

**根本原因**：系统只更新了数据库状态，没有调用微信企业付款API进行实际转账。

## 核心需求

### 需求1：微信企业付款API集成

**用户故事：** 作为管理员，当我确认提现打款时，系统应该自动调用微信企业付款API将资金转账到陪诊师的微信零钱，确保陪诊师能够实际收到款项。

#### 验收标准

1. WHEN 管理员点击"确认打款"按钮 THEN 系统应调用微信企业付款API进行实际转账
2. WHEN 微信API调用成功 THEN 系统应更新提现状态为"已打款"并记录微信交易单号
3. WHEN 微信API调用失败 THEN 系统应保持"审核通过"状态并记录失败原因
4. WHEN 提现金额超过微信单笔限额 THEN 系统应提示错误并阻止操作

### 需求2：微信企业付款配置管理

**用户故事：** 作为系统管理员，我需要配置微信企业付款的相关参数，包括商户号、API密钥、证书等，以确保企业付款功能正常工作。

#### 验收标准

1. WHEN 系统启动时 THEN 应验证微信企业付款配置的完整性
2. WHEN 配置缺失或错误时 THEN 系统应记录错误日志并禁用企业付款功能
3. WHEN 配置更新时 THEN 系统应重新验证配置有效性
4. WHEN 使用沙箱环境时 THEN 系统应使用测试配置进行模拟转账

### 需求3：提现状态流程优化

**用户故事：** 作为陪诊师，我希望能够准确了解我的提现申请的真实状态，包括是否已经实际到账，以便合理安排资金使用。

#### 验收标准

1. WHEN 提现申请创建时 THEN 状态应为"待审核"
2. WHEN 管理员审核通过时 THEN 状态应为"审核通过"
3. WHEN 微信API调用成功时 THEN 状态应为"转账中"
4. WHEN 微信确认到账时 THEN 状态应为"已到账"
5. WHEN 转账失败时 THEN 状态应为"转账失败"并解冻余额

### 需求4：转账结果回调处理

**用户故事：** 作为系统，我需要处理微信企业付款的异步回调通知，确保能够及时更新提现状态并处理转账异常情况。

#### 验收标准

1. WHEN 收到微信转账成功回调 THEN 系统应更新提现状态为"已到账"
2. WHEN 收到微信转账失败回调 THEN 系统应更新状态为"转账失败"并解冻余额
3. WHEN 回调签名验证失败 THEN 系统应拒绝处理并记录安全日志
4. WHEN 长时间未收到回调 THEN 系统应主动查询转账状态

### 需求5：异常处理和重试机制

**用户故事：** 作为系统管理员，当微信企业付款出现网络异常或临时故障时，系统应该具备重试机制和异常处理能力，确保转账的可靠性。

#### 验收标准

1. WHEN 微信API调用超时 THEN 系统应自动重试最多3次
2. WHEN 网络异常导致调用失败 THEN 系统应记录详细错误信息并支持手动重试
3. WHEN 微信返回业务错误 THEN 系统应根据错误码进行相应处理
4. WHEN 重试次数耗尽仍失败 THEN 系统应通知管理员进行人工处理

### 需求6：转账记录和审计

**用户故事：** 作为财务管理员，我需要查看详细的转账记录和审计日志，确保所有提现操作都有完整的记录可追溯。

#### 验收标准

1. WHEN 发起微信转账时 THEN 系统应记录转账请求的详细信息
2. WHEN 收到微信响应时 THEN 系统应记录响应结果和微信交易单号
3. WHEN 转账状态变更时 THEN 系统应记录状态变更的时间和原因
4. WHEN 查询转账记录时 THEN 系统应提供完整的操作日志和状态历史

### 需求7：Backend为主、Admin调用架构

**用户故事：** 作为系统架构师，我需要实现Backend为主导的微信转账架构，Admin系统通过安全的API调用Backend服务，确保转账逻辑的统一性和数据一致性。

#### 验收标准

1. WHEN Admin系统需要发起转账时 THEN 应通过HTTP API调用Backend系统的转账服务
2. WHEN Backend系统处理转账请求时 THEN 应验证请求来源和权限
3. WHEN 转账状态发生变化时 THEN Backend系统应主动更新数据库状态
4. WHEN Admin系统查询转账状态时 THEN 应从Backend系统获取最新的状态信息
5. WHEN 微信回调通知到达时 THEN 应直接发送到Backend系统处理

### 需求8：API密钥认证安全机制

**用户故事：** 作为安全管理员，我需要确保Admin系统调用Backend API时使用安全的认证机制，防止未授权访问和数据泄露。

#### 验收标准

1. WHEN Admin系统调用Backend API时 THEN 必须提供有效的API密钥和请求签名
2. WHEN Backend系统收到API请求时 THEN 应验证API密钥的有效性和签名的正确性
3. WHEN 请求签名验证失败时 THEN 系统应拒绝请求并记录安全日志
4. WHEN 请求时间戳超过有效期时 THEN 系统应拒绝请求防止重放攻击
5. WHEN API调用频率超过限制时 THEN 系统应实施频率限制保护

### 需求9：跨系统数据一致性

**用户故事：** 作为系统管理员，我需要确保Admin和Backend系统之间的数据保持一致，避免因系统间通信问题导致的数据不一致。

#### 验收标准

1. WHEN Backend系统更新转账状态时 THEN Admin系统应能及时获取最新状态
2. WHEN Admin系统显示转账信息时 THEN 应与Backend系统的数据保持一致
3. WHEN 网络异常导致API调用失败时 THEN 系统应有重试和恢复机制
4. WHEN 数据同步出现问题时 THEN 系统应提供数据修复和对账功能

### 需求7：Backend为主、Admin调用的系统架构

**用户故事：**

## 技术约束

1. **微信支付版本**：使用微信支付API v3
2. **转账方式**：企业付款到零钱
3. **金额限制**：单笔转账金额不超过2万元，日累计不超过10万元
4. **安全要求**：所有API调用必须使用商户证书进行签名验证
5. **环境支持**：支持生产环境和沙箱环境配置
6. **架构约束**：Backend系统为转账功能的主导系统，Admin系统通过API调用
7. **认证方式**：Admin调用Backend API必须使用API密钥认证和HMAC-SHA256签名
8. **通信协议**：系统间通信使用HTTPS协议，确保数据传输安全
9. **数据一致性**：Backend系统为数据的唯一真实来源，Admin系统不直接操作转账相关数据

## 业务规则

1. **转账条件**：只有审核通过的提现申请才能发起转账
2. **金额验证**：转账金额必须与提现申请金额一致
3. **账户验证**：必须验证陪诊师的微信openid有效性
4. **重复转账防护**：同一提现申请不能重复发起转账
5. **余额处理**：转账失败时必须解冻相应的账户余额
6. **系统调用规则**：Admin系统只能通过Backend API发起转账，不能直接调用微信API
7. **权限验证**：每次API调用都必须验证操作员权限和请求合法性
8. **状态同步规则**：转账状态变更必须在Backend系统中完成，然后同步到Admin系统
9. **回调处理规则**：微信回调通知只能由Backend系统处理，Admin系统不直接接收回调
10. **数据访问规则**：Admin系统只能通过Backend API查询转账相关数据，不能直接访问数据库