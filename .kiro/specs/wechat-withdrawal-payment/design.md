# 微信提现打款功能设计文档

## 系统架构

### 整体架构设计（Backend为主，Admin调用）

```mermaid
graph TB
    subgraph "管理后台系统 (Admin)"
        A[提现管理页面] --> B[AdminWithdrawalHandler]
        B --> C[AdminWithdrawalService]
        C --> D[BackendAPIClient]
    end
    
    subgraph "核心后端系统 (Backend)"
        D -->|API密钥认证| E[SecurityMiddleware]
        E --> F[WechatTransferHandler]
        F --> G[WechatTransferService]
        G --> H[WechatPaymentClient]
        G --> I[WithdrawalRepository]
        G --> J[AccountService]
    end
    
    subgraph "外部服务"
        H --> K[微信企业付款API]
        L[微信回调通知] --> M[TransferCallbackHandler]
    end
    
    subgraph "数据存储"
        I --> N[withdrawals表]
        I --> O[withdrawal_transfers表]
        J --> P[accounts表]
        J --> Q[account_logs表]
    end
    
    M --> G
    G --> I
```

### 架构设计原则

#### 1. Backend为主导架构
- **统一转账逻辑**: 所有微信转账相关的核心业务逻辑都在Backend系统中实现
- **数据一致性**: Backend系统直接操作数据库，确保数据一致性
- **配置集中管理**: 微信支付配置、转账参数等都在Backend系统中统一管理
- **回调统一处理**: 微信回调通知直接发送到Backend系统处理

#### 2. Admin系统调用模式
- **轻量化Admin**: Admin系统只负责界面展示和用户交互
- **API调用**: Admin通过HTTP API调用Backend的转账服务
- **状态同步**: Admin定期从Backend同步转账状态和结果
- **权限控制**: Admin系统负责用户权限验证，Backend负责业务权限验证

### 核心组件设计

#### Backend系统核心组件

##### 1. WechatTransferService (微信转账服务)
负责微信企业付款的核心业务逻辑：
- 转账请求构建和发送
- 转账状态查询和更新
- 异常处理和重试机制
- 回调通知处理

##### 2. WechatPaymentClient (微信支付客户端)
封装微信支付API调用：
- 企业付款API调用
- 转账查询API调用
- 签名验证和加密解密
- 配置管理和环境切换

##### 3. TransferCallbackHandler (转账回调处理器)
处理微信企业付款回调：
- 回调签名验证
- 转账结果处理
- 状态同步更新
- 异常情况处理

##### 4. SecurityMiddleware (安全中间件)
处理Admin系统的API调用认证：
- API密钥验证
- 请求签名校验
- 访问频率限制
- 操作日志记录

#### Admin系统核心组件

##### 1. BackendAPIClient (Backend API客户端)
封装对Backend系统的API调用：
- HTTP请求封装
- API密钥签名
- 错误处理和重试
- 响应数据解析

##### 2. AdminWithdrawalService (Admin提现服务)
Admin系统的提现业务逻辑：
- 提现申请管理
- 转账状态同步
- 界面数据组装
- 用户权限验证

## 数据模型设计

### 新增数据表

#### withdrawal_transfers (提现转账记录表)
```sql
CREATE TABLE `withdrawal_transfers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `withdrawal_id` bigint unsigned NOT NULL COMMENT '提现申请ID',
  `transfer_no` varchar(32) NOT NULL COMMENT '转账单号',
  `wechat_batch_no` varchar(64) DEFAULT NULL COMMENT '微信批次号',
  `wechat_detail_id` varchar(64) DEFAULT NULL COMMENT '微信明细单号',
  `amount` decimal(10,2) NOT NULL COMMENT '转账金额',
  `openid` varchar(64) NOT NULL COMMENT '收款人openid',
  `real_name` varchar(50) NOT NULL COMMENT '收款人姓名',
  `desc` varchar(100) NOT NULL COMMENT '转账描述',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1转账中 2转账成功 3转账失败',
  `wechat_status` varchar(20) DEFAULT NULL COMMENT '微信转账状态',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `transfer_time` timestamp NULL DEFAULT NULL COMMENT '转账发起时间',
  `success_time` timestamp NULL DEFAULT NULL COMMENT '转账成功时间',
  `retry_count` int NOT NULL DEFAULT '0' COMMENT '重试次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_transfer_no` (`transfer_no`),
  KEY `idx_withdrawal_id` (`withdrawal_id`),
  KEY `idx_wechat_batch_no` (`wechat_batch_no`),
  KEY `idx_status` (`status`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现转账记录表';
```

### 修改现有数据表

#### withdrawals 表状态扩展
```sql
-- 新增状态：5转账中 6已到账 7转账失败
ALTER TABLE `withdrawals` MODIFY COLUMN `status` int NOT NULL DEFAULT '1' 
COMMENT '状态：1待审核 2审核通过 3已打款 4已驳回 5转账中 6已到账 7转账失败';

-- 新增字段
ALTER TABLE `withdrawals` ADD COLUMN `transfer_no` varchar(32) DEFAULT NULL COMMENT '转账单号';
ALTER TABLE `withdrawals` ADD COLUMN `wechat_batch_no` varchar(64) DEFAULT NULL COMMENT '微信批次号';
ALTER TABLE `withdrawals` ADD COLUMN `transfer_fail_reason` varchar(255) DEFAULT NULL COMMENT '转账失败原因';
```

## 接口设计

### 1. 微信企业付款配置

#### WechatTransferConfig 配置结构
```go
type WechatTransferConfig struct {
    AppID           string `yaml:"app_id"`           // 小程序AppID
    MchID           string `yaml:"mch_id"`           // 商户号
    APIv3Key        string `yaml:"api_v3_key"`       // APIv3密钥
    SerialNo        string `yaml:"serial_no"`        // 证书序列号
    PrivateKeyFile  string `yaml:"private_key_file"` // 私钥文件路径
    PrivateKey      string `yaml:"private_key"`      // 私钥内容
    Environment     string `yaml:"environment"`      // 环境：sandbox/production
    NotifyURL       string `yaml:"notify_url"`       // 回调通知地址
    
    // 转账限制配置
    SingleLimit     int64  `yaml:"single_limit"`     // 单笔转账限额（分）
    DailyLimit      int64  `yaml:"daily_limit"`      // 日累计限额（分）
    MinAmount       int64  `yaml:"min_amount"`       // 最小转账金额（分）
}
```

### 2. 核心服务接口

#### IWechatTransferService 接口定义
```go
type IWechatTransferService interface {
    // 发起转账
    Transfer(ctx context.Context, req *TransferRequest) (*TransferResponse, error)
    
    // 查询转账状态
    QueryTransfer(ctx context.Context, transferNo string) (*TransferQueryResponse, error)
    
    // 处理转账回调
    HandleCallback(ctx context.Context, callbackData string) error
    
    // 重试失败转账
    RetryTransfer(ctx context.Context, transferNo string) (*TransferResponse, error)
    
    // 获取转账记录
    GetTransferRecord(ctx context.Context, withdrawalID uint) (*TransferRecord, error)
}
```

#### 转账请求结构
```go
type TransferRequest struct {
    WithdrawalID uint    `json:"withdrawal_id"`  // 提现申请ID
    Amount       int64   `json:"amount"`         // 转账金额（分）
    OpenID       string  `json:"openid"`         // 收款人openid
    RealName     string  `json:"real_name"`      // 收款人姓名
    Description  string  `json:"description"`    // 转账描述
    OperatorID   uint    `json:"operator_id"`    // 操作员ID
}

type TransferResponse struct {
    TransferNo    string    `json:"transfer_no"`     // 转账单号
    WechatBatchNo string    `json:"wechat_batch_no"` // 微信批次号
    Status        int       `json:"status"`          // 转账状态
    Message       string    `json:"message"`         // 响应消息
    TransferTime  time.Time `json:"transfer_time"`   // 转账时间
}
```

### 3. API接口设计

#### Backend系统API接口（核心转账服务）

##### 发起转账接口
```
POST /api/v1/internal/wechat/transfer
Headers: {
  "Authorization": "APIKey keyId=admin-key-001,signature=xxx,timestamp=1691234567",
  "Content-Type": "application/json"
}
Request: {
  "withdrawal_ids": [1, 2, 3],
  "operator_id": 1001,
  "remark": "批量转账"
}
Response: {
  "code": 0,
  "data": {
    "success_count": 2,
    "fail_count": 1,
    "results": [
      {
        "withdrawal_id": 1,
        "transfer_no": "TF20250803000001",
        "status": 5, // 转账中
        "message": "转账发起成功",
        "processed": true
      }
    ]
  }
}
```

##### 转账状态查询接口
```
GET /api/v1/internal/wechat/transfer/{withdrawal_id}
Headers: {
  "Authorization": "APIKey keyId=admin-key-001,signature=xxx,timestamp=1691234567"
}
Response: {
  "code": 0,
  "data": {
    "withdrawal_id": 1,
    "transfer_no": "TF20250803000001",
    "wechat_batch_no": "1030000071100999991182020050700019480001",
    "amount": 10000,
    "status": 2, // 转账成功
    "wechat_status": "SUCCESS",
    "transfer_time": "2025-08-03T10:30:00Z",
    "success_time": "2025-08-03T10:31:00Z",
    "retry_count": 0
  }
}
```

##### 转账重试接口
```
POST /api/v1/internal/wechat/transfer/{withdrawal_id}/retry
Headers: {
  "Authorization": "APIKey keyId=admin-key-001,signature=xxx,timestamp=1691234567"
}
Request: {
  "operator_id": 1001,
  "reason": "网络异常重试"
}
Response: {
  "code": 0,
  "data": {
    "transfer_no": "TF20250803000002",
    "status": 1, // 转账中
    "message": "重试转账发起成功"
  }
}
```

##### 批量查询转账状态接口
```
POST /api/v1/internal/wechat/transfer/batch-query
Headers: {
  "Authorization": "APIKey keyId=admin-key-001,signature=xxx,timestamp=1691234567"
}
Request: {
  "withdrawal_ids": [1, 2, 3, 4, 5]
}
Response: {
  "code": 0,
  "data": [
    {
      "withdrawal_id": 1,
      "transfer_no": "TF20250803000001",
      "status": 2,
      "wechat_status": "SUCCESS"
    }
  ]
}
```

#### Admin系统接口（调用Backend API）

##### 确认打款接口（保持现有接口不变）
```
POST /api/admin/withdrawals/pay
Request: {
  "withdrawal_ids": [1, 2, 3],
  "payment_method": "wechat_transfer",
  "remark": "批量转账"
}
Response: {
  "code": 0,
  "data": {
    "success_count": 2,
    "fail_count": 1,
    "results": [
      {
        "withdrawal_id": 1,
        "transfer_no": "TF20250803000001",
        "status": 5, // 转账中
        "message": "转账发起成功",
        "processed": true
      }
    ]
  }
}
```

##### 转账状态查询接口（保持现有接口不变）
```
GET /api/admin/withdrawals/{id}/transfer
Response: {
  "code": 0,
  "data": {
    "transfer_no": "TF20250803000001",
    "wechat_batch_no": "1030000071100999991182020050700019480001",
    "amount": 10000,
    "status": 2, // 转账成功
    "wechat_status": "SUCCESS",
    "transfer_time": "2025-08-03T10:30:00Z",
    "success_time": "2025-08-03T10:31:00Z",
    "retry_count": 0
  }
}
```

#### 微信回调接口

##### 转账结果回调
```
POST /api/v1/callback/wechat/transfer
Headers: {
  "Wechatpay-Signature": "...",
  "Wechatpay-Timestamp": "...",
  "Wechatpay-Nonce": "...",
  "Wechatpay-Serial": "..."
}
Request: {
  "id": "EV-2018022511223320873",
  "create_time": "2025-08-03T10:31:00+08:00",
  "resource_type": "encrypt-resource",
  "event_type": "BATCH_TRANSFER.SUCCESS",
  "resource": {
    "original_type": "batch_transfer",
    "algorithm": "AEAD_AES_256_GCM",
    "ciphertext": "...",
    "associated_data": "batch_transfer",
    "nonce": "..."
  }
}
Response: {
  "code": "SUCCESS",
  "message": "成功"
}
```

## 业务流程设计

### 1. 转账发起流程（Backend为主，Admin调用）

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant AdminHandler as AdminHandler
    participant APIClient as BackendAPIClient
    participant Security as SecurityMiddleware
    participant BackendHandler as BackendHandler
    participant Transfer as WechatTransferService
    participant WechatAPI as 微信API
    participant DB as 数据库

    Admin->>AdminHandler: 确认打款
    AdminHandler->>APIClient: 调用Backend转账API
    APIClient->>APIClient: 生成API签名
    APIClient->>Security: 发送转账请求
    Security->>Security: 验证API密钥和签名
    Security->>BackendHandler: 转发请求
    BackendHandler->>Transfer: 发起转账
    Transfer->>DB: 验证提现状态
    Transfer->>DB: 创建转账记录
    Transfer->>WechatAPI: 调用企业付款API
    WechatAPI-->>Transfer: 返回转账结果
    Transfer->>DB: 更新转账状态
    Transfer->>DB: 更新提现状态
    Transfer-->>BackendHandler: 返回转账结果
    BackendHandler-->>Security: 返回响应
    Security-->>APIClient: 返回响应
    APIClient-->>AdminHandler: 返回转账结果
    AdminHandler-->>Admin: 显示转账结果
```

### 2. 回调处理流程

```mermaid
sequenceDiagram
    participant WechatAPI as 微信API
    participant Callback as TransferCallbackHandler
    participant Transfer as WechatTransferService
    participant DB as 数据库
    participant Account as AccountService

    WechatAPI->>Callback: 发送转账结果回调
    Callback->>Callback: 验证签名
    Callback->>Transfer: 处理回调数据
    Transfer->>DB: 查询转账记录
    Transfer->>DB: 更新转账状态
    Transfer->>DB: 更新提现状态
    
    alt 转账失败
        Transfer->>Account: 解冻账户余额
        Account->>DB: 更新账户余额
    end
    
    Transfer-->>Callback: 返回处理结果
    Callback-->>WechatAPI: 返回成功响应
```

### 3. 异常处理流程

```mermaid
flowchart TD
    A[发起转账] --> B{API调用成功?}
    B -->|是| C[记录转账中状态]
    B -->|否| D{网络异常?}
    
    D -->|是| E[重试机制]
    D -->|否| F[记录业务错误]
    
    E --> G{重试次数<3?}
    G -->|是| H[延迟重试]
    G -->|否| I[标记转账失败]
    
    H --> A
    F --> I
    I --> J[解冻账户余额]
    I --> K[通知管理员]
    
    C --> L[等待回调]
    L --> M{收到回调?}
    M -->|是| N[更新最终状态]
    M -->|否| O{超时?}
    
    O -->|是| P[主动查询状态]
    O -->|否| L
    
    P --> Q{查询成功?}
    Q -->|是| N
    Q -->|否| R[人工处理]
```

## 错误处理设计

### 1. 错误分类

#### 配置错误
- `WECHAT_CONFIG_MISSING`: 微信配置缺失
- `WECHAT_CONFIG_INVALID`: 微信配置无效
- `CERTIFICATE_EXPIRED`: 证书已过期

#### 业务错误
- `WITHDRAWAL_NOT_FOUND`: 提现记录不存在
- `WITHDRAWAL_STATUS_INVALID`: 提现状态不允许转账
- `AMOUNT_EXCEED_LIMIT`: 转账金额超限
- `OPENID_INVALID`: 收款人openid无效

#### 网络错误
- `NETWORK_TIMEOUT`: 网络超时
- `API_UNAVAILABLE`: API服务不可用
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

#### 微信API错误
- `INSUFFICIENT_BALANCE`: 商户余额不足
- `RECEIVER_NOT_FOUND`: 收款人不存在
- `TRANSFER_QUOTA_EXCEEDED`: 转账额度超限

### 2. 重试策略

#### 可重试错误
- 网络超时
- 服务暂时不可用
- 系统繁忙

#### 重试配置
```go
type RetryConfig struct {
    MaxRetries    int           `yaml:"max_retries"`    // 最大重试次数
    InitialDelay  time.Duration `yaml:"initial_delay"`  // 初始延迟
    MaxDelay      time.Duration `yaml:"max_delay"`      // 最大延迟
    BackoffFactor float64       `yaml:"backoff_factor"` // 退避因子
}
```

## 安全设计

### 1. API密钥认证方案

#### 认证流程设计
```mermaid
sequenceDiagram
    participant Admin as Admin系统
    participant Backend as Backend系统
    participant DB as 数据库

    Admin->>Admin: 生成请求签名
    Admin->>Backend: 发送API请求 + 签名
    Backend->>Backend: 验证API密钥
    Backend->>Backend: 验证请求签名
    Backend->>Backend: 检查时间戳防重放
    Backend->>DB: 执行业务操作
    Backend->>Admin: 返回操作结果
```

#### API密钥配置
```go
type APIKeyConfig struct {
    KeyID     string `yaml:"key_id"`     // 密钥ID
    SecretKey string `yaml:"secret_key"` // 密钥内容
    Algorithm string `yaml:"algorithm"`  // 签名算法 (HMAC-SHA256)
    TTL       int    `yaml:"ttl"`        // 请求有效期(秒)
}
```

#### 请求签名算法
```
签名字符串 = HTTP方法 + "\n" + 请求路径 + "\n" + 时间戳 + "\n" + 请求体
签名结果 = HMAC-SHA256(签名字符串, SecretKey)
```

#### 请求头格式
```
Authorization: APIKey keyId=admin-key-001,signature=xxx,timestamp=1691234567
Content-Type: application/json
```

### 2. 数据安全
- 敏感配置信息加密存储
- 转账记录完整性校验
- 操作日志审计追踪
- API密钥定期轮换

### 3. 接口安全
- 微信回调签名验证
- 请求参数严格验证
- 防重放攻击机制（时间戳 + nonce）
- API调用频率限制

### 4. 业务安全
- 转账金额二次确认
- 异常转账自动熔断
- 风险转账人工审核
- 跨系统操作审计日志

### 5. 网络安全
- HTTPS强制加密传输
- 内网API调用（生产环境）
- IP白名单限制
- 请求体大小限制

## 监控与告警

### 1. 关键指标监控
- 转账成功率
- 转账平均耗时
- API调用失败率
- 回调处理延迟

### 2. 告警规则
- 转账失败率超过5%
- 单笔大额转账（>1000元）
- 连续转账失败
- 回调处理异常

### 3. 日志记录
- 转账请求详细日志
- 微信API响应日志
- 异常处理日志
- 性能监控日志

## 测试策略

### 1. 单元测试
- 转账服务核心逻辑测试
- 配置验证测试
- 错误处理测试

### 2. 集成测试
- 微信API集成测试
- 数据库事务测试
- 回调处理测试

### 3. 端到端测试
- 完整转账流程测试
- 异常场景测试
- 性能压力测试

### 4. 沙箱测试
- 使用微信沙箱环境
- 模拟各种转账场景
- 验证回调处理逻辑