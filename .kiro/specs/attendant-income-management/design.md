# 陪诊师收入管理功能设计文档

## 系统架构

### 整体架构
陪诊师收入管理功能采用三层架构设计：
- **前端层**: 微信小程序界面，提供用户交互
- **服务层**: Go后端API服务，处理业务逻辑
- **数据层**: MySQL数据库，存储财务数据
- **管理层**: Vue.js管理后台，提供管理功能

### 核心组件

#### 1. 账户管理组件
- **AccountService**: 账户余额管理服务
- **Account模型**: 账户数据模型
- **AccountLog模型**: 账户变动记录模型

#### 2. 结算分账组件
- **SettlementService**: 订单分账服务
- **CommissionService**: 费率管理服务
- **AttendantIncome模型**: 收入记录模型

#### 3. 提现管理组件
- **WithdrawalService**: 提现申请服务
- **Withdrawal模型**: 提现记录模型
- **WithdrawalHandler**: 提现API处理器

## 数据模型设计

### 核心数据表

#### accounts (账户表)
```sql
CREATE TABLE `accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入',
  `total_withdrawal` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总提现',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
);
```

#### attendant_income (收入记录表)
```sql
CREATE TABLE `attendant_income` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `attendant_id` bigint unsigned NOT NULL COMMENT '陪诊师ID',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `amount` decimal(10,2) NOT NULL COMMENT '收入金额',
  `platform_amount` decimal(10,2) DEFAULT '0.00' COMMENT '平台获得金额',
  `commission_rate` decimal(5,4) DEFAULT '0.1000' COMMENT '适用抽成比例',
  `net_amount` decimal(10,2) DEFAULT '0.00' COMMENT '陪诊师净收入',
  `settlement_batch_id` varchar(32) DEFAULT NULL COMMENT '结算批次号',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1待结算 2已结算 3已提现',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  PRIMARY KEY (`id`),
  KEY `idx_attendant_id` (`attendant_id`),
  KEY `idx_order_id` (`order_id`)
);
```

#### withdrawals (提现申请表)
```sql
CREATE TABLE `withdrawals` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `withdraw_no` varchar(32) NOT NULL COMMENT '提现单号',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `method` int NOT NULL COMMENT '提现方式：1微信 2支付宝 3银行卡',
  `account` varchar(50) NOT NULL COMMENT '提现账户',
  `account_name` varchar(50) NOT NULL COMMENT '账户姓名',
  `bank_name` varchar(50) DEFAULT NULL COMMENT '银行名称',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1待审核 2审核通过 3已打款 4已驳回',
  `reject_reason` varchar(255) DEFAULT NULL COMMENT '驳回原因',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `pay_time` timestamp NULL DEFAULT NULL COMMENT '打款时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
);
```

#### account_logs (账户流水表)
```sql
CREATE TABLE `account_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `type` int NOT NULL COMMENT '1:收入 2:支出 3:冻结 4:解冻',
  `amount` decimal(10,2) NOT NULL COMMENT '变动金额',
  `balance` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `frozen_balance` decimal(10,2) NOT NULL COMMENT '变动后冻结余额',
  `related_id` bigint unsigned DEFAULT NULL COMMENT '关联ID',
  `related_type` varchar(20) DEFAULT NULL COMMENT '关联类型：order/withdrawal/refund',
  `description` varchar(255) NOT NULL COMMENT '描述',
  `operator_id` bigint unsigned NOT NULL COMMENT '操作人ID',
  `operator_type` int NOT NULL COMMENT '1:用户 2:系统',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`)
);
```

## 业务流程设计

### 1. 订单分账流程
```mermaid
sequenceDiagram
    participant Order as 订单系统
    participant Settlement as 分账服务
    participant Commission as 费率服务
    participant Account as 账户服务
    participant Income as 收入记录

    Order->>Settlement: 订单完成，触发分账
    Settlement->>Commission: 获取费率规则
    Commission-->>Settlement: 返回抽成比例
    Settlement->>Settlement: 计算分账金额
    Settlement->>Account: 更新陪诊师余额
    Settlement->>Income: 创建收入记录
    Settlement->>Order: 更新订单分账状态
```

### 2. 提现申请流程
```mermaid
sequenceDiagram
    participant User as 陪诊师
    participant Frontend as 小程序前端
    participant API as 后端API
    participant DB as 数据库
    participant Admin as 管理后台

    User->>Frontend: 申请提现
    Frontend->>API: 提交提现申请
    API->>DB: 验证余额并创建提现记录
    API->>DB: 冻结提现金额
    API-->>Frontend: 返回申请成功
    Admin->>API: 审核提现申请
    API->>DB: 更新提现状态
    API->>DB: 处理账户余额变动
```

### 3. 收入统计流程
```mermaid
sequenceDiagram
    participant Frontend as 小程序前端
    participant API as 后端API
    participant Income as 收入服务
    participant DB as 数据库

    Frontend->>API: 请求收入统计
    API->>Income: 获取今日收入
    Income->>DB: 查询今日收入记录
    DB-->>Income: 返回收入数据
    API->>Income: 获取月度收入
    Income->>DB: 查询月度收入记录
    DB-->>Income: 返回收入数据
    API-->>Frontend: 返回统计结果
```

## 接口设计

### 前端API接口

#### 1. 获取账户信息
```
GET /api/v1/attendant/account/info
Response: {
  "code": 0,
  "data": {
    "balance": 150.00,
    "frozen_balance": 50.00,
    "total_income": 1000.00,
    "total_withdrawal": 800.00
  }
}
```

#### 2. 获取收入明细
```
GET /api/v1/attendant/account/logs?page=1&page_size=20&type=1
Response: {
  "code": 0,
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

#### 3. 申请提现
```
POST /api/v1/attendant/withdrawal/apply
Request: {
  "amount": 100.00,
  "method": 1,
  "account": "微信零钱",
  "account_name": "张三"
}
Response: {
  "code": 0,
  "data": {
    "withdraw_no": "WD202501290001"
  }
}
```

#### 4. 获取提现记录
```
GET /api/v1/attendant/withdrawal/list?page=1&page_size=20&status=1
Response: {
  "code": 0,
  "data": {
    "list": [...],
    "total": 50,
    "page": 1,
    "page_size": 20
  }
}
```

### 管理后台API接口

#### 1. 获取提现列表
```
GET /api/admin/withdrawals?page=1&page_size=20&status=1
Response: {
  "code": 0,
  "data": {
    "list": [...],
    "total": 200,
    "page": 1,
    "page_size": 20
  }
}
```

#### 2. 审核提现申请
```
POST /api/admin/withdrawals/{id}/approve
Request: {
  "action": "approve", // approve/reject
  "reject_reason": "余额不足"
}
Response: {
  "code": 0,
  "message": "审核成功"
}
```

## 安全设计

### 1. 数据安全
- 使用数据库事务确保财务操作的原子性
- 实施乐观锁防止并发修改账户余额
- 敏感财务数据加密存储

### 2. 接口安全
- JWT令牌认证确保用户身份
- 接口权限控制，陪诊师只能访问自己的数据
- 请求参数验证防止恶意输入

### 3. 业务安全
- 提现金额验证，防止超额提现
- 重复提现检测，防止重复申请
- 操作日志记录，便于审计追踪

## 性能优化

### 1. 数据库优化
- 为高频查询字段添加索引
- 使用分页查询减少数据传输量
- 定期清理历史数据

### 2. 缓存策略
- Redis缓存账户余额信息
- 缓存收入统计数据
- 使用缓存预热提升响应速度

### 3. 前端优化
- 列表数据懒加载
- 图片资源压缩
- 接口请求防抖处理

## 监控与告警

### 1. 业务监控
- 监控分账成功率
- 监控提现申请处理时间
- 监控账户余额异常

### 2. 系统监控
- API接口响应时间监控
- 数据库连接池监控
- 服务器资源使用监控

### 3. 告警机制
- 财务数据不一致告警
- 大额提现申请告警
- 系统异常自动告警