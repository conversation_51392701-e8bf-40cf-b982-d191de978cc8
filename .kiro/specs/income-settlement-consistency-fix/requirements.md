# 收入结算数据一致性修复需求文档

## 介绍

订单ORD202507311203108006显示已结算，但在陪诊师收入管理页面仍显示"处理中"状态。经过分析发现，这是由于订单表(`orders`)和陪诊师收入表(`attendant_income`)之间的数据不一致导致的。订单表中`settlement_status=1`(已结算)，但对应的收入记录`status=1`(待结算)，应该是`status=2`(已结算)。

## 需求

### 需求1：数据一致性检查和修复

**用户故事：** 作为系统管理员，我希望能够检查和修复订单结算与收入记录之间的数据不一致问题，确保陪诊师看到正确的收入状态。

#### 验收标准

1. WHEN 系统检测到订单表中`settlement_status=1`但对应收入记录`status=1`时 THEN 系统应该能够识别这种数据不一致
2. WHEN 执行数据修复操作时 THEN 系统应该将收入记录的状态更新为`status=2`(已结算)并设置正确的结算时间
3. WHEN 修复完成后 THEN 陪诊师在收入管理页面应该看到正确的"已结算"状态而不是"处理中"

### 需求2：预防性监控机制

**用户故事：** 作为系统管理员，我希望系统能够自动监控和预防类似的数据不一致问题，避免未来再次发生。

#### 验收标准

1. WHEN 订单结算过程执行时 THEN 系统应该确保订单状态和收入记录状态在同一事务中更新
2. WHEN 系统检测到数据不一致时 THEN 系统应该记录告警日志并通知管理员
3. WHEN 定期检查运行时 THEN 系统应该能够自动发现并报告任何数据不一致问题

### 需求3：数据完整性验证

**用户故事：** 作为系统管理员，我希望能够验证修复后的数据完整性，确保所有相关数据都是正确和一致的。

#### 验收标准

1. WHEN 执行数据验证时 THEN 系统应该检查订单状态与收入记录状态的一致性
2. WHEN 验证完成时 THEN 系统应该提供详细的验证报告，包括修复的记录数量和当前状态
3. WHEN 发现问题时 THEN 系统应该提供具体的问题描述和建议的修复方案

### 需求4：用户体验改善

**用户故事：** 作为陪诊师，我希望在收入管理页面看到准确的收入状态，能够清楚了解哪些订单已经结算，哪些还在处理中。

#### 验收标准

1. WHEN 陪诊师查看收入管理页面时 THEN 应该显示正确的收入状态（已结算/处理中/已提现）
2. WHEN 订单已经结算时 THEN 收入记录应该显示"已结算"状态和正确的结算时间
3. WHEN 陪诊师查看具体订单收入详情时 THEN 应该能够看到完整的结算信息包括结算时间和金额