# 收入结算数据一致性修复实施计划

## 实施任务

- [x] 1. 创建数据一致性检查服务核心组件
  - 实现 `IIncomeConsistencyService` 接口定义
  - 创建数据一致性检查的核心业务逻辑
  - 实现不一致记录的识别和分类算法
  - _需求: 1.1, 1.2, 3.1_

- [x] 1.1 实现数据一致性检查逻辑
  - 编写检查订单表和收入表状态一致性的查询逻辑
  - 实现多种不一致场景的识别（状态不匹配、时间不匹配、记录缺失）
  - 创建筛选条件和分页查询功能
  - 编写单元测试验证检查逻辑的正确性
  - _需求: 1.1, 3.1_

- [x] 1.2 实现数据修复功能
  - 编写数据修复的事务处理逻辑
  - 实现批量修复和单个修复功能
  - 添加修复前的数据备份机制
  - 实现修复操作的回滚功能
  - 编写修复功能的单元测试
  - _需求: 1.2, 1.3_

- [x] 1.3 实现修复结果验证
  - 编写修复后的数据验证逻辑
  - 实现修复结果的完整性检查
  - 创建验证报告生成功能
  - 添加验证失败的处理机制
  - _需求: 1.3, 3.2_

- [ ] 2. 创建数据模型和数据库迁移
  - 设计一致性检查记录表结构
  - 创建审计日志表结构
  - 编写数据库迁移脚本
  - 添加必要的索引优化查询性能
  - _需求: 2.2, 3.2_

- [ ] 2.1 创建一致性检查记录模型
  - 定义 `IncomeConsistencyCheck` 模型结构
  - 定义 `InconsistentRecord` 模型结构
  - 实现模型的验证规则和关联关系
  - 编写模型的单元测试
  - _需求: 3.2_

- [ ] 2.2 创建审计日志模型
  - 定义 `IncomeConsistencyAuditLog` 模型结构
  - 实现审计日志的自动记录机制
  - 添加敏感操作的详细记录功能
  - 编写审计日志的查询和分析功能
  - _需求: 2.2_

- [ ] 3. 实现数据访问层
  - 创建一致性检查相关的Repository接口
  - 实现数据查询和修复的Repository实现
  - 优化数据库查询性能
  - 添加数据访问层的单元测试
  - _需求: 1.1, 1.2, 3.1_

- [ ] 3.1 实现一致性检查Repository
  - 创建 `IIncomeConsistencyRepository` 接口
  - 实现复杂的数据一致性查询逻辑
  - 优化大数据量查询的性能
  - 实现分页和筛选功能
  - _需求: 1.1, 3.1_

- [ ] 3.2 实现数据修复Repository
  - 实现批量数据更新的Repository方法
  - 添加事务支持确保数据一致性
  - 实现数据备份和恢复功能
  - 编写Repository层的集成测试
  - _需求: 1.2, 1.3_

- [x] 4. 创建管理员API接口
  - 实现数据一致性检查的HTTP接口
  - 实现数据修复操作的HTTP接口
  - 添加权限控制和参数验证
  - 实现接口的错误处理和响应格式
  - _需求: 1.1, 1.2, 3.1, 3.2_

- [x] 4.1 实现一致性检查API
  - 创建 `POST /api/v1/admin/income/consistency/check` 接口
  - 实现检查参数的验证和处理
  - 添加分页和筛选参数支持
  - 实现异步检查任务的状态查询
  - 编写API接口的集成测试
  - _需求: 1.1, 3.1_

- [x] 4.2 实现数据修复API
  - 创建 `POST /api/v1/admin/income/consistency/fix` 接口
  - 实现修复操作的权限验证
  - 添加修复前的确认机制
  - 实现修复进度的实时查询
  - 编写修复API的集成测试
  - _需求: 1.2, 1.3_

- [x] 4.3 实现一致性报告API
  - 创建 `GET /api/v1/admin/income/consistency/report` 接口
  - 实现详细的一致性报告生成
  - 添加报告的导出功能
  - 实现历史报告的查询功能
  - _需求: 3.2, 3.3_

- [x] 5. 实现监控和告警机制
  - 创建数据一致性监控服务
  - 实现自动告警功能
  - 添加监控指标的收集和展示
  - 实现告警通知的发送机制
  - _需求: 2.1, 2.2_

- [x] 5.1 实现结算过程监控
  - 在结算服务中添加一致性监控钩子
  - 实现结算异常的自动检测
  - 添加结算失败的自动重试机制
  - 实现监控数据的实时收集
  - _需求: 2.1_

- [x] 5.2 实现告警通知系统
  - 创建告警规则配置功能
  - 实现多种告警通知方式（邮件、短信、钉钉）
  - 添加告警的去重和聚合功能
  - 实现告警的确认和处理流程
  - _需求: 2.2_

- [x] 6. 创建定期检查任务
  - 实现定期数据一致性检查的调度任务
  - 添加自动修复功能
  - 实现检查结果的统计和报告
  - 添加任务执行状态的监控
  - _需求: 2.1, 2.3, 3.1_

- [x] 6.1 实现定期检查调度器
  - 创建 `IncomeConsistencyScheduler` 调度器
  - 实现每日凌晨的全量数据检查
  - 添加检查任务的并发控制
  - 实现任务执行日志的记录
  - _需求: 2.3_

- [x] 6.2 实现自动修复机制
  - 添加低风险问题的自动修复功能
  - 实现修复阈值的配置管理
  - 添加自动修复的安全检查
  - 实现修复结果的自动验证
  - _需求: 2.1, 2.3_

- [x] 7. 修复当前问题订单
  - 立即修复订单ORD202507311203108006的数据不一致问题
  - 验证修复后陪诊师收入页面显示正确
  - 检查是否还有其他类似问题的订单
  - 生成修复报告和验证结果
  - _需求: 1.1, 1.2, 1.3, 4.1, 4.2, 4.3_

- [x] 7.1 执行紧急数据修复
  - 运行数据一致性检查脚本识别所有问题订单
  - 执行数据修复操作更新收入记录状态
  - 验证修复后的数据完整性
  - 测试陪诊师前端页面显示是否正确
  - _需求: 1.2, 1.3, 4.2_

- [x] 7.2 生成修复验证报告
  - 统计修复的订单数量和涉及的陪诊师
  - 验证所有修复记录的数据一致性
  - 生成详细的修复操作日志
  - 确认用户体验问题已解决
  - _需求: 3.2, 3.3, 4.3_

- [ ] 8. 编写集成测试和端到端测试
  - 创建完整的数据一致性检查和修复流程测试
  - 实现大数据量处理的性能测试
  - 添加并发操作的安全性测试
  - 编写用户界面的端到端测试
  - _需求: 1.1, 1.2, 1.3, 4.1, 4.2, 4.3_

- [ ] 8.1 实现集成测试套件
  - 创建测试数据不一致场景的测试用例
  - 实现完整修复流程的自动化测试
  - 添加错误处理和边界条件的测试
  - 实现测试数据的自动清理机制
  - _需求: 1.1, 1.2, 1.3_

- [ ] 8.2 实现性能和安全测试
  - 编写大数据量处理的性能测试
  - 实现并发修复操作的安全性测试
  - 添加权限控制的安全测试
  - 实现系统资源使用的监控测试
  - _需求: 2.1, 2.2_

- [x] 9. 部署和上线
  - 准备生产环境的部署脚本
  - 实施灰度部署和验证
  - 配置监控和告警系统
  - 编写操作文档和故障处理手册
  - _需求: 2.1, 2.2, 3.2_

- [x] 9.1 准备生产部署
  - 编写数据库迁移脚本和回滚脚本
  - 配置生产环境的系统参数
  - 实施数据备份和恢复机制
  - 准备部署前的检查清单
  - _需求: 1.2, 1.3_

- [x] 9.2 配置监控和文档
  - 配置生产环境的监控指标和告警规则
  - 编写系统操作和维护文档
  - 创建故障排查和应急处理手册
  - 培训相关运维和管理人员
  - _需求: 2.1, 2.2, 3.2_