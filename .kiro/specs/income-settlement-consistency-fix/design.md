# 收入结算数据一致性修复设计文档

## 概述

本设计文档旨在解决订单结算状态与陪诊师收入记录状态不一致的问题。通过分析现有系统架构和数据流，设计一套完整的数据一致性检查、修复和预防机制。

## 架构

### 当前系统架构分析

```mermaid
graph TD
    A[订单完成] --> B[T+2审核流程]
    B --> C[审核通过 status=10]
    C --> D[结算服务 ProcessOrderSettlement]
    D --> E[更新订单 settlement_status=1]
    D --> F[创建收入记录 status=1]
    F --> G[更新账户余额]
    
    H[问题点] --> I[事务不完整或异常]
    I --> J[订单已结算但收入记录未更新]
```

### 目标架构设计

```mermaid
graph TD
    A[数据一致性检查服务] --> B[检测不一致记录]
    B --> C[生成修复计划]
    C --> D[执行数据修复]
    D --> E[验证修复结果]
    
    F[预防性监控] --> G[结算事务监控]
    G --> H[数据一致性告警]
    H --> I[自动修复机制]
    
    J[定期检查任务] --> K[扫描全量数据]
    K --> L[生成一致性报告]
    L --> M[自动修复或告警]
```

## 组件和接口

### 1. 数据一致性检查服务 (IncomeConsistencyService)

```go
type IIncomeConsistencyService interface {
    // 检查数据一致性
    CheckConsistency(ctx context.Context, filter *ConsistencyCheckFilter) (*ConsistencyReport, error)
    
    // 修复数据不一致
    FixInconsistency(ctx context.Context, orderIDs []uint) (*FixResult, error)
    
    // 验证修复结果
    ValidateFixResult(ctx context.Context, orderIDs []uint) (*ValidationResult, error)
    
    // 获取不一致记录列表
    GetInconsistentRecords(ctx context.Context, filter *ConsistencyCheckFilter) ([]*InconsistentRecord, error)
}
```

### 2. 数据修复处理器 (IncomeConsistencyHandler)

```go
type IncomeConsistencyHandler struct {
    consistencyService IIncomeConsistencyService
    logger            *zap.Logger
}

// API接口
// POST /api/v1/admin/income/consistency/check - 检查数据一致性
// POST /api/v1/admin/income/consistency/fix - 修复数据不一致
// GET /api/v1/admin/income/consistency/report - 获取一致性报告
```

### 3. 监控和告警组件

```go
type IIncomeMonitorService interface {
    // 监控结算过程
    MonitorSettlement(ctx context.Context, orderID uint) error
    
    // 检查数据一致性并告警
    CheckAndAlert(ctx context.Context) error
    
    // 记录一致性问题
    LogInconsistency(ctx context.Context, issue *ConsistencyIssue) error
}
```

### 4. 定期检查任务

```go
type IncomeConsistencyScheduler struct {
    consistencyService IIncomeConsistencyService
    monitorService    IIncomeMonitorService
}

// 定期任务：每日凌晨2点执行全量数据一致性检查
func (s *IncomeConsistencyScheduler) DailyConsistencyCheck() error
```

## 数据模型

### 一致性检查记录

```go
type IncomeConsistencyCheck struct {
    BaseModel
    CheckType        string    `json:"check_type"`        // manual/scheduled/auto
    TotalRecords     int       `json:"total_records"`     // 检查的总记录数
    InconsistentCount int      `json:"inconsistent_count"` // 不一致记录数
    FixedCount       int       `json:"fixed_count"`       // 已修复记录数
    Status           int       `json:"status"`            // 1:进行中 2:完成 3:失败
    StartTime        time.Time `json:"start_time"`
    EndTime          *time.Time `json:"end_time"`
    ErrorMessage     string    `json:"error_message"`
    OperatorID       uint      `json:"operator_id"`
}

type InconsistentRecord struct {
    OrderID              uint      `json:"order_id"`
    OrderNo              string    `json:"order_no"`
    OrderSettlementStatus int      `json:"order_settlement_status"`
    IncomeStatus         int       `json:"income_status"`
    OrderSettlementTime  *time.Time `json:"order_settlement_time"`
    IncomeSettleTime     *time.Time `json:"income_settle_time"`
    AttendantID          uint      `json:"attendant_id"`
    Amount               float64   `json:"amount"`
    IssueType            string    `json:"issue_type"` // status_mismatch/time_mismatch/missing_record
}
```

## 错误处理

### 错误类型定义

```go
var (
    ErrInconsistentData     = errors.New("数据不一致")
    ErrFixFailed           = errors.New("修复失败")
    ErrValidationFailed    = errors.New("验证失败")
    ErrRecordNotFound      = errors.New("记录不存在")
    ErrTransactionFailed   = errors.New("事务执行失败")
)
```

### 错误处理策略

1. **数据检查错误**：记录日志，返回详细错误信息
2. **修复过程错误**：回滚事务，记录失败原因
3. **验证错误**：标记为需要人工介入
4. **系统错误**：发送告警通知，记录系统日志

## 测试策略

### 单元测试

1. **数据一致性检查逻辑测试**
   - 测试各种不一致场景的识别
   - 测试筛选条件的正确性
   - 测试边界条件处理

2. **数据修复逻辑测试**
   - 测试修复操作的正确性
   - 测试事务回滚机制
   - 测试并发修复的安全性

### 集成测试

1. **端到端修复流程测试**
   - 创建测试数据不一致场景
   - 执行完整修复流程
   - 验证修复结果的正确性

2. **API接口测试**
   - 测试管理员接口的权限控制
   - 测试接口参数验证
   - 测试错误响应格式

### 性能测试

1. **大数据量处理测试**
   - 测试处理10万+记录的性能
   - 测试内存使用情况
   - 测试数据库连接池管理

2. **并发处理测试**
   - 测试多个修复任务并发执行
   - 测试数据库锁竞争情况
   - 测试系统资源占用

## 部署和监控

### 部署策略

1. **灰度部署**
   - 先在测试环境验证
   - 小批量数据修复测试
   - 逐步扩大修复范围

2. **回滚机制**
   - 保留修复前的数据快照
   - 提供一键回滚功能
   - 记录所有修复操作日志

### 监控指标

1. **业务指标**
   - 数据不一致记录数量
   - 修复成功率
   - 修复耗时统计

2. **技术指标**
   - API响应时间
   - 数据库查询性能
   - 系统资源使用率

3. **告警规则**
   - 发现大量不一致数据时告警
   - 修复失败率超过阈值时告警
   - 系统异常时立即告警

## 安全考虑

### 权限控制

1. **操作权限**
   - 只有超级管理员可以执行数据修复
   - 记录所有操作的审计日志
   - 敏感操作需要二次确认

2. **数据保护**
   - 修复前自动备份相关数据
   - 使用事务确保数据一致性
   - 提供数据恢复机制

### 审计日志

```go
type IncomeConsistencyAuditLog struct {
    BaseModel
    OperationType    string    `json:"operation_type"`    // check/fix/validate
    OperatorID       uint      `json:"operator_id"`
    AffectedRecords  int       `json:"affected_records"`
    OperationDetails string    `json:"operation_details"` // JSON格式的详细信息
    IPAddress        string    `json:"ip_address"`
    UserAgent        string    `json:"user_agent"`
    Result           string    `json:"result"`            // success/failed/partial
    ErrorMessage     string    `json:"error_message"`
}
```