# 订单匹配状态优化实现计划

## 实现任务列表

- [x] 1. 创建数据库迁移脚本和新增表结构
  - 为 order_matching 表添加状态重置相关字段
  - 创建 matching_status_logs 状态变更日志表
  - 创建 order_acceptance_cache 接单验证缓存表
  - 添加必要的数据库索引优化查询性能
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7_

- [x] 2. 实现匹配状态管理核心服务
- [x] 2.1 创建 MatchingStatusManager 接口定义
  - 定义状态重置、验证、更新等核心方法
  - 定义 MatchingStatus 枚举和相关数据结构
  - 创建状态流转规则和验证逻辑
  - _需求: 2.1, 2.2_

- [x] 2.2 实现 MatchingStatusManager 服务
  - 实现 ResetToOffered 方法，将超时状态重置为已推送状态
  - 实现 ValidateAcceptance 方法，验证陪诊师是否可以接单
  - 实现 UpdateStatus 方法，处理状态更新和日志记录
  - 实现 GetCurrentStatus 方法，获取当前匹配状态信息
  - _需求: 1.1, 1.2, 1.3, 1.4_

- [x] 3. 增强人工分配服务功能
- [x] 3.1 修改现有 ManualAssignOrder 方法
  - 集成状态重置逻辑，在分配时自动重置匹配状态
  - 添加响应截止时间设置（默认30分钟）
  - 增强错误处理和验证逻辑
  - _需求: 3.1, 3.2, 3.3_

- [x] 3.2 实现批量分配功能
  - 创建 BatchAssignOrders 方法支持批量操作
  - 实现事务处理确保批量操作的原子性
  - 添加批量操作结果统计和错误报告
  - _需求: 3.1, 3.2, 3.3_

- [x] 3.3 添加分配取消功能
  - 实现 CancelAssignment 方法取消已分配的订单
  - 处理取消后的状态回滚和通知
  - 记录取消操作的详细日志
  - _需求: 3.1, 3.2, 3.3_

- [ ] 4. 实现接单验证和错误处理机制
- [ ] 4.1 创建 OrderAcceptanceValidator 服务
  - 实现 ValidateAcceptance 方法验证接单条件
  - 实现 GetRejectionReason 方法提供详细的拒绝原因
  - 添加验证结果缓存机制提升性能
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 4.2 优化错误信息和用户提示
  - 定义详细的错误码和错误信息
  - 实现友好的错误提示和解决建议
  - 创建多语言错误信息支持
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 5. 实现状态同步和一致性保障
- [ ] 5.1 创建 StatusSynchronizer 服务
  - 实现 SyncStatuses 方法同步订单状态和匹配状态
  - 实现 LogStatusChange 方法记录所有状态变更
  - 实现 CheckConsistency 方法检查状态一致性
  - _需求: 5.1, 5.2, 5.3_

- [ ] 5.2 添加状态变更日志记录
  - 在所有状态更新操作中添加日志记录
  - 实现日志查询和分析功能
  - 添加日志清理和归档机制
  - _需求: 5.1, 5.2, 5.3_

- [ ] 6. 修改现有的订单接单处理逻辑
- [x] 6.1 更新后端接单API
  - 修改接单验证逻辑，使用新的 OrderAcceptanceValidator
  - 优化接单成功后的状态更新流程
  - 添加详细的接单失败错误处理
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 6.2 优化前端接单界面
  - 更新错误提示显示逻辑，显示具体的失败原因
  - 添加接单状态的实时刷新功能
  - 优化用户交互体验和加载状态显示
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 7. 更新管理后台人工分配功能
- [x] 7.1 修改分配订单接口
  - 集成新的状态重置逻辑
  - 添加分配结果的详细反馈
  - 实现分配操作的撤销功能
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 7.2 优化管理界面显示
  - 更新订单状态显示，反映最新的状态信息
  - 添加状态变更历史查看功能
  - 优化分配操作的用户体验
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 8. 实现通知和提醒功能
- [ ] 8.1 添加分配成功通知
  - 向陪诊师发送订单分配通知
  - 实现多渠道通知（推送、短信、邮件）
  - 添加通知发送状态跟踪
  - _需求: 3.4_

- [ ] 8.2 实现接单超时提醒
  - 在接单截止时间前发送提醒通知
  - 实现超时后的自动处理逻辑
  - 添加超时统计和分析功能
  - _需求: 3.3_

- [ ] 9. 创建单元测试和集成测试
- [ ] 9.1 编写核心服务单元测试
  - 测试 MatchingStatusManager 的所有方法
  - 测试 OrderAcceptanceValidator 的验证逻辑
  - 测试 StatusSynchronizer 的同步功能
  - 覆盖正常流程和异常情况的测试用例
  - _需求: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7_

- [ ] 9.2 编写API接口集成测试
  - 测试完整的人工分配到接单流程
  - 测试并发操作和状态冲突处理
  - 测试错误处理和异常恢复机制
  - _需求: 1.1, 1.2, 1.3, 1.4, 3.1, 3.2, 3.3, 4.1, 4.2, 4.3, 4.4_

- [ ] 9.3 创建端到端测试用例
  - 模拟完整的用户操作流程
  - 测试前后端交互的正确性
  - 验证用户界面的状态显示和错误提示
  - _需求: 4.1, 4.2, 4.3, 4.4, 6.1, 6.2, 6.3, 6.4_

- [ ] 10. 实现监控和日志功能
- [ ] 10.1 添加业务指标监控
  - 实现人工分配成功率统计
  - 实现陪诊师接单成功率监控
  - 添加状态流转异常检测和告警
  - _需求: 5.1, 5.2, 5.3_

- [ ] 10.2 优化系统日志记录
  - 添加详细的操作日志记录
  - 实现日志级别控制和过滤
  - 添加日志分析和查询功能
  - _需求: 5.1, 5.2, 5.3_

- [ ] 11. 创建数据迁移和修复工具
- [ ] 11.1 实现历史数据状态修复
  - 分析现有数据中的状态不一致问题
  - 创建数据修复脚本处理历史订单
  - 实现数据验证和完整性检查
  - _需求: 5.1, 5.2, 5.3_

- [ ] 11.2 创建状态一致性检查工具
  - 实现定期状态一致性检查任务
  - 创建自动修复机制处理发现的问题
  - 添加检查结果报告和告警功能
  - _需求: 5.1, 5.2, 5.3_

- [ ] 12. 性能优化和缓存实现
- [ ] 12.1 优化数据库查询性能
  - 添加必要的数据库索引
  - 优化复杂查询的执行计划
  - 实现查询结果缓存机制
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7_

- [ ] 12.2 实现接单验证缓存
  - 缓存频繁查询的验证结果
  - 实现缓存失效和更新机制
  - 添加缓存命中率监控
  - _需求: 4.1, 4.2, 4.3, 4.4_

- [ ] 13. 文档编写和部署准备
- [ ] 13.1 编写API文档和使用说明
  - 更新相关API接口文档
  - 编写状态流转说明和最佳实践
  - 创建故障排查和运维指南
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 13.2 准备部署脚本和配置
  - 创建数据库迁移脚本
  - 准备配置文件和环境变量
  - 编写部署和回滚流程文档
  - _需求: 1.1, 1.2, 1.3, 1.4_