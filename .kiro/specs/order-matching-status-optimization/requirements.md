# 订单匹配状态优化需求文档

## 介绍

当前订单匹配流程存在状态管理问题：订单匹配超时后进入人工处理阶段，管理员手动分配订单给陪诊师，但订单匹配状态仍然是"超时状态(4)"，导致陪诊师无法成功接单。需要优化订单匹配状态的流转逻辑，确保人工分配后的订单能够被陪诊师正常接受。

## 需求

### 需求 1：优化人工分配后的状态流转

**用户故事：** 作为管理员，我希望在手动分配订单给陪诊师后，订单匹配状态能够自动更新为可接受状态，以便陪诊师能够正常接单。

#### 验收标准

1. WHEN 管理员手动分配订单给陪诊师 THEN 系统应将订单匹配状态从"超时(4)"更新为"已推送(1)"
2. WHEN 订单匹配状态更新为"已推送(1)" THEN 陪诊师应能够在工作台看到待接单的订单
3. WHEN 陪诊师点击"确认接单" THEN 系统应成功处理接单请求，不再提示"匹配状态不允许接受"错误
4. WHEN 陪诊师成功接单 THEN 订单匹配状态应更新为"已接受(2)"，订单状态应更新为"进行中(3)"

### 需求 2：完善匹配状态管理机制

**用户故事：** 作为系统管理员，我希望订单匹配状态能够准确反映当前的匹配阶段，避免状态不一致导致的业务流程中断。

#### 验收标准

1. WHEN 订单首次进入匹配流程 THEN 匹配状态应为"待匹配(0)"
2. WHEN 系统推送订单给陪诊师 THEN 匹配状态应为"已推送(1)"
3. WHEN 陪诊师接受订单 THEN 匹配状态应为"已接受(2)"
4. WHEN 陪诊师拒绝订单 THEN 匹配状态应为"已拒绝(3)"
5. WHEN 订单匹配超时 THEN 匹配状态应为"超时(4)"
6. WHEN 管理员手动分配超时订单 THEN 匹配状态应重置为"已推送(1)"
7. WHEN 订单被取消 THEN 匹配状态应为"已取消(6)"

### 需求 3：增强人工分配功能

**用户故事：** 作为管理员，我希望在手动分配订单时，系统能够自动处理相关的状态更新和通知，确保整个流程的连贯性。

#### 验收标准

1. WHEN 管理员选择陪诊师进行手动分配 THEN 系统应验证陪诊师的可用性
2. WHEN 手动分配成功 THEN 系统应创建新的匹配记录，状态为"已推送(1)"
3. WHEN 手动分配成功 THEN 系统应设置合理的响应截止时间（如30分钟）
4. WHEN 手动分配成功 THEN 系统应向陪诊师发送接单通知
5. WHEN 手动分配成功 THEN 系统应记录操作日志，包含操作员信息和分配原因

### 需求 4：优化陪诊师接单体验

**用户故事：** 作为陪诊师，我希望能够清楚地看到待接单的订单，并能够顺利完成接单操作，不会因为系统状态问题而被阻止。

#### 验收标准

1. WHEN 订单被分配给我 THEN 我应能在工作台看到"待接单"的订单
2. WHEN 我点击"确认接单" THEN 系统应立即处理我的请求
3. IF 接单成功 THEN 系统应显示成功提示，订单状态应变为"进行中"
4. IF 接单失败 THEN 系统应显示具体的失败原因和建议操作
5. WHEN 我接单后 THEN 其他陪诊师应无法再看到或接受该订单

### 需求 5：状态一致性保障

**用户故事：** 作为系统架构师，我希望订单状态和匹配状态能够保持一致，避免数据不一致导致的业务问题。

#### 验收标准

1. WHEN 订单匹配状态发生变化 THEN 相关的订单状态应同步更新
2. WHEN 订单状态发生变化 THEN 相关的匹配状态应保持一致
3. WHEN 发生状态更新异常 THEN 系统应有回滚机制，确保数据一致性
4. WHEN 状态更新完成 THEN 系统应记录状态变更日志，便于问题追踪

### 需求 6：错误处理和用户提示优化

**用户故事：** 作为用户（管理员和陪诊师），我希望在操作失败时能够收到清晰的错误提示和解决建议。

#### 验收标准

1. WHEN 陪诊师尝试接受不可接受状态的订单 THEN 系统应显示友好的错误提示，说明当前状态和可能的解决方案
2. WHEN 管理员尝试分配已分配的订单 THEN 系统应提示订单当前状态和分配情况
3. WHEN 系统检测到状态异常 THEN 应自动记录错误日志并通知相关人员
4. WHEN 用户操作成功 THEN 系统应提供明确的成功反馈

## 状态流转图

```
订单创建 → 待匹配(0) → 已推送(1) → 已接受(2) → 服务完成
                ↓           ↓
              超时(4)    已拒绝(3)
                ↓           ↓
            人工分配 → 已推送(1) → 已接受(2)
                ↓
            已取消(6)
```

## 优先级

1. **高优先级**：需求1（优化人工分配后的状态流转）- 解决当前阻塞问题
2. **高优先级**：需求2（完善匹配状态管理机制）- 确保状态流转正确
3. **中优先级**：需求3（增强人工分配功能）- 提升管理效率
4. **中优先级**：需求4（优化陪诊师接单体验）- 改善用户体验
5. **低优先级**：需求5（状态一致性保障）- 系统稳定性
6. **低优先级**：需求6（错误处理优化）- 用户体验优化

## 成功标准

- 陪诊师能够成功接受管理员手动分配的订单
- 订单匹配状态流转逻辑清晰、准确
- 用户操作体验流畅，错误提示友好
- 系统数据一致性得到保障
- 操作日志完整，便于问题追踪和分析