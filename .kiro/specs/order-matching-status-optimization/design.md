# 订单匹配状态优化设计文档

## 概述

本设计文档旨在解决订单匹配超时后，管理员手动分配订单给陪诊师时，陪诊师无法成功接单的问题。核心解决方案是优化订单匹配状态的流转逻辑，确保人工分配后的订单能够被陪诊师正常接受。

## 架构

### 系统架构图

```mermaid
graph TB
    A[订单创建] --> B[匹配服务]
    B --> C[自动匹配]
    B --> D[人工分配]
    
    C --> E[匹配成功]
    C --> F[匹配超时]
    
    F --> D
    D --> G[状态重置]
    G --> H[陪诊师接单]
    
    E --> H
    H --> I[订单进行中]
    
    subgraph "状态管理层"
        J[订单状态]
        K[匹配状态]
        L[状态同步器]
    end
    
    subgraph "通知服务"
        M[推送通知]
        N[短信通知]
    end
    
    B --> J
    B --> K
    J --> L
    K --> L
    
    H --> M
    H --> N
```

### 核心组件

1. **匹配状态管理器 (MatchingStatusManager)**
   - 负责匹配状态的流转和验证
   - 处理状态重置逻辑
   - 确保状态一致性

2. **人工分配服务 (ManualAssignmentService)**
   - 处理管理员手动分配逻辑
   - 集成状态重置功能
   - 触发通知服务

3. **接单验证器 (OrderAcceptanceValidator)**
   - 验证陪诊师接单条件
   - 检查匹配状态是否允许接单
   - 提供详细的错误信息

4. **状态同步器 (StatusSynchronizer)**
   - 保持订单状态和匹配状态的一致性
   - 处理状态更新事务
   - 记录状态变更日志

## 组件和接口

### 1. 匹配状态管理器

```go
type MatchingStatusManager interface {
    // 重置匹配状态为可接单状态
    ResetToOffered(ctx context.Context, orderID uint, attendantID uint, operatorID uint) error
    
    // 验证状态是否允许接单
    ValidateAcceptance(ctx context.Context, orderID uint, attendantID uint) error
    
    // 更新匹配状态
    UpdateStatus(ctx context.Context, orderID uint, status MatchingStatus, reason string) error
    
    // 获取当前匹配状态
    GetCurrentStatus(ctx context.Context, orderID uint) (*MatchingStatusInfo, error)
}

type MatchingStatusInfo struct {
    OrderID       uint          `json:"order_id"`
    AttendantID   uint          `json:"attendant_id"`
    Status        MatchingStatus `json:"status"`
    StatusText    string        `json:"status_text"`
    UpdatedAt     time.Time     `json:"updated_at"`
    CanAccept     bool          `json:"can_accept"`
    TimeoutAt     *time.Time    `json:"timeout_at"`
}

type MatchingStatus int

const (
    MatchingStatusPending   MatchingStatus = 0 // 待匹配
    MatchingStatusOffered   MatchingStatus = 1 // 已推送
    MatchingStatusAccepted  MatchingStatus = 2 // 已接受
    MatchingStatusRejected  MatchingStatus = 3 // 已拒绝
    MatchingStatusTimeout   MatchingStatus = 4 // 超时
    MatchingStatusMatching  MatchingStatus = 5 // 匹配中
    MatchingStatusCancelled MatchingStatus = 6 // 已取消
)
```

### 2. 增强的人工分配服务

```go
type EnhancedManualAssignmentService interface {
    // 手动分配订单（包含状态重置）
    AssignOrderWithStatusReset(ctx context.Context, req *ManualAssignmentRequest) error
    
    // 批量分配订单
    BatchAssignOrders(ctx context.Context, req *BatchAssignmentRequest) (*BatchAssignmentResult, error)
    
    // 取消分配
    CancelAssignment(ctx context.Context, orderID uint, reason string) error
}

type ManualAssignmentRequest struct {
    OrderID     uint   `json:"order_id" binding:"required"`
    AttendantID uint   `json:"attendant_id" binding:"required"`
    OperatorID  uint   `json:"operator_id" binding:"required"`
    Reason      string `json:"reason"`
    TimeoutMinutes int `json:"timeout_minutes"` // 接单超时时间，默认30分钟
}

type BatchAssignmentRequest struct {
    Assignments []ManualAssignmentRequest `json:"assignments" binding:"required"`
    OperatorID  uint                     `json:"operator_id" binding:"required"`
}

type BatchAssignmentResult struct {
    SuccessCount int                    `json:"success_count"`
    FailureCount int                    `json:"failure_count"`
    Results      []AssignmentResult     `json:"results"`
}

type AssignmentResult struct {
    OrderID uint   `json:"order_id"`
    Success bool   `json:"success"`
    Error   string `json:"error,omitempty"`
}
```

### 3. 接单验证器

```go
type OrderAcceptanceValidator interface {
    // 验证是否可以接单
    ValidateAcceptance(ctx context.Context, orderID uint, attendantID uint) (*ValidationResult, error)
    
    // 获取不能接单的详细原因
    GetRejectionReason(ctx context.Context, orderID uint, attendantID uint) (*RejectionInfo, error)
}

type ValidationResult struct {
    CanAccept bool     `json:"can_accept"`
    Reason    string   `json:"reason"`
    Details   []string `json:"details"`
}

type RejectionInfo struct {
    Code        string `json:"code"`
    Message     string `json:"message"`
    Suggestion  string `json:"suggestion"`
    CurrentStatus string `json:"current_status"`
}
```

### 4. 状态同步器

```go
type StatusSynchronizer interface {
    // 同步订单状态和匹配状态
    SyncStatuses(ctx context.Context, orderID uint) error
    
    // 记录状态变更
    LogStatusChange(ctx context.Context, change *StatusChange) error
    
    // 检查状态一致性
    CheckConsistency(ctx context.Context, orderID uint) (*ConsistencyReport, error)
}

type StatusChange struct {
    OrderID       uint      `json:"order_id"`
    ChangeType    string    `json:"change_type"` // order_status, matching_status
    OldValue      int       `json:"old_value"`
    NewValue      int       `json:"new_value"`
    Reason        string    `json:"reason"`
    OperatorID    uint      `json:"operator_id"`
    Timestamp     time.Time `json:"timestamp"`
}

type ConsistencyReport struct {
    OrderID       uint   `json:"order_id"`
    IsConsistent  bool   `json:"is_consistent"`
    Issues        []string `json:"issues"`
    Suggestions   []string `json:"suggestions"`
}
```

## 数据模型

### 1. 增强的订单匹配表

```sql
-- 为 order_matching 表添加新字段
ALTER TABLE order_matching ADD COLUMN IF NOT EXISTS reset_count INT DEFAULT 0 COMMENT '重置次数';
ALTER TABLE order_matching ADD COLUMN IF NOT EXISTS reset_reason VARCHAR(500) COMMENT '重置原因';
ALTER TABLE order_matching ADD COLUMN IF NOT EXISTS original_status INT COMMENT '原始状态（重置前）';
ALTER TABLE order_matching ADD COLUMN IF NOT EXISTS assignment_type TINYINT DEFAULT 0 COMMENT '分配类型：0-自动，1-人工，2-重新分配';
ALTER TABLE order_matching ADD COLUMN IF NOT EXISTS operator_id INT COMMENT '操作员ID（人工分配时）';
```

### 2. 状态变更日志表

```sql
CREATE TABLE IF NOT EXISTS matching_status_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL COMMENT '订单ID',
    attendant_id INT COMMENT '陪诊师ID',
    old_status TINYINT COMMENT '原状态',
    new_status TINYINT NOT NULL COMMENT '新状态',
    change_reason VARCHAR(500) COMMENT '变更原因',
    operator_id INT COMMENT '操作员ID',
    change_type VARCHAR(50) NOT NULL COMMENT '变更类型：auto, manual, reset, timeout',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_attendant_id (attendant_id),
    INDEX idx_created_at (created_at)
) COMMENT='匹配状态变更日志';
```

### 3. 接单验证缓存表

```sql
CREATE TABLE IF NOT EXISTS order_acceptance_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    attendant_id INT NOT NULL,
    can_accept BOOLEAN NOT NULL,
    rejection_reason VARCHAR(500),
    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    UNIQUE KEY uk_order_attendant (order_id, attendant_id),
    INDEX idx_expires_at (expires_at)
) COMMENT='接单验证缓存';
```

## 错误处理

### 错误码定义

```go
const (
    // 匹配状态相关错误
    ErrMatchingStatusNotAllowed    = "MATCHING_STATUS_NOT_ALLOWED"
    ErrMatchingStatusTimeout       = "MATCHING_STATUS_TIMEOUT"
    ErrMatchingStatusConflict      = "MATCHING_STATUS_CONFLICT"
    
    // 人工分配相关错误
    ErrManualAssignmentFailed      = "MANUAL_ASSIGNMENT_FAILED"
    ErrAttendantNotAvailable       = "ATTENDANT_NOT_AVAILABLE"
    ErrOrderAlreadyAssigned        = "ORDER_ALREADY_ASSIGNED"
    
    // 接单验证相关错误
    ErrAcceptanceValidationFailed  = "ACCEPTANCE_VALIDATION_FAILED"
    ErrOrderNotAssignedToAttendant = "ORDER_NOT_ASSIGNED_TO_ATTENDANT"
    ErrAcceptanceTimeout           = "ACCEPTANCE_TIMEOUT"
    
    // 状态同步相关错误
    ErrStatusSyncFailed            = "STATUS_SYNC_FAILED"
    ErrStatusInconsistent          = "STATUS_INCONSISTENT"
)

type MatchingError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Details map[string]interface{} `json:"details,omitempty"`
}

func (e *MatchingError) Error() string {
    return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}
```

### 错误处理策略

1. **状态冲突处理**
   - 检测并发状态更新冲突
   - 使用乐观锁机制
   - 提供重试机制

2. **数据一致性保障**
   - 使用数据库事务确保原子性
   - 实现补偿机制处理异常情况
   - 定期检查和修复数据不一致

3. **用户友好的错误提示**
   - 提供具体的错误原因
   - 给出可行的解决建议
   - 记录详细的错误日志

## 测试策略

### 1. 单元测试

- **匹配状态管理器测试**
  - 状态重置逻辑测试
  - 状态验证逻辑测试
  - 边界条件测试

- **人工分配服务测试**
  - 正常分配流程测试
  - 异常情况处理测试
  - 并发分配测试

- **接单验证器测试**
  - 各种状态下的验证测试
  - 错误信息准确性测试

### 2. 集成测试

- **完整流程测试**
  - 从匹配超时到人工分配到成功接单的完整流程
  - 多个订单并发处理测试
  - 状态同步测试

- **异常场景测试**
  - 网络异常情况下的状态处理
  - 数据库异常情况下的恢复机制
  - 并发操作冲突处理

### 3. 性能测试

- **高并发场景测试**
  - 大量订单同时进行状态更新
  - 多个管理员同时进行人工分配
  - 多个陪诊师同时接单

- **数据库性能测试**
  - 状态查询性能测试
  - 日志写入性能测试
  - 索引优化验证

## 部署和监控

### 1. 部署策略

- **灰度发布**
  - 先在测试环境验证
  - 小范围生产环境测试
  - 逐步全量发布

- **数据库迁移**
  - 新增字段的兼容性处理
  - 历史数据的状态修复
  - 索引的创建和优化

### 2. 监控指标

- **业务指标**
  - 人工分配成功率
  - 陪诊师接单成功率
  - 状态流转异常率
  - 平均处理时间

- **技术指标**
  - API响应时间
  - 数据库查询性能
  - 错误日志统计
  - 系统资源使用率

### 3. 告警机制

- **业务告警**
  - 接单失败率超过阈值
  - 状态不一致检测
  - 人工分配失败率异常

- **技术告警**
  - API响应时间过长
  - 数据库连接异常
  - 系统资源不足

## 安全考虑

### 1. 权限控制

- **管理员权限验证**
  - 验证管理员身份
  - 检查操作权限
  - 记录操作日志

- **陪诊师权限验证**
  - 验证陪诊师身份
  - 检查订单分配关系
  - 防止越权操作

### 2. 数据安全

- **敏感信息保护**
  - 操作日志脱敏
  - 错误信息过滤
  - 数据传输加密

- **防止恶意操作**
  - 操作频率限制
  - 异常行为检测
  - 自动封禁机制

## 向后兼容性

### 1. API兼容性

- **现有接口保持不变**
  - 不修改现有API签名
  - 新增可选参数
  - 保持响应格式兼容

- **新增接口设计**
  - 遵循RESTful规范
  - 提供详细的API文档
  - 版本化管理

### 2. 数据兼容性

- **数据库结构变更**
  - 新增字段设置默认值
  - 保持现有字段不变
  - 提供数据迁移脚本

- **历史数据处理**
  - 兼容旧版本数据格式
  - 提供数据升级工具
  - 保证数据完整性

这个设计方案全面解决了订单匹配状态优化的需求，确保了系统的稳定性、可扩展性和用户体验。