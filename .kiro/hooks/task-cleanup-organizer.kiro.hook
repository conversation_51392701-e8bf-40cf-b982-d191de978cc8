{"enabled": true, "name": "任务完成后清理和整理", "description": "执行完任务之后，首先检查是否有无用的脚本文件，有的话清理一下；然后检查一下是否有MD文件，有的话按照项目根目录下docs目录下的文档分类，移动到docs对应的目录下", "version": "1", "when": {"type": "userTriggered", "patterns": ["*.sh", "*.sql", "*.js", "*.go", "*.md"]}, "then": {"type": "askAgent", "prompt": "请执行以下任务：\n1. 检查项目中是否有无用的脚本文件（.sh, .sql, .js, .go等），如果发现临时或测试脚本文件，请清理它们\n2. 检查项目根目录下是否有散落的MD文档文件\n3. 如果有MD文件，请根据docs目录下的现有分类结构，将这些文件移动到相应的子目录中：\n   - API相关文档 → docs/api/\n   - 部署相关文档 → docs/deployment/\n   - 维护相关文档 → docs/maintenance/\n   - 测试相关文档 → docs/test/\n   - 需求相关文档 → docs/requirements/\n   - 用户指南 → docs/user-guides/\n   - 设计相关文档 → docs/design/\n   - 安全相关文档 → docs/security/\n   - 其他通用文档保留在docs根目录\n\n请确保文件移动后项目结构更加整洁有序。"}}