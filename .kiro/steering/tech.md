# 技术栈

## 后端技术
- **编程语言**: Go 1.23+
- **Web框架**: <PERSON><PERSON> (HTTP路由器)
- **数据库**: MySQL 8.0+ 配合 GORM ORM
- **缓存**: Redis 6.0+
- **消息队列**: RabbitMQ
- **身份认证**: JWT令牌
- **支付系统**: 微信支付 API v3
- **文件存储**: 本地上传 + 七牛云集成
- **日志系统**: Zap日志器，支持分类日志
- **配置管理**: Viper + YAML配置文件

## 前端技术
- **平台**: 微信小程序
- **开发语言**: JavaScript, WXML, WXSS
- **架构模式**: 页面驱动 + 服务层抽象

## 管理后台
- **后端**: 独立的Go服务 (admin/server)
- **前端**: Vue.js网页应用 (admin/web)
- **构建工具**: Vite

## 开发工具
- **依赖管理**: Go Modules
- **工作空间**: Go Work 多模块工作空间
- **数据库迁移**: 基于SQL的数据库迁移

## 常用命令

### 后端开发
```bash
# 进入后端目录
cd backend

# 安装依赖
go mod tidy

# 运行开发服务器
go run main.go

# 指定环境运行
APP_ENV=dev go run main.go

# 构建二进制文件
go build -o bin/peizhen main.go
```

### 管理后台开发
```bash
# 后端服务
cd admin/server
go mod tidy
go run main.go

# 前端应用
cd admin/web
npm install
npm run dev
npm run build
```

### 数据库操作
```bash
# 运行数据库迁移
cd backend/scripts
./run_migrations.sh

# 导入测试数据
./import_test_data.sh
```

### 测试命令
```bash
# 运行所有测试
go test ./...

# 运行特定测试
go test ./internal/service/...

# 运行测试并显示覆盖率
go test -cover ./...
```

## 环境配置
- 使用 `backend/config/conf/` 目录下的YAML配置文件
- 环境特定配置: `config.dev.yaml`, `config.prod.yaml`
- 支持环境变量替换
- 默认环境: `dev` (通过 `APP_ENV` 控制)