# 项目结构

## 根目录组织
```
peizhen/
├── backend/           # 主要的Go后端服务
├── admin/            # 管理后台（独立服务）
├── frontend/         # 微信小程序
├── docs/            # 文档
├── scripts/         # 部署和工具脚本
└── go.work          # Go工作空间配置
```

## 后端架构 (`backend/`)
遵循整洁架构原则，关注点清晰分离：

```
backend/
├── cmd/                    # 应用程序入口点
├── internal/              # 私有应用程序代码
│   ├── app/              # 应用程序设置和依赖注入
│   ├── handler/          # HTTP请求处理器（控制器）
│   ├── service/          # 业务逻辑层
│   │   └── impl/         # 服务实现
│   ├── repository/       # 数据访问层
│   │   └── impl/         # 仓库实现
│   ├── model/           # 领域模型和实体
│   ├── dto/             # 数据传输对象
│   ├── router/          # 路由定义
│   ├── middleware/      # HTTP中间件
│   ├── scheduler/       # 后台任务调度器
│   └── utils/           # 内部工具
├── pkg/                 # 公共/共享包
│   ├── auth/           # 认证工具
│   ├── db/             # 数据库连接
│   ├── logger/         # 日志工具
│   ├── redis/          # Redis客户端
│   └── response/       # HTTP响应助手
├── config/             # 配置管理
├── migrations/         # 数据库迁移文件
├── scripts/           # 后端特定脚本
└── main.go            # 应用程序入口点
```

## 管理后台 (`admin/`)
管理功能的独立微服务：

```
admin/
├── server/            # 管理后台Go后端
│   ├── handler/      # 管理API处理器
│   ├── service/      # 管理业务逻辑
│   ├── repository/   # 管理数据访问
│   ├── router/       # 管理路由
│   └── main.go       # 管理服务器入口
└── web/              # Vue.js前端
    ├── src/          # Vue源代码
    ├── dist/         # 构建资源
    └── package.json  # Node依赖
```

## 前端 (`frontend/`)
微信小程序结构：

```
frontend/
├── pages/            # 小程序页面
├── components/       # 可复用组件
├── services/         # API服务层
├── utils/           # 工具函数
├── config/          # 前端配置
├── assets/          # 静态资源
├── app.js           # 应用入口点
└── app.json         # 应用配置
```

## 命名规范

### Go代码
- **文件名**: snake_case (例如: `order_service.go`)
- **包名**: 小写，尽可能使用单个单词
- **接口**: 以 `I` 为前缀 (例如: `IOrderService`)
- **结构体**: PascalCase (例如: `OrderHandler`)
- **函数/方法**: 公共方法用PascalCase，私有方法用camelCase
- **常量**: UPPER_SNAKE_CASE

### 前端代码
- **文件名**: 页面用kebab-case，服务用camelCase
- **组件**: PascalCase目录
- **函数**: camelCase
- **常量**: UPPER_SNAKE_CASE

## 层级职责

### Handler层（处理器层）
- HTTP请求/响应处理
- 输入验证和数据绑定
- 身份认证/授权检查
- 调用相应的服务方法

### Service层（服务层）
- 业务逻辑实现
- 事务管理
- 横切关注点（日志、缓存）
- 编排仓库调用

### Repository层（仓库层）
- 数据访问和持久化
- 数据库查询实现
- 数据映射和转换
- 不包含业务逻辑

### Model层（模型层）
- 领域实体和值对象
- 数据库表映射（GORM标签）
- 验证规则
- 业务规则和约束

## 配置管理
- `backend/config/conf/` 目录下的环境特定YAML文件
- 支持环境变量替换和默认值
- 集中化配置结构体
- 启动时配置验证