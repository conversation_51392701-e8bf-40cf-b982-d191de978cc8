# 发布需要执行的SQL文件清单

## 执行顺序和说明

### 1. 创建system_settings表
**文件**: `backend/migrations/20250803000002_create_system_settings_table.sql`
**说明**: 创建简化的系统配置表，用于存储键值对配置
**必需**: 是

### 2. 删除旧的system_configs表
**文件**: `backend/migrations/20250803000001_drop_system_configs_table.sql`
**说明**: 删除复杂的system_configs表，统一使用system_settings表
**必需**: 是（如果生产环境存在system_configs表）

### 3. 添加结算配置数据
**文件**: `backend/migrations/20250203000001_add_settlement_config.sql`
**说明**: 插入T+N结算天数、收入冻结天数等结算相关配置
**必需**: 是

## 执行命令示例

```bash
# 1. 创建system_settings表
mysql -u username -p database_name < backend/migrations/20250803000002_create_system_settings_table.sql

# 2. 删除旧的system_configs表（如果存在）
mysql -u username -p database_name < backend/migrations/20250803000001_drop_system_configs_table.sql

# 3. 插入结算配置数据
mysql -u username -p database_name < backend/migrations/20250203000001_add_settlement_config.sql
```

## 验证步骤

执行完成后，可以通过以下SQL验证配置是否正确插入：

```sql
-- 查看所有系统配置
SELECT * FROM system_settings ORDER BY `key`;

-- 查看结算相关配置
SELECT * FROM system_settings WHERE `key` LIKE 'settlement.%' OR `key` LIKE 'finance.%';
```

## 预期结果

执行完成后，system_settings表应该包含以下配置：

- `settlement.review_period_days`: T+N结算天数（默认2天）
- `finance.freeze_days`: 收入冻结天数（默认7天）
- `settlement.auto_settlement_enabled`: 自动结算开关
- `settlement.review_period_hours`: 审核期限小时数
- `settlement.dispute_timeout_days`: 争议超时天数
- `settlement.min_auto_amount`: 最小自动结算金额
- `settlement.max_auto_amount`: 最大自动结算金额

## 注意事项

1. **备份数据**: 执行前请备份生产数据库
2. **检查依赖**: 确保应用程序代码已更新为使用system_settings表
3. **配置验证**: 执行后通过管理后台验证配置功能正常
4. **回滚准备**: 如有问题，可以通过备份快速回滚

## 相关功能

这些SQL文件支持以下功能：
- T+N结算天数配置化
- 收入冻结天数配置化
- 管理后台结算配置界面
- 系统配置的统一管理