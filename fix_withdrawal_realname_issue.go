package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/gemeijie/peizhen/backend/config"
	"github.com/gemeijie/peizhen/backend/internal/model"
	"github.com/gemeijie/peizhen/backend/pkg/db"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("=== 修复提现申请收款人姓名问题 ===")

	// 设置环境
	os.Setenv("APP_ENV", "prod")

	// 初始化配置
	if err := config.InitConfig(); err != nil {
		log.Fatalf("配置初始化失败: %v", err)
	}

	// 初始化数据库连接
	database, err := db.InitDB()
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	ctx := context.Background()

	fmt.Println("\n=== 检查提现申请数据 ===")

	// 查找所有 real_name 为空的提现申请
	var withdrawalsWithEmptyRealName []model.Withdrawal
	err = database.Where("real_name IS NULL OR real_name = ''").Find(&withdrawalsWithEmptyRealName).Error
	if err != nil {
		log.Fatalf("查询提现申请失败: %v", err)
	}

	fmt.Printf("找到 %d 条 real_name 为空的提现申请\n", len(withdrawalsWithEmptyRealName))

	if len(withdrawalsWithEmptyRealName) == 0 {
		fmt.Println("所有提现申请的 real_name 都已设置")
	} else {
		// 显示前几条记录
		fmt.Println("\n前几条记录:")
		for i, withdrawal := range withdrawalsWithEmptyRealName {
			if i >= 5 {
				break
			}
			realNameStr := "NULL"
			if withdrawal.RealName != nil {
				realNameStr = *withdrawal.RealName
			}
			fmt.Printf("ID: %d, WithdrawNo: %s, UserID: %d, RealName: %s\n",
				withdrawal.ID, withdrawal.WithdrawNo, withdrawal.UserID, realNameStr)
		}
	}

	fmt.Println("\n=== 检查陪诊师数据 ===")

	// 检查陪诊师姓名数据
	var attendantsWithEmptyName []model.Attendant
	err = database.Where("name IS NULL OR name = ''").Find(&attendantsWithEmptyName).Error
	if err != nil {
		log.Fatalf("查询陪诊师失败: %v", err)
	}

	fmt.Printf("找到 %d 条姓名为空的陪诊师记录\n", len(attendantsWithEmptyName))

	if len(attendantsWithEmptyName) > 0 {
		fmt.Println("\n姓名为空的陪诊师:")
		for _, attendant := range attendantsWithEmptyName {
			fmt.Printf("ID: %d, UserID: %d, Name: '%s'\n", attendant.ID, attendant.UserID, attendant.Name)
		}
	}

	fmt.Println("\n=== 修复提现申请的 real_name 字段 ===")

	fixedCount := 0
	for _, withdrawal := range withdrawalsWithEmptyRealName {
		// 查找对应的陪诊师
		var attendant model.Attendant
		err = database.Where("user_id = ?", withdrawal.UserID).First(&attendant).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				fmt.Printf("⚠️  提现申请 ID %d 对应的陪诊师不存在 (UserID: %d)\n", withdrawal.ID, withdrawal.UserID)
				continue
			}
			fmt.Printf("❌ 查询陪诊师失败 (UserID: %d): %v\n", withdrawal.UserID, err)
			continue
		}

		if attendant.Name == "" {
			fmt.Printf("⚠️  陪诊师 ID %d 的姓名为空，无法修复提现申请 ID %d\n", attendant.ID, withdrawal.ID)
			continue
		}

		// 更新提现申请的 real_name
		err = database.Model(&withdrawal).Update("real_name", attendant.Name).Error
		if err != nil {
			fmt.Printf("❌ 更新提现申请 ID %d 的 real_name 失败: %v\n", withdrawal.ID, err)
			continue
		}

		fmt.Printf("✅ 已修复提现申请 ID %d: real_name = '%s'\n", withdrawal.ID, attendant.Name)
		fixedCount++
	}

	fmt.Printf("\n=== 修复完成 ===\n")
	fmt.Printf("总共修复了 %d 条提现申请记录\n", fixedCount)

	// 验证修复结果
	fmt.Println("\n=== 验证修复结果 ===")

	var remainingEmptyRealName []model.Withdrawal
	err = database.Where("real_name IS NULL OR real_name = ''").Find(&remainingEmptyRealName).Error
	if err != nil {
		log.Fatalf("验证查询失败: %v", err)
	}

	if len(remainingEmptyRealName) == 0 {
		fmt.Println("🎉 所有提现申请的 real_name 都已正确设置")
	} else {
		fmt.Printf("⚠️  仍有 %d 条提现申请的 real_name 为空\n", len(remainingEmptyRealName))
		for _, withdrawal := range remainingEmptyRealName {
			fmt.Printf("   ID: %d, WithdrawNo: %s, UserID: %d\n",
				withdrawal.ID, withdrawal.WithdrawNo, withdrawal.UserID)
		}
	}

	// 检查特定的提现申请 ID 1
	fmt.Println("\n=== 检查提现申请 ID 1 ===")
	var withdrawal1 model.Withdrawal
	err = database.First(&withdrawal1, 1).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			fmt.Println("提现申请 ID 1 不存在")
		} else {
			fmt.Printf("查询提现申请 ID 1 失败: %v\n", err)
		}
	} else {
		realNameStr := "NULL"
		if withdrawal1.RealName != nil {
			realNameStr = *withdrawal1.RealName
		}
		openIDStr := "NULL"
		if withdrawal1.OpenID != nil {
			openIDStr = *withdrawal1.OpenID
		}
		fmt.Printf("提现申请 ID 1 详情:\n")
		fmt.Printf("  WithdrawNo: %s\n", withdrawal1.WithdrawNo)
		fmt.Printf("  UserID: %d\n", withdrawal1.UserID)
		fmt.Printf("  Amount: %.2f\n", withdrawal1.Amount)
		fmt.Printf("  Status: %d\n", withdrawal1.Status)
		fmt.Printf("  RealName: %s\n", realNameStr)
		fmt.Printf("  OpenID: %s\n", openIDStr)

		// 查找对应的陪诊师
		var attendant model.Attendant
		err = database.Where("user_id = ?", withdrawal1.UserID).First(&attendant).Error
		if err != nil {
			fmt.Printf("  对应陪诊师: 未找到 (错误: %v)\n", err)
		} else {
			fmt.Printf("  对应陪诊师: ID %d, Name '%s'\n", attendant.ID, attendant.Name)
		}
	}

	fmt.Println("\n📋 修复建议:")
	fmt.Println("1. 如果仍有 real_name 为空的记录，请检查对应的陪诊师数据")
	fmt.Println("2. 确保所有陪诊师都有正确的姓名")
	fmt.Println("3. 重新测试转账功能")
}
