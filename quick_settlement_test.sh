#!/bin/bash

# 快速结算测试脚本
# 使用方法：./quick_settlement_test.sh [admin_username] [admin_password]

BASE_URL="https://www.kanghuxing.cn"
ORDER_ID=10017
ATTENDANT_ID=13

# 检查参数
if [ $# -ne 2 ]; then
    echo "使用方法: $0 <admin_username> <admin_password>"
    echo "例如: $0 admin admin123"
    exit 1
fi

ADMIN_USERNAME=$1
ADMIN_PASSWORD=$2

echo "=== 快速结算测试 ==="

# 1. 获取JWT Token
echo "1. 获取管理员Token..."
LOGIN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$ADMIN_USERNAME\",
    \"password\": \"$ADMIN_PASSWORD\"
  }")

JWT_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token // empty')

if [ -z "$JWT_TOKEN" ] || [ "$JWT_TOKEN" = "null" ]; then
    echo "❌ 登录失败: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ 登录成功"

# 2. 触发自动结算
echo -e "\n2. 触发自动结算..."
SETTLEMENT_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/admin/reviews/trigger-settlement" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "结算响应: $SETTLEMENT_RESPONSE"

# 3. 如果自动结算失败，尝试手动结算
echo -e "\n3. 尝试手动单个订单结算..."
MANUAL_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/settlement/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "{
    \"order_id\": $ORDER_ID
  }")

echo "手动结算响应: $MANUAL_RESPONSE"

# 4. 检查结果
echo -e "\n4. 检查结算结果..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '最终状态检查' as check_type,
    o.id as order_id,
    o.order_no,
    o.status as order_status,
    o.settlement_status,
    ai.status as income_status,
    CASE 
        WHEN o.status = 13 THEN '✅ 订单已结算'
        WHEN o.status = 10 THEN '⏳ 订单审核通过，待结算'
        ELSE CONCAT('❓ 订单状态: ', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 2 THEN '✅ 收入已结算'
        WHEN ai.status = 1 THEN '⏳ 收入待结算'
        ELSE CONCAT('❓ 收入状态: ', ai.status)
    END as income_status_text
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = $ATTENDANT_ID
WHERE o.id = $ORDER_ID;
"

echo -e "\n=== 测试完成 ==="