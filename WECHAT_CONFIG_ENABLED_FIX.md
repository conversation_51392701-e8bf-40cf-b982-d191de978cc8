# 微信转账配置启用修复

## 🔍 问题根因分析

通过日志分析发现的问题：

```json
{
  "client_type": "",           // ❌ 空字符串，应该是 "official"
  "enabled": false,            // ❌ 被禁用，应该是 true
  "mock_mode": false,          // ✅ 正确
  "environment": "production"  // ✅ 正确
}
```

### 问题原因
1. **配置文件中缺少环境变量映射**：`enabled` 和 `mock_mode` 字段没有使用环境变量
2. **环境变量缺失**：`production.env` 中缺少 `WECHAT_TRANSFER_ENABLED` 和 `WECHAT_TRANSFER_MOCK_MODE`

## 🛠️ 修复内容

### 1. 修复配置文件映射
**文件**: `backend/config/config.yaml`

**修复前**:
```yaml
# 功能开关
enabled: true            # 硬编码值
mock_mode: false         # 硬编码值
```

**修复后**:
```yaml
# 功能开关
enabled: "${WECHAT_TRANSFER_ENABLED:true}"      # 使用环境变量
mock_mode: "${WECHAT_TRANSFER_MOCK_MODE:false}" # 使用环境变量
```

### 2. 添加环境变量
**文件**: `production.env`

**新增内容**:
```bash
# Function Control
WECHAT_TRANSFER_ENABLED=true
WECHAT_TRANSFER_MOCK_MODE=false
```

## 📊 修复前后对比

### 修复前的日志
```
"client_type":"","enabled":false,"mock_mode":false
微信企业付款功能被禁用 enabled=false client_type=simple
```

### 修复后预期日志
```
"client_type":"official","enabled":true,"mock_mode":false
尝试创建官方SDK客户端 V2
开始创建官方微信转账客户端V2
开始调用微信企业付款API
```

## 🚀 部署步骤

### 1. 更新服务器配置
```bash
# 在服务器上执行
cd /var/www/html/peizhen

# 确认配置文件已更新
grep -A 2 "功能开关" backend/config/config.yaml

# 确认环境变量已添加
tail -5 /etc/peizhen/production.env
```

### 2. 重启服务
```bash
# 重启后端服务
sudo systemctl restart peizhen-backend

# 或者使用具体的服务名
sudo systemctl restart your-service-name
```

### 3. 验证修复
```bash
# 监控日志
tail -f /var/www/html/peizhen/logs/app.prod.log | grep -E "(微信|wechat|transfer|enabled)" -i

# 测试转账功能
# 在管理后台点击"确认打款"按钮
```

## ✅ 预期结果

修复后，点击"确认打款"应该看到：

1. **配置正确加载**:
   ```
   开始创建微信转账客户端 client_type=official enabled=true mock_mode=false
   ```

2. **使用官方客户端**:
   ```
   尝试创建官方SDK客户端 V2
   开始创建官方微信转账客户端V2
   ```

3. **调用真实API**:
   ```
   开始调用微信企业付款API
   微信企业付款API调用成功/失败
   ```

## 🔧 故障排除

### 如果仍然显示 enabled=false
1. 检查环境变量是否正确加载：
   ```bash
   ps aux | grep peizhen
   cat /proc/PID/environ | tr '\0' '\n' | grep WECHAT_TRANSFER_ENABLED
   ```

2. 检查systemd服务配置：
   ```bash
   sudo systemctl show your-service-name | grep Environment
   ```

### 如果官方客户端创建失败
查看详细错误信息：
```bash
tail -f logs/app.prod.log | grep "创建官方微信转账客户端失败"
```

常见原因：
- 证书文件路径错误
- 证书文件权限问题
- APIv3密钥错误
- 证书序列号不匹配

## 📝 完整的环境变量清单

确保以下环境变量都已正确设置：

```bash
# 基础配置
WECHAT_TRANSFER_CLIENT_TYPE=official
WECHAT_TRANSFER_APP_ID=wx2ce88b500238c799
WECHAT_TRANSFER_MCH_ID=1717184423
WECHAT_TRANSFER_APIV3_KEY=0C9821B2517645438B93B1B21CC62901

# 证书配置
WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247
WECHAT_TRANSFER_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem

# 环境配置
WECHAT_TRANSFER_ENVIRONMENT=production
WECHAT_TRANSFER_NOTIFY_URL=https://www.kanghuxing.cn/api/v1/callback/wechat/transfer

# 功能控制 (新增)
WECHAT_TRANSFER_ENABLED=true
WECHAT_TRANSFER_MOCK_MODE=false

# 兼容性配置
WECHAT_TRANSFER_SERIAL_NO=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247
WECHAT_TRANSFER_PRIVATE_KEY_FILE=/etc/peizhen/certs/wechat/apiclient_key.pem
```

现在配置应该能够正确加载，微信转账功能应该可以正常工作了！