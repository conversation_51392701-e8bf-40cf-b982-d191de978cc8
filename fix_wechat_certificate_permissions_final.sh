#!/bin/bash

# 最终修复微信证书权限问题
# 基于实际进程用户信息：www-data

echo "🔧 最终修复微信证书权限问题"
echo "=================================="
echo "基于实际进程信息：应用以 www-data 用户运行"

# 1. 确认当前进程用户
echo -e "\n1. 确认当前进程用户："
echo "   后端进程: www-data 3015515 /var/www/html/peizhen/peizhen-app"
echo "   管理后台: www-data 3015358 /var/www/html/admin/server/peizhen-admin"

# 2. 检查证书文件当前状态
echo -e "\n2. 检查证书文件当前状态："
cert_files=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem"
    "/etc/peizhen/certs/wechat/apiclient_cert.pem"
)

for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then 
        echo "   📄 $cert_file"  
        echo "      当前权限: $(ls -la "$cert_file")"
        
        # 检查www-data用户是否可读
        if sudo -u www-data test -r "$cert_file" 2>/dev/null; then
            echo "      www-data可读: ✅"
        else
            echo "      www-data可读: ❌ 需要修复"
        fi
    else
        echo "   ❌ $cert_file 不存在"
    fi
done

# 3. 修复证书文件权限
echo -e "\n3. 修复证书文件权限："

for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "   🔧 修复 $cert_file"
        
        # 备份当前权限信息
        current_perm=$(ls -la "$cert_file")
        echo "      修复前: $current_perm"
        
        # 修复所有者为www-data
        sudo chown www-data:www-data "$cert_file"
        
        # 设置适当权限（所有者可读写，其他用户无权限）
        sudo chmod 600 "$cert_file"
        
        # 显示修复后权限
        new_perm=$(ls -la "$cert_file")
        echo "      修复后: $new_perm"
        
        # 验证www-data用户可读性
        if sudo -u www-data test -r "$cert_file" 2>/dev/null; then
            echo "      验证结果: ✅ www-data可读"
        else
            echo "      验证结果: ❌ 仍然无法读取"
        fi
    fi
done

# 4. 修复证书目录权限
echo -e "\n4. 修复证书目录权限："

cert_dirs=(
    "/etc/peizhen"
    "/etc/peizhen/certs"
    "/etc/peizhen/certs/wechat"
)

for cert_dir in "${cert_dirs[@]}"; do
    if [ -d "$cert_dir" ]; then
        echo "   📁 $cert_dir"
        echo "      修复前: $(ls -ld "$cert_dir")"
        
        # 确保www-data用户可以访问目录
        sudo chown www-data:www-data "$cert_dir"
        sudo chmod 755 "$cert_dir"
        
        echo "      修复后: $(ls -ld "$cert_dir")"
    fi
done

# 5. 验证修复结果
echo -e "\n5. 验证修复结果："

echo "   🧪 测试www-data用户对证书文件的访问："
for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        if sudo -u www-data test -r "$cert_file" 2>/dev/null; then
            echo "   ✅ $cert_file: www-data可读"
        else
            echo "   ❌ $cert_file: www-data不可读"
        fi
    fi
done

# 6. 更新环境变量配置
echo -e "\n6. 确认环境变量配置："

if [ -f "production.env" ]; then
    echo "   📋 production.env 关键配置："
    echo "      WECHAT_TRANSFER_CLIENT_TYPE: $(grep WECHAT_TRANSFER_CLIENT_TYPE production.env | cut -d'=' -f2)"
    echo "      WECHAT_PRIVATE_KEY_PATH: $(grep WECHAT_PRIVATE_KEY_PATH production.env | cut -d'=' -f2)"
    echo "      WECHAT_PUBLIC_KEY_PATH: $(grep WECHAT_PUBLIC_KEY_PATH production.env | cut -d'=' -f2)"
fi

# 7. 创建最终测试脚本
echo -e "\n7. 创建最终测试脚本："

cat > test_certificate_access_as_www_data.sh << 'EOF'
#!/bin/bash

echo "🧪 测试www-data用户证书访问"
echo "=========================="

cert_files=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem"
    "/etc/peizhen/certs/wechat/apiclient_cert.pem"
)

echo "以www-data用户身份测试证书文件访问："
for cert_file in "${cert_files[@]}"; do
    echo -n "   $cert_file: "
    if sudo -u www-data test -r "$cert_file" 2>/dev/null; then
        echo "✅ 可读"
        # 尝试读取文件头部验证格式
        header=$(sudo -u www-data head -1 "$cert_file" 2>/dev/null)
        if echo "$header" | grep -q "BEGIN"; then
            echo "      格式: ✅ PEM格式正确"
        else
            echo "      格式: ❌ 非PEM格式"
        fi
    else
        echo "❌ 不可读"
    fi
done

echo -e "\n测试完成！"
EOF

chmod +x test_certificate_access_as_www_data.sh
echo "   ✅ 已创建测试脚本: test_certificate_access_as_www_data.sh"

# 8. 提供重启服务命令
echo -e "\n8. 重启服务命令："
echo "   # 重启后端服务"
echo "   sudo systemctl restart peizhen-backend"
echo "   echo '后端服务状态:'"
echo "   sudo systemctl status peizhen-backend"
echo ""
echo "   # 重启管理后台服务"
echo "   sudo systemctl restart peizhen-admin"
echo "   echo '管理后台状态:'"
echo "   sudo systemctl status peizhen-admin"

echo -e "\n🎯 修复完成！"
echo "=================================="
echo "主要修复内容："
echo "1. ✅ 修复环境变量 WECHAT_TRANSFER_CLIENT_TYPE=official"
echo "2. ✅ 修复证书文件所有者: www-data:www-data"
echo "3. ✅ 修复证书文件权限: 600"
echo "4. ✅ 修复证书目录权限: 755"
echo "5. ✅ 创建验证测试脚本"
echo ""
echo "下一步操作："
echo "1. 运行测试脚本: ./test_certificate_access_as_www_data.sh"
echo "2. 重启服务使配置生效"
echo "3. 检查应用日志确认使用官方客户端"
echo "4. 测试实际转账功能"