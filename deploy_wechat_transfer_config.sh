#!/bin/bash

echo "=== 微信企业付款配置部署脚本 ==="

# 检查是否在服务器环境
if [ ! -f "production.env" ]; then
    echo "❌ production.env 文件不存在，请确保在正确的目录下运行"
    exit 1
fi

echo "1. 备份当前环境配置"
if [ -f "/etc/environment.backup" ]; then
    echo "   ⚠️  备份文件已存在，跳过备份"
else
    sudo cp /etc/environment /etc/environment.backup 2>/dev/null || echo "   ⚠️  无法备份 /etc/environment"
fi

echo -e "\n2. 加载 production.env 到当前会话"
set -a  # 自动导出所有变量
source production.env
set +a

echo "   ✅ 已加载环境变量到当前会话"

echo -e "\n3. 验证关键环境变量"
required_vars=(
    "WECHAT_TRANSFER_CLIENT_TYPE"
    "WECHAT_TRANSFER_APP_ID"
    "WECHAT_TRANSFER_MCH_ID"
    "WECHAT_TRANSFER_APIV3_KEY"
    "WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER"
    "WECHAT_TRANSFER_PRIVATE_KEY_PATH"
)

all_set=true
for var in "${required_vars[@]}"; do
    if [ -n "${!var}" ]; then
        echo "   ✅ $var = ${!var}"
    else
        echo "   ❌ $var = (未设置)"
        all_set=false
    fi
done

if [ "$all_set" = false ]; then
    echo "   ❌ 存在未设置的环境变量，请检查 production.env 文件"
    exit 1
fi

echo -e "\n4. 将环境变量添加到系统环境"
echo "   正在更新 /etc/environment..."

# 创建临时文件包含微信转账配置
cat > /tmp/wechat_transfer_env << EOF
# WeChat Transfer Configuration - Added $(date)
WECHAT_TRANSFER_CLIENT_TYPE=$WECHAT_TRANSFER_CLIENT_TYPE
WECHAT_TRANSFER_APP_ID=$WECHAT_TRANSFER_APP_ID
WECHAT_TRANSFER_MCH_ID=$WECHAT_TRANSFER_MCH_ID
WECHAT_TRANSFER_APIV3_KEY=$WECHAT_TRANSFER_APIV3_KEY
WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER=$WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER
WECHAT_TRANSFER_PRIVATE_KEY_PATH=$WECHAT_TRANSFER_PRIVATE_KEY_PATH
WECHAT_TRANSFER_ENVIRONMENT=$WECHAT_TRANSFER_ENVIRONMENT
WECHAT_TRANSFER_NOTIFY_URL=$WECHAT_TRANSFER_NOTIFY_URL
WECHAT_TRANSFER_SERIAL_NO=$WECHAT_TRANSFER_SERIAL_NO
WECHAT_TRANSFER_PRIVATE_KEY_FILE=$WECHAT_TRANSFER_PRIVATE_KEY_FILE
EOF

# 检查是否已存在微信转账配置
if grep -q "WECHAT_TRANSFER_CLIENT_TYPE" /etc/environment 2>/dev/null; then
    echo "   ⚠️  /etc/environment 中已存在微信转账配置，跳过添加"
else
    # 添加到系统环境
    sudo bash -c 'cat /tmp/wechat_transfer_env >> /etc/environment'
    echo "   ✅ 已添加到 /etc/environment"
fi

# 清理临时文件
rm -f /tmp/wechat_transfer_env

echo -e "\n5. 检查证书文件"
cert_path="$WECHAT_TRANSFER_PRIVATE_KEY_PATH"
if [ -f "$cert_path" ]; then
    echo "   ✅ 证书文件存在: $cert_path"
    
    # 检查文件权限
    file_perms=$(stat -c "%a" "$cert_path" 2>/dev/null)
    if [ "$file_perms" = "600" ] || [ "$file_perms" = "400" ]; then
        echo "   ✅ 文件权限正确: $file_perms"
    else
        echo "   ⚠️  文件权限: $file_perms，建议设置为600"
        echo "      sudo chmod 600 $cert_path"
    fi
else
    echo "   ❌ 证书文件不存在: $cert_path"
    echo "      请确保证书文件已正确部署"
fi

echo -e "\n6. 重启应用服务"
echo "   请选择重启方式："
echo "   A) systemctl 服务"
echo "   B) Docker 容器"
echo "   C) 手动重启"
echo "   D) 跳过重启"

read -p "   请选择 (A/B/C/D): " restart_choice

case $restart_choice in
    [Aa]* )
        read -p "   请输入服务名称: " service_name
        if [ -n "$service_name" ]; then
            sudo systemctl restart "$service_name"
            echo "   ✅ 已重启服务: $service_name"
        else
            echo "   ❌ 服务名称不能为空"
        fi
        ;;
    [Bb]* )
        read -p "   请输入容器名称: " container_name
        if [ -n "$container_name" ]; then
            docker restart "$container_name"
            echo "   ✅ 已重启容器: $container_name"
        else
            echo "   ❌ 容器名称不能为空"
        fi
        ;;
    [Cc]* )
        echo "   ℹ️  请手动重启应用服务"
        ;;
    [Dd]* )
        echo "   ⚠️  跳过重启，请稍后手动重启服务"
        ;;
    * )
        echo "   ⚠️  无效选择，跳过重启"
        ;;
esac

echo -e "\n7. 验证部署结果"
echo "   等待5秒让服务启动..."
sleep 5

echo "   检查环境变量："
env | grep WECHAT_TRANSFER | head -3

echo -e "\n8. 测试建议"
echo "   1️⃣ 检查应用日志："
echo "      tail -f logs/app.log | grep -E '(官方SDK|简化客户端)'"
echo ""
echo "   2️⃣ 测试确认打款功能："
echo "      在管理后台点击'确认打款'按钮"
echo ""
echo "   3️⃣ 观察预期日志："
echo "      - '使用官方SDK客户端 V2'"
echo "      - '发起微信企业付款'"
echo "      - '微信企业付款API调用成功'"

echo -e "\n=== 部署完成 ==="
echo "✅ 微信企业付款配置已部署"
echo "✅ 环境变量已设置"
echo "⚠️  请确保应用服务已重启"
echo "🧪 现在可以测试确认打款功能"