# 准备测试提现数据

## 操作目标
为了测试转账功能，需要将提现单号 `WD20250803000001` 的数据调整为：
- 金额：0.3元（符合微信支付最小实名校验要求）
- 状态：审核通过（可以发起转账）

## 执行的操作

### 1. 查看原始数据
```sql
SELECT id, withdrawal_no, user_id, amount, actual_amount, status, openid, real_name 
FROM withdrawals WHERE withdrawal_no = 'WD20250803000001';
```

**原始状态**：
- ID: 1
- 提现单号: WD20250803000001
- 用户ID: 1
- 金额: 0.01元
- 状态: 7 (转账失败)
- OpenID: oU9Ku7fG1rB7gukJQtIgtkf2y4uw
- 真实姓名: 葛美洁

### 2. 更新提现记录
```sql
UPDATE withdrawals SET 
    amount = 0.30, 
    actual_amount = 0.30, 
    status = 2,
    audit_time = NOW(),
    audit_admin_id = 1,
    audit_remark = '调整金额为0.3元用于测试转账功能',
    transfer_no = NULL,
    wechat_batch_no = NULL,
    transfer_fail_reason = NULL,
    pay_time = NULL,
    pay_admin_id = NULL
WHERE withdrawal_no = 'WD20250803000001';
```

**修改内容**：
- ✅ 金额：0.01元 → 0.30元
- ✅ 实际金额：0.01元 → 0.30元  
- ✅ 状态：7 (转账失败) → 2 (审核通过)
- ✅ 审核时间：设置为当前时间
- ✅ 审核人：设置为管理员ID 1
- ✅ 审核备注：添加说明
- ✅ 清空转账相关字段：transfer_no, wechat_batch_no, transfer_fail_reason, pay_time, pay_admin_id

### 3. 清理转账记录
```sql
-- 查看相关转账记录
SELECT id, withdrawal_id, transfer_no, wechat_batch_no, amount, status 
FROM withdrawal_transfers WHERE withdrawal_id = 1;

-- 删除失败的转账记录
DELETE FROM withdrawal_transfers WHERE withdrawal_id = 1;
```

**清理原因**：
- 删除之前失败的转账记录
- 让系统可以重新发起转账

## 最终状态

### 提现记录 (withdrawals)
| 字段 | 值 | 说明 |
|------|----|----- |
| id | 1 | 提现记录ID |
| withdrawal_no | WD20250803000001 | 提现单号 |
| user_id | 1 | 用户ID |
| amount | 0.30 | 提现金额（元）|
| actual_amount | 0.30 | 实际到账金额（元）|
| status | 2 | 状态：审核通过 |
| openid | oU9Ku7fG1rB7gukJQtIgtkf2y4uw | 微信OpenID |
| real_name | 葛美洁 | 真实姓名 |
| audit_time | 2025-08-08 12:13:11 | 审核时间 |
| audit_admin_id | 1 | 审核人ID |
| audit_remark | 调整金额为0.3元用于测试转账功能 | 审核备注 |

### 转账记录 (withdrawal_transfers)
- ✅ 已清空，无相关记录

## 提现状态说明

根据 `backend/internal/model/const.go` 中的定义：

```go
const (
    WithdrawalStatusPending      = 1 // 待审核
    WithdrawalStatusApproved     = 2 // 审核通过 ← 当前状态
    WithdrawalStatusPaid         = 3 // 已打款
    WithdrawalStatusRejected     = 4 // 已驳回
    WithdrawalStatusTransferring = 5 // 转账中
    WithdrawalStatusReceived     = 6 // 已到账
    WithdrawalStatusTransferFail = 7 // 转账失败
)
```

**当前状态 2 (审核通过)** 表示：
- ✅ 提现申请已通过审核
- ✅ 可以发起转账操作
- ✅ 金额符合微信支付要求（≥ 0.3元）

## 测试准备完成

现在可以进行转账测试：

1. **系统配置**：最小提现金额已调整为0.3元
2. **提现记录**：金额0.3元，状态为审核通过
3. **转账记录**：已清空，可以重新发起
4. **微信配置**：公钥模式已修复，批次号格式已修复

### 预期测试结果
- ✅ 转账金额0.3元符合微信支付要求
- ✅ 不会出现"0.3元以下不支持实名校验"错误
- ✅ 批次号格式正确（TF + 数字）
- ✅ 公钥模式正常工作
- ✅ 转账应该能够成功

### 测试步骤
1. 在管理后台找到提现单号 `WD20250803000001`
2. 点击"确认打款"按钮
3. 观察转账日志，确认无错误
4. 验证转账状态更新

---

**准备完成时间**：2025年8月8日 12:13  
**操作人员**：技术团队  
**状态**：✅ 已完成，可以开始测试