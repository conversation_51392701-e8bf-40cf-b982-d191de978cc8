# 微信支付平台证书问题修复部署指南

## 问题概述

**错误现象**: 点击"确认打款"时返回 `无可用的平台证书，请在商户平台-API安全申请使用微信支付公钥` 错误

**错误原因**: 微信支付API v3需要平台证书来验证响应签名，当前配置缺少或错误配置了平台证书

## 修复内容

### 1. 代码修复 ✅
- 修复了微信转账客户端初始化逻辑
- 启用自动证书下载模式
- 添加了详细的错误处理和日志

### 2. 配置修复 ✅
- 更新生产环境配置文件
- 启用 `use_auto_cert_mode: true`
- 完善环境变量绑定

## 部署步骤

### 第一步：验证当前修复状态

```bash
# 1. 检查配置文件修复状态
grep "use_auto_cert_mode.*true" backend/config/conf/config.prod.yaml

# 2. 运行测试验证修复
cd backend && go run ../test_wechat_platform_cert_simple.go
```

### 第二步：设置生产环境变量

在生产服务器上设置以下环境变量：

```bash
# 微信小程序基础配置
export WECHAT_APP_ID="你的微信小程序AppID"
export WECHAT_MCH_ID="你的微信支付商户号"
export WECHAT_API_V3_KEY="你的微信支付APIv3密钥"

# 证书配置
export WECHAT_CERT_SERIAL_NUMBER="你的商户证书序列号"
export WECHAT_TRANSFER_PRIVATE_KEY_PATH="/etc/peizhen/certs/wechat/apiclient_key.pem"

# 转账功能配置
export WECHAT_TRANSFER_CLIENT_TYPE="official"
export WECHAT_TRANSFER_ENABLED="true"
export WECHAT_TRANSFER_MOCK_MODE="false"
```

### 第三步：验证证书文件

```bash
# 1. 检查私钥文件存在
ls -la /etc/peizhen/certs/wechat/apiclient_key.pem

# 2. 验证文件权限
chmod 600 /etc/peizhen/certs/wechat/apiclient_key.pem

# 3. 验证私钥格式
head -1 /etc/peizhen/certs/wechat/apiclient_key.pem
# 应该显示: -----BEGIN PRIVATE KEY----- 或 -----BEGIN RSA PRIVATE KEY-----
```

### 第四步：微信商户平台配置

1. **登录微信商户平台**
   - 访问: https://pay.weixin.qq.com
   - 使用商户账号登录

2. **申请微信支付公钥**
   - 进入: 账户中心 -> API安全
   - 找到: 微信支付公钥 部分
   - 点击: 申请使用微信支付公钥
   - 按照指引完成申请流程

3. **验证证书序列号**
   - 在API安全页面查看证书序列号
   - 确保与环境变量 `WECHAT_CERT_SERIAL_NUMBER` 一致

### 第五步：重启服务

```bash
# 1. 重启后端服务
sudo systemctl restart peizhen-backend

# 2. 检查服务状态
sudo systemctl status peizhen-backend

# 3. 查看启动日志
tail -f backend/logs/app.prod.log | grep -E "(证书|certificate|transfer|微信)"
```

### 第六步：功能测试

```bash
# 1. 测试转账接口
curl -X POST https://admin.kanghuxing.cn/api/admin/withdrawals/pay \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_admin_token" \
  -d '{"withdrawal_ids": [1]}'

# 2. 检查响应
# 成功响应应该包含:
# {
#   "code": 0,
#   "message": "操作成功",
#   "data": {
#     "success_count": 1,
#     "fail_count": 0,
#     ...
#   }
# }
```

## 验证清单

### ✅ 代码层面
- [x] 启用自动证书下载模式
- [x] 修复客户端初始化逻辑
- [x] 添加详细错误处理

### ✅ 配置层面
- [x] 更新生产配置文件
- [x] 完善环境变量绑定
- [x] 启用官方SDK客户端

### ⚠️ 部署层面（需要在生产环境完成）
- [ ] 设置所有必要的环境变量
- [ ] 验证私钥文件存在且权限正确
- [ ] 在微信商户平台申请微信支付公钥
- [ ] 重启后端服务
- [ ] 测试转账功能

## 故障排除

### 如果仍然出现平台证书错误

1. **检查环境变量**
   ```bash
   echo $WECHAT_CERT_SERIAL_NUMBER
   echo $WECHAT_TRANSFER_PRIVATE_KEY_PATH
   ```

2. **检查私钥文件**
   ```bash
   ls -la $WECHAT_TRANSFER_PRIVATE_KEY_PATH
   openssl rsa -in $WECHAT_TRANSFER_PRIVATE_KEY_PATH -check -noout
   ```

3. **检查网络连接**
   ```bash
   curl -I https://api.mch.weixin.qq.com/v3/certificates
   ```

4. **查看详细日志**
   ```bash
   tail -f backend/logs/app.prod.log | grep -E "(ERROR|WARN|证书|certificate)"
   ```

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|---------|------|---------|
| `无可用的平台证书` | 未申请微信支付公钥 | 在商户平台申请公钥 |
| `证书序列号不匹配` | 环境变量配置错误 | 检查并更新序列号 |
| `私钥文件读取失败` | 文件不存在或权限错误 | 检查文件路径和权限 |
| `网络连接超时` | 网络问题 | 检查防火墙和网络设置 |

## 监控建议

### 1. 添加健康检查
```bash
# 创建转账功能健康检查脚本
cat > /usr/local/bin/check-wechat-transfer.sh << 'EOF'
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" \
  https://admin.kanghuxing.cn/api/admin/health/wechat-transfer)
if [ "$response" != "200" ]; then
  echo "微信转账功能异常: HTTP $response" | \
    mail -s "系统告警" <EMAIL>
fi
EOF
chmod +x /usr/local/bin/check-wechat-transfer.sh

# 添加到crontab
echo "*/5 * * * * /usr/local/bin/check-wechat-transfer.sh" | crontab -
```

### 2. 日志监控
```bash
# 监控证书相关错误
tail -f backend/logs/app.prod.log | grep -E "(证书|certificate|RESOURCE_NOT_EXISTS)" | \
  while read line; do
    echo "$(date): $line" >> /var/log/wechat-cert-errors.log
  done &
```

## 联系支持

如果按照以上步骤操作后仍然遇到问题，请提供以下信息：

1. 错误日志片段
2. 环境变量配置（隐藏敏感信息）
3. 微信商户平台证书状态截图
4. 网络连接测试结果

## 相关文档

- [微信支付API v3证书和回调报文解密](https://pay.weixin.qq.com/doc/v3/merchant/**********)
- [微信支付Go SDK使用指南](https://github.com/wechatpay-apiv3/wechatpay-go)
- [商户平台API安全设置](https://pay.weixin.qq.com/index.php/core/cert/api_cert)