#!/bin/bash

# 验证API认证配置的脚本
# 确保后端和管理后台使用相同的API认证配置

echo "🔧 验证API认证配置"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 1. 检查环境变量文件
echo "步骤1: 检查环境变量文件"

# 检查后端环境变量文件
if [ -f "production.env" ]; then
    echo -e "${GREEN}✅ 后端环境变量文件存在: production.env${NC}"
    
    # 检查关键变量
    if grep -q "BACKEND_API_KEY_ID=peizhen-api-key-2025" production.env; then
        echo -e "${GREEN}✅ 后端API Key ID配置正确${NC}"
    else
        echo -e "${RED}❌ 后端API Key ID配置错误${NC}"
        exit 1
    fi
    
    if grep -q "BACKEND_API_SECRET_KEY=PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1" production.env; then
        echo -e "${GREEN}✅ 后端API Secret Key配置正确${NC}"
    else
        echo -e "${RED}❌ 后端API Secret Key配置错误${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 后端环境变量文件不存在: production.env${NC}"
    exit 1
fi

# 检查管理后台环境变量文件
if [ -f "admin.production.env" ]; then
    echo -e "${GREEN}✅ 管理后台环境变量文件存在: admin.production.env${NC}"
    
    # 检查关键变量
    if grep -q "BACKEND_API_KEY_ID=peizhen-api-key-2025" admin.production.env; then
        echo -e "${GREEN}✅ 管理后台API Key ID配置正确${NC}"
    else
        echo -e "${RED}❌ 管理后台API Key ID配置错误${NC}"
        exit 1
    fi
    
    if grep -q "BACKEND_API_SECRET_KEY=PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1" admin.production.env; then
        echo -e "${GREEN}✅ 管理后台API Secret Key配置正确${NC}"
    else
        echo -e "${RED}❌ 管理后台API Secret Key配置错误${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 管理后台环境变量文件不存在: admin.production.env${NC}"
    exit 1
fi

# 2. 检查配置文件
echo ""
echo "步骤2: 检查配置文件"

# 检查后端配置文件
if [ -f "backend/config/conf/config.prod.yaml" ]; then
    echo -e "${GREEN}✅ 后端配置文件存在${NC}"
    
    if grep -q "key_id: \"\${BACKEND_API_KEY_ID}\"" backend/config/conf/config.prod.yaml; then
        echo -e "${GREEN}✅ 后端配置使用环境变量${NC}"
    else
        echo -e "${RED}❌ 后端配置未使用环境变量${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 后端配置文件不存在${NC}"
    exit 1
fi

# 检查管理后台配置文件
if [ -f "admin/server/config/config.prod.yaml" ]; then
    echo -e "${GREEN}✅ 管理后台配置文件存在${NC}"
    
    if grep -q "key_id: \"\${BACKEND_API_KEY_ID}\"" admin/server/config/config.prod.yaml; then
        echo -e "${GREEN}✅ 管理后台配置使用环境变量${NC}"
    else
        echo -e "${RED}❌ 管理后台配置未使用环境变量${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 管理后台配置文件不存在${NC}"
    exit 1
fi

# 3. 验证密钥强度
echo ""
echo "步骤3: 验证密钥强度"

SECRET_KEY="PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1"
KEY_LENGTH=${#SECRET_KEY}

if [ $KEY_LENGTH -ge 32 ]; then
    echo -e "${GREEN}✅ API密钥长度符合要求 (${KEY_LENGTH} 字符)${NC}"
else
    echo -e "${RED}❌ API密钥长度不足 (${KEY_LENGTH} 字符，需要至少32字符)${NC}"
    exit 1
fi

# 4. 显示配置摘要
echo ""
echo "步骤4: 配置摘要"
echo -e "${YELLOW}API认证配置:${NC}"
echo "  Key ID: peizhen-api-key-2025"
echo "  Secret Key: PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1"
echo "  Algorithm: HMAC-SHA256"
echo "  TTL: 300 seconds"

# 5. 部署建议
echo ""
echo "步骤5: 部署建议"
echo -e "${YELLOW}请按以下顺序部署:${NC}"
echo "1. 加载后端环境变量:"
echo "   source production.env"
echo ""
echo "2. 重启后端服务:"
echo "   cd backend && go build -o bin/peizhen main.go && ./bin/peizhen"
echo ""
echo "3. 加载管理后台环境变量:"
echo "   source admin.production.env"
echo ""
echo "4. 重启管理后台服务:"
echo "   cd admin/server && APP_ENV=prod go run main.go"
echo ""

# 6. 测试建议
echo "步骤6: 测试建议"
echo -e "${YELLOW}部署完成后，可以通过以下方式测试:${NC}"
echo "1. 检查服务启动日志，确认API认证配置加载成功"
echo "2. 在管理后台尝试发起转账操作"
echo "3. 观察后端日志，确认API请求认证成功"

echo ""
echo -e "${GREEN}✅ API认证配置验证完成${NC}"
echo -e "${GREEN}✅ 后端和管理后台现在使用相同的API认证配置${NC}"