#!/bin/bash

# 检查数据库表结构
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

echo "=== 检查数据库表结构 ==="

# 查看orders表结构
echo "1. orders表结构:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "DESCRIBE orders;"
echo ""

# 查看attendant_income表结构
echo "2. attendant_income表结构:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "DESCRIBE attendant_income;"
echo ""

# 查看所有表
echo "3. 所有表列表:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SHOW TABLES;"
echo ""

# 查看特定订单信息
echo "4. 订单ORD202507311203108006的详细信息:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT * FROM orders WHERE order_no = 'ORD202507311203108006';
"
echo ""

# 查看该订单的收入记录
echo "5. 该订单的收入记录:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT ai.* FROM attendant_income ai 
JOIN orders o ON ai.order_id = o.id 
WHERE o.order_no = 'ORD202507311203108006';
"