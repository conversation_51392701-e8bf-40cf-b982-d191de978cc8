#!/bin/bash

# 订单结算问题诊断脚本
# 专门诊断订单 ORD202507311203108006 的结算问题

# 配置
ORDER_NO="ORD202507311203108006"
ATTENDANT_ID=13
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# MySQL 查询函数
run_mysql() {
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$1"
}

echo "=== 订单结算问题诊断 ==="
echo "订单号: $ORDER_NO"
echo "陪诊师ID: $ATTENDANT_ID"
echo "诊断时间: $(date)"
echo ""

# 1. 查询订单基本信息
log_step "1. 查询订单基本信息..."
run_mysql "
SELECT 
    '订单基本信息' as type,
    o.id as order_id,
    o.order_no,
    o.status as order_status,
    o.attendant_id,
    o.total_amount,
    o.created_at,
    o.completion_time,
    o.completion_type,
    CASE 
        WHEN o.status = 13 THEN '✅ 已结算'
        WHEN o.status = 10 THEN '⏳ 审核通过'
        WHEN o.status = 9 THEN '📋 已完成'
        WHEN o.status = 8 THEN '🔄 服务中'
        ELSE CONCAT('❓ 状态: ', o.status)
    END as status_text,
    TIMESTAMPDIFF(HOUR, o.completion_time, NOW()) as hours_since_completion
FROM orders o 
WHERE o.order_no = '$ORDER_NO';
"
echo ""

# 2. 查询陪诊师收入记录
log_step "2. 查询陪诊师收入记录..."
run_mysql "
SELECT 
    '收入记录' as type,
    ai.id as income_id,
    ai.order_id,
    ai.attendant_id,
    ai.amount,
    ai.status as income_status,
    ai.created_at,
    ai.settlement_time,
    CASE 
        WHEN ai.status = 2 THEN '✅ 已结算'
        WHEN ai.status = 1 THEN '⏳ 待结算'
        WHEN ai.status = 0 THEN '❌ 未生成'
        ELSE CONCAT('❓ 状态: ', ai.status)
    END as income_status_text
FROM attendant_income ai
JOIN orders o ON ai.order_id = o.id
WHERE o.order_no = '$ORDER_NO' AND ai.attendant_id = $ATTENDANT_ID;
"
echo ""

# 3. 查询T+2审核记录
log_step "3. 查询T+2审核记录..."
run_mysql "
SELECT 
    '审核记录' as type,
    osr.id as review_id,
    osr.order_id,
    osr.status as review_status,
    osr.review_period_hours,
    osr.created_at,
    osr.expires_at,
    osr.reviewed_at,
    osr.reviewer_id,
    osr.review_notes,
    CASE 
        WHEN osr.status = 'approved' THEN '✅ 审核通过'
        WHEN osr.status = 'pending' THEN '⏳ 待审核'
        WHEN osr.status = 'in_progress' THEN '🔄 审核中'
        WHEN osr.status = 'rejected' THEN '❌ 审核拒绝'
        ELSE CONCAT('❓ 状态: ', osr.status)
    END as review_status_text,
    CASE 
        WHEN osr.expires_at < NOW() THEN '⚠️ 已过期'
        ELSE '✅ 未过期'
    END as expiry_status
FROM order_settlement_reviews osr
JOIN orders o ON osr.order_id = o.id
WHERE o.order_no = '$ORDER_NO'
ORDER BY osr.created_at DESC;
"
echo ""

# 4. 检查自动结算配置
log_step "4. 检查自动结算配置..."
run_mysql "
SELECT 
    '自动结算配置' as type,
    asc.auto_settlement_enabled,
    asc.review_period_hours,
    asc.updated_at,
    asc.updated_by,
    CASE 
        WHEN asc.auto_settlement_enabled = 1 THEN '✅ 已启用'
        ELSE '❌ 未启用'
    END as config_status
FROM auto_settlement_config asc
ORDER BY asc.updated_at DESC
LIMIT 1;
"
echo ""

# 5. 查询陪诊师账户余额
log_step "5. 查询陪诊师账户余额..."
run_mysql "
SELECT 
    '账户余额' as type,
    ab.attendant_id,
    ab.available_balance as 可提现金额,
    ab.frozen_balance as 冻结金额,
    ab.total_income as 总收入,
    ab.total_withdrawn as 总提现,
    ab.updated_at as 更新时间
FROM attendant_balance ab
WHERE ab.attendant_id = $ATTENDANT_ID;
"
echo ""

# 6. 查询相关的结算记录
log_step "6. 查询相关的结算记录..."
run_mysql "
SELECT 
    '结算记录' as type,
    sr.id as settlement_id,
    sr.order_id,
    sr.attendant_id,
    sr.amount,
    sr.status as settlement_status,
    sr.created_at,
    sr.processed_at,
    CASE 
        WHEN sr.status = 'completed' THEN '✅ 已完成'
        WHEN sr.status = 'processing' THEN '🔄 处理中'
        WHEN sr.status = 'failed' THEN '❌ 失败'
        ELSE CONCAT('❓ 状态: ', sr.status)
    END as settlement_status_text
FROM settlement_records sr
JOIN orders o ON sr.order_id = o.id
WHERE o.order_no = '$ORDER_NO'
ORDER BY sr.created_at DESC;
"
echo ""

# 7. 检查是否有异常记录
log_step "7. 检查异常处理记录..."
run_mysql "
SELECT 
    '异常记录' as type,
    aor.id as abnormal_id,
    aor.order_id,
    aor.issue_type,
    aor.description,
    aor.status as abnormal_status,
    aor.created_at,
    aor.resolved_at,
    CASE 
        WHEN aor.status = 'resolved' THEN '✅ 已解决'
        WHEN aor.status = 'pending' THEN '⏳ 待处理'
        ELSE CONCAT('❓ 状态: ', aor.status)
    END as abnormal_status_text
FROM abnormal_order_records aor
JOIN orders o ON aor.order_id = o.id
WHERE o.order_no = '$ORDER_NO'
ORDER BY aor.created_at DESC;
"
echo ""

# 8. 分析问题原因
log_step "8. 问题分析..."

# 获取订单状态
ORDER_STATUS=$(run_mysql "SELECT status FROM orders WHERE order_no = '$ORDER_NO';" | tail -n 1)
COMPLETION_TIME=$(run_mysql "SELECT completion_time FROM orders WHERE order_no = '$ORDER_NO';" | tail -n 1)
HOURS_SINCE=$(run_mysql "SELECT TIMESTAMPDIFF(HOUR, completion_time, NOW()) FROM orders WHERE order_no = '$ORDER_NO';" | tail -n 1)

# 获取收入记录状态
INCOME_STATUS=$(run_mysql "SELECT ai.status FROM attendant_income ai JOIN orders o ON ai.order_id = o.id WHERE o.order_no = '$ORDER_NO' AND ai.attendant_id = $ATTENDANT_ID;" | tail -n 1)

# 获取审核记录状态
REVIEW_STATUS=$(run_mysql "SELECT osr.status FROM order_settlement_reviews osr JOIN orders o ON osr.order_id = o.id WHERE o.order_no = '$ORDER_NO' ORDER BY osr.created_at DESC LIMIT 1;" | tail -n 1)

# 获取自动结算配置
AUTO_ENABLED=$(run_mysql "SELECT auto_settlement_enabled FROM auto_settlement_config ORDER BY updated_at DESC LIMIT 1;" | tail -n 1)

echo "=== 问题分析结果 ==="
echo "• 订单状态: $ORDER_STATUS"
echo "• 完成时间: $COMPLETION_TIME"
echo "• 完成后小时数: $HOURS_SINCE"
echo "• 收入记录状态: $INCOME_STATUS"
echo "• 审核记录状态: $REVIEW_STATUS"
echo "• 自动结算启用: $AUTO_ENABLED"
echo ""

# 问题诊断
echo "=== 问题诊断 ==="

if [ "$ORDER_STATUS" != "13" ]; then
    log_error "问题1: 订单状态不是已结算(13)，当前状态: $ORDER_STATUS"
fi

if [ "$INCOME_STATUS" != "2" ]; then
    log_error "问题2: 收入记录状态不是已结算(2)，当前状态: $INCOME_STATUS"
fi

if [ "$REVIEW_STATUS" != "approved" ]; then
    log_error "问题3: T+2审核状态不是已通过，当前状态: $REVIEW_STATUS"
fi

if [ "$AUTO_ENABLED" != "1" ]; then
    log_error "问题4: 自动结算未启用"
fi

if [ -n "$HOURS_SINCE" ] && [ "$HOURS_SINCE" -gt 24 ]; then
    log_error "问题5: 订单完成超过24小时但未结算"
fi

echo ""
log_info "诊断完成，请根据上述问题进行修复"