#!/bin/bash

echo "=== 服务器微信企业付款配置验证 ==="

echo "1. 检查环境变量文件"
ENV_FILE="/etc/peizhen/production.env"
if [ -f "$ENV_FILE" ]; then
    echo "   ✅ 环境变量文件存在: $ENV_FILE"
    echo "   微信转账配置数量: $(grep -c WECHAT_TRANSFER $ENV_FILE)"
else
    echo "   ❌ 环境变量文件不存在: $ENV_FILE"
    exit 1
fi

echo -e "\n2. 加载环境变量到当前会话"
set -a
source $ENV_FILE
set +a
echo "   ✅ 已加载环境变量"

echo -e "\n3. 验证关键环境变量"
required_vars=(
    "WECHAT_TRANSFER_CLIENT_TYPE"
    "WECHAT_TRANSFER_APP_ID"
    "WECHAT_TRANSFER_MCH_ID"
    "WECHAT_TRANSFER_APIV3_KEY"
    "WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER"
    "WECHAT_TRANSFER_PRIVATE_KEY_PATH"
    "WECHAT_TRANSFER_ENVIRONMENT"
    "WECHAT_TRANSFER_NOTIFY_URL"
)

all_set=true
for var in "${required_vars[@]}"; do
    if [ -n "${!var}" ]; then
        echo "   ✅ $var = ${!var}"
    else
        echo "   ❌ $var = (未设置)"
        all_set=false
    fi
done

echo -e "\n4. 检查证书文件"
cert_path="$WECHAT_TRANSFER_PRIVATE_KEY_PATH"
if [ -f "$cert_path" ]; then
    echo "   ✅ 证书文件存在: $cert_path"
    
    # 检查文件权限
    file_perms=$(stat -c "%a" "$cert_path" 2>/dev/null)
    if [ "$file_perms" = "600" ] || [ "$file_perms" = "400" ]; then
        echo "   ✅ 文件权限正确: $file_perms"
    else
        echo "   ⚠️  文件权限: $file_perms，建议设置为600"
    fi
    
    # 检查文件大小
    file_size=$(stat -c "%s" "$cert_path" 2>/dev/null)
    if [ "$file_size" -gt 100 ]; then
        echo "   ✅ 证书文件大小正常: ${file_size} bytes"
    else
        echo "   ⚠️  证书文件可能为空或损坏: ${file_size} bytes"
    fi
else
    echo "   ❌ 证书文件不存在: $cert_path"
    all_set=false
fi

echo -e "\n5. 检查服务状态"
# 检查可能的服务名称
service_names=("peizhen-backend" "backend" "peizhen" "go-app")
service_found=false

for service in "${service_names[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        echo "   ✅ 服务运行中: $service"
        echo "   服务状态: $(systemctl is-active $service)"
        service_found=true
        
        # 检查服务启动时间
        start_time=$(systemctl show -p ActiveEnterTimestamp --value "$service")
        echo "   启动时间: $start_time"
        break
    fi
done

if [ "$service_found" = false ]; then
    echo "   ⚠️  未找到运行中的后端服务"
    echo "   请检查服务名称或手动启动服务"
fi

echo -e "\n6. 检查进程环境变量"
# 查找Go进程
go_pids=$(pgrep -f "go.*main" 2>/dev/null || pgrep -f "peizhen" 2>/dev/null || pgrep -f "backend" 2>/dev/null)
if [ -n "$go_pids" ]; then
    echo "   ✅ 找到Go进程: $go_pids"
    
    # 检查第一个进程的环境变量
    first_pid=$(echo $go_pids | cut -d' ' -f1)
    if [ -f "/proc/$first_pid/environ" ]; then
        wechat_env_count=$(tr '\0' '\n' < /proc/$first_pid/environ | grep -c WECHAT_TRANSFER 2>/dev/null || echo "0")
        if [ "$wechat_env_count" -gt 0 ]; then
            echo "   ✅ 进程已加载微信转账环境变量: $wechat_env_count 个"
        else
            echo "   ❌ 进程未加载微信转账环境变量"
            all_set=false
        fi
    else
        echo "   ⚠️  无法读取进程环境变量"
    fi
else
    echo "   ⚠️  未找到Go进程"
fi

echo -e "\n7. 检查应用日志"
log_paths=(
    "/var/log/peizhen/app.log"
    "/var/www/html/peizhen/logs/app.log"
    "/opt/peizhen/logs/app.log"
    "logs/app.log"
)

log_found=false
for log_path in "${log_paths[@]}"; do
    if [ -f "$log_path" ]; then
        echo "   ✅ 找到日志文件: $log_path"
        
        # 检查最近的微信转账相关日志
        recent_logs=$(tail -100 "$log_path" | grep -E "(官方SDK|简化客户端|FUNCTION_DISABLED|微信企业付款)" | tail -3)
        if [ -n "$recent_logs" ]; then
            echo "   最近的微信转账日志:"
            echo "$recent_logs" | sed 's/^/      /'
        else
            echo "   ⚠️  未找到微信转账相关日志"
        fi
        log_found=true
        break
    fi
done

if [ "$log_found" = false ]; then
    echo "   ⚠️  未找到应用日志文件"
fi

echo -e "\n8. 验证结果总结"
if [ "$all_set" = true ]; then
    echo "   ✅ 所有配置检查通过"
    echo "   ✅ 环境变量已正确设置"
    echo "   ✅ 证书文件存在且权限正确"
    
    echo -e "\n9. 下一步操作建议"
    echo "   1️⃣ 如果服务未重启，请重启服务:"
    echo "      sudo systemctl restart peizhen-backend"
    echo ""
    echo "   2️⃣ 测试确认打款功能:"
    echo "      在管理后台点击'确认打款'按钮"
    echo ""
    echo "   3️⃣ 观察应用日志:"
    echo "      tail -f /var/log/peizhen/app.log | grep -E '(官方SDK|微信企业付款)'"
    
else
    echo "   ❌ 存在配置问题，请检查上述错误"
    echo "   ❌ 建议修复问题后重新验证"
fi

echo -e "\n=== 验证完成 ==="