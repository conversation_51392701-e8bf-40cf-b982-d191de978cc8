#!/bin/bash

# 服务器端结算功能测试脚本
# 使用方法：在服务器上执行此脚本

set -e  # 遇到错误立即退出

echo "=== 陪诊师收入结算功能测试 ==="

# 配置
BASE_URL="https://www.kanghuxing.cn"
ADMIN_API_URL="${BASE_URL}/api/admin"
API_URL="${BASE_URL}/api/v1"
ORDER_ID=10017
ATTENDANT_ID=13

# 数据库配置
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

echo "1. 检查当前订单和收入状态..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    '订单状态' as type,
    o.id,
    o.order_no,
    o.status,
    o.settlement_status,
    o.attendant_amount,
    o.settlement_time
FROM orders o 
WHERE o.id = $ORDER_ID

UNION ALL

SELECT 
    '收入状态' as type,
    ai.id,
    CONCAT('PZ', ai.order_id) as order_no,
    ai.status,
    0 as settlement_status,
    ai.net_amount,
    ai.settle_time
FROM attendant_income ai 
WHERE ai.attendant_id = $ATTENDANT_ID AND ai.order_id = $ORDER_ID;
"

echo -e "\n2. 检查审核记录和自动结算配置..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    'T+2审核记录' as type,
    orr.id,
    orr.order_id,
    orr.completion_type,
    orr.status,
    orr.created_at,
    TIMESTAMPDIFF(HOUR, orr.created_at, NOW()) as hours_since_created
FROM order_review_records orr
WHERE orr.order_id = $ORDER_ID

UNION ALL

SELECT 
    '自动结算配置' as type,
    asc.id,
    0 as order_id,
    0 as completion_type,
    CASE WHEN asc.config_value = 'true' THEN 1 ELSE 0 END as status,
    asc.updated_at,
    CAST(CASE WHEN asc.config_key = 'review_period_hours' THEN asc.config_value ELSE '0' END AS SIGNED) as hours_config
FROM auto_settlement_configs asc
WHERE asc.config_key IN ('auto_settlement_enabled', 'review_period_hours');
"

echo -e "\n3. 尝试获取管理员JWT Token..."
# 这里需要你提供管理员登录凭据
read -p "请输入管理员用户名: " ADMIN_USERNAME
read -s -p "请输入管理员密码: " ADMIN_PASSWORD
echo

# 获取JWT Token
echo "正在获取JWT Token..."
LOGIN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"username\": \"$ADMIN_USERNAME\",
    \"password\": \"$ADMIN_PASSWORD\"
  }")

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
JWT_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token // empty')

if [ -z "$JWT_TOKEN" ] || [ "$JWT_TOKEN" = "null" ]; then
    echo "❌ 获取JWT Token失败，请检查用户名和密码"
    echo "登录响应: $LOGIN_RESPONSE"
    exit 1
fi

echo "✅ JWT Token获取成功"

echo -e "\n4. 触发自动结算..."
SETTLEMENT_RESPONSE=$(curl -s -X POST "${ADMIN_API_URL}/reviews/trigger-settlement" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "自动结算响应: $SETTLEMENT_RESPONSE"

echo -e "\n5. 等待3秒后检查结算结果..."
sleep 3

mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    '结算后订单状态' as type,
    o.id,
    o.order_no,
    o.status,
    o.settlement_status,
    o.attendant_amount,
    o.settlement_time
FROM orders o 
WHERE o.id = $ORDER_ID

UNION ALL

SELECT 
    '结算后收入状态' as type,
    ai.id,
    CONCAT('PZ', ai.order_id) as order_no,
    ai.status,
    0 as settlement_status,
    ai.net_amount,
    ai.settle_time
FROM attendant_income ai 
WHERE ai.attendant_id = $ATTENDANT_ID AND ai.order_id = $ORDER_ID;
"

echo -e "\n6. 检查结算批次记录..."
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    sb.id,
    sb.batch_no,
    sb.settlement_date,
    sb.total_orders,
    sb.total_amount,
    sb.attendant_amount,
    sb.status,
    sb.created_at
FROM settlement_batches sb
ORDER BY sb.created_at DESC
LIMIT 3;
"

echo -e "\n7. 如果自动结算未触发，尝试手动单个订单结算..."
if [ "$JWT_TOKEN" != "" ]; then
    MANUAL_SETTLEMENT_RESPONSE=$(curl -s -X POST "${API_URL}/settlement/process" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $JWT_TOKEN" \
      -d "{
        \"order_id\": $ORDER_ID
      }")
    
    echo "手动结算响应: $MANUAL_SETTLEMENT_RESPONSE"
    
    echo -e "\n等待3秒后检查手动结算结果..."
    sleep 3
    
    mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
    SELECT 
        '手动结算后状态' as type,
        o.id,
        o.order_no,
        o.status,
        o.settlement_status,
        o.attendant_amount,
        o.settlement_time,
        ai.status as income_status,
        ai.settle_time as income_settle_time
    FROM orders o 
    LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = $ATTENDANT_ID
    WHERE o.id = $ORDER_ID;
    "
fi

echo -e "\n=== 测试完成 ==="
echo "请检查上述输出结果："
echo "- 订单状态应该从 10 变为 13 (已结算)"
echo "- 收入状态应该从 1 变为 2 (已结算)"
echo "- 应该有结算时间记录"
echo "- 陪诊师收入页面应该不再显示'正在结算中'"