-- 检查订单ORD202507311203108006的详细状态
-- 连接生产数据库: mysql -h 39.107.58.211 -u carego_prod_user -p carego_prod

-- 1. 检查订单基本信息
SELECT 
    id,
    order_no,
    status,
    settlement_status,
    created_at,
    updated_at,
    completion_time,
    appointment_time,
    total_amount,
    attendant_id
FROM orders 
WHERE order_no = 'ORD202507311203108006';

-- 2. 检查订单审核记录
SELECT 
    id,
    order_id,
    review_status,
    review_type,
    created_at,
    reviewed_at,
    auto_reviewed,
    reviewer_id,
    review_notes
FROM order_review_records 
WHERE order_id = (SELECT id FROM orders WHERE order_no = 'ORD202507311203108006');

-- 3. 检查T+2审核记录
SELECT 
    id,
    order_id,
    review_status,
    completion_type,
    created_at,
    reviewed_at,
    auto_settlement_eligible,
    settlement_triggered_at
FROM order_settlement_reviews 
WHERE order_id = (SELECT id FROM orders WHERE order_no = 'ORD202507311203108006');

-- 4. 检查自动分账配置
SELECT 
    id,
    enabled,
    review_period_hours,
    auto_review_threshold,
    created_at,
    updated_at,
    updated_by,
    update_reason
FROM auto_settlement_configs 
ORDER BY created_at DESC 
LIMIT 5;

-- 5. 检查结算记录
SELECT 
    id,
    order_id,
    settlement_status,
    settlement_amount,
    created_at,
    settled_at,
    settlement_type
FROM settlements 
WHERE order_id = (SELECT id FROM orders WHERE order_no = 'ORD202507311203108006');

-- 6. 检查是否有相关的定时任务日志
SELECT 
    id,
    task_name,
    status,
    created_at,
    completed_at,
    error_message
FROM scheduler_logs 
WHERE task_name LIKE '%settlement%' 
   OR task_name LIKE '%review%'
ORDER BY created_at DESC 
LIMIT 10;

-- 7. 检查最近的T+2到期订单（用于验证调度器是否正常工作）
SELECT 
    o.id,
    o.order_no,
    o.status,
    o.settlement_status,
    o.completion_time,
    TIMESTAMPDIFF(HOUR, o.completion_time, NOW()) as hours_since_completion
FROM orders o
WHERE o.status = 10  -- 已完成状态
  AND o.settlement_status = 0  -- 未结算
  AND o.completion_time IS NOT NULL
  AND TIMESTAMPDIFF(HOUR, o.completion_time, NOW()) >= 48  -- 超过48小时
ORDER BY o.completion_time DESC
LIMIT 20;
