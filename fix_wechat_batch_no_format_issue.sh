#!/bin/bash

# 修复微信支付批次号格式问题
# 问题：批次号包含下划线，违反微信支付API规范

echo "🔧 修复微信支付批次号格式问题"
echo "=================================="

# 1. 问题分析
echo -e "\n1. 问题分析："
echo "   ❌ 错误信息：商家批次单号字符串规则校验失败，字符串只能是数字和字母"
echo "   ❌ 当前格式：BATCH_1754619176523209950 (包含下划线)"
echo "   ✅ 正确格式：BATCH1754619176523209950 (只有数字和字母)"

# 2. 检查当前代码
echo -e "\n2. 检查当前代码："
if grep -q "BATCH_%d" backend/internal/service/impl/wechat_transfer_service_impl.go; then
    echo "   ❌ 发现问题：批次号生成包含下划线"
    echo "   位置：backend/internal/service/impl/wechat_transfer_service_impl.go"
    echo "   函数：generateBatchNo()"
else
    echo "   ✅ 批次号格式已修复"
fi

if grep -q "DETAIL_%d" backend/internal/service/impl/wechat_transfer_service_impl.go; then
    echo "   ❌ 发现问题：明细号生成包含下划线"
    echo "   位置：backend/internal/service/impl/wechat_transfer_service_impl.go"
    echo "   函数：generateDetailNo()"
else
    echo "   ✅ 明细号格式已修复"
fi

# 3. 验证修复
echo -e "\n3. 验证修复："
echo "   检查修复后的代码..."

# 检查批次号生成函数
if grep -q "BATCH%d" backend/internal/service/impl/wechat_transfer_service_impl.go; then
    echo "   ✅ 批次号格式修复成功：BATCH%d"
else
    echo "   ❌ 批次号格式修复失败"
fi

# 检查明细号生成函数
if grep -q "DETAIL%d" backend/internal/service/impl/wechat_transfer_service_impl.go; then
    echo "   ✅ 明细号格式修复成功：DETAIL%d"
else
    echo "   ❌ 明细号格式修复失败"
fi

# 4. 编译检查
echo -e "\n4. 编译检查："
echo "   开始编译后端服务..."

cd backend
if go build -o /tmp/peizhen_batch_fix_test main.go; then
    echo "   ✅ 编译成功"
    rm -f /tmp/peizhen_batch_fix_test
else
    echo "   ❌ 编译失败，请检查代码"
    cd ..
    exit 1
fi
cd ..

# 5. 格式对比
echo -e "\n5. 格式对比："
echo "=================================="
echo "修复前："
echo "  批次号：BATCH_1754619176523209950  ❌ 包含下划线"
echo "  明细号：DETAIL_1754619176523212469 ❌ 包含下划线"
echo ""
echo "修复后："
echo "  批次号：BATCH1754619176523209950   ✅ 只有数字和字母"
echo "  明细号：DETAIL1754619176523212469  ✅ 只有数字和字母"

# 6. 微信支付API规范
echo -e "\n6. 微信支付API规范："
echo "=================================="
echo "根据微信支付官方文档："
echo "- out_batch_no（商家批次单号）：只能包含数字和字母"
echo "- out_detail_no（商家明细单号）：只能包含数字和字母"
echo "- 长度限制：1-32位"
echo "- 不能包含特殊字符：下划线(_)、连字符(-)、空格等"

# 7. 测试建议
echo -e "\n7. 测试建议："
echo "=================================="
echo "1. 重启服务使修改生效"
echo "2. 发起一笔小额转账测试"
echo "3. 检查日志确认批次号格式正确"
echo "4. 验证转账是否成功"

# 8. 示例代码
echo -e "\n8. 修复的代码示例："
echo "=================================="
echo "修复前："
echo '  return fmt.Sprintf("BATCH_%d", time.Now().UnixNano())'
echo ""
echo "修复后："
echo '  return fmt.Sprintf("BATCH%d", time.Now().UnixNano())'

echo -e "\n🎉 批次号格式修复完成！"
echo "=================================="
echo "✅ 已修复的问题："
echo "1. 批次号生成函数：移除下划线"
echo "2. 明细号生成函数：移除下划线"
echo "3. 符合微信支付API规范"
echo ""
echo "📋 下一步操作："
echo "1. 重启后端服务"
echo "2. 测试转账功能"
echo "3. 监控转账日志"

echo -e "\n✨ 准备就绪，可以重新测试转账功能！"