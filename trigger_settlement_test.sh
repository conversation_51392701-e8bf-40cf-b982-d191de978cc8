#!/bin/bash

# T+2自动结算完整测试脚本
# 用于测试自动结算系统的完整流程
# 使用方法: ./trigger_settlement_test.sh admin_username admin_password [order_id]

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    echo "使用方法: $0 <admin_username> <admin_password> [order_id]"
    echo "示例:"
    echo "  $0 admin Pei<PERSON>hen@Admin2024"
    echo "  $0 admin Pei<PERSON>hen@Admin2024 10017"
    exit 1
fi

BASE_URL="https://www.kanghuxing.cn"
ADMIN_USERNAME=$1
ADMIN_PASSWORD=$2
TEST_ORDER_ID=${3:-10017}  # 默认测试订单ID

echo "=== T+2自动结算完整测试 ==="
echo "测试时间: $(date)"
echo "目标服务器: $BASE_URL"
echo "测试订单ID: $TEST_ORDER_ID"
echo ""

# 1. 登录获取Token
log_step "1. 获取认证Token..."
LOGIN_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$ADMIN_USERNAME\", \"password\": \"$ADMIN_PASSWORD\"}")

JWT_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token // .data.token // empty')

if [ -z "$JWT_TOKEN" ] || [ "$JWT_TOKEN" = "null" ]; then
    log_error "登录失败: $LOGIN_RESPONSE"
    exit 1
fi

log_info "登录成功"
echo ""

# 2. 检查自动结算配置
log_step "2. 检查自动结算配置..."
CONFIG_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/admin/auto-settlement/config" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "当前配置:"
echo "$CONFIG_RESPONSE" | jq '.' 2>/dev/null || echo "$CONFIG_RESPONSE"

# 检查是否启用自动结算
AUTO_ENABLED=$(echo $CONFIG_RESPONSE | jq -r '.data.auto_settlement_enabled // .auto_settlement_enabled // false')
if [ "$AUTO_ENABLED" = "true" ]; then
    log_info "自动结算已启用"
else
    log_warn "自动结算未启用"
fi
echo ""

# 3. 查询待结算订单
log_step "3. 查询待结算订单..."
PENDING_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/admin/t2-review/pending-reviews?limit=10" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "待结算订单:"
echo "$PENDING_RESPONSE" | jq '.' 2>/dev/null || echo "$PENDING_RESPONSE"

PENDING_COUNT=$(echo $PENDING_RESPONSE | jq -r '.data.total // .total // 0')
log_info "找到 $PENDING_COUNT 个待结算订单"
echo ""

# 4. 查询测试订单当前状态
log_step "4. 查询测试订单当前状态..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '结算前状态' as type,
    o.id as order_id,
    o.order_no,
    o.status as order_status,
    o.completion_time,
    ai.status as income_status,
    ai.amount as income_amount,
    CASE 
        WHEN o.status = 13 THEN '已结算'
        WHEN o.status = 10 THEN '审核通过'
        WHEN o.status = 9 THEN '已完成'
        ELSE CONCAT('状态: ', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 2 THEN '收入已结算'
        WHEN ai.status = 1 THEN '收入待结算'
        ELSE CONCAT('收入状态: ', IFNULL(ai.status, 'NULL'))
    END as income_status_text
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id
WHERE o.id = $TEST_ORDER_ID;
"
echo ""

# 5. 触发T+2审核检查（模拟调度器）
log_step "5. 触发T+2审核检查..."
T2_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/admin/t2-review/trigger-manual" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "T+2审核触发响应:"
echo "$T2_RESPONSE" | jq '.' 2>/dev/null || echo "$T2_RESPONSE"

T2_SUCCESS=$(echo $T2_RESPONSE | jq -r '.success // .data.success // false')
if [ "$T2_SUCCESS" = "true" ]; then
    log_info "T+2审核触发成功"
else
    log_warn "T+2审核触发可能失败，继续测试..."
fi
echo ""

# 6. 触发手动结算（针对特定订单）
log_step "6. 触发特定订单结算..."
SETTLEMENT_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/settlement/process" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "{\"order_id\": $TEST_ORDER_ID}")

echo "结算触发响应:"
echo "$SETTLEMENT_RESPONSE" | jq '.' 2>/dev/null || echo "$SETTLEMENT_RESPONSE"

SETTLEMENT_SUCCESS=$(echo $SETTLEMENT_RESPONSE | jq -r '.success // .data.success // false')
if [ "$SETTLEMENT_SUCCESS" = "true" ]; then
    log_info "订单结算触发成功"
else
    log_warn "订单结算触发可能失败"
fi
echo ""

# 7. 等待处理完成
log_step "7. 等待结算处理完成..."
sleep 5

# 8. 检查结算后状态
log_step "8. 检查结算后状态..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '结算后状态' as type,
    o.id as order_id,
    o.order_no,
    o.status as order_status,
    o.completion_time,
    ai.status as income_status,
    ai.amount as income_amount,
    ai.settlement_time,
    CASE 
        WHEN o.status = 13 THEN '✅ 订单已结算'
        WHEN o.status = 10 THEN '⏳ 订单审核通过'
        WHEN o.status = 9 THEN '📋 订单已完成'
        ELSE CONCAT('❓ 订单状态: ', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 2 THEN '✅ 收入已结算'
        WHEN ai.status = 1 THEN '⏳ 收入待结算'
        ELSE CONCAT('❓ 收入状态: ', IFNULL(ai.status, 'NULL'))
    END as income_status_text
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id
WHERE o.id = $TEST_ORDER_ID;
"
echo ""

# 9. 查询结算历史记录
log_step "9. 查询最近的结算历史记录..."
HISTORY_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/admin/settlement/history?limit=5" \
  -H "Authorization: Bearer $JWT_TOKEN")

echo "最近结算记录:"
echo "$HISTORY_RESPONSE" | jq '.' 2>/dev/null || echo "$HISTORY_RESPONSE"
echo ""

# 10. 检查审核记录
log_step "10. 检查T+2审核记录..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '审核记录' as type,
    osr.id as review_id,
    osr.order_id,
    osr.status as review_status,
    osr.review_period_hours,
    osr.created_at,
    osr.expires_at,
    osr.reviewed_at,
    CASE 
        WHEN osr.status = 'approved' THEN '✅ 审核通过'
        WHEN osr.status = 'pending' THEN '⏳ 待审核'
        WHEN osr.status = 'in_progress' THEN '🔄 审核中'
        ELSE CONCAT('❓ 状态: ', osr.status)
    END as review_status_text
FROM order_settlement_reviews osr
WHERE osr.order_id = $TEST_ORDER_ID
ORDER BY osr.created_at DESC
LIMIT 3;
"
echo ""

# 11. 验证结算完整性
log_step "11. 验证结算完整性..."
mysql -h 39.107.58.211 -u carego_prod_user -p'4leJXDlbDRMiRYutuhCmlebljrPeTTvR!' carego_prod -e "
SELECT 
    '完整性检查' as type,
    o.id as order_id,
    o.total_amount as order_amount,
    ai.amount as income_amount,
    (o.total_amount - IFNULL(ai.amount, 0)) as platform_amount,
    CASE 
        WHEN o.status = 13 AND ai.status = 2 THEN '✅ 结算完整'
        WHEN o.status = 13 AND ai.status != 2 THEN '⚠️ 订单已结算但收入未结算'
        WHEN o.status != 13 AND ai.status = 2 THEN '⚠️ 收入已结算但订单未结算'
        ELSE '❌ 结算不完整'
    END as integrity_status
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id
WHERE o.id = $TEST_ORDER_ID;
"
echo ""

# 12. 最终状态汇总
log_step "12. 最终状态汇总..."
FINAL_PENDING_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/admin/t2-review/pending-reviews?limit=10" \
  -H "Authorization: Bearer $JWT_TOKEN")

FINAL_PENDING_COUNT=$(echo $FINAL_PENDING_RESPONSE | jq -r '.data.total // .total // 0')

echo "=== 测试结果汇总 ==="
echo "• 测试订单ID: $TEST_ORDER_ID"
echo "• 自动结算状态: $AUTO_ENABLED"
echo "• 结算前待处理订单: $PENDING_COUNT"
echo "• 结算后待处理订单: $FINAL_PENDING_COUNT"
echo "• T+2审核触发: $T2_SUCCESS"
echo "• 订单结算触发: $SETTLEMENT_SUCCESS"

if [ "$FINAL_PENDING_COUNT" -lt "$PENDING_COUNT" ]; then
    log_info "✅ 待处理订单数量减少，结算可能成功"
else
    log_warn "⚠️ 待处理订单数量未减少，请检查结算逻辑"
fi

echo ""
log_info "T+2自动结算完整测试完成"
echo "=== 测试结束 ==="