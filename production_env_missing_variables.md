# 生产环境缺失的环境变量分析

## 当前 production.env 分析

### ✅ 已存在的关键变量
```bash
# 基础配置
APP_ENV=prod
WECHAT_APP_ID=wx2ce88b500238c799
WECHAT_MCH_ID=1717184423
WECHAT_API_V3_KEY=0C9821B2517645438B93B1B21CC62901
WECHAT_SERIAL_NO=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247
WECHAT_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem

# 转账配置
WECHAT_TRANSFER_CLIENT_TYPE=official
WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247
WECHAT_TRANSFER_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem
WECHAT_TRANSFER_ENABLED=true
WECHAT_TRANSFER_MOCK_MODE=false
```

### ❌ 需要补充的关键变量

根据配置文件分析，需要添加以下环境变量：

```bash
# 1. 证书序列号（统一命名）
WECHAT_CERT_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247

# 2. 内部API配置
INTERNAL_API_KEY=your_internal_api_key_here
BACKEND_API_KEY_ID=admin-key-prod-001
BACKEND_API_SECRET_KEY=your-32-char-secret-key-here-123456789012

# 3. 自动证书下载模式（修复平台证书问题）
APP_WECHAT_USE_AUTO_CERT_MODE=true

# 4. 转账相关的完整配置
WECHAT_TRANSFER_APIV3_KEY=0C9821B2517645438B93B1B21CC62901
WECHAT_TRANSFER_APP_ID=wx2ce88b500238c799
WECHAT_TRANSFER_MCH_ID=1717184423
```

### ⚠️ 需要修正的变量

```bash
# 修正前
APP_WECHAT_USE_AUTO_CERT_MODE=false

# 修正后（启用自动证书下载）
APP_WECHAT_USE_AUTO_CERT_MODE=true
```

## 完整的补充方案

### 方案1：直接添加到 production.env
在 `production.env` 文件末尾添加：

```bash
# 证书序列号统一配置
WECHAT_CERT_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247

# 内部API安全配置
INTERNAL_API_KEY=s137xrkVgGxJTX6gitt1DZ29j9tEZRF5iCOsPKIE
BACKEND_API_KEY_ID=admin-key-prod-001
BACKEND_API_SECRET_KEY=peizhen-backend-api-secret-key-2024-prod-32chars

# 启用自动证书下载（修复平台证书问题）
# APP_WECHAT_USE_AUTO_CERT_MODE=true  # 修改现有行

# 转账配置补充
WECHAT_TRANSFER_APIV3_KEY=0C9821B2517645438B93B1B21CC62901
WECHAT_TRANSFER_APP_ID=wx2ce88b500238c799
WECHAT_TRANSFER_MCH_ID=1717184423
```

### 方案2：使用脚本自动补充
```bash
#!/bin/bash
# 自动补充缺失的环境变量

ENV_FILE="production.env"

# 备份原文件
cp $ENV_FILE ${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)

# 添加缺失的变量
cat >> $ENV_FILE << 'EOF'

# === 补充的环境变量 ===
# 证书序列号统一配置
WECHAT_CERT_SERIAL_NUMBER=3B2F1BB6FBF9CD4D2448AB6310720C15CD668247

# 内部API安全配置
INTERNAL_API_KEY=s137xrkVgGxJTX6gitt1DZ29j9tEZRF5iCOsPKIE
BACKEND_API_KEY_ID=admin-key-prod-001
BACKEND_API_SECRET_KEY=peizhen-backend-api-secret-key-2024-prod-32chars

# 转账配置补充
WECHAT_TRANSFER_APIV3_KEY=0C9821B2517645438B93B1B21CC62901
WECHAT_TRANSFER_APP_ID=wx2ce88b500238c799
WECHAT_TRANSFER_MCH_ID=1717184423
EOF

# 修改现有变量
sed -i 's/APP_WECHAT_USE_AUTO_CERT_MODE=false/APP_WECHAT_USE_AUTO_CERT_MODE=true/' $ENV_FILE

echo "环境变量补充完成"
```