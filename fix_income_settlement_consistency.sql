-- 修复收入结算数据一致性问题
-- 用于修复订单表和陪诊师收入表之间的数据不一致

-- 1. 检查数据不一致的记录
SELECT 
    ai.id as income_id,
    ai.attendant_id,
    ai.order_id,
    o.order_no,
    ai.status as income_status,
    o.settlement_status as order_settlement_status,
    ai.settle_time as income_settle_time,
    o.settlement_time as order_settlement_time,
    CASE 
        WHEN ai.status = 1 THEN '待结算'
        WHEN ai.status = 2 THEN '已结算'
        WHEN ai.status = 3 THEN '已提现'
        ELSE '未知状态'
    END as income_status_text,
    CASE 
        WHEN o.settlement_status = 0 THEN '未结算'
        WHEN o.settlement_status = 1 THEN '已结算'
        ELSE '未知状态'
    END as order_status_text
FROM attendant_income ai
JOIN orders o ON ai.order_id = o.id
WHERE (o.settlement_status = 1 AND ai.status = 1) -- 订单已结算但收入记录未结算
   OR (o.settlement_status = 0 AND ai.status = 2) -- 订单未结算但收入记录已结算
ORDER BY ai.created_at DESC;

-- 2. 修复数据不一致：将已结算订单对应的收入记录状态更新为已结算
UPDATE attendant_income ai
JOIN orders o ON ai.order_id = o.id
SET 
    ai.status = 2,
    ai.settle_time = COALESCE(ai.settle_time, o.settlement_time, NOW()),
    ai.updated_at = NOW()
WHERE o.settlement_status = 1 AND ai.status = 1;

-- 3. 验证修复结果
SELECT 
    '修复后数据一致性检查' as check_type,
    COUNT(*) as inconsistent_count
FROM attendant_income ai
JOIN orders o ON ai.order_id = o.id
WHERE (o.settlement_status = 1 AND ai.status = 1)
   OR (o.settlement_status = 0 AND ai.status = 2);

-- 4. 显示陪诊师收入统计
SELECT 
    ai.attendant_id,
    COUNT(*) as total_orders,
    SUM(ai.amount) as total_amount,
    SUM(ai.net_amount) as total_net_amount,
    SUM(CASE WHEN ai.status = 1 THEN ai.net_amount ELSE 0 END) as pending_amount,
    SUM(CASE WHEN ai.status = 2 THEN ai.net_amount ELSE 0 END) as settled_amount,
    SUM(CASE WHEN ai.status = 3 THEN ai.net_amount ELSE 0 END) as withdrawn_amount
FROM attendant_income ai
WHERE ai.attendant_id = 13
GROUP BY ai.attendant_id;