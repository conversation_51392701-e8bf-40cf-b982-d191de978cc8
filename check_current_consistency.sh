#!/bin/bash

# 检查当前数据一致性状态的脚本
# 直接查询数据库验证数据一致性

echo "=== 当前数据一致性检查 ==="

# 数据库连接信息
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

# 执行SQL查询的函数
run_query() {
    local query="$1"
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$query" 2>/dev/null
}

echo "1. 总体数据统计："
run_query "
SELECT 
    '总体统计' as section,
    COUNT(DISTINCT o.id) as total_orders,
    COUNT(DISTINCT ai.id) as total_income_records,
    COUNT(DISTINCT CASE WHEN o.settlement_status > 0 THEN o.id END) as settled_orders,
    COUNT(DISTINCT CASE WHEN ai.status = 2 THEN ai.id END) as settled_income_records
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id;
"

echo ""
echo "2. 数据一致性检查："
run_query "
SELECT 
    '一致性检查' as section,
    COUNT(*) as total_checked,
    SUM(CASE 
        WHEN (o.settlement_status > 0 AND ai.status IS NULL) THEN 1
        WHEN (o.settlement_status > 0 AND ai.status = 1) THEN 1
        WHEN (o.settlement_status = 0 AND ai.status = 2) THEN 1
        WHEN (o.settlement_status > 0 AND ai.status = 2 AND o.settlement_time != ai.settle_time) THEN 1
        ELSE 0 
    END) as inconsistent_records,
    SUM(CASE 
        WHEN (o.settlement_status > 0 AND ai.status = 2 AND (o.settlement_time = ai.settle_time OR (o.settlement_time IS NULL AND ai.settle_time IS NULL))) THEN 1
        WHEN (o.settlement_status = 0 AND (ai.status IS NULL OR ai.status = 1)) THEN 1
        ELSE 0 
    END) as consistent_records
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id;
"

echo ""
echo "3. 不一致记录详情："
run_query "
SELECT 
    '不一致记录' as section,
    o.id as order_id,
    o.order_no,
    o.attendant_id,
    o.settlement_status as order_settlement_status,
    DATE_FORMAT(o.settlement_time, '%Y-%m-%d %H:%i:%s') as order_settlement_time,
    ai.status as income_status,
    DATE_FORMAT(ai.settle_time, '%Y-%m-%d %H:%i:%s') as income_settle_time,
    CASE 
        WHEN o.settlement_status > 0 AND ai.status IS NULL THEN 'missing_income_record'
        WHEN o.settlement_status > 0 AND ai.status = 1 THEN 'status_mismatch'
        WHEN o.settlement_status = 0 AND ai.status = 2 THEN 'status_mismatch'
        WHEN o.settlement_status > 0 AND ai.status = 2 AND o.settlement_time != ai.settle_time THEN 'time_mismatch'
        ELSE 'unknown'
    END as issue_type
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id
WHERE (
    (o.settlement_status > 0 AND ai.status IS NULL) OR
    (o.settlement_status > 0 AND ai.status = 1) OR
    (o.settlement_status = 0 AND ai.status = 2) OR
    (o.settlement_status > 0 AND ai.status = 2 AND o.settlement_time != ai.settle_time)
)
ORDER BY o.id;
"

echo ""
echo "4. 问题订单ORD202507311203108006状态："
run_query "
SELECT 
    '问题订单状态' as section,
    o.id,
    o.order_no,
    o.status as order_status,
    o.settlement_status,
    DATE_FORMAT(o.settlement_time, '%Y-%m-%d %H:%i:%s') as settlement_time,
    ai.status as income_status,
    DATE_FORMAT(ai.settle_time, '%Y-%m-%d %H:%i:%s') as income_settle_time,
    ai.net_amount,
    CASE 
        WHEN o.settlement_status > 0 AND ai.status = 2 THEN '✓ 一致'
        WHEN o.settlement_status = 0 AND ai.status IS NULL THEN '✓ 一致'
        ELSE '✗ 不一致'
    END as consistency_status
FROM orders o
LEFT JOIN attendant_income ai ON o.id = ai.order_id
WHERE o.order_no = 'ORD202507311203108006';
"

echo ""
echo "5. 陪诊师收入统计："
run_query "
SELECT 
    '陪诊师收入统计' as section,
    ai.attendant_id,
    COUNT(*) as total_records,
    SUM(ai.amount) as total_amount,
    SUM(ai.net_amount) as total_net_amount,
    SUM(CASE WHEN ai.status = 1 THEN ai.net_amount ELSE 0 END) as pending_amount,
    SUM(CASE WHEN ai.status = 2 THEN ai.net_amount ELSE 0 END) as settled_amount,
    SUM(CASE WHEN ai.status = 3 THEN ai.net_amount ELSE 0 END) as withdrawn_amount
FROM attendant_income ai
GROUP BY ai.attendant_id
ORDER BY ai.attendant_id;
"

echo ""
echo "=== 检查完成 ==="
echo ""
echo "状态说明："
echo "- settlement_status: 0=未结算, 1=已结算, 2=已结算"
echo "- income status: 1=待结算, 2=已结算, 3=已提现"
echo "- 一致性要求: 订单已结算时，收入记录也应该是已结算状态"