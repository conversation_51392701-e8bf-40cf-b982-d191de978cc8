package main

import (
	"context"
	"fmt"
	"log"

	"github.com/gemeijie/peizhen/backend/config"
	"github.com/gemeijie/peizhen/backend/pkg/db"
	"github.com/gemeijie/peizhen/backend/pkg/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 综合修复提现申请中收款人姓名为空的问题
// 这个脚本会：
// 1. 检查所有提现申请中 real_name 为空的记录
// 2. 从对应的陪诊师信息中获取姓名
// 3. 更新提现申请和转账记录
// 4. 确保数据一致性

func main() {
	// 初始化配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	zapLogger, err := logger.NewLogger(&cfg.Log)
	if err != nil {
		log.Fatalf("初始化日志失败: %v", err)
	}
	defer zapLogger.Sync()

	// 初始化数据库
	database, err := db.NewDatabase(&cfg.Database, zapLogger)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	ctx := context.Background()

	// 执行修复
	if err := fixWithdrawalRealNameComprehensive(ctx, database.DB, zapLogger); err != nil {
		log.Fatalf("修复失败: %v", err)
	}

	fmt.Println("✅ 综合修复完成")
}

func fixWithdrawalRealNameComprehensive(ctx context.Context, db *gorm.DB, logger *zap.Logger) error {
	logger.Info("开始综合修复提现申请中的收款人姓名问题")

	// 1. 检查所有 real_name 为空的提现申请
	var emptyRealNameWithdrawals []struct {
		ID       uint   `json:"id"`
		UserID   uint   `json:"user_id"`
		RealName string `json:"real_name"`
		OpenID   string `json:"openid"`
		Status   int    `json:"status"`
	}

	err := db.Raw(`
		SELECT id, user_id, real_name, openid, status 
		FROM withdrawals 
		WHERE real_name IS NULL OR real_name = ''
		ORDER BY id
	`).Scan(&emptyRealNameWithdrawals).Error

	if err != nil {
		return fmt.Errorf("查询空姓名提现申请失败: %w", err)
	}

	logger.Info("找到空姓名的提现申请",
		zap.Int("count", len(emptyRealNameWithdrawals)))

	if len(emptyRealNameWithdrawals) == 0 {
		logger.Info("没有找到空姓名的提现申请，检查转账记录")
	} else {
		// 修复提现申请中的空姓名
		for _, withdrawal := range emptyRealNameWithdrawals {
			if err := fixWithdrawalRealName(ctx, db, logger, withdrawal.ID, withdrawal.UserID); err != nil {
				logger.Error("修复提现申请姓名失败",
					zap.Uint("withdrawal_id", withdrawal.ID),
					zap.Error(err))
				continue
			}
		}
	}

	// 2. 检查所有 real_name 为空的转账记录
	var emptyRealNameTransfers []struct {
		ID           uint   `json:"id"`
		WithdrawalID uint   `json:"withdrawal_id"`
		RealName     string `json:"real_name"`
		OpenID       string `json:"openid"`
		Status       int    `json:"status"`
	}

	err = db.Raw(`
		SELECT id, withdrawal_id, real_name, openid, status 
		FROM withdrawal_transfers 
		WHERE real_name IS NULL OR real_name = ''
		ORDER BY id
	`).Scan(&emptyRealNameTransfers).Error

	if err != nil {
		return fmt.Errorf("查询空姓名转账记录失败: %w", err)
	}

	logger.Info("找到空姓名的转账记录",
		zap.Int("count", len(emptyRealNameTransfers)))

	if len(emptyRealNameTransfers) == 0 {
		logger.Info("没有找到空姓名的转账记录")
	} else {
		// 修复转账记录中的空姓名
		for _, transfer := range emptyRealNameTransfers {
			if err := fixTransferRealName(ctx, db, logger, transfer.ID, transfer.WithdrawalID); err != nil {
				logger.Error("修复转账记录姓名失败",
					zap.Uint("transfer_id", transfer.ID),
					zap.Error(err))
				continue
			}
		}
	}

	// 3. 检查数据一致性
	if err := checkDataConsistency(ctx, db, logger); err != nil {
		logger.Error("数据一致性检查失败", zap.Error(err))
		return err
	}

	logger.Info("综合修复完成")
	return nil
}

func fixWithdrawalRealName(ctx context.Context, db *gorm.DB, logger *zap.Logger, withdrawalID, userID uint) error {
	logger.Info("修复提现申请姓名",
		zap.Uint("withdrawal_id", withdrawalID),
		zap.Uint("user_id", userID))

	// 从陪诊师表中获取姓名
	var attendantName string
	err := db.Raw(`
		SELECT name FROM attendants 
		WHERE user_id = ? AND name IS NOT NULL AND name != ''
		LIMIT 1
	`, userID).Scan(&attendantName).Error

	if err != nil {
		return fmt.Errorf("查询陪诊师姓名失败: %w", err)
	}

	if attendantName == "" {
		logger.Warn("未找到陪诊师姓名",
			zap.Uint("user_id", userID))
		return fmt.Errorf("用户 %d 没有对应的陪诊师姓名", userID)
	}

	// 更新提现申请的姓名
	err = db.Exec(`
		UPDATE withdrawals 
		SET real_name = ?, updated_at = NOW() 
		WHERE id = ?
	`, attendantName, withdrawalID).Error

	if err != nil {
		return fmt.Errorf("更新提现申请姓名失败: %w", err)
	}

	logger.Info("成功修复提现申请姓名",
		zap.Uint("withdrawal_id", withdrawalID),
		zap.String("real_name", attendantName))

	return nil
}

func fixTransferRealName(ctx context.Context, db *gorm.DB, logger *zap.Logger, transferID, withdrawalID uint) error {
	logger.Info("修复转账记录姓名",
		zap.Uint("transfer_id", transferID),
		zap.Uint("withdrawal_id", withdrawalID))

	// 首先尝试从对应的提现申请中获取姓名
	var realName string
	err := db.Raw(`
		SELECT real_name FROM withdrawals 
		WHERE id = ? AND real_name IS NOT NULL AND real_name != ''
		LIMIT 1
	`, withdrawalID).Scan(&realName).Error

	if err != nil {
		return fmt.Errorf("查询提现申请姓名失败: %w", err)
	}

	// 如果提现申请中也没有姓名，从陪诊师表中获取
	if realName == "" {
		var userID uint
		err = db.Raw(`
			SELECT user_id FROM withdrawals WHERE id = ?
		`, withdrawalID).Scan(&userID).Error

		if err != nil {
			return fmt.Errorf("查询用户ID失败: %w", err)
		}

		err = db.Raw(`
			SELECT name FROM attendants 
			WHERE user_id = ? AND name IS NOT NULL AND name != ''
			LIMIT 1
		`, userID).Scan(&realName).Error

		if err != nil {
			return fmt.Errorf("查询陪诊师姓名失败: %w", err)
		}

		if realName == "" {
			logger.Warn("未找到陪诊师姓名",
				zap.Uint("user_id", userID))
			return fmt.Errorf("用户 %d 没有对应的陪诊师姓名", userID)
		}

		// 同时更新提现申请的姓名
		db.Exec(`
			UPDATE withdrawals 
			SET real_name = ?, updated_at = NOW() 
			WHERE id = ?
		`, realName, withdrawalID)
	}

	// 更新转账记录的姓名
	err = db.Exec(`
		UPDATE withdrawal_transfers 
		SET real_name = ?, updated_at = NOW() 
		WHERE id = ?
	`, realName, transferID).Error

	if err != nil {
		return fmt.Errorf("更新转账记录姓名失败: %w", err)
	}

	logger.Info("成功修复转账记录姓名",
		zap.Uint("transfer_id", transferID),
		zap.String("real_name", realName))

	return nil
}

func checkDataConsistency(ctx context.Context, db *gorm.DB, logger *zap.Logger) error {
	logger.Info("检查数据一致性")

	// 检查提现申请和转账记录的姓名一致性
	var inconsistentRecords []struct {
		WithdrawalID   uint   `json:"withdrawal_id"`
		WithdrawalName string `json:"withdrawal_name"`
		TransferID     uint   `json:"transfer_id"`
		TransferName   string `json:"transfer_name"`
	}

	err := db.Raw(`
		SELECT 
			w.id as withdrawal_id,
			w.real_name as withdrawal_name,
			wt.id as transfer_id,
			wt.real_name as transfer_name
		FROM withdrawals w
		LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
		WHERE w.real_name != wt.real_name 
		   OR (w.real_name IS NULL AND wt.real_name IS NOT NULL)
		   OR (w.real_name IS NOT NULL AND wt.real_name IS NULL)
		ORDER BY w.id
	`).Scan(&inconsistentRecords).Error

	if err != nil {
		return fmt.Errorf("检查数据一致性失败: %w", err)
	}

	if len(inconsistentRecords) > 0 {
		logger.Warn("发现数据不一致的记录",
			zap.Int("count", len(inconsistentRecords)))

		for _, record := range inconsistentRecords {
			logger.Warn("数据不一致",
				zap.Uint("withdrawal_id", record.WithdrawalID),
				zap.String("withdrawal_name", record.WithdrawalName),
				zap.Uint("transfer_id", record.TransferID),
				zap.String("transfer_name", record.TransferName))

			// 自动修复：使用提现申请中的姓名更新转账记录
			if record.WithdrawalName != "" && record.TransferName != record.WithdrawalName {
				err = db.Exec(`
					UPDATE withdrawal_transfers 
					SET real_name = ?, updated_at = NOW() 
					WHERE id = ?
				`, record.WithdrawalName, record.TransferID).Error

				if err != nil {
					logger.Error("自动修复数据不一致失败",
						zap.Uint("transfer_id", record.TransferID),
						zap.Error(err))
				} else {
					logger.Info("自动修复数据不一致成功",
						zap.Uint("transfer_id", record.TransferID),
						zap.String("real_name", record.WithdrawalName))
				}
			}
		}
	} else {
		logger.Info("数据一致性检查通过")
	}

	// 最终统计
	var stats struct {
		TotalWithdrawals     int `json:"total_withdrawals"`
		EmptyNameWithdrawals int `json:"empty_name_withdrawals"`
		TotalTransfers       int `json:"total_transfers"`
		EmptyNameTransfers   int `json:"empty_name_transfers"`
	}

	db.Raw("SELECT COUNT(*) FROM withdrawals").Scan(&stats.TotalWithdrawals)
	db.Raw("SELECT COUNT(*) FROM withdrawals WHERE real_name IS NULL OR real_name = ''").Scan(&stats.EmptyNameWithdrawals)
	db.Raw("SELECT COUNT(*) FROM withdrawal_transfers").Scan(&stats.TotalTransfers)
	db.Raw("SELECT COUNT(*) FROM withdrawal_transfers WHERE real_name IS NULL OR real_name = ''").Scan(&stats.EmptyNameTransfers)

	logger.Info("数据统计",
		zap.Int("total_withdrawals", stats.TotalWithdrawals),
		zap.Int("empty_name_withdrawals", stats.EmptyNameWithdrawals),
		zap.Int("total_transfers", stats.TotalTransfers),
		zap.Int("empty_name_transfers", stats.EmptyNameTransfers))

	return nil
}
