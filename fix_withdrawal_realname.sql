-- 修复提现申请收款人姓名问题的SQL脚本

-- 1. 检查 real_name 为空的提现申请
SELECT 
    id,
    withdrawal_no,
    user_id,
    amount,
    status,
    real_name,
    openid
FROM withdrawals 
WHERE real_name IS NULL OR real_name = ''
ORDER BY id;

-- 2. 检查陪诊师姓名数据
SELECT 
    id,
    user_id,
    name,
    phone
FROM attendants 
WHERE name IS NULL OR name = ''
ORDER BY id;

-- 3. 查看提现申请与陪诊师的关联情况
SELECT 
    w.id as withdrawal_id,
    w.withdrawal_no,
    w.user_id,
    w.real_name as withdrawal_real_name,
    a.id as attendant_id,
    a.name as attendant_name
FROM withdrawals w
LEFT JOIN attendants a ON w.user_id = a.user_id
WHERE w.real_name IS NULL OR w.real_name = ''
ORDER BY w.id;

-- 4. 修复 real_name 字段（将陪诊师的姓名更新到提现申请中）
UPDATE withdrawals w
INNER JOIN attendants a ON w.user_id = a.user_id
SET w.real_name = a.name
WHERE (w.real_name IS NULL OR w.real_name = '') 
  AND a.name IS NOT NULL 
  AND a.name != '';

-- 5. 验证修复结果
SELECT 
    w.id as withdrawal_id,
    w.withdrawal_no,
    w.user_id,
    w.real_name as withdrawal_real_name,
    a.name as attendant_name,
    CASE 
        WHEN w.real_name IS NULL OR w.real_name = '' THEN '❌ 仍为空'
        ELSE '✅ 已修复'
    END as status
FROM withdrawals w
LEFT JOIN attendants a ON w.user_id = a.user_id
ORDER BY w.id;

-- 6. 检查特定的提现申请 ID 1
SELECT 
    id,
    withdrawal_no,
    user_id,
    amount,
    actual_amount,
    status,
    real_name,
    openid,
    created_at
FROM withdrawals 
WHERE id = 1;