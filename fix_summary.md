# 问题修复总结

## 问题描述

1. **完成类型未显示**：在管理后台的审核页面中，订单的完成类型显示为"未知类型"
2. **陪诊师上传的图片未显示**：服务照片在审核详情中无法正确显示

## 问题分析

### 问题1：完成类型未显示

**根本原因**：
- 管理后台的审核处理器 (`admin/server/handler/review_handler.go`) 试图从订单表的 `completion_type` 字段获取完成类型
- 但实际上完成类型数据存储在 `order_review_records` 表中，而不是 `orders` 表中
- 代码中直接使用 `order.CompletionType`，但该字段在订单表中可能为空或默认值

**解决方案**：
- 修改审核处理器，从 `order_review_records` 表中获取完成类型数据
- 在 `GetPendingReviews` 和 `GetReviewDetail` 方法中添加查询逻辑

### 问题2：陪诊师上传的图片未显示

**根本原因**：
- 服务记录表中存储的图片路径是相对路径（如 `uploads/service/2024/01/photo1.jpg`）
- 前端需要完整的URL才能正确显示图片
- 缺少域名前缀处理逻辑

**解决方案**：
- 在审核处理器中添加图片URL处理逻辑
- 使用配置中的 `upload_domain` 为相对路径添加域名前缀
- 保持已有的完整URL不变

## 修复内容

### 1. 数据库迁移文件

创建了 `backend/migrations/20250202000001_add_completion_type_to_orders.sql`：
- 为订单表添加 `completion_type` 字段（实际上该字段已存在）
- 添加索引和约束

### 2. 审核处理器修复

修改了 `admin/server/handler/review_handler.go`：

#### 完成类型获取逻辑
```go
// 获取完成类型 - 从审核记录表中获取
var completionType int = 0
var reviewRecord struct {
    CompletionType int `json:"completion_type"`
}
if err := h.db.Table("order_review_records").
    Select("completion_type").
    Where("order_id = ?", order.ID).
    First(&reviewRecord).Error; err == nil {
    completionType = reviewRecord.CompletionType
}
completionTypeText := getCompletionTypeText(completionType)
```

#### 图片URL处理逻辑
```go
rawPhotos := serviceRecord.GetServicePhotos()
// 处理图片URL，确保包含完整路径
for _, photo := range rawPhotos {
    if photo != "" {
        // 如果是相对路径，添加域名前缀
        if !strings.HasPrefix(photo, "http") {
            fullURL := strings.TrimRight(h.config.URLs.UploadDomain, "/") + "/" + strings.TrimLeft(photo, "/")
            servicePhotos = append(servicePhotos, fullURL)
        } else {
            servicePhotos = append(servicePhotos, photo)
        }
    }
}
```

### 3. 配置传递修复

修改了路由设置，确保配置正确传递给审核处理器：

- `admin/server/router/main_router.go`：添加配置参数
- `admin/server/main.go`：传递配置到路由设置
- `admin/server/handler/review_handler.go`：接收和使用配置

### 4. 测试验证

创建了测试文件验证修复效果：
- `test_review_api.sh`：API测试脚本
- `admin/server/test_image_url.go`：图片URL处理测试

## 测试结果

图片URL处理测试结果：
```
原始图片列表: [uploads/service/2024/01/photo1.jpg https://example.com/photo2.jpg uploads/service/2024/01/photo3.jpg]
处理后的图片列表: [http://localhost:8080/uploads/service/2024/01/photo1.jpg https://example.com/photo2.jpg http://localhost:8080/uploads/service/2024/01/photo3.jpg]
```

## 预期效果

修复后：
1. **完成类型正确显示**：审核页面中的完成类型将显示为"陪诊师完成"、"管理员强制完成"等具体类型，而不是"未知类型"
2. **图片正确显示**：服务照片将以完整URL显示，前端可以正常加载和预览图片

## 部署说明

1. 确保数据库中 `orders` 表已有 `completion_type` 字段（通常已存在）
2. 重新编译并部署管理后台服务
3. 验证配置文件中的 `upload_domain` 设置正确
4. 测试审核页面的完成类型和图片显示功能

## 注意事项

1. 图片URL处理依赖于配置中的 `upload_domain` 设置
2. 完成类型数据来源于 `order_review_records` 表，确保该表有相关数据
3. 如果图片存储方式发生变化，可能需要调整URL处理逻辑