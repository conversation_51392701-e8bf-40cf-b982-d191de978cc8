-- 数据迁移脚本：从 withdrawals_old 迁移到 withdrawals
-- 创建时间：2024-12-20
-- 说明：将旧版提现表数据迁移到新版表结构

-- 开始事务
START TRANSACTION;

-- 插入数据到新的 withdrawals 表
INSERT INTO withdrawals (
    withdrawal_no,
    user_id,
    amount,
    fee,
    actual_amount,
    payment_method,
    openid,
    real_name,
    status,
    apply_time,
    audit_time,
    audit_admin_id,
    audit_remark,
    pay_time,
    pay_admin_id,
    pay_remark,
    transfer_no,
    wechat_batch_no,
    transfer_fail_reason,
    created_at,
    updated_at,
    deleted_at
)
SELECT 
    withdraw_no as withdrawal_no,
    user_id,
    amount,
    0.00 as fee,  -- 旧数据没有手续费，默认为0
    amount as actual_amount,  -- 实际到账金额等于申请金额（无手续费）
    CASE 
        WHEN method = 1 THEN 'wechat'
        WHEN method = 2 THEN 'alipay'
        WHEN method = 3 THEN 'bank'
        ELSE 'wechat'
    END as payment_method,
    CASE 
        WHEN method = 1 THEN account  -- 微信方式时，account字段作为openid
        ELSE NULL
    END as openid,
    account_name as real_name,
    status,
    created_at as apply_time,
    audit_time,
    auditor_id as audit_admin_id,
    reject_reason as audit_remark,  -- 将拒绝原因作为审核备注
    pay_time,
    payer_id as pay_admin_id,
    NULL as pay_remark,  -- 旧数据没有打款备注
    NULL as transfer_no,  -- 旧数据没有转账单号
    NULL as wechat_batch_no,  -- 旧数据没有微信批次号
    NULL as transfer_fail_reason,  -- 旧数据没有转账失败原因
    created_at,
    updated_at,
    deleted_at
FROM withdrawals_old
WHERE deleted_at IS NULL  -- 只迁移未删除的数据
ORDER BY id;

-- 检查迁移结果
SELECT 
    '迁移前 withdrawals_old 记录数' as description,
    COUNT(*) as count
FROM withdrawals_old
WHERE deleted_at IS NULL

UNION ALL

SELECT 
    '迁移后 withdrawals 记录数' as description,
    COUNT(*) as count
FROM withdrawals
WHERE withdrawal_no IN (
    SELECT withdraw_no FROM withdrawals_old WHERE deleted_at IS NULL
);

-- 验证数据完整性
SELECT 
    'withdrawals_old' as source_table,
    withdraw_no,
    user_id,
    amount,
    method,
    account_name,
    status
FROM withdrawals_old
WHERE deleted_at IS NULL
ORDER BY id
LIMIT 3;

SELECT 
    'withdrawals' as target_table,
    withdrawal_no,
    user_id,
    amount,
    payment_method,
    real_name,
    status
FROM withdrawals
WHERE withdrawal_no IN (
    SELECT withdraw_no FROM withdrawals_old WHERE deleted_at IS NULL
)
ORDER BY id
LIMIT 3;

-- 提交事务（手动执行）
-- COMMIT;

-- 如果需要回滚，执行以下命令：
-- ROLLBACK;

-- 迁移完成后的清理工作（可选，需要确认数据无误后执行）
-- 1. 备份 withdrawals_old 表
-- CREATE TABLE withdrawals_old_backup AS SELECT * FROM withdrawals_old;
-- 
-- 2. 删除 withdrawals_old 表（谨慎操作）
-- DROP TABLE withdrawals_old;