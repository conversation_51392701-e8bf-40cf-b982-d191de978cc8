# 收入状态显示问题修复方案

## 问题分析

陪诊师收入页面显示问题：
1. **配置已正确设置**：`system_settings` 表中 `finance.freeze_days = 0`
2. **数据库状态正确**：收入记录 `status = 2`（已结算）
3. **时间条件满足**：收入创建时间已超过73小时
4. **前端显示错误**：仍显示"处理中"和"7天结算期"

## 根本原因

Repository实现中的字段映射错误：
```go
// 错误的更新语句
return db.WithContext(ctx).Model(&config).Update("value", value).Error

// 应该是
return db.WithContext(ctx).Model(&config).Update("config_value", value).Error
```

## 修复步骤

### 1. 已修复的代码
- ✅ `backend/internal/repository/impl/system_config_repository_impl.go`
  - 修复了 `SetValue` 方法中的字段映射错误

### 2. 需要重启的服务
- 🔄 **后端服务**：应用Repository修复
- 🔄 **前端缓存**：清理可能的缓存数据

### 3. 验证步骤

#### 步骤1：重启后端服务
```bash
# 重启后端服务以应用修复
systemctl restart peizhen-backend
# 或者根据实际部署方式重启
```

#### 步骤2：验证配置读取
```bash
# 测试API是否正确读取配置
curl -X GET "http://localhost:8080/api/v1/attendant/account/info" \
  -H "Authorization: Bearer ATTENDANT_TOKEN"
```

#### 步骤3：检查收入状态
```bash
# 检查收入流水状态
curl -X GET "http://localhost:8080/api/v1/attendant/account/logs" \
  -H "Authorization: Bearer ATTENDANT_TOKEN"
```

预期结果：
- `balance` 应该为 `0.01`
- 收入记录的 `status_text` 应该为 "已到账"
- `showSettlementTip` 应该为 `false`

### 4. 前端处理

前端代码逻辑是正确的，会根据后端返回的数据自动更新：
- `balance > 0` 时显示可提现金额
- `status_text = "已到账"` 时不显示"处理中"
- `frozen_balance = 0` 时不显示结算提示

## 测试用例

### 测试1：配置读取
```sql
-- 验证配置值
SELECT `key`, `value` FROM system_settings WHERE `key` = 'finance.freeze_days';
-- 预期：value = '0'
```

### 测试2：收入计算
```sql
-- 验证收入记录
SELECT 
    ai.created_at,
    TIMESTAMPDIFF(HOUR, ai.created_at, NOW()) as hours_passed,
    ai.status,
    ai.net_amount
FROM attendant_income ai
JOIN orders o ON ai.order_id = o.id
WHERE o.order_no = 'ORD202507311203108006';
-- 预期：hours_passed > 0, status = 2, net_amount = 0.01
```

### 测试3：API响应
```json
// GET /api/v1/attendant/account/info 预期响应
{
  "code": 0,
  "data": {
    "total_income": 0.01,
    "balance": 0.01,
    "frozen_balance": 0.00,
    "total_withdrawal": 0.00
  }
}

// GET /api/v1/attendant/account/logs 预期响应
{
  "code": 0,
  "data": {
    "list": [{
      "status": "success",
      "status_text": "已到账",
      "amount": 0.01
    }]
  }
}
```

## 部署清单

### 需要更新的文件
- ✅ `backend/internal/repository/impl/system_config_repository_impl.go`

### 需要执行的操作
1. 🔄 重启后端服务
2. 🧪 执行API测试验证
3. 📱 清理前端缓存（如果需要）

### 验证标准
- [ ] 配置 `finance.freeze_days` 正确读取为 `0`
- [ ] 可提现余额显示为 `0.01`
- [ ] 收入状态显示为 "已到账"
- [ ] 不再显示 "7天结算期" 提示
- [ ] 可以正常发起提现申请

## 预防措施

1. **字段映射检查**：确保所有Repository中的字段映射与模型定义一致
2. **配置变更测试**：每次配置变更后验证API响应
3. **端到端测试**：从前端到后端的完整流程测试
4. **监控告警**：添加配置读取异常的监控

## 回滚方案

如果修复后仍有问题：
1. 检查服务是否正确重启
2. 验证数据库连接和权限
3. 检查是否有其他缓存层
4. 临时手动更新收入状态（如果紧急）

---

**修复完成后，陪诊师应该能够看到正确的收入状态和可提现金额。**