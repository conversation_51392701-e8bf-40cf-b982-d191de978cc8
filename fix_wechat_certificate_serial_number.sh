#!/bin/bash

# 修复微信证书序列号问题
# 基于商户平台截图信息

echo "🔧 修复微信证书序列号问题"
echo "=================================="

# 1. 当前配置检查
echo -e "\n1. 当前配置检查："
echo "   配置中的序列号: $(grep WECHAT_SERIAL_NO production.env | cut -d'=' -f2)"
echo "   商户平台有效序列号: 3B2F1BB6FBF9CD4D2448AB6310720C15CD668247"

# 2. 备份当前配置
echo -e "\n2. 备份当前配置："
if [ -f "production.env" ]; then
    cp production.env "production.env.backup.$(date +%Y%m%d_%H%M%S)"
    echo "   ✅ 已备份 production.env"
fi

# 3. 更新证书序列号
echo -e "\n3. 更新证书序列号："

# 确保使用商户平台显示的有效证书序列号
CORRECT_SERIAL_NO="3B2F1BB6FBF9CD4D2448AB6310720C15CD668247"

# 更新所有相关的序列号配置
echo "   🔧 更新 WECHAT_SERIAL_NO"
sed -i "s/WECHAT_SERIAL_NO=.*/WECHAT_SERIAL_NO=$CORRECT_SERIAL_NO/" production.env

echo "   🔧 更新 WECHAT_CERT_SERIAL_NUMBER"
sed -i "s/WECHAT_CERT_SERIAL_NUMBER=.*/WECHAT_CERT_SERIAL_NUMBER=$CORRECT_SERIAL_NO/" production.env

echo "   🔧 更新 WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER"
sed -i "s/WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER=.*/WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER=$CORRECT_SERIAL_NO/" production.env

echo "   🔧 更新 WECHAT_TRANSFER_SERIAL_NO"
sed -i "s/WECHAT_TRANSFER_SERIAL_NO=.*/WECHAT_TRANSFER_SERIAL_NO=$CORRECT_SERIAL_NO/" production.env

# 4. 验证更新结果
echo -e "\n4. 验证更新结果："
echo "   WECHAT_SERIAL_NO: $(grep WECHAT_SERIAL_NO production.env | cut -d'=' -f2)"
echo "   WECHAT_CERT_SERIAL_NUMBER: $(grep WECHAT_CERT_SERIAL_NUMBER production.env | cut -d'=' -f2)"
echo "   WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER: $(grep WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER production.env | cut -d'=' -f2)"
echo "   WECHAT_TRANSFER_SERIAL_NO: $(grep WECHAT_TRANSFER_SERIAL_NO production.env | cut -d'=' -f2)"

# 5. 检查证书文件
echo -e "\n5. 检查证书文件："
cert_file="/etc/peizhen/certs/wechat/apiclient_key.pem"
if [ -f "$cert_file" ]; then
    echo "   ✅ 商户私钥文件存在: $cert_file"
    echo "   文件权限: $(ls -la "$cert_file" | awk '{print $1, $3":"$4}')"
    
    # 验证证书序列号
    if command -v openssl >/dev/null 2>&1; then
        echo "   🔍 验证证书序列号..."
        # 注意：这里检查的是商户证书，不是私钥
        cert_cert_file="/etc/peizhen/certs/wechat/apiclient_cert.pem"
        if [ -f "$cert_cert_file" ]; then
            serial_from_cert=$(openssl x509 -in "$cert_cert_file" -noout -serial | cut -d'=' -f2)
            echo "   证书文件中的序列号: $serial_from_cert"
            echo "   配置中的序列号: $CORRECT_SERIAL_NO"
            
            if [ "$serial_from_cert" = "$CORRECT_SERIAL_NO" ]; then
                echo "   ✅ 序列号匹配"
            else
                echo "   ⚠️  序列号不匹配，可能需要重新下载证书"
            fi
        else
            echo "   ⚠️  商户证书文件不存在: $cert_cert_file"
        fi
    fi
else
    echo "   ❌ 商户私钥文件不存在: $cert_file"
fi

# 6. 创建测试脚本
echo -e "\n6. 创建测试脚本："
cat > test_wechat_certificate_serial.go << 'EOF'
package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/certificates"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

func main() {
	fmt.Println("🧪 测试微信证书序列号")
	fmt.Println("========================")

	// 从环境变量读取配置
	mchID := os.Getenv("WECHAT_MCH_ID")
	serialNo := os.Getenv("WECHAT_SERIAL_NO")
	apiV3Key := os.Getenv("WECHAT_API_V3_KEY")
	privateKeyPath := os.Getenv("WECHAT_PRIVATE_KEY_PATH")

	fmt.Printf("配置信息:\n")
	fmt.Printf("  商户号: %s\n", mchID)
	fmt.Printf("  证书序列号: %s\n", serialNo)
	fmt.Printf("  私钥路径: %s\n", privateKeyPath)
	fmt.Printf("  APIv3密钥: %s\n", maskString(apiV3Key))

	// 检查必要参数
	if mchID == "" || serialNo == "" || apiV3Key == "" || privateKeyPath == "" {
		log.Fatal("❌ 缺少必要的环境变量")
	}

	// 加载私钥
	fmt.Println("\n加载商户私钥...")
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(privateKeyPath)
	if err != nil {
		log.Fatalf("❌ 加载商户私钥失败: %v", err)
	}
	fmt.Println("  ✅ 商户私钥加载成功")

	// 初始化客户端
	fmt.Println("\n初始化微信支付客户端...")
	ctx := context.Background()
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, serialNo, mchPrivateKey, apiV3Key),
	}

	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		fmt.Printf("❌ 初始化微信支付客户端失败: %v\n", err)
		fmt.Println("\n可能的原因:")
		fmt.Println("1. 证书序列号不正确")
		fmt.Println("2. APIv3密钥错误")
		fmt.Println("3. 商户私钥与商户平台证书不匹配")
		fmt.Println("4. 商户平台API权限未开通")
		return
	}
	fmt.Println("  ✅ 微信支付客户端初始化成功")

	// 尝试下载平台证书
	fmt.Println("\n尝试下载平台证书...")
	svc := certificates.CertificatesApiService{Client: client}
	resp, result, err := svc.DownloadCertificates(ctx)

	if err != nil {
		fmt.Printf("❌ 下载平台证书失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 平台证书下载成功\n")
	fmt.Printf("  HTTP状态码: %d\n", result.Response.StatusCode)
	fmt.Printf("  证书数量: %d\n", len(resp.Data))
	
	// 显示证书信息
	for i, cert := range resp.Data {
		fmt.Printf("  证书 %d:\n", i+1)
		fmt.Printf("    序列号: %s\n", *cert.SerialNo)
		fmt.Printf("    有效期: %s - %s\n", *cert.EffectiveTime, *cert.ExpireTime)
	}
}

func maskString(s string) string {
	if len(s) <= 8 {
		return "****"
	}
	return s[:4] + "****" + s[len(s)-4:]
}
EOF

echo "   ✅ 已创建测试脚本: test_wechat_certificate_serial.go"

# 7. 提供下一步操作
echo -e "\n7. 下一步操作："
echo "=================================="
echo "1. 重启服务使配置生效："
echo "   sudo systemctl restart peizhen-backend"
echo "   sudo systemctl restart peizhen-admin"
echo ""
echo "2. 运行测试脚本验证："
echo "   source production.env"
echo "   go run test_wechat_certificate_serial.go"
echo ""
echo "3. 如果仍然失败，检查："
echo "   - 商户平台是否有企业付款权限"
echo "   - APIv3密钥是否正确"
echo "   - 商户私钥是否与平台证书匹配"

echo -e "\n🎯 修复完成！"
echo "=================================="
echo "主要修复内容："
echo "1. ✅ 更新证书序列号为商户平台显示的有效序列号"
echo "2. ✅ 统一所有相关配置项的序列号"
echo "3. ✅ 创建测试脚本验证修复结果"
echo "4. ✅ 提供详细的验证步骤"