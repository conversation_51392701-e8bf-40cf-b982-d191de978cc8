#!/bin/bash

# 修复API认证问题的脚本
# 确保管理后台和后端使用相同的API密钥配置

echo "🔧 修复API认证问题"

# 1. 检查环境变量
echo "步骤1: 检查关键环境变量"
echo "BACKEND_API_KEY_ID: ${BACKEND_API_KEY_ID:-未设置}"
echo "BACKEND_API_SECRET_KEY: ${BACKEND_API_SECRET_KEY:-未设置}"

if [ -z "$BACKEND_API_KEY_ID" ] || [ -z "$BACKEND_API_SECRET_KEY" ]; then
    echo "❌ 关键环境变量未设置"
    echo "请确保设置了以下环境变量："
    echo "  BACKEND_API_KEY_ID=admin-key-prod-001"
    echo "  BACKEND_API_SECRET_KEY=peizhen-backend-api-secret-key-2024-prod-32chars"
    exit 1
fi

# 2. 检查管理后台配置文件
echo "步骤2: 检查管理后台配置文件"
ADMIN_CONFIG_FILE="admin/server/config/config.prod.yaml"

if [ ! -f "$ADMIN_CONFIG_FILE" ]; then
    echo "❌ 管理后台生产环境配置文件不存在: $ADMIN_CONFIG_FILE"
    exit 1
fi

echo "✅ 管理后台生产环境配置文件存在"

# 3. 验证配置文件内容
echo "步骤3: 验证配置文件内容"
if grep -q "BACKEND_API_KEY_ID" "$ADMIN_CONFIG_FILE"; then
    echo "✅ 配置文件包含正确的API密钥配置"
else
    echo "❌ 配置文件缺少API密钥配置"
    exit 1
fi

# 4. 检查后端配置
echo "步骤4: 检查后端配置"
BACKEND_CONFIG_FILE="backend/config/conf/config.prod.yaml"

if [ ! -f "$BACKEND_CONFIG_FILE" ]; then
    echo "❌ 后端生产环境配置文件不存在: $BACKEND_CONFIG_FILE"
    exit 1
fi

if grep -q "BACKEND_API_KEY_ID" "$BACKEND_CONFIG_FILE"; then
    echo "✅ 后端配置文件包含正确的API密钥配置"
else
    echo "❌ 后端配置文件缺少API密钥配置"
    exit 1
fi

# 5. 重启服务建议
echo "步骤5: 服务重启建议"
echo "请按以下顺序重启服务："
echo "1. 重启后端服务："
echo "   cd backend && go build -o bin/peizhen main.go && ./bin/peizhen"
echo ""
echo "2. 重启管理后台服务："
echo "   cd admin/server && APP_ENV=prod go run main.go"
echo ""

# 6. 测试API连接
echo "步骤6: 测试API连接"
echo "可以使用以下命令测试API连接："
echo "curl -X POST https://www.kanghuxing.cn/api/v1/internal/wechat/transfer \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"withdrawal_ids\":[1],\"operator_id\":1,\"remark\":\"测试\"}'"

echo ""
echo "✅ API认证问题修复完成"
echo "请确保："
echo "1. 环境变量正确设置"
echo "2. 管理后台使用生产环境配置 (APP_ENV=prod)"
echo "3. 两个服务都已重启"