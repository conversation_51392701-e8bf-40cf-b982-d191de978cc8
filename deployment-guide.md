# 订单状态流转优化项目 - 线上发布指南

## 📋 发布概述

本次发布将完全移除 `status = 4` (OrderStatusCompleted) 的使用，统一所有订单完成流程为T+2审核机制。

## 🗄️ 数据库迁移文件执行顺序

### 必须执行的SQL文件（按顺序）

```bash
# 1. 创建订单审核记录表
backend/migrations/20240131_create_order_review_records_table.sql

# 2. 创建监控相关表
backend/migrations/20240131_create_monitor_tables.sql

# 3. 创建订单状态迁移日志表
backend/migrations/20250731000002_create_order_status_migration_log.sql
```

### 数据迁移脚本（生产环境执行前需要备份）

```sql
-- 4. 执行订单状态迁移（status 4 -> 10）
-- 注意：这个脚本需要在业务低峰期执行，建议凌晨2-4点
-- 执行前务必备份 orders 表和相关数据

-- 迁移脚本示例（需要根据实际数据量调整批次大小）
START TRANSACTION;

-- 记录迁移开始
INSERT INTO order_status_migration_logs (
    order_id, old_status, new_status, migration_type, 
    migration_reason, migration_batch, migrated_by
) 
SELECT 
    id, 4, 10, 'status_4_to_10', 
    '统一T+2审核流程迁移', 
    CONCAT('batch_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s')), 
    0
FROM orders 
WHERE status = 4 
LIMIT 1000; -- 分批处理，避免锁表时间过长

-- 更新订单状态
UPDATE orders 
SET status = 10, updated_at = NOW() 
WHERE status = 4 
LIMIT 1000;

-- 为迁移的订单创建审核记录
INSERT INTO order_review_records (
    order_id, completion_type, status, auto_approved, 
    review_notes, reviewed_at, created_at, updated_at
)
SELECT 
    id, 2, 3, 1, 
    '系统迁移：原status=4订单自动审核通过', 
    NOW(), NOW(), NOW()
FROM orders 
WHERE status = 10 
AND id NOT IN (SELECT order_id FROM order_review_records)
LIMIT 1000;

COMMIT;
```

## 🚀 应用程序发布

### 后端发布文件

```bash
# 主要发布目录和文件
backend/
├── main.go                                    # 主程序入口
├── internal/
│   ├── app/setup.go                          # 服务容器配置
│   ├── handler/
│   │   ├── order_review_handler.go           # 订单审核处理器
│   │   ├── status_flow_monitor_handler.go    # 状态流转监控处理器
│   │   └── alert_handler.go                  # 告警处理器
│   ├── service/
│   │   ├── order_completion_service.go       # 订单完成服务接口
│   │   ├── order_review_service.go           # 订单审核服务接口
│   │   ├── status_flow_monitor_service.go    # 状态流转监控服务接口
│   │   ├── alert_service.go                  # 告警服务接口
│   │   ├── audit_log_service.go              # 审计日志服务接口
│   │   └── impl/
│   │       ├── order_completion_service_impl.go    # 订单完成服务实现
│   │       ├── order_review_service_impl.go        # 订单审核服务实现
│   │       ├── status_flow_monitor_service_impl.go # 状态流转监控服务实现
│   │       └── settlement_service_impl.go          # 结算服务（已更新）
│   ├── repository/
│   │   ├── order_review_repository.go        # 订单审核仓库接口
│   │   ├── status_flow_monitor_repository.go # 状态流转监控仓库接口
│   │   └── impl/
│   │       ├── order_review_repository_impl.go        # 订单审核仓库实现
│   │       └── status_flow_monitor_repository_impl.go # 状态流转监控仓库实现
│   ├── model/
│   │   ├── order.go                          # 订单模型（已更新）
│   │   ├── order_review.go                   # 订单审核模型
│   │   └── monitor.go                        # 监控模型
│   ├── router/
│   │   ├── router.go                         # 主路由（已更新）
│   │   ├── order_review_router.go            # 订单审核路由
│   │   ├── status_flow_monitor_router.go     # 状态流转监控路由
│   │   └── alert_router.go                   # 告警路由
│   ├── middleware/
│   │   └── legacy_api_monitor.go             # 旧接口监控中间件
│   └── dto/
│       └── order_completion.go               # 订单完成DTO
```

### 前端发布文件

```bash
# 微信小程序发布文件
frontend/
├── utils/
│   └── order-status-constants.js            # 订单状态常量（已更新，移除status=4）
├── services/
│   └── order.js                             # 订单服务（已更新接口调用）
└── pages/
    └── attendant/
        └── workbench/
            └── index.js                     # 陪诊师工作台（已更新）
```

### 管理后台发布文件

```bash
# 管理后台发布文件（如果有的话）
admin/
├── web/                                     # Vue.js前端
│   ├── src/
│   │   ├── views/
│   │   │   └── order/                       # 订单管理页面（需要更新状态显示）
│   │   └── utils/
│   │       └── constants.js                 # 状态常量（需要更新）
└── server/                                  # Go后端
    └── handler/
        └── order_handler.go                 # 订单处理器（需要更新）
```

## 📝 发布前检查清单

### 数据库检查
- [ ] 确认数据库连接正常
- [ ] 备份生产数据库（特别是orders表）
- [ ] 检查是否有status=4的订单需要迁移
- [ ] 验证新表结构创建成功

### 应用程序检查
- [ ] 后端代码编译成功
- [ ] 所有服务正确注册到Container
- [ ] API路由配置正确
- [ ] 前端状态常量已更新

### 功能验证
- [ ] 订单完成流程测试
- [ ] T+2审核流程测试
- [ ] 状态显示正确性测试
- [ ] 旧接口废弃提示测试

## 🔄 发布步骤

### 1. 数据库迁移（业务低峰期执行）

```bash
# 连接生产数据库
mysql -h [host] -u [username] -p [database]

# 执行迁移脚本（按顺序）
source backend/migrations/20240131_create_order_review_records_table.sql
source backend/migrations/20240131_create_monitor_tables.sql
source backend/migrations/20250731000002_create_order_status_migration_log.sql

# 执行数据迁移（分批执行，监控执行时间）
# 建议每批1000条，观察系统负载
```

### 2. 后端应用发布

```bash
# 编译后端应用
cd backend
go build -o peizhen main.go

# 停止旧服务
systemctl stop peizhen

# 备份旧版本
cp peizhen peizhen.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
cp peizhen /path/to/production/
systemctl start peizhen

# 检查服务状态
systemctl status peizhen
curl http://localhost:8080/health
```

### 3. 前端应用发布

```bash
# 微信小程序
# 1. 使用微信开发者工具上传代码
# 2. 在微信公众平台提交审核
# 3. 审核通过后发布

# 管理后台（如果有）
cd admin/web
npm run build
# 部署dist目录到生产环境
```

## 📊 发布后验证

### 关键功能验证
1. **订单完成流程**
   - 陪诊师完成订单 → 状态变为8（待审核）
   - 管理员强制完成 → 自动审核通过，状态变为10
   - 异常处理完成 → 自动审核通过，状态变为10

2. **审核流程**
   - 待审核订单列表正常显示
   - 审核通过/拒绝功能正常
   - 状态流转正确

3. **前端显示**
   - 订单状态文本显示正确
   - 不再显示status=4的订单
   - status=10显示为"已完成"

### 监控指标
- 订单完成成功率
- 审核处理时效性
- 系统错误率
- 旧接口调用次数（应该为0）

## 🚨 应急回滚方案

### 数据库回滚
```sql
-- 如果需要回滚，执行以下脚本
START TRANSACTION;

-- 恢复订单状态
UPDATE orders o
JOIN order_status_migration_logs l ON o.id = l.order_id
SET o.status = l.old_status
WHERE l.migration_type = 'status_4_to_10';

-- 删除创建的审核记录
DELETE orr FROM order_review_records orr
JOIN order_status_migration_logs l ON orr.order_id = l.order_id
WHERE l.migration_type = 'status_4_to_10';

-- 记录回滚操作
INSERT INTO order_status_migration_logs (
    order_id, old_status, new_status, migration_type, 
    migration_reason, migrated_by
)
SELECT 
    order_id, new_status, old_status, 'rollback',
    '紧急回滚操作', 0
FROM order_status_migration_logs 
WHERE migration_type = 'status_4_to_10';

COMMIT;
```

### 应用程序回滚
```bash
# 停止新服务
systemctl stop peizhen

# 恢复旧版本
cp peizhen.backup.[timestamp] peizhen

# 启动旧服务
systemctl start peizhen
```

## 📞 发布联系人

- **技术负责人**: [姓名] - [电话]
- **数据库管理员**: [姓名] - [电话]  
- **运维负责人**: [姓名] - [电话]
- **产品负责人**: [姓名] - [电话]

## 📅 发布时间建议

- **数据库迁移**: 凌晨2:00-4:00（业务低峰期）
- **应用发布**: 凌晨4:00-6:00
- **验证测试**: 上午9:00-12:00
- **全面监控**: 发布后24小时内

---

**注意**: 本次发布涉及核心业务流程变更，请严格按照步骤执行，确保每个环节都有充分的验证和备份。