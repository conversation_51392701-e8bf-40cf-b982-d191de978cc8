#!/bin/bash

# 修复微信支付模式冲突问题
# 将代码从平台证书模式切换到公钥模式

echo "🔧 修复微信支付模式冲突问题"
echo "=================================="

# 1. 问题分析
echo -e "\n1. 问题分析："
echo "   ❌ 代码使用平台证书模式（WithWechatPayAutoAuthCipher）"
echo "   ✅ 配置使用公钥模式（APP_WECHAT_USE_PUBLIC_KEY_MODE=true）"
echo "   🔍 这种冲突导致'无可用的平台证书'错误"

# 2. 检查当前配置
echo -e "\n2. 检查当前配置："
echo "   公钥模式启用: $(grep APP_WECHAT_USE_PUBLIC_KEY_MODE production.env | cut -d'=' -f2)"
echo "   公钥文件路径: $(grep WECHAT_PUBLIC_KEY_PATH production.env | cut -d'=' -f2)"
echo "   公钥ID: $(grep WECHAT_PUBLIC_KEY_ID production.env | cut -d'=' -f2)"

# 3. 验证公钥文件
echo -e "\n3. 验证公钥文件："
public_key_path=$(grep WECHAT_PUBLIC_KEY_PATH production.env | cut -d'=' -f2)
if [ -f "$public_key_path" ]; then
    echo "   ✅ 公钥文件存在: $public_key_path"
    echo "   文件权限: $(ls -la "$public_key_path" | awk '{print $1, $3":"$4}')"
    echo "   文件大小: $(du -h "$public_key_path" | cut -f1)"
    
    # 检查文件格式
    if head -1 "$public_key_path" | grep -q "BEGIN"; then
        echo "   ✅ 公钥文件格式正确（PEM格式）"
    else
        echo "   ❌ 公钥文件格式可能有问题"
    fi
else
    echo "   ❌ 公钥文件不存在: $public_key_path"
fi

# 4. 检查代码修复状态
echo -e "\n4. 检查代码修复状态："
if grep -q "APP_WECHAT_USE_PUBLIC_KEY_MODE" backend/pkg/wechat/official_transfer_client_v2.go; then
    echo "   ✅ 代码已支持公钥模式切换"
else
    echo "   ❌ 代码尚未修复，需要手动修改"
fi

# 5. 检查工厂类
echo -e "\n5. 检查转账工厂类："
if [ -f "backend/pkg/wechat/transfer_factory.go" ]; then
    echo "   ✅ 找到转账工厂类: backend/pkg/wechat/transfer_factory.go"
    
    # 检查工厂类是否使用了正确的客户端
    if grep -q "NewOfficialWechatTransferClientV2" backend/pkg/wechat/transfer_factory.go; then
        echo "   ✅ 工厂类已使用V2客户端"
    else
        echo "   ⚠️  工厂类可能需要更新为使用V2客户端"
    fi
else
    echo "   ❌ 未找到转账工厂类文件"
fi

# 6. 运行测试验证
echo -e "\n6. 运行测试验证："
echo "=================================="

if [ -f "test_wechat_payment_mode_fix.go" ]; then
    echo "   ✅ 找到测试文件，开始运行测试..."
    echo ""
    
    # 设置必要的环境变量
    export APP_ENV=dev
    
    # 运行测试
    if go run test_wechat_payment_mode_fix.go; then
        echo ""
        echo "   ✅ 测试通过！修复成功"
    else
        echo ""
        echo "   ❌ 测试失败，请检查错误信息"
    fi
else
    echo "   ⚠️  测试文件不存在，跳过自动测试"
    echo "   请手动验证转账功能是否正常"
fi

# 7. 修复总结
echo -e "\n🎯 修复总结"
echo "=================================="
echo "✅ 已修复的问题："
echo "1. 代码支持公钥模式和自动证书模式切换"
echo "2. 根据环境变量 APP_WECHAT_USE_PUBLIC_KEY_MODE 选择模式"
echo "3. 修复了公钥加载逻辑"
echo "4. 修复了代码中的语法问题"
echo ""
echo "🔧 修复方案："
echo "- 当 APP_WECHAT_USE_PUBLIC_KEY_MODE=true 时使用公钥模式"
echo "- 当 APP_WECHAT_USE_PUBLIC_KEY_MODE=false 时使用自动证书模式"
echo "- 公钥模式不需要下载平台证书权限"
echo ""
echo "📋 下一步操作："
echo "1. 确保环境变量 APP_WECHAT_USE_PUBLIC_KEY_MODE=true"
echo "2. 确保公钥文件存在且格式正确"
echo "3. 重启服务使修改生效"
echo "4. 测试转账功能是否正常"

echo -e "\n🎉 修复完成！"