#!/bin/bash

# 修复管理后台微信配置问题的脚本
# 解决 "商户私钥不能为空" 错误

echo "🔧 修复管理后台微信配置问题"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 检查环境变量文件
echo -e "${BLUE}步骤1: 检查环境变量文件${NC}"

if [ ! -f "admin.production.env" ]; then
    echo -e "${RED}❌ 管理后台环境变量文件不存在: admin.production.env${NC}"
    exit 1
fi

# 检查关键的微信配置变量
echo "检查微信配置变量:"

REQUIRED_VARS=(
    "WECHAT_APP_ID"
    "WECHAT_MCH_ID"
    "WECHAT_API_V3_KEY"
    "WECHAT_SERIAL_NO"
    "WECHAT_PRIVATE_KEY_PATH"
    "WECHAT_PUBLIC_KEY_PATH"
)

MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if grep -q "^${var}=" admin.production.env; then
        echo -e "${GREEN}✅ $var 已配置${NC}"
    else
        echo -e "${RED}❌ $var 缺失${NC}"
        MISSING_VARS+=("$var")
    fi
done

# 如果有缺失的变量，添加它们
if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo -e "${YELLOW}正在添加缺失的环境变量...${NC}"
    
    for var in "${MISSING_VARS[@]}"; do
        case $var in
            "WECHAT_PRIVATE_KEY_PATH")
                echo "WECHAT_PRIVATE_KEY_PATH=/etc/peizhen/certs/wechat/apiclient_key.pem" >> admin.production.env
                ;;
            "WECHAT_PUBLIC_KEY_PATH")
                echo "WECHAT_PUBLIC_KEY_PATH=/etc/peizhen/certs/wechat/wechat_public_key.pem" >> admin.production.env
                ;;
        esac
        echo -e "${GREEN}✅ 已添加 $var${NC}"
    done
fi

# 2. 检查证书文件
echo ""
echo -e "${BLUE}步骤2: 检查证书文件${NC}"

CERT_FILES=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem"
)

for file in "${CERT_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ 证书文件存在: $file${NC}"
        
        # 检查文件权限
        if [ -r "$file" ]; then
            echo -e "${GREEN}✅ 文件可读${NC}"
        else
            echo -e "${RED}❌ 文件不可读，请检查权限${NC}"
        fi
    else
        echo -e "${RED}❌ 证书文件不存在: $file${NC}"
        echo -e "${YELLOW}请确保证书文件已正确部署${NC}"
    fi
done

# 3. 验证配置文件
echo ""
echo -e "${BLUE}步骤3: 验证配置文件${NC}"

if [ -f "admin/server/config/config.prod.yaml" ]; then
    echo -e "${GREEN}✅ 管理后台配置文件存在${NC}"
    
    # 检查配置文件中的微信配置
    if grep -q "private_key_file: \"\${WECHAT_PRIVATE_KEY_PATH}\"" admin/server/config/config.prod.yaml; then
        echo -e "${GREEN}✅ 配置文件正确引用私钥路径${NC}"
    else
        echo -e "${RED}❌ 配置文件未正确引用私钥路径${NC}"
    fi
else
    echo -e "${RED}❌ 管理后台配置文件不存在${NC}"
    exit 1
fi

# 4. 测试环境变量加载
echo ""
echo -e "${BLUE}步骤4: 测试环境变量加载${NC}"

# 临时加载环境变量进行测试
set -a
source admin.production.env
set +a

echo "关键微信配置变量:"
echo "  WECHAT_APP_ID: ${WECHAT_APP_ID:-未设置}"
echo "  WECHAT_MCH_ID: ${WECHAT_MCH_ID:-未设置}"
echo "  WECHAT_PRIVATE_KEY_PATH: ${WECHAT_PRIVATE_KEY_PATH:-未设置}"
echo "  WECHAT_PUBLIC_KEY_PATH: ${WECHAT_PUBLIC_KEY_PATH:-未设置}"

# 5. 显示修复后的配置
echo ""
echo -e "${BLUE}步骤5: 显示修复后的配置${NC}"

echo -e "${YELLOW}管理后台环境变量 (admin.production.env):${NC}"
echo "微信相关配置:"
grep "^WECHAT_" admin.production.env | head -10

# 6. 重启建议
echo ""
echo -e "${BLUE}步骤6: 重启建议${NC}"
echo -e "${YELLOW}配置修复完成，请重启管理后台服务:${NC}"
echo ""
echo "1. 如果使用 systemd 服务:"
echo "   sudo systemctl restart peizhen-admin.service"
echo "   sudo systemctl status peizhen-admin.service"
echo ""
echo "2. 如果手动启动:"
echo "   # 停止现有服务"
echo "   pkill -f 'admin.*server'"
echo "   "
echo "   # 重新启动"
echo "   source admin.production.env"
echo "   cd admin/server && APP_ENV=prod go run main.go"
echo ""
echo "3. 检查服务状态:"
echo "   journalctl -u peizhen-admin.service -f"
echo ""

# 7. 故障排除提示
echo -e "${BLUE}步骤7: 故障排除提示${NC}"
echo -e "${YELLOW}如果仍有问题，请检查:${NC}"
echo "1. 证书文件是否存在且可读"
echo "2. 环境变量是否正确加载"
echo "3. 配置文件语法是否正确"
echo "4. 微信支付配置是否有效"
echo ""
echo "查看详细错误日志:"
echo "  sudo journalctl -u peizhen-admin.service --no-pager -n 20"

echo ""
echo -e "${GREEN}✅ 管理后台微信配置修复完成${NC}"