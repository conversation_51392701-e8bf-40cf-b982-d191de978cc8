-- 精确检查提现申请ID 1的数据情况
-- 确定 "收款用户姓名不能为空" 错误的具体原因

-- 1. 检查提现申请表 withdrawals 中 ID 1 的数据
SELECT 
    '=== withdrawals 表数据 ===' as section,
    id,
    withdrawal_no,
    user_id,
    amount,
    actual_amount,
    real_name,
    openid,
    status,
    created_at
FROM withdrawals 
WHERE id = 1;

-- 2. 检查转账记录表 withdrawal_transfers 中相关的数据
SELECT 
    '=== withdrawal_transfers 表数据 ===' as section,
    id,
    withdrawal_id,
    transfer_no,
    amount,
    openid,
    real_name,  -- 这个字段可能是问题所在
    status,
    fail_reason,
    retry_count,
    created_at,
    updated_at
FROM withdrawal_transfers 
WHERE withdrawal_id = 1
ORDER BY created_at DESC;

-- 3. 检查数据一致性 - 对比两个表的 real_name 和 openid
SELECT 
    '=== 数据一致性对比 ===' as section,
    w.id as withdrawal_id,
    w.real_name as w_real_name,
    w.openid as w_openid,
    wt.real_name as wt_real_name,
    wt.openid as wt_openid,
    CASE 
        WHEN w.real_name IS NULL OR w.real_name = '' THEN 'withdrawals.real_name 为空'
        WHEN wt.real_name IS NULL OR wt.real_name = '' THEN 'withdrawal_transfers.real_name 为空'
        WHEN w.real_name != wt.real_name THEN 'real_name 不一致'
        ELSE 'real_name 一致'
    END as real_name_status,
    CASE 
        WHEN w.openid IS NULL OR w.openid = '' THEN 'withdrawals.openid 为空'
        WHEN wt.openid IS NULL OR wt.openid = '' THEN 'withdrawal_transfers.openid 为空'
        WHEN w.openid != wt.openid THEN 'openid 不一致'
        ELSE 'openid 一致'
    END as openid_status
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.id = 1;

-- 4. 检查 withdrawal_transfers 表结构
SELECT 
    '=== withdrawal_transfers 表结构 ===' as section,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'withdrawal_transfers' 
  AND COLUMN_NAME IN ('real_name', 'openid')
ORDER BY ORDINAL_POSITION;

-- 5. 检查所有转账记录中 real_name 为空的情况
SELECT 
    '=== 所有转账记录中 real_name 为空的情况 ===' as section,
    COUNT(*) as total_transfers,
    COUNT(CASE WHEN real_name IS NULL OR real_name = '' THEN 1 END) as empty_real_name_count
FROM withdrawal_transfers;

-- 6. 显示最近的转账记录，查看 real_name 字段情况
SELECT 
    '=== 最近的转账记录 ===' as section,
    id,
    withdrawal_id,
    real_name,
    openid,
    status,
    fail_reason,
    created_at
FROM withdrawal_transfers 
ORDER BY created_at DESC 
LIMIT 5;