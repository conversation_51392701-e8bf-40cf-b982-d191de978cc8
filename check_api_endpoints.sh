#!/bin/bash

# 检查API端点可用性
BASE_URL="https://www.kanghuxing.cn"

echo "=== 检查API端点可用性 ==="

echo "1. 检查健康状态..."
curl -s "${BASE_URL}/health" | head -1

echo -e "\n2. 检查管理员登录端点..."
curl -s -X POST "${BASE_URL}/api/admin/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}' | head -1

echo -e "\n3. 检查自动结算触发端点（需要认证）..."
curl -s -X POST "${BASE_URL}/api/admin/reviews/trigger-settlement" \
  -H "Content-Type: application/json" | head -1

echo -e "\n4. 检查手动结算端点（需要认证）..."
curl -s -X POST "${BASE_URL}/api/v1/settlement/process" \
  -H "Content-Type: application/json" \
  -d '{"order_id":10017}' | head -1

echo -e "\n5. 检查结算批次查询端点（需要认证）..."
curl -s "${BASE_URL}/api/v1/settlement/batches" | head -1

echo -e "\n=== 检查完成 ==="
echo "如果看到 '401' 或 'Unauthorized'，说明端点存在但需要认证"
echo "如果看到 '404' 或 'page not found'，说明端点不存在或路径错误"