#!/bin/bash

# 运行微信转账API增强版测试
# 尝试使用不同的参数组合来解决 NO_AUTH 问题

echo "🔧 微信转账API增强版测试"
echo "========================="

# 检查环境
if [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 加载环境配置
echo "📋 加载环境配置..."
if [ -f "production.env" ]; then
    source production.env
    echo "   ✅ 已加载 production.env"
else
    echo "   ⚠️  未找到 production.env，使用环境变量"
fi

# 检查必要的环境变量
required_vars=(
    "WECHAT_MCH_ID"
    "WECHAT_APP_ID" 
    "WECHAT_API_V3_KEY"
    "WECHAT_CERTIFICATE_SERIAL_NUMBER"
    "WECHAT_PRIVATE_KEY_PATH"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "❌ 缺少必要的环境变量:"
    printf '   - %s\n' "${missing_vars[@]}"
    exit 1
fi

echo "✅ 环境变量检查通过"

# 显示配置信息
echo ""
echo "🔧 当前配置:"
echo "   商户号: $WECHAT_MCH_ID"
echo "   应用ID: $WECHAT_APP_ID"
echo "   私钥路径: $WECHAT_PRIVATE_KEY_PATH"
echo "   证书序列号: $WECHAT_CERTIFICATE_SERIAL_NUMBER"
if [ -n "$WECHAT_TRANSFER_NOTIFY_URL" ]; then
    echo "   回调URL: $WECHAT_TRANSFER_NOTIFY_URL"
fi

# 编译测试程序
echo ""
echo "🔨 编译增强版API测试程序..."
cd backend
if ! go build -o ../enhanced_api_test wechat_transfer_api_enhanced.go; then
    echo "❌ 编译失败"
    exit 1
fi
cd ..

echo "✅ 编译成功"

# 运行测试
echo ""
echo "🚀 开始增强版API测试..."
echo "=================================="

# 设置可选的回调URL（如果需要）
if [ -z "$WECHAT_TRANSFER_NOTIFY_URL" ]; then
    export WECHAT_TRANSFER_NOTIFY_URL="https://your-domain.com/wechat/transfer/notify"
    echo "   设置默认回调URL: $WECHAT_TRANSFER_NOTIFY_URL"
fi

# 运行测试
if ./enhanced_api_test; then
    echo ""
    echo "✅ 增强版API测试完成"
    echo ""
    echo "📋 测试结果分析:"
    echo "   如果测试成功，说明找到了可用的API参数组合"
    echo "   如果仍然失败，说明问题确实在商户平台配置"
else
    echo ""
    echo "❌ 增强版API测试失败"
    echo ""
    echo "📋 下一步建议:"
    echo "1. 根据上面的错误分析确定问题类型"
    echo "2. 如果仍然是 NO_AUTH 错误:"
    echo "   - 登录微信支付商户平台 (https://pay.weixin.qq.com/)"
    echo "   - 检查产品中心 → 商家转账功能"
    echo "   - 联系微信支付客服: 95017"
    echo "3. 如果是参数错误:"
    echo "   - 说明权限正常，需要调整API参数"
    echo "   - 可以尝试使用真实的OpenID和用户名"
fi

# 清理临时文件
rm -f enhanced_api_test

echo ""
echo "🎯 增强版API测试完成！"

# 提供额外的诊断信息
echo ""
echo "📊 额外诊断信息:"
echo "=================================="
echo "如果所有技术方案都无效，问题很可能是："
echo ""
echo "🔴 商户平台配置问题:"
echo "   1. 商户号 $WECHAT_MCH_ID 已升级到新版本转账功能"
echo "   2. 但在商户平台的配置不完整或需要重新审核"
echo "   3. 需要在微信支付商户平台手动处理"
echo ""
echo "🟡 可能的解决步骤:"
echo "   1. 登录 https://pay.weixin.qq.com/"
echo "   2. 进入 产品中心 → 商家转账"
echo "   3. 检查功能状态、API权限、审核状态"
echo "   4. 如有问题，重新申请或联系客服"
echo ""
echo "📞 技术支持:"
echo "   微信支付客服: 95017"
echo "   商户号: $WECHAT_MCH_ID"
echo "   错误代码: NO_AUTH"