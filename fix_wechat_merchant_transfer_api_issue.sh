#!/bin/bash

# 修复微信商家转账API问题
# 基于商户平台显示的"商家转账"功能已开通

echo "🔧 修复微信商家转账API问题"
echo "=================================="

# 1. 问题重新分析
echo -e "\n1. 问题重新分析："
echo "   ✅ 商户平台显示'商家转账'功能已开通"
echo "   ❌ 但调用API时仍然提示'无可用的平台证书'"
echo "   🔍 可能是API版本或权限范围问题"

# 2. 检查当前API配置
echo -e "\n2. 检查当前API配置："
echo "   商户号: $(grep WECHAT_MCH_ID production.env | cut -d'=' -f2)"
echo "   证书序列号: $(grep WECHAT_SERIAL_NO production.env | cut -d'=' -f2)"
echo "   转账客户端类型: $(grep WECHAT_TRANSFER_CLIENT_TYPE production.env | cut -d'=' -f2)"

# 3. 创建详细的API测试脚本
echo -e "\n3. 创建详细的API测试脚本："

cat > test_wechat_merchant_transfer_detailed.go << 'EOF'
package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/certificates"
	"github.com/wechatpay-apiv3/wechatpay-go/services/transferbatch"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

func main() {
	fmt.Println("🧪 详细测试微信商家转账API")
	fmt.Println("============================")

	// 从环境变量读取配置
	mchID := os.Getenv("WECHAT_MCH_ID")
	serialNo := os.Getenv("WECHAT_SERIAL_NO")
	apiV3Key := os.Getenv("WECHAT_API_V3_KEY")
	privateKeyPath := os.Getenv("WECHAT_PRIVATE_KEY_PATH")
	appID := os.Getenv("WECHAT_APP_ID")

	fmt.Printf("配置信息:\n")
	fmt.Printf("  商户号: %s\n", mchID)
	fmt.Printf("  应用ID: %s\n", appID)
	fmt.Printf("  证书序列号: %s\n", serialNo)
	fmt.Printf("  私钥路径: %s\n", privateKeyPath)
	fmt.Printf("  APIv3密钥: %s\n", maskString(apiV3Key))

	// 检查必要参数
	if mchID == "" || serialNo == "" || apiV3Key == "" || privateKeyPath == "" {
		log.Fatal("❌ 缺少必要的环境变量")
	}

	// 加载私钥
	fmt.Println("\n=== 步骤1: 加载商户私钥 ===")
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(privateKeyPath)
	if err != nil {
		log.Fatalf("❌ 加载商户私钥失败: %v", err)
	}
	fmt.Println("✅ 商户私钥加载成功")

	// 初始化客户端
	fmt.Println("\n=== 步骤2: 初始化微信支付客户端 ===")
	ctx := context.Background()
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, serialNo, mchPrivateKey, apiV3Key),
	}

	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		fmt.Printf("❌ 初始化微信支付客户端失败: %v\n", err)
		
		// 详细错误分析
		analyzeError(err)
		return
	}
	fmt.Println("✅ 微信支付客户端初始化成功")

	// 测试证书下载
	fmt.Println("\n=== 步骤3: 测试平台证书下载 ===")
	certSvc := certificates.CertificatesApiService{Client: client}
	certResp, certResult, err := certSvc.DownloadCertificates(ctx)

	if err != nil {
		fmt.Printf("❌ 下载平台证书失败: %v\n", err)
		analyzeError(err)
		return
	}

	fmt.Printf("✅ 平台证书下载成功\n")
	fmt.Printf("  HTTP状态码: %d\n", certResult.Response.StatusCode)
	fmt.Printf("  证书数量: %d\n", len(certResp.Data))
	
	for i, cert := range certResp.Data {
		fmt.Printf("  证书 %d: %s (有效期: %s - %s)\n", 
			i+1, *cert.SerialNo, *cert.EffectiveTime, *cert.ExpireTime)
	}

	// 测试转账API权限
	fmt.Println("\n=== 步骤4: 测试转账API权限 ===")
	transferSvc := transferbatch.TransferBatchApiService{Client: client}
	
	// 尝试创建一个测试转账请求（不会真正执行）
	testReq := transferbatch.InitiateBatchTransferRequest{
		Appid:       core.String(appID),
		OutBatchNo:  core.String("TEST_BATCH_" + fmt.Sprintf("%d", 1234567890)),
		BatchName:   core.String("测试批次"),
		BatchRemark: core.String("API权限测试"),
		TotalAmount: core.Int64(1), // 1分
		TotalNum:    core.Int64(1),
		TransferDetailList: []transferbatch.TransferDetailInput{
			{
				OutDetailNo:    core.String("TEST_DETAIL_" + fmt.Sprintf("%d", 1234567890)),
				TransferAmount: core.Int64(1),
				TransferRemark: core.String("测试明细"),
				Openid:         core.String("test_openid_for_permission_check"),
				UserName:       core.String("测试用户"),
			},
		},
		TransferSceneId: core.String("1000"),
	}

	fmt.Println("尝试调用转账API（预期会失败，但可以检查权限）...")
	_, transferResult, transferErr := transferSvc.InitiateBatchTransfer(ctx, testReq)

	if transferErr != nil {
		fmt.Printf("⚠️  转账API调用失败（预期）: %v\n", transferErr)
		
		// 分析转账API错误
		errStr := transferErr.Error()
		if contains(errStr, "RESOURCE_NOT_EXISTS") {
			fmt.Println("🔍 分析: 仍然是平台证书问题")
		} else if contains(errStr, "PARAM_ERROR") {
			fmt.Println("✅ 分析: API权限正常，只是参数问题")
		} else if contains(errStr, "PERMISSION_DENIED") {
			fmt.Println("🔍 分析: 权限不足，可能需要申请转账权限")
		} else {
			fmt.Printf("🔍 分析: 其他错误 - %s\n", errStr)
		}
	} else {
		fmt.Printf("✅ 转账API调用成功（意外）\n")
		fmt.Printf("  HTTP状态码: %d\n", transferResult.Response.StatusCode)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("如果平台证书下载成功，说明基础权限正常")
	fmt.Println("如果转账API仍然失败，可能需要检查具体的转账权限配置")
}

func analyzeError(err error) {
	errStr := err.Error()
	fmt.Println("\n🔍 错误详细分析:")
	
	if contains(errStr, "RESOURCE_NOT_EXISTS") {
		fmt.Println("  错误类型: 资源不存在")
		fmt.Println("  可能原因:")
		fmt.Println("    1. 商户平台虽然显示功能开通，但API权限可能未完全激活")
		fmt.Println("    2. 需要在商户平台进行额外的API权限配置")
		fmt.Println("    3. 功能开通后需要等待一段时间才能使用API")
		fmt.Println("  建议操作:")
		fmt.Println("    1. 检查商户平台 -> 账户中心 -> API安全 -> API权限列表")
		fmt.Println("    2. 联系微信支付客服确认API权限状态")
		fmt.Println("    3. 检查是否需要签署相关协议")
	} else if contains(errStr, "certificate") {
		fmt.Println("  错误类型: 证书相关")
		fmt.Println("  可能原因: 商户私钥或证书配置问题")
	} else if contains(errStr, "signature") {
		fmt.Println("  错误类型: 签名验证失败")
		fmt.Println("  可能原因: APIv3密钥或证书序列号配置错误")
	} else {
		fmt.Println("  错误类型: 其他")
		fmt.Printf("  错误详情: %s\n", errStr)
	}
}

func maskString(s string) string {
	if len(s) <= 8 {
		return "****"
	}
	return s[:4] + "****" + s[len(s)-4:]
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s[len(s)-len(substr):] == substr || 
		    s[:len(substr)] == substr ||
		    (len(s) > len(substr) && 
		     s[len(s)/2-len(substr)/2:len(s)/2+len(substr)/2] == substr))
}
EOF

echo "   ✅ 已创建详细测试脚本: test_wechat_merchant_transfer_detailed.go"

# 4. 创建权限检查脚本
echo -e "\n4. 创建权限检查脚本："

cat > check_wechat_api_permissions.sh << 'EOF'
#!/bin/bash

echo "🔍 检查微信API权限配置"
echo "======================"

echo -e "\n请在微信商户平台进行以下检查："
echo ""
echo "1. 登录微信商户平台 (https://pay.weixin.qq.com)"
echo ""
echo "2. 检查产品中心 -> 商家转账："
echo "   ✅ 确认功能状态为'已开通'或'制作功能'"
echo "   ✅ 查看是否有使用限制或条件"
echo ""
echo "3. 检查账户中心 -> API安全："
echo "   ✅ 查看API权限列表"
echo "   ✅ 确认是否包含'商家转账'相关API"
echo "   ✅ 检查API调用限制"
echo ""
echo "4. 检查账户中心 -> 账户信息："
echo "   ✅ 确认商户主体类型"
echo "   ✅ 查看账户状态是否正常"
echo ""
echo "5. 检查是否需要签署协议："
echo "   ✅ 查看是否有未签署的服务协议"
echo "   ✅ 确认商家转账服务协议状态"
echo ""
echo "6. 联系微信支付客服："
echo "   如果以上都正常，建议联系微信支付客服"
echo "   提供商户号: $(grep WECHAT_MCH_ID production.env | cut -d'=' -f2)"
echo "   提供错误Request-ID: 08EFFBD1C40610B90318EF96ECF5012084162882BA05-269542984"
EOF

chmod +x check_wechat_api_permissions.sh
echo "   ✅ 已创建权限检查脚本: check_wechat_api_permissions.sh"

# 5. 提供下一步操作建议
echo -e "\n5. 下一步操作建议："
echo "=================================="
echo "1. 运行详细测试脚本："
echo "   source production.env"
echo "   go run test_wechat_merchant_transfer_detailed.go"
echo ""
echo "2. 运行权限检查脚本："
echo "   ./check_wechat_api_permissions.sh"
echo ""
echo "3. 如果测试仍然失败："
echo "   - 检查商户平台API权限配置"
echo "   - 联系微信支付客服确认API权限状态"
echo "   - 确认是否需要签署额外的服务协议"

echo -e "\n🎯 分析完成！"
echo "=================================="
echo "既然商家转账功能已开通，问题可能在于："
echo "1. ✅ 功能开通了，但API权限可能需要额外配置"
echo "2. ✅ 可能需要签署相关的服务协议"
echo "3. ✅ 功能开通后可能需要等待一段时间才能使用API"
echo "4. ✅ 可能是API版本或调用方式的问题"