# 微信官方客户端配置指南

## 🔍 当前状态

### 客户端类型
- ✅ **官方客户端已启用**: 使用微信官方SDK进行真实API调用
- ✅ **配置已更新**: 支持官方客户端所需的所有配置项
- ✅ **工厂类已修复**: 正确创建官方客户端实例

### 配置文件更新
**文件**: `backend/config/config.yaml`

```yaml
wechat:
  transfer:
    # 客户端类型配置
    client_type: "${WECHAT_TRANSFER_CLIENT_TYPE:official}"  # 使用官方SDK
    
    # 微信企业付款配置
    app_id: "${WECHAT_TRANSFER_APP_ID:}"                    # 小程序AppID
    mch_id: "${WECHAT_TRANSFER_MCH_ID:}"                    # 商户号
    api_v3_key: "${WECHAT_TRANSFER_APIV3_KEY:}"             # APIv3密钥
    
    # 证书配置（官方SDK需要）
    certificate_serial_number: "${WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER:}" # 商户证书序列号
    private_key_path: "${WECHAT_TRANSFER_PRIVATE_KEY_PATH:}"                   # 私钥文件路径
    
    # 环境配置
    environment: "${WECHAT_TRANSFER_ENVIRONMENT:production}" # 生产环境
    notify_url: "${WECHAT_TRANSFER_NOTIFY_URL:https://www.kanghuxing.cn/api/v1/callback/wechat/transfer}"
    
    # 功能开关
    enabled: true            # 启用企业付款功能
    mock_mode: false         # 关闭模拟模式，使用真实API
```

## 📋 环境变量配置

### 必需的环境变量
```bash
# 基础配置
export WECHAT_TRANSFER_CLIENT_TYPE="official"
export WECHAT_TRANSFER_APP_ID="your_app_id"
export WECHAT_TRANSFER_MCH_ID="your_merchant_id"
export WECHAT_TRANSFER_APIV3_KEY="your_api_v3_key"

# 证书配置
export WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER="your_certificate_serial_number"
export WECHAT_TRANSFER_PRIVATE_KEY_PATH="/path/to/apiclient_key.pem"

# 环境配置
export WECHAT_TRANSFER_ENVIRONMENT="production"
export WECHAT_TRANSFER_NOTIFY_URL="https://www.kanghuxing.cn/api/v1/callback/wechat/transfer"
```

### 可选的环境变量
```bash
# 兼容性配置（如果使用旧配置）
export WECHAT_TRANSFER_SERIAL_NO="your_certificate_serial_number"
export WECHAT_TRANSFER_PRIVATE_KEY_FILE="/path/to/apiclient_key.pem"
```

## 🔧 证书文件准备

### 1. 下载证书文件
从微信商户平台下载以下文件：
- `apiclient_cert.pem` - 商户证书
- `apiclient_key.pem` - 商户私钥
- `apiclient_cert.p12` - 商户证书（PKCS12格式，可选）

### 2. 证书文件部署
```bash
# 创建证书目录
mkdir -p /etc/wechat/certs

# 复制证书文件
cp apiclient_cert.pem /etc/wechat/certs/
cp apiclient_key.pem /etc/wechat/certs/

# 设置文件权限
chmod 600 /etc/wechat/certs/apiclient_key.pem
chmod 644 /etc/wechat/certs/apiclient_cert.pem

# 设置所有者
chown app:app /etc/wechat/certs/*
```

### 3. 获取证书序列号
```bash
# 方法1：使用openssl命令
openssl x509 -in /etc/wechat/certs/apiclient_cert.pem -noout -serial

# 方法2：从微信商户平台获取
# 登录微信商户平台 > 账户中心 > API安全 > API证书
```

## 🚀 部署步骤

### 1. 设置环境变量
```bash
# 在服务器上设置环境变量
vim /etc/environment

# 或者在应用启动脚本中设置
export WECHAT_TRANSFER_CLIENT_TYPE="official"
export WECHAT_TRANSFER_APP_ID="wxd678efh567hg6787"
export WECHAT_TRANSFER_MCH_ID="1230000109"
export WECHAT_TRANSFER_APIV3_KEY="your_actual_api_v3_key"
export WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER="3775B6A45ACD588826D15E583A95F5DD40A78BA1"
export WECHAT_TRANSFER_PRIVATE_KEY_PATH="/etc/wechat/certs/apiclient_key.pem"
export WECHAT_TRANSFER_ENVIRONMENT="production"
export WECHAT_TRANSFER_NOTIFY_URL="https://www.kanghuxing.cn/api/v1/callback/wechat/transfer"
```

### 2. 重启应用
```bash
# 重启后端服务以加载新配置
systemctl restart your-app-service

# 或者如果使用Docker
docker restart your-app-container
```

### 3. 验证配置
```bash
# 检查日志，确认使用官方客户端
tail -f /var/log/your-app/app.log | grep "使用官方SDK客户端"

# 应该看到类似的日志：
# "使用官方SDK客户端 V2"
```

## 🧪 测试验证

### 1. 配置验证测试
创建测试脚本验证配置：

```bash
#!/bin/bash
echo "=== 微信官方客户端配置验证 ==="

# 检查环境变量
echo "1. 检查环境变量："
echo "   CLIENT_TYPE: $WECHAT_TRANSFER_CLIENT_TYPE"
echo "   APP_ID: $WECHAT_TRANSFER_APP_ID"
echo "   MCH_ID: $WECHAT_TRANSFER_MCH_ID"
echo "   ENVIRONMENT: $WECHAT_TRANSFER_ENVIRONMENT"

# 检查证书文件
echo "2. 检查证书文件："
if [ -f "$WECHAT_TRANSFER_PRIVATE_KEY_PATH" ]; then
    echo "   ✅ 私钥文件存在: $WECHAT_TRANSFER_PRIVATE_KEY_PATH"
else
    echo "   ❌ 私钥文件不存在: $WECHAT_TRANSFER_PRIVATE_KEY_PATH"
fi

# 检查证书序列号
echo "3. 证书序列号: $WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER"

echo "=== 验证完成 ==="
```

### 2. API调用测试
在管理后台测试"确认打款"功能：

**预期行为**:
- 使用官方SDK进行真实API调用
- 调用微信企业付款API v3
- 返回真实的微信批次号和状态
- 支持真实的转账查询和回调

## 🔍 故障排除

### 常见问题

#### 1. 证书加载失败
**错误**: `加载商户私钥失败`
**解决方案**:
```bash
# 检查文件路径和权限
ls -la $WECHAT_TRANSFER_PRIVATE_KEY_PATH
# 确保应用有读取权限
chmod 600 $WECHAT_TRANSFER_PRIVATE_KEY_PATH
```

#### 2. 证书序列号错误
**错误**: `证书序列号验证失败`
**解决方案**:
```bash
# 重新获取证书序列号
openssl x509 -in /path/to/apiclient_cert.pem -noout -serial
# 更新环境变量
export WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER="正确的序列号"
```

#### 3. API调用失败
**错误**: `微信支付API调用失败`
**解决方案**:
- 检查商户号是否开通企业付款功能
- 确认服务器IP已添加到白名单
- 验证APIv3密钥是否正确

### 日志分析
```bash
# 查看详细日志
tail -f logs/app.log | grep -E "(官方SDK|微信企业付款|Transfer)"

# 成功日志示例：
# "使用官方SDK客户端 V2"
# "发起微信企业付款" out_batch_no="BATCH_xxx" total_amount=1000
# "微信企业付款API调用成功" status_code=200
```

## 📝 配置检查清单

### 部署前检查
- [ ] 环境变量已正确设置
- [ ] 证书文件已部署到正确位置
- [ ] 证书文件权限已正确设置
- [ ] 证书序列号已正确配置
- [ ] 微信商户平台已开通企业付款功能
- [ ] 服务器IP已添加到白名单

### 部署后验证
- [ ] 应用启动无错误
- [ ] 日志显示使用官方SDK客户端
- [ ] 配置验证测试通过
- [ ] API调用测试成功
- [ ] 转账功能正常工作

## 🔄 回滚方案

如果官方客户端出现问题，可以快速回滚到简化客户端：

```bash
# 临时回滚到简化客户端
export WECHAT_TRANSFER_CLIENT_TYPE="simple"
export WECHAT_TRANSFER_MOCK_MODE="true"

# 重启应用
systemctl restart your-app-service
```

这样可以确保服务的可用性，同时排查官方客户端的问题。