# API密钥配置修复

## 问题描述
管理后台提现管理页面点击"重新打款"时，后端服务返回API密钥认证失败错误：

```json
{
  "level": "WARN",
  "msg": "API密钥认证失败: 密钥ID无效",
  "provided_key_id": "admin-key-prod-001",
  "expected_key_id": "${BACKEND_API_KEY_ID:admin-key-prod-001}",
  "path": "/api/v1/internal/wechat/transfer/1/retry",
  "method": "POST"
}
```

## 问题分析

### 1. 根本原因
后端服务的配置加载器没有正确绑定内部API相关的环境变量，导致配置文件中的环境变量占位符没有被解析，仍然保持原始格式。

### 2. 配置对比

#### 管理后台发送的密钥ID
```
admin-key-prod-001
```

#### 后端服务期望的密钥ID
```
${BACKEND_API_KEY_ID:admin-key-prod-001}  # 环境变量未解析
```

### 3. 配置文件分析

#### 后端配置 (`backend/config/conf/config.prod.yaml`)
```yaml
security:
  internal_api:
    key_id: "${BACKEND_API_KEY_ID:admin-key-prod-001}"
    secret_key: "${BACKEND_API_SECRET_KEY:prod-32-char-secret-key-here-456}"
    algorithm: "HMAC-SHA256"
    ttl: 300
```

#### 管理后台配置 (`admin/server/config/config.prod.yaml`)
```yaml
backend:
  api:
    api_key:
      key_id: "${BACKEND_API_KEY_ID:admin-key-prod-001}"
      secret_key: "${BACKEND_API_SECRET_KEY:prod-32-char-secret-key-here-456}"
      algorithm: "HMAC-SHA256"
      ttl: 300
```

## 修复方案

### 1. 添加环境变量绑定
**文件**: `backend/config/loader.go`

在安全配置部分添加内部API环境变量绑定：

```go
// 安全配置
viper.BindEnv("security.api_key", "APP_API_KEY", "API_KEY")
viper.BindEnv("security.internal_api.key_id", "APP_INTERNAL_API_KEY_ID", "BACKEND_API_KEY_ID")
viper.BindEnv("security.internal_api.secret_key", "APP_INTERNAL_API_SECRET_KEY", "BACKEND_API_SECRET_KEY")
viper.BindEnv("security.internal_api.algorithm", "APP_INTERNAL_API_ALGORITHM", "BACKEND_API_ALGORITHM")
viper.BindEnv("security.internal_api.ttl", "APP_INTERNAL_API_TTL", "BACKEND_API_TTL")
```

### 2. 环境变量优先级
配置加载器支持多个环境变量名，按优先级顺序：
1. `APP_INTERNAL_API_KEY_ID` (应用特定)
2. `BACKEND_API_KEY_ID` (通用)

### 3. 配置解析流程
```
配置文件 → Viper加载 → 环境变量绑定 → 环境变量解析 → 最终配置值
```

## 环境变量设置

### 1. 生产环境变量
```bash
export BACKEND_API_KEY_ID=admin-key-prod-001
export BACKEND_API_SECRET_KEY=prod-32-char-secret-key-here-456
export BACKEND_API_ALGORITHM=HMAC-SHA256
export BACKEND_API_TTL=300
```

### 2. 开发环境变量
```bash
export BACKEND_API_KEY_ID=admin-key-001
export BACKEND_API_SECRET_KEY=your-32-char-secret-key-here-123
export BACKEND_API_ALGORITHM=HMAC-SHA256
export BACKEND_API_TTL=300
```

## 配置验证

### 1. 后端服务配置验证
启动后端服务时，检查日志中的配置信息：
```
设置内部转账路由
✓ 内部转账路由设置完成
```

### 2. API密钥认证验证
成功的认证日志应该显示：
```json
{
  "level": "INFO",
  "msg": "API密钥认证成功",
  "key_id": "admin-key-prod-001",
  "path": "/api/v1/internal/wechat/transfer/1/retry"
}
```

### 3. 失败的认证日志
如果配置仍然有问题，会显示：
```json
{
  "level": "WARN",
  "msg": "API密钥认证失败: 密钥ID无效",
  "provided_key_id": "admin-key-prod-001",
  "expected_key_id": "admin-key-prod-001"
}
```

## 部署步骤

### 1. 更新代码
```bash
# 后端服务
cd backend
git pull
go build -o bin/peizhen main.go
```

### 2. 设置环境变量
```bash
# 在服务器上设置环境变量
export BACKEND_API_KEY_ID=admin-key-prod-001
export BACKEND_API_SECRET_KEY=prod-32-char-secret-key-here-456
export BACKEND_API_ALGORITHM=HMAC-SHA256
export BACKEND_API_TTL=300
```

### 3. 重启服务
```bash
# 重启后端服务
systemctl restart peizhen-backend

# 重启管理后台服务
systemctl restart peizhen-admin
```

### 4. 验证配置
```bash
# 检查服务状态
systemctl status peizhen-backend
systemctl status peizhen-admin

# 检查日志
journalctl -u peizhen-backend -f
journalctl -u peizhen-admin -f
```

## 安全考虑

### 1. 密钥管理
- 生产环境使用强密钥（至少32字符）
- 定期轮换API密钥
- 不要在代码中硬编码密钥

### 2. 环境变量安全
- 使用系统环境变量而不是配置文件存储敏感信息
- 限制环境变量的访问权限
- 在日志中不要输出完整的密钥信息

### 3. 网络安全
- 使用HTTPS确保传输安全
- 配置防火墙限制内部API访问
- 监控异常的API调用

## 监控和告警

### 1. 关键指标
- API密钥认证成功率
- API密钥认证失败次数
- 内部API调用延迟
- 转账重试成功率

### 2. 告警规则
- API密钥认证失败率 > 5%
- 连续认证失败 > 10次
- 内部API调用延迟 > 5秒
- 转账重试失败率 > 20%

## 故障排查

### 1. 常见问题
- **环境变量未设置**: 检查系统环境变量
- **配置文件错误**: 验证YAML语法和格式
- **服务未重启**: 确保修改后重启了服务
- **网络问题**: 检查服务间网络连通性

### 2. 调试步骤
1. 检查环境变量是否正确设置
2. 验证配置文件语法
3. 查看服务启动日志
4. 测试API密钥认证
5. 检查网络连接

## 测试验证

### 1. 单元测试
```bash
cd backend
go test ./internal/middleware/...
```

### 2. 集成测试
```bash
# 使用测试脚本验证
./test_api_key_config.sh
```

### 3. 功能测试
1. 在管理后台提现管理页面点击"重新打款"
2. 检查API调用是否成功
3. 验证转账重试功能是否正常

## 影响范围

- 修复了内部API的密钥认证问题
- 完善了配置管理的环境变量支持
- 提升了系统的安全性和可维护性
- 为后续功能扩展提供了稳定的基础