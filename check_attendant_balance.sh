#!/bin/bash

# 检查陪诊师账户余额
ATTENDANT_ID=13
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

echo "=== 检查陪诊师账户余额 ==="
echo "陪诊师ID: $ATTENDANT_ID"
echo ""

# 1. 检查陪诊师收入汇总
echo "1. 陪诊师收入汇总:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '收入汇总' as type,
    ai.attendant_id,
    COUNT(*) as 订单数量,
    SUM(ai.amount) as 总收入金额,
    SUM(CASE WHEN ai.status = 2 THEN ai.amount ELSE 0 END) as 已结算金额,
    SUM(CASE WHEN ai.status = 1 THEN ai.amount ELSE 0 END) as 待结算金额
FROM attendant_income ai
WHERE ai.attendant_id = $ATTENDANT_ID
GROUP BY ai.attendant_id;
" 2>/dev/null
echo ""

# 2. 检查accounts表
echo "2. 检查accounts表:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '账户信息' as type,
    a.*
FROM accounts a
WHERE a.user_id = $ATTENDANT_ID;
" 2>/dev/null
echo ""

# 3. 检查所有收入记录
echo "3. 陪诊师所有收入记录:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    ai.id,
    ai.order_id,
    o.order_no,
    ai.amount,
    ai.status,
    ai.settle_time,
    CASE 
        WHEN ai.status = 2 THEN '✅ 已结算'
        WHEN ai.status = 1 THEN '⏳ 待结算'
        ELSE CONCAT('❓ 状态: ', ai.status)
    END as status_text
FROM attendant_income ai
LEFT JOIN orders o ON ai.order_id = o.id
WHERE ai.attendant_id = $ATTENDANT_ID
ORDER BY ai.created_at DESC;
" 2>/dev/null
echo ""

# 4. 检查提现记录
echo "4. 提现记录:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '提现记录' as type,
    w.*
FROM withdrawals w
WHERE w.user_id = $ATTENDANT_ID
ORDER BY w.created_at DESC
LIMIT 5;
" 2>/dev/null