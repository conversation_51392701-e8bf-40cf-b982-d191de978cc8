-- 修复提现数据不一致问题
-- 问题：提现申请状态为"转账中"(status=5)，但没有对应的转账记录

-- 1. 查看当前问题数据
SELECT 
    w.id as withdrawal_id,
    w.withdrawal_no,
    w.amount,
    w.status,
    w.openid,
    w.real_name,
    CASE w.status
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '已打款'
        WHEN 4 THEN '已驳回'
        WHEN 5 THEN '转账中'
        WHEN 6 THEN '已到账'
        WHEN 7 THEN '转账失败'
        ELSE '未知状态'
    END as status_text,
    COUNT(wt.id) as transfer_count
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.status = 5  -- 转账中状态
GROUP BY w.id
HAVING transfer_count = 0;  -- 但没有转账记录

-- 2. 修复数据：将状态重置为"审核通过"(status=2)
-- 这样就可以重新发起转账了
UPDATE withdrawals 
SET 
    status = 2,  -- 审核通过
    updated_at = NOW()
WHERE id = 1 
  AND status = 5  -- 当前是转账中
  AND NOT EXISTS (
      SELECT 1 FROM withdrawal_transfers 
      WHERE withdrawal_id = withdrawals.id
  );

-- 3. 验证修复结果
SELECT 
    id,
    withdrawal_no,
    amount,
    status,
    CASE status
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '已打款'
        WHEN 4 THEN '已驳回'
        WHEN 5 THEN '转账中'
        WHEN 6 THEN '已到账'
        WHEN 7 THEN '转账失败'
        ELSE '未知状态'
    END as status_text,
    updated_at
FROM withdrawals 
WHERE id = 1;