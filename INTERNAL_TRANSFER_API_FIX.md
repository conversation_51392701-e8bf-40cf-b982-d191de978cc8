# 内部转账API 404错误修复

## 问题描述
管理后台提现管理页面点击"重新打款"按钮时，虽然前端API路径已修复为正确的 `/api/admin/withdrawals/transfer/retry`，但管理后台通过BackendAPIClient调用后端服务的内部API `/api/v1/internal/wechat/transfer/1/retry` 仍然返回404错误。

## 问题根因
后端服务的main.go中没有注册内部转账路由 `SetupInternalTransferRoutes`，导致管理后台无法调用后端服务的内部转账API。

## 修复方案

### 1. 添加内部转账处理器初始化
**文件**: `backend/main.go`

```go
// 在处理器初始化部分添加
// 初始化内部转账处理器
internalTransferHandler := handler.NewInternalTransferHandler(serviceContainer.WechatTransferService, zapLogger)
```

### 2. 注册内部转账路由
**文件**: `backend/main.go`

```go
// 在路由设置部分添加
// 设置内部转账路由
log.Println("设置内部转账路由")
apiKeyConfig := dto.APIKeyConfig{
    KeyID:     cfg.Security.InternalAPI.KeyID,
    SecretKey: cfg.Security.InternalAPI.SecretKey,
    Algorithm: cfg.Security.InternalAPI.Algorithm,
    TTL:       cfg.Security.InternalAPI.TTL,
}
apiKeyAuthMiddleware := middleware.NewAPIKeyAuthMiddleware(apiKeyConfig, zapLogger)
router.SetupInternalTransferRoutes(r, internalTransferHandler, apiKeyAuthMiddleware)
log.Printf("✓ 内部转账路由设置完成")
```

## 相关组件验证

### 1. 内部转账路由配置
**文件**: `backend/internal/router/internal_transfer_router.go`

```go
func SetupInternalTransferRoutes(r *gin.Engine, transferHandler *handler.InternalTransferHandler, authMiddleware *middleware.APIKeyAuthMiddleware) {
    internal := r.Group("/api/v1/internal")
    internal.Use(authMiddleware.AuthorizeAPIKey())
    internal.Use(authMiddleware.RateLimitMiddleware())

    wechatTransfer := internal.Group("/wechat")
    {
        // 发起转账
        wechatTransfer.POST("/transfer", transferHandler.InitiateTransfer)
        // 查询转账状态
        wechatTransfer.GET("/transfer/:withdrawal_id", transferHandler.QueryTransferStatus)
        // 重试转账
        wechatTransfer.POST("/transfer/:withdrawal_id/retry", transferHandler.RetryTransfer)
        // 批量查询转账状态
        wechatTransfer.POST("/transfer/batch-query", transferHandler.BatchQueryTransferStatus)
    }
}
```

### 2. 内部转账处理器
**文件**: `backend/internal/handler/internal_transfer_handler.go`

```go
type InternalTransferHandler struct {
    transferService service.IWechatTransferService
    logger          *zap.Logger
}

func (h *InternalTransferHandler) RetryTransfer(c *gin.Context) {
    // 处理转账重试逻辑
}
```

### 3. API密钥认证中间件
**文件**: `backend/internal/middleware/api_key_auth.go`

```go
type APIKeyAuthMiddleware struct {
    config dto.APIKeyConfig
    logger *zap.Logger
}

func (m *APIKeyAuthMiddleware) AuthorizeAPIKey() gin.HandlerFunc {
    // API密钥认证逻辑
}
```

## 配置要求

### 1. 后端配置
**文件**: `backend/config/conf/config.dev.yaml`

```yaml
security:
  internal_api:
    key_id: "admin-key-001"
    secret_key: "your-32-char-secret-key-here-123"
    algorithm: "HMAC-SHA256"
    ttl: 300
```

### 2. 管理后台配置
**文件**: `admin/server/config/config.dev.yaml`

```yaml
backend:
  api:
    base_url: "https://www.kanghuxing.cn"
    timeout: 30s
    api_key:
      key_id: "admin-key-001"
      secret_key: "your-32-char-secret-key-here-123"
      algorithm: "HMAC-SHA256"
      ttl: 300
```

## 完整的API调用流程

1. **管理后台前端** → 调用管理后台API
   ```
   POST /api/admin/withdrawals/transfer/retry
   ```

2. **管理后台后端** → 通过BackendAPIClient调用后端服务内部API
   ```
   POST /api/v1/internal/wechat/transfer/{withdrawal_id}/retry
   Headers: Authorization: APIKey keyId=xxx, signature=xxx, timestamp=xxx
   ```

3. **后端服务** → 处理转账重试请求
   ```
   - API密钥认证
   - 调用微信转账服务
   - 返回处理结果
   ```

## 测试验证

### 1. 编译测试
```bash
cd backend
go build -o /tmp/test_build main.go
```

### 2. API测试
使用提供的测试脚本 `test_internal_transfer_api.sh` 验证API可用性。

### 3. 集成测试
1. 重启后端服务
2. 在管理后台提现管理页面点击"重新打款"
3. 验证API调用成功

## 部署说明

1. **后端服务**：需要重新编译并重启
2. **管理后台**：无需修改，已在之前修复
3. **配置检查**：确保API密钥配置正确且一致

## 安全注意事项

1. **API密钥管理**：确保生产环境使用强密钥
2. **签名验证**：所有内部API调用都需要HMAC-SHA256签名
3. **时间戳验证**：防止重放攻击
4. **频率限制**：内置频率限制机制

## 监控和日志

- 所有API调用都有详细的日志记录
- 认证失败会记录警告日志
- 转账操作会记录操作日志
- 支持性能监控和错误统计

## 影响范围

- 修复了提现管理的"重新打款"功能
- 完善了内部API的路由注册
- 提升了系统的完整性和可用性
- 为后续内部API扩展奠定了基础