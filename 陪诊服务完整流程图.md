# 陪诊服务完整流程图

## 概述
本文档描述了陪诊服务从患者预约到陪诊师最终提现的完整业务流程，包括订单状态流转、结算机制和提现流程。

## 主要参与角色
- **患者**: 服务需求方
- **陪诊师**: 服务提供方  
- **系统**: 自动匹配和调度
- **管理员**: 人工干预和审核
- **微信支付**: 支付和转账服务

---

## 完整流程图

```mermaid
graph TD
    %% 预约阶段
    A[患者发起预约] --> B{选择陪诊师}
    B -->|指定陪诊师| C[直接预约]
    B -->|系统推荐| D[自动匹配]
    
    C --> E[创建订单]
    D --> F{匹配成功?}
    F -->|是| E
    F -->|否| G[人工分配]
    G --> E
    
    %% 支付阶段
    E --> H[订单状态: 待支付]
    H --> I[患者支付]
    I --> J{支付成功?}
    J -->|是| K[订单状态: 已支付]
    J -->|否| L[支付失败，订单取消]
    
    %% 确认阶段
    K --> M[陪诊师确认接单]
    M --> N{陪诊师响应}
    N -->|接受| O[订单状态: 已确认]
    N -->|拒绝| P[重新匹配/人工分配]
    P --> G
    
    %% 服务执行阶段
    O --> Q[订单状态: 进行中]
    Q --> R[陪诊师提供服务]
    R --> S[服务完成]
    
    %% 完成确认阶段
    S --> T{完成类型}
    T -->|正常完成| U[订单状态: 已完成]
    T -->|异常完成| V[订单状态: 异常完成]
    T -->|需要审核| W[订单状态: 待审核]
    
    %% 审核流程
    W --> X[管理员审核]
    X --> Y{审核结果}
    Y -->|通过| U
    Y -->|拒绝| Z[订单状态: 审核不通过]
    Y -->|需要补充信息| AA[要求补充材料]
    AA --> W
    
    %% 评价阶段
    U --> BB[患者评价]
    BB --> CC[订单状态: 已评价]
    
    %% 结算阶段 - T+2自动结算
    CC --> DD[T+2结算检查]
    DD --> EE{结算条件检查}
    EE -->|满足条件| FF[自动结算]
    EE -->|不满足条件| GG[等待下次检查]
    
    %% 结算审核
    FF --> HH{需要审核?}
    HH -->|是| II[结算审核]
    HH -->|否| JJ[直接结算]
    
    II --> KK{审核结果}
    KK -->|通过| JJ
    KK -->|拒绝| LL[结算失败]
    KK -->|需要人工处理| MM[转人工处理]
    
    %% 收入记录
    JJ --> NN[生成陪诊师收入记录]
    NN --> OO[收入状态: 已结算]
    
    %% 提现流程
    OO --> PP[陪诊师申请提现]
    PP --> QQ{提现条件检查}
    QQ -->|满足| RR[创建提现申请]
    QQ -->|不满足| SS[提现失败]
    
    RR --> TT[提现状态: 处理中]
    TT --> UU[调用微信转账API]
    UU --> VV{转账结果}
    VV -->|成功| WW[提现状态: 成功]
    VV -->|失败| XX[提现状态: 失败]
    VV -->|处理中| YY[等待微信回调]
    
    %% 失败重试
    XX --> ZZ[自动重试机制]
    ZZ --> AAA{重试次数检查}
    AAA -->|未超限| UU
    AAA -->|超限| BBB[转人工处理]
    
    %% 回调处理
    YY --> CCC[微信支付回调]
    CCC --> DDD{回调结果}
    DDD -->|成功| WW
    DDD -->|失败| XX
    
    %% 异常处理
    L --> EEE[订单异常处理]
    Z --> EEE
    LL --> EEE
    SS --> EEE
    BBB --> EEE
    
    %% 最终状态
    WW --> FFF[流程完成]
    EEE --> GGG[异常处理完成]
    
    %% 样式定义
    classDef orderStatus fill:#e1f5fe
    classDef paymentStatus fill:#f3e5f5
    classDef settlementStatus fill:#e8f5e8
    classDef withdrawalStatus fill:#fff3e0
    classDef errorStatus fill:#ffebee
    
    class H,K,O,Q,U,V,W,CC orderStatus
    class I,J paymentStatus
    class DD,FF,JJ,NN,OO settlementStatus
    class PP,RR,TT,WW withdrawalStatus
    class L,Z,LL,SS,XX,EEE errorStatus
```

---

## 详细状态说明

### 1. 订单状态流转

| 状态 | 说明 | 触发条件 |
|------|------|----------|
| 待支付 | 订单已创建，等待支付 | 匹配成功后创建订单 |
| 已支付 | 支付完成，等待陪诊师确认 | 微信支付成功回调 |
| 已确认 | 陪诊师已接单 | 陪诊师确认接单 |
| 进行中 | 服务正在进行 | 到达服务时间 |
| 已完成 | 服务正常完成 | 陪诊师标记完成 |
| 异常完成 | 服务异常完成 | 出现异常情况 |
| 待审核 | 需要管理员审核 | 系统判断需要人工审核 |
| 已评价 | 患者已评价 | 患者提交评价 |

### 2. 结算机制

#### T+2自动结算规则
- **触发时间**: 订单完成后第2个工作日
- **检查条件**:
  - 订单状态为"已评价"
  - 无争议或投诉
  - 超过冻结期（可配置）
- **结算金额**: 订单金额 - 平台佣金 - 其他费用

#### 结算审核机制
- **自动审核**: 金额小于阈值且无异常
- **人工审核**: 大额订单或存在异常标记
- **审核内容**: 服务质量、投诉记录、合规性检查

### 3. 提现流程

#### 提现条件
- 账户余额 ≥ 最小提现金额
- 实名认证已完成
- 无冻结状态
- 符合提现频率限制

#### 微信转账集成
- **API版本**: 微信支付API v3
- **转账方式**: 企业付款到零钱
- **回调处理**: 异步回调更新状态
- **失败重试**: 最多3次自动重试

---

## 关键配置参数

### 结算配置
```yaml
settlement:
  freeze_days: 2          # 冻结天数
  auto_threshold: 1000    # 自动结算阈值(元)
  review_threshold: 5000  # 需要审核的金额阈值(元)
  batch_size: 100         # 批量处理数量
```

### 提现配置
```yaml
withdrawal:
  min_amount: 1           # 最小提现金额(元)
  max_amount: 50000       # 最大提现金额(元)
  daily_limit: 3          # 每日提现次数限制
  retry_times: 3          # 失败重试次数
  retry_interval: 300     # 重试间隔(秒)
```

---

## 异常处理机制

### 1. 订单异常
- **支付失败**: 自动取消订单，释放陪诊师档期
- **陪诊师拒单**: 重新匹配或人工分配
- **服务异常**: 进入异常处理流程，可能涉及退款

### 2. 结算异常
- **数据不一致**: 触发一致性检查和修复
- **审核超时**: 自动升级到高级管理员
- **系统故障**: 记录错误日志，支持手动重试

### 3. 提现异常
- **微信API异常**: 自动重试机制
- **账户异常**: 冻结提现功能，人工处理
- **网络超时**: 状态查询和补偿机制

---

## 监控和报警

### 关键指标监控
- 订单完成率
- 结算成功率  
- 提现成功率
- 异常订单比例
- 资金流水一致性

### 报警规则
- 结算失败率 > 5%
- 提现失败率 > 10%
- 资金不一致检测
- 长时间未处理的异常订单

---

## 总结

这个完整的流程涵盖了陪诊服务的全生命周期，从患者预约到陪诊师最终收到款项。系统设计了多层保障机制：

1. **自动化优先**: 大部分流程自动化处理，提高效率
2. **人工兜底**: 关键节点支持人工干预，确保服务质量
3. **异常处理**: 完善的异常处理和重试机制
4. **资金安全**: 多重校验确保资金流转安全
5. **监控报警**: 实时监控关键指标，及时发现问题

整个流程设计既保证了用户体验，又确保了平台的稳定运营和资金安全。