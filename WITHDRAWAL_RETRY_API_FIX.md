# 提现重试API 404错误修复

## 问题描述
管理后台提现管理页面点击"重新打款"按钮时，调用接口 `https://www.kanghuxing.cn/api/admin/transfers/retry` 返回404错误。

## 问题原因
前端API调用路径与后端路由配置不匹配：
- **前端调用路径**: `/api/admin/transfers/retry`
- **后端路由配置**: `/api/admin/withdrawals/transfer/retry`

## 修复方案

### 1. 修复前端API调用路径
**文件**: `admin/web/src/api/withdrawal-management.js`

```javascript
// 修复前
export function retryTransfer(data) {
  return request({
    url: '/api/admin/transfers/retry',  // 错误路径
    method: 'post',
    data
  })
}

// 修复后
export function retryTransfer(data) {
  return request({
    url: '/api/admin/withdrawals/transfer/retry',  // 正确路径
    method: 'post',
    data
  })
}
```

### 2. 补充缺失的转账相关API
在前端API文件中添加了其他转账相关的API调用：

```javascript
// 查询转账状态
export function queryTransferStatus(params) {
  return request({
    url: '/api/admin/withdrawals/transfer/status',
    method: 'get',
    params
  })
}

// 获取转账详情
export function getTransferDetail(batchNo) {
  return request({
    url: `/api/admin/withdrawals/transfer/${batchNo}/detail`,
    method: 'get'
  })
}
```

### 3. 修复后端注释
**文件**: `admin/server/handler/withdrawal_handler.go`

```go
// 修复前
// @Router /api/admin/transfers/retry [post]

// 修复后
// @Router /api/admin/withdrawals/transfer/retry [post]
```

## 后端路由配置
**文件**: `admin/server/router/withdrawal_router.go`

```go
func SetupWithdrawalRoutes(r *gin.RouterGroup, withdrawalHandler *handler.WithdrawalHandler) {
	withdrawals := r.Group("/withdrawals")
	{
		// 转账管理功能
		withdrawals.GET("/transfer/status", withdrawalHandler.QueryTransferStatus)
		withdrawals.POST("/transfer/retry", withdrawalHandler.RetryTransfer)         // 重试转账
		withdrawals.GET("/transfer/:batch_no/detail", withdrawalHandler.GetTransferDetail)
	}
}
```

## 完整的提现管理API列表

| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取提现列表 | GET | `/api/admin/withdrawals` | 分页查询提现记录 |
| 获取提现详情 | GET | `/api/admin/withdrawals/:id` | 获取单个提现记录详情 |
| 获取提现统计 | GET | `/api/admin/withdrawals/stats` | 获取提现统计数据 |
| 获取今日统计 | GET | `/api/admin/withdrawals/stats/today` | 获取今日提现统计 |
| 审批提现 | POST | `/api/admin/withdrawals/:id/approve` | 单个提现审批 |
| 批量审批 | POST | `/api/admin/withdrawals/batch-approve` | 批量审批提现 |
| 确认打款 | POST | `/api/admin/withdrawals/pay` | 确认打款操作 |
| 查询转账状态 | GET | `/api/admin/withdrawals/transfer/status` | 查询转账状态 |
| 重试转账 | POST | `/api/admin/withdrawals/transfer/retry` | 重试失败的转账 |
| 获取转账详情 | GET | `/api/admin/withdrawals/transfer/:batch_no/detail` | 获取转账详情 |

## 测试验证
创建了测试脚本 `test_withdrawal_retry_api.sh` 用于验证修复效果。

## 部署说明
1. 前端需要重新构建并部署
2. 后端无需重启，路由配置本身是正确的
3. 建议清除浏览器缓存以确保加载新的前端代码

## 影响范围
- 修复了提现管理页面的"重新打款"功能
- 补充了完整的转账管理API调用
- 提升了提现管理功能的完整性

## 注意事项
- 确保管理员有相应的权限访问这些API
- 转账重试功能需要配置正确的微信支付参数
- 建议在生产环境部署前先在测试环境验证