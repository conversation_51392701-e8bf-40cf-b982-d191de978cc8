# 环境变量配置指南

## 概述
管理后台和后端服务都需要相同的API密钥配置，但它们通过不同的方式读取环境变量。

## 环境变量设置

### 需要设置的环境变量
无论是管理后台还是后端服务，都需要设置以下**相同的环境变量**：

```bash
# API密钥配置
export BACKEND_API_KEY_ID="admin-key-prod-001"
export BACKEND_API_SECRET_KEY="prod-32-char-secret-key-here-456"
export BACKEND_API_ALGORITHM="HMAC-SHA256"
export BACKEND_API_TTL="300"
```

## 配置读取机制

### 1. 管理后台 (admin/server)

#### 配置文件路径
- 开发环境: `admin/server/config/config.dev.yaml`
- 生产环境: `admin/server/config/config.prod.yaml`

#### 配置内容
```yaml
backend:
  api:
    base_url: "https://www.kanghuxing.cn"
    timeout: 30
    api_key:
      key_id: "${BACKEND_API_KEY_ID:admin-key-prod-001}"
      secret_key: "${BACKEND_API_SECRET_KEY:prod-32-char-secret-key-here-456}"
      algorithm: "HMAC-SHA256"
      ttl: 300
```

#### 读取方式
管理后台使用自定义的 `expandEnvVars` 函数解析 `${VAR:default}` 语法：
- 如果环境变量 `BACKEND_API_KEY_ID` 存在，使用环境变量值
- 如果环境变量不存在，使用默认值 `admin-key-prod-001`

### 2. 后端服务 (backend)

#### 配置文件路径
- 开发环境: `backend/config/conf/config.dev.yaml`
- 生产环境: `backend/config/conf/config.prod.yaml`

#### 配置内容
```yaml
security:
  internal_api:
    key_id: "${BACKEND_API_KEY_ID:admin-key-prod-001}"
    secret_key: "${BACKEND_API_SECRET_KEY:prod-32-char-secret-key-here-456}"
    algorithm: "HMAC-SHA256"
    ttl: 300
```

#### 读取方式
后端服务使用 Viper 的环境变量绑定机制：

**文件**: `backend/config/loader.go`
```go
// 安全配置
viper.BindEnv("security.internal_api.key_id", "APP_INTERNAL_API_KEY_ID", "BACKEND_API_KEY_ID")
viper.BindEnv("security.internal_api.secret_key", "APP_INTERNAL_API_SECRET_KEY", "BACKEND_API_SECRET_KEY")
viper.BindEnv("security.internal_api.algorithm", "APP_INTERNAL_API_ALGORITHM", "BACKEND_API_ALGORITHM")
viper.BindEnv("security.internal_api.ttl", "APP_INTERNAL_API_TTL", "BACKEND_API_TTL")
```

#### 环境变量优先级
1. `APP_INTERNAL_API_KEY_ID` (优先)
2. `BACKEND_API_KEY_ID` (回退)

## 部署配置

### 生产环境
```bash
# 在服务器上设置环境变量
export BACKEND_API_KEY_ID="admin-key-prod-001"
export BACKEND_API_SECRET_KEY="prod-32-char-secret-key-here-456"
export BACKEND_API_ALGORITHM="HMAC-SHA256"
export BACKEND_API_TTL="300"
```

### 开发环境
```bash
# 在开发环境设置环境变量
export BACKEND_API_KEY_ID="admin-key-001"
export BACKEND_API_SECRET_KEY="your-32-char-secret-key-here-123"
export BACKEND_API_ALGORITHM="HMAC-SHA256"
export BACKEND_API_TTL="300"
```

### Docker 环境
```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    environment:
      - BACKEND_API_KEY_ID=admin-key-prod-001
      - BACKEND_API_SECRET_KEY=prod-32-char-secret-key-here-456
      - BACKEND_API_ALGORITHM=HMAC-SHA256
      - BACKEND_API_TTL=300
  
  admin:
    environment:
      - BACKEND_API_KEY_ID=admin-key-prod-001
      - BACKEND_API_SECRET_KEY=prod-32-char-secret-key-here-456
      - BACKEND_API_ALGORITHM=HMAC-SHA256
      - BACKEND_API_TTL=300
```

### Systemd 服务
```ini
# /etc/systemd/system/peizhen-backend.service
[Unit]
Description=Peizhen Backend Service

[Service]
Environment=BACKEND_API_KEY_ID=admin-key-prod-001
Environment=BACKEND_API_SECRET_KEY=prod-32-char-secret-key-here-456
Environment=BACKEND_API_ALGORITHM=HMAC-SHA256
Environment=BACKEND_API_TTL=300
ExecStart=/path/to/backend/bin/peizhen

# /etc/systemd/system/peizhen-admin.service
[Unit]
Description=Peizhen Admin Service

[Service]
Environment=BACKEND_API_KEY_ID=admin-key-prod-001
Environment=BACKEND_API_SECRET_KEY=prod-32-char-secret-key-here-456
Environment=BACKEND_API_ALGORITHM=HMAC-SHA256
Environment=BACKEND_API_TTL=300
ExecStart=/path/to/admin/server/bin/admin
```

## 验证配置

### 1. 检查环境变量
```bash
echo "BACKEND_API_KEY_ID: $BACKEND_API_KEY_ID"
echo "BACKEND_API_SECRET_KEY: $BACKEND_API_SECRET_KEY"
echo "BACKEND_API_ALGORITHM: $BACKEND_API_ALGORITHM"
echo "BACKEND_API_TTL: $BACKEND_API_TTL"
```

### 2. 检查服务日志
**后端服务启动日志**:
```
设置内部转账路由
✓ 内部转账路由设置完成
```

**管理后台启动日志**:
```
Backend API配置加载成功
API密钥配置: key_id=admin-key-prod-001
```

### 3. 测试API调用
```bash
# 在管理后台提现管理页面点击"重新打款"
# 检查日志中的认证信息
```

**成功的认证日志**:
```json
{
  "level": "INFO",
  "msg": "API密钥认证成功",
  "key_id": "admin-key-prod-001",
  "path": "/api/v1/internal/wechat/transfer/1/retry"
}
```

## 故障排查

### 1. 认证失败
如果看到以下错误：
```json
{
  "msg": "API密钥认证失败: 密钥ID无效",
  "provided_key_id": "admin-key-prod-001",
  "expected_key_id": "${BACKEND_API_KEY_ID:admin-key-prod-001}"
}
```

**解决方案**:
1. 检查环境变量是否正确设置
2. 重启相关服务
3. 检查配置文件语法

### 2. 环境变量未生效
**检查步骤**:
1. 确认环境变量在服务启动前设置
2. 检查服务启动脚本或systemd配置
3. 验证配置文件中的环境变量语法

### 3. 配置不一致
**确保一致性**:
1. 管理后台和后端服务使用相同的环境变量值
2. 密钥ID和密钥内容必须完全匹配
3. 算法和TTL配置保持一致

## 安全建议

1. **密钥强度**: 使用至少32字符的强密钥
2. **定期轮换**: 定期更换API密钥
3. **权限控制**: 限制环境变量的访问权限
4. **日志安全**: 不要在日志中输出完整密钥
5. **传输安全**: 确保使用HTTPS传输

## 总结

**关键点**:
- 两个服务需要设置**相同的环境变量**
- 环境变量名称: `BACKEND_API_KEY_ID`, `BACKEND_API_SECRET_KEY`, `BACKEND_API_ALGORITHM`, `BACKEND_API_TTL`
- 管理后台通过配置文件的 `${VAR:default}` 语法读取
- 后端服务通过 Viper 的环境变量绑定读取
- 配置必须在两个服务中保持完全一致