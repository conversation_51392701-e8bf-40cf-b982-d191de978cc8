-- 最终修复收款人姓名问题的SQL脚本
-- 确保所有相关表中的数据都是正确的

-- 1. 检查当前数据状态
SELECT '=== 当前数据状态检查 ===' as section;

-- 检查提现申请数据
SELECT 
    '提现申请数据' as type,
    id,
    real_name,
    openid,
    status,
    CASE 
        WHEN real_name IS NULL THEN '❌ NULL'
        WHEN real_name = '' THEN '❌ 空字符串'
        ELSE '✅ 正常'
    END as real_name_status,
    CASE 
        WHEN openid IS NULL THEN '❌ NULL'
        WHEN openid = '' THEN '❌ 空字符串'
        ELSE '✅ 正常'
    END as openid_status
FROM withdrawals 
WHERE id = 1;

-- 检查转账记录数据
SELECT 
    '转账记录数据' as type,
    id,
    withdrawal_id,
    real_name,
    openid,
    status,
    fail_reason,
    CASE 
        WHEN real_name IS NULL THEN '❌ NULL'
        WHEN real_name = '' THEN '❌ 空字符串'
        ELSE '✅ 正常'
    END as real_name_status,
    CASE 
        WHEN openid IS NULL THEN '❌ NULL'
        WHEN openid = '' THEN '❌ 空字符串'
        ELSE '✅ 正常'
    END as openid_status
FROM withdrawal_transfers 
WHERE withdrawal_id = 1
ORDER BY created_at DESC;

-- 检查陪诊师数据
SELECT 
    '陪诊师数据' as type,
    a.id,
    a.user_id,
    a.name,
    CASE 
        WHEN a.name IS NULL THEN '❌ NULL'
        WHEN a.name = '' THEN '❌ 空字符串'
        ELSE '✅ 正常'
    END as name_status
FROM attendants a
INNER JOIN withdrawals w ON a.user_id = w.user_id
WHERE w.id = 1;

-- 2. 数据修复操作
SELECT '=== 开始数据修复 ===' as section;

-- 修复提现申请中可能的空姓名（从陪诊师表获取）
UPDATE withdrawals w
INNER JOIN attendants a ON w.user_id = a.user_id
SET w.real_name = a.name,
    w.updated_at = NOW()
WHERE w.id = 1
  AND (w.real_name IS NULL OR w.real_name = '')
  AND a.name IS NOT NULL 
  AND a.name != '';

-- 显示修复结果
SELECT 
    '修复提现申请姓名' as operation,
    ROW_COUNT() as affected_rows;

-- 修复转账记录中可能的空姓名（从提现申请获取）
UPDATE withdrawal_transfers wt
INNER JOIN withdrawals w ON wt.withdrawal_id = w.id
SET wt.real_name = w.real_name,
    wt.updated_at = NOW()
WHERE wt.withdrawal_id = 1
  AND (wt.real_name IS NULL OR wt.real_name = '')
  AND w.real_name IS NOT NULL 
  AND w.real_name != '';

-- 显示修复结果
SELECT 
    '修复转账记录姓名' as operation,
    ROW_COUNT() as affected_rows;

-- 3. 强制数据同步（确保一致性）
SELECT '=== 强制数据同步 ===' as section;

-- 将转账记录的姓名同步为提现申请中的姓名
UPDATE withdrawal_transfers wt
INNER JOIN withdrawals w ON wt.withdrawal_id = w.id
SET wt.real_name = w.real_name,
    wt.openid = w.openid,
    wt.updated_at = NOW()
WHERE wt.withdrawal_id = 1
  AND w.real_name IS NOT NULL 
  AND w.real_name != ''
  AND w.openid IS NOT NULL 
  AND w.openid != '';

-- 显示同步结果
SELECT 
    '强制数据同步' as operation,
    ROW_COUNT() as affected_rows;

-- 4. 验证修复结果
SELECT '=== 修复结果验证 ===' as section;

-- 最终数据状态
SELECT 
    w.id as withdrawal_id,
    w.real_name as w_real_name,
    w.openid as w_openid,
    w.status as w_status,
    wt.id as transfer_id,
    wt.real_name as wt_real_name,
    wt.openid as wt_openid,
    wt.status as wt_status,
    wt.fail_reason,
    CASE 
        WHEN w.real_name = wt.real_name AND w.openid = wt.openid THEN '✅ 数据一致'
        ELSE '❌ 数据不一致'
    END as consistency_status
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.id = 1;

-- 5. 重置转账状态（允许重新发起）
SELECT '=== 重置转账状态 ===' as section;

-- 重置转账记录状态，清除失败原因，允许重新发起
UPDATE withdrawal_transfers 
SET 
    status = 3,  -- 失败状态
    fail_reason = '数据修复完成，允许重新发起转账',
    retry_count = 0,  -- 重置重试次数
    wechat_batch_no = NULL,
    wechat_detail_id = NULL,
    wechat_status = NULL,
    updated_at = NOW()
WHERE withdrawal_id = 1;

-- 重置提现状态为审核通过
UPDATE withdrawals 
SET 
    status = 2,  -- 审核通过
    transfer_no = NULL,
    pay_time = NULL,
    pay_admin_id = NULL,
    updated_at = NOW()
WHERE id = 1;

-- 显示重置结果
SELECT 
    '重置转账状态' as operation,
    '转账记录和提现申请状态已重置' as result;

-- 6. 最终验证
SELECT '=== 最终验证 ===' as section;

SELECT 
    '最终数据状态' as description,
    w.id as withdrawal_id,
    w.real_name,
    w.openid,
    w.status as withdrawal_status,
    wt.id as transfer_id,
    wt.status as transfer_status,
    wt.retry_count,
    wt.fail_reason,
    CASE 
        WHEN w.real_name IS NOT NULL AND w.real_name != '' 
         AND w.openid IS NOT NULL AND w.openid != ''
         AND wt.real_name IS NOT NULL AND wt.real_name != ''
         AND wt.openid IS NOT NULL AND wt.openid != ''
        THEN '✅ 数据完整，可以发起转账'
        ELSE '❌ 数据仍有问题'
    END as final_status
FROM withdrawals w
LEFT JOIN withdrawal_transfers wt ON w.id = wt.withdrawal_id
WHERE w.id = 1;