#!/bin/bash

# API认证修复部署脚本
# 自动化部署新的API认证配置

echo "🚀 开始部署API认证修复"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误处理
set -e
trap 'echo -e "${RED}❌ 部署失败，请检查错误信息${NC}"' ERR

# 1. 验证配置
echo -e "${BLUE}步骤1: 验证配置${NC}"
if ! ./verify_api_auth_config.sh > /dev/null 2>&1; then
    echo -e "${RED}❌ 配置验证失败，请先修复配置问题${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 配置验证通过${NC}"

# 2. 备份现有服务（如果正在运行）
echo -e "${BLUE}步骤2: 检查现有服务${NC}"

# 检查后端服务
if pgrep -f "peizhen" > /dev/null; then
    echo -e "${YELLOW}⚠️ 检测到后端服务正在运行，将在重启时停止${NC}"
fi

# 检查管理后台服务
if pgrep -f "admin.*server" > /dev/null; then
    echo -e "${YELLOW}⚠️ 检测到管理后台服务正在运行，将在重启时停止${NC}"
fi

# 3. 构建后端服务
echo -e "${BLUE}步骤3: 构建后端服务${NC}"
cd backend

# 加载环境变量
echo -e "${YELLOW}加载后端环境变量...${NC}"
set -a
source ../production.env
set +a

# 构建服务
echo -e "${YELLOW}构建后端服务...${NC}"
go build -o bin/peizhen main.go
echo -e "${GREEN}✅ 后端服务构建完成${NC}"

cd ..

# 4. 验证环境变量加载
echo -e "${BLUE}步骤4: 验证环境变量${NC}"
if [ -z "$BACKEND_API_KEY_ID" ] || [ -z "$BACKEND_API_SECRET_KEY" ]; then
    echo -e "${RED}❌ 关键环境变量未加载${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境变量加载成功${NC}"
echo "  API Key ID: $BACKEND_API_KEY_ID"
echo "  API Secret Key: ${BACKEND_API_SECRET_KEY:0:10}...${BACKEND_API_SECRET_KEY: -10}"

# 5. 创建启动脚本
echo -e "${BLUE}步骤5: 创建启动脚本${NC}"

# 后端启动脚本
cat > start_backend.sh << 'EOF'
#!/bin/bash
echo "🚀 启动后端服务"

# 加载环境变量
set -a
source production.env
set +a

# 启动服务
cd backend
echo "启动时间: $(date)"
echo "API Key ID: $BACKEND_API_KEY_ID"
echo "环境: $APP_ENV"

./bin/peizhen
EOF

# 管理后台启动脚本
cat > start_admin.sh << 'EOF'
#!/bin/bash
echo "🚀 启动管理后台服务"

# 加载环境变量
set -a
source admin.production.env
set +a

# 启动服务
cd admin/server
echo "启动时间: $(date)"
echo "API Key ID: $BACKEND_API_KEY_ID"
echo "环境: $APP_ENV"

APP_ENV=prod go run main.go
EOF

chmod +x start_backend.sh start_admin.sh
echo -e "${GREEN}✅ 启动脚本创建完成${NC}"

# 6. 创建服务状态检查脚本
cat > check_services.sh << 'EOF'
#!/bin/bash
echo "🔍 检查服务状态"

echo "后端服务:"
if pgrep -f "peizhen" > /dev/null; then
    echo "  ✅ 正在运行 (PID: $(pgrep -f "peizhen"))"
else
    echo "  ❌ 未运行"
fi

echo "管理后台服务:"
if pgrep -f "admin.*server" > /dev/null; then
    echo "  ✅ 正在运行 (PID: $(pgrep -f "admin.*server"))"
else
    echo "  ❌ 未运行"
fi

echo ""
echo "端口占用情况:"
echo "  后端端口 8080:"
if netstat -an 2>/dev/null | grep -q ":8080.*LISTEN" || ss -an 2>/dev/null | grep -q ":8080.*LISTEN"; then
    echo "    ✅ 端口已监听"
else
    echo "    ❌ 端口未监听"
fi

echo "  管理后台端口 8081:"
if netstat -an 2>/dev/null | grep -q ":8081.*LISTEN" || ss -an 2>/dev/null | grep -q ":8081.*LISTEN"; then
    echo "    ✅ 端口已监听"
else
    echo "    ❌ 端口未监听"
fi
EOF

chmod +x check_services.sh

# 7. 显示部署完成信息
echo ""
echo -e "${GREEN}🎉 API认证修复部署完成！${NC}"
echo ""
echo -e "${YELLOW}接下来的操作:${NC}"
echo "1. 启动后端服务:"
echo "   ./start_backend.sh"
echo ""
echo "2. 启动管理后台服务 (在新的终端窗口):"
echo "   ./start_admin.sh"
echo ""
echo "3. 检查服务状态:"
echo "   ./check_services.sh"
echo ""
echo -e "${YELLOW}测试API认证:${NC}"
echo "服务启动后，在管理后台尝试发起转账操作"
echo "观察日志确认API认证成功，不再出现签名验证失败的错误"
echo ""
echo -e "${BLUE}配置摘要:${NC}"
echo "  ✅ 统一的API密钥配置"
echo "  ✅ 后端和管理后台使用相同认证"
echo "  ✅ 环境变量驱动的配置"
echo "  ✅ 安全的50字符密钥"
echo ""
echo -e "${GREEN}✅ 部署脚本执行完成${NC}"