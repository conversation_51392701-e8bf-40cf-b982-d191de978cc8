#!/bin/bash

echo "=== 检查应用日志 ==="

# 可能的日志路径
log_paths=(
    "/var/log/peizhen/app.log"
    "/var/www/html/peizhen/logs/app.prod.log"
    "/var/www/html/peizhen/logs/app.log"
    "/opt/peizhen/logs/app.log"
    "/home/<USER>/logs/app.log"
)

echo "1. 查找日志文件："
found_logs=()
for log_path in "${log_paths[@]}"; do
    if [ -f "$log_path" ]; then
        echo "   ✅ 找到日志: $log_path"
        found_logs+=("$log_path")
    else
        echo "   ❌ 不存在: $log_path"
    fi
done

if [ ${#found_logs[@]} -eq 0 ]; then
    echo "   ⚠️  未找到标准日志文件，尝试查找其他日志"
    
    # 查找可能的日志文件
    find /var/www/html/peizhen -name "*.log" 2>/dev/null | head -5
    find /var/log -name "*peizhen*" 2>/dev/null | head -5
    
    exit 1
fi

echo -e "\n2. 检查最新的日志内容："
main_log="${found_logs[0]}"
echo "   使用日志文件: $main_log"

# 检查日志文件大小和最后修改时间
echo "   文件大小: $(stat -c%s "$main_log") bytes"
echo "   最后修改: $(stat -c%y "$main_log")"

echo -e "\n3. 查找微信转账相关日志："
echo "   最近50行中的微信转账日志:"
tail -50 "$main_log" | grep -E "(微信|wechat|transfer|官方SDK|简化客户端|FUNCTION_DISABLED)" -i | tail -10

echo -e "\n4. 查找错误日志："
echo "   最近的错误日志:"
tail -50 "$main_log" | grep -E "(error|ERROR|失败|错误)" | tail -5

echo -e "\n5. 查找启动日志："
echo "   应用启动相关日志:"
tail -100 "$main_log" | grep -E "(启动|start|server|listen)" | tail -3

echo -e "\n6. 实时监控建议："
echo "   要实时监控微信转账日志，请运行:"
echo "   tail -f $main_log | grep -E '(微信|wechat|transfer|官方SDK|简化客户端)' -i"

echo -e "\n=== 检查完成 ==="