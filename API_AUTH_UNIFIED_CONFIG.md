# API认证统一配置方案

## 🎯 目标

统一后端和管理后台的API认证配置，解决签名验证失败问题，确保两个系统使用相同的认证密钥。

## 🔧 已完成的配置

### 1. 生成新的安全API密钥

**新的API认证配置**：
- `BACKEND_API_KEY_ID`: `peizhen-api-key-2025`
- `BACKEND_API_SECRET_KEY`: `PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1`
- `BACKEND_API_ALGORITHM`: `HMAC-SHA256`
- `BACKEND_API_TTL`: `300`

**密钥特点**：
- ✅ 50字符长度，超过32字符安全要求
- ✅ 包含项目标识、年份、用途描述
- ✅ 大小写字母、数字、下划线组合
- ✅ 易于识别和管理

### 2. 环境变量文件更新

#### 后端环境变量 (`production.env`)
```bash
# 🔧 API认证配置（后端和管理后台共享）
BACKEND_API_KEY_ID=peizhen-api-key-2025
BACKEND_API_SECRET_KEY=PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1
```

#### 管理后台环境变量 (`admin.production.env`)
```bash
# 🔧 API认证配置（与后端保持一致）
BACKEND_API_KEY_ID=peizhen-api-key-2025
BACKEND_API_SECRET_KEY=PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1
BACKEND_API_ALGORITHM=HMAC-SHA256
BACKEND_API_TTL=300
```

### 3. 配置文件优化

#### 后端配置 (`backend/config/conf/config.prod.yaml`)
```yaml
security:
  # 🔧 内部API配置（生产环境）- 与管理后台共享认证配置
  internal_api:
    key_id: "${BACKEND_API_KEY_ID}"
    secret_key: "${BACKEND_API_SECRET_KEY}"
    algorithm: "${BACKEND_API_ALGORITHM:HMAC-SHA256}"
    ttl: "${BACKEND_API_TTL:300}"
```

#### 管理后台配置 (`admin/server/config/config.prod.yaml`)
```yaml
backend:
  # 🔧 Backend API配置（与后端保持一致）
  api:
    base_url: "https://www.kanghuxing.cn"
    timeout: 30
    api_key:
      key_id: "${BACKEND_API_KEY_ID}"
      secret_key: "${BACKEND_API_SECRET_KEY}"
      algorithm: "${BACKEND_API_ALGORITHM:HMAC-SHA256}"
      ttl: "${BACKEND_API_TTL:300}"
```

## 🚀 部署步骤

### 自动化部署（推荐）
```bash
# 1. 验证配置
./verify_api_auth_config.sh

# 2. 执行部署
./deploy_api_auth_fix.sh

# 3. 启动服务
./start_backend.sh      # 启动后端
./start_admin.sh        # 启动管理后台（新终端）

# 4. 检查状态
./check_services.sh
```

### 手动部署
```bash
# 1. 加载后端环境变量并启动
source production.env
cd backend && go build -o bin/peizhen main.go && ./bin/peizhen

# 2. 加载管理后台环境变量并启动（新终端）
source admin.production.env
cd admin/server && APP_ENV=prod go run main.go
```

## 📋 验证清单

### 配置验证
- ✅ 两个环境变量文件包含相同的API密钥
- ✅ 配置文件正确引用环境变量
- ✅ 密钥长度符合安全要求（50字符）
- ✅ 算法和TTL配置一致

### 服务验证
- ✅ 后端服务正常启动（端口8080）
- ✅ 管理后台服务正常启动（端口8081）
- ✅ 环境变量正确加载
- ✅ API认证配置生效

### 功能验证
- ✅ 管理后台到后端的API请求认证成功
- ✅ 不再出现"签名验证失败"错误
- ✅ 转账功能可以正常调用

## 🔍 故障排除

### 常见问题

1. **环境变量未加载**
   ```bash
   # 检查环境变量
   echo $BACKEND_API_KEY_ID
   echo $BACKEND_API_SECRET_KEY
   
   # 重新加载
   source production.env  # 或 admin.production.env
   ```

2. **配置文件路径错误**
   ```bash
   # 确认配置文件存在
   ls -la admin/server/config/config.prod.yaml
   ls -la backend/config/conf/config.prod.yaml
   ```

3. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -an | grep :8080
   netstat -an | grep :8081
   
   # 检查进程
   ps aux | grep peizhen
   ps aux | grep admin
   ```

### 日志检查

**后端日志关键信息**：
```
INFO: 内部API配置加载成功
INFO: API Key ID: peizhen-api-key-2025
```

**管理后台日志关键信息**：
```
INFO: Backend API配置加载成功
INFO: API认证配置初始化完成
```

**成功的API请求日志**：
```
INFO: API请求认证成功
INFO: 发起微信企业付款转账
```

## 📊 配置对比

### 修复前（问题配置）
| 系统 | Key ID | Secret Key | 状态 |
|------|--------|------------|------|
| 后端 | admin-key-prod-001 | peizhen-backend-api-secret-key-2024-prod-32chars | ❌ |
| 管理后台 | admin-key-001 | your-32-char-secret-key-here-123 | ❌ |

### 修复后（统一配置）
| 系统 | Key ID | Secret Key | 状态 |
|------|--------|------------|------|
| 后端 | peizhen-api-key-2025 | PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1 | ✅ |
| 管理后台 | peizhen-api-key-2025 | PZ2025_API_SECRET_KEY_32CHARS_SECURE_AUTH_TOKEN_V1 | ✅ |

## 🎉 预期结果

修复完成后，应该看到：

1. **API认证成功**：
   ```json
   {
     "code": 0,
     "message": "操作成功",
     "data": {
       "success_count": 1,
       "fail_count": 0,
       "results": [...]
     }
   }
   ```

2. **日志正常**：
   - 不再出现"API密钥认证失败"
   - 不再出现"签名验证失败"
   - 转账请求正常到达后端服务

3. **转账功能恢复**：
   - 管理后台可以正常发起转账
   - 微信转账API调用成功
   - 获得真实的微信批次号

## 🔒 安全说明

1. **密钥管理**：
   - 密钥仅存储在环境变量文件中
   - 不在代码中硬编码
   - 生产环境文件权限设置为600

2. **密钥轮换**：
   - 建议定期更换API密钥
   - 更换时需同时更新两个环境变量文件
   - 确保服务重启后生效

3. **访问控制**：
   - 环境变量文件仅限运维人员访问
   - 日志中不记录完整密钥内容
   - 使用HTTPS确保传输安全