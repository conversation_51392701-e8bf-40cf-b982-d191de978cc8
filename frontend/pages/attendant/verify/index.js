const {uploadFile} = require('../../../utils/upload');
const {CONFIG} = require('../../../config/index');
const {getVerificationStatus, submitAttendantVerification} = require('../../../services/attendant');
const {uploadImage, compressImage} = require('../../../services/upload');

Page({
  data: {
    step: 1,
    counting: false,
    countDown: 60,
    form: {
      realName: '',
      idCard: '',
      phone: '',
      verifyCode: '',
      idCardFront: '',
      idCardBack: '',
      degree: '',
      certificate: '',
      health: '',
      experience: ''
    }
  },

  onLoad(options) {
    console.log('陪诊师认证页面加载 onLoad', options);
    
    // 如果有参数，进行处理
    if (options && options.step) {
      console.log('指定步骤:', options.step);
      this.setData({
        step: parseInt(options.step)
      });
    }
    
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      console.log('用户信息:', userInfo);
      // 预填充表单数据
      this.setData({
        'form.name': userInfo.nickname || '',
        'form.phone': userInfo.phone || '',
        'form.avatar': userInfo.avatarUrl || ''
      });
    }
    
    // 检查认证状态
    this.checkVerifyStatus();
  },

  onShow() {
    console.log('陪诊师认证页面显示 onShow');
  },

  // 检查认证状态
  checkVerifyStatus() {
    console.log('开始检查认证状态');
    
    wx.showLoading({
      title: '加载中...'
    })
    
    // 调用获取认证状态接口
    getVerificationStatus()
      .then(res => {
        wx.hideLoading()
        console.log('获取认证状态响应:', res);
        
        if (res.code === 0 && res.data) {
          // 如果已经提交认证，设置表单数据
          const verification = res.data
          console.log('已有认证信息:', verification);
          
          // 设置表单数据，方便用户查看或编辑
          this.setData({
            'form.name': verification.name || '',
            'form.gender': verification.gender || 1,
            'form.age': verification.age || '',
            'form.phone': verification.phone || '',
            'form.idCardFront': verification.id_card_front || '',
            'form.idCardBack': verification.id_card_back || '',
            'form.healthCert': verification.health_cert || '',
            'form.experience': verification.experience || '',
            'form.introduction': verification.introduction || '',
            'form.avatar': verification.avatar || '',
            'form.region': verification.service_area ? verification.service_area.split(',') : ['北京市', '北京市', '东城区'],
          })
          
          // 已经提交，根据状态设置显示步骤
          if (verification.status === 1) {
            // 待审核，显示提交完成页
            console.log('认证状态: 待审核');
            this.setData({ step: 3 })
          } else if (verification.status === 2) {
            // 已通过，跳转到陪诊师首页
            console.log('认证状态: 已通过');
            wx.showToast({
              title: '您的认证已通过',
              icon: 'success',
              duration: 2000
            })
            
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/attendant/mine/index',
                success: () => console.log('跳转到陪诊师首页成功'),
                fail: (err) => console.error('跳转到陪诊师首页失败:', err)
              })
            }, 2000)
          } else if (verification.status === 3) {
            // 已拒绝，显示拒绝原因
            console.log('认证状态: 已拒绝, 原因:', verification.remark);
            wx.showModal({
              title: '认证未通过',
              content: verification.remark || '您的认证申请未通过审核，请重新提交',
              confirmText: '重新提交',
              success: (result) => {
                if (result.confirm) {
                  // 重置到第一步
                  this.setData({ step: 1 })
                } else {
                  // 返回上一页
                  wx.navigateBack()
                }
              }
            })
          }
        } else {
          console.log('没有认证信息或未正确返回');
        }
      })
      .catch(err => {
        wx.hideLoading()
        console.error('获取认证状态失败:', err)
      })
  },

  // 输入真实姓名
  inputRealName(e) {
    this.setData({
      'form.realName': e.detail.value
    })
  },

  // 输入身份证号
  inputIdCard(e) {
    this.setData({
      'form.idCard': e.detail.value
    })
  },

  // 输入手机号
  inputPhone(e) {
    this.setData({
      'form.phone': e.detail.value
    })
  },

  // 输入验证码
  inputVerifyCode(e) {
    this.setData({
      'form.verifyCode': e.detail.value
    })
  },

  // 发送验证码
  sendVerifyCode() {
    const { phone } = this.data.form
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }

    // TODO: 调用发送验证码接口
    this.setData({ counting: true })
    this.startCountDown()
  },

  // 开始倒计时
  startCountDown() {
    const timer = setInterval(() => {
      if (this.data.countDown === 1) {
        clearInterval(timer)
        this.setData({
          counting: false,
          countDown: 60
        })
      } else {
        this.setData({
          countDown: this.data.countDown - 1
        })
      }
    }, 1000)
  },

  // 选择身份证正面照片
  chooseIdCardFront() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        wx.showLoading({
          title: '上传中'
        })
        try {
          const url = await uploadFile(res.tempFilePaths[0])
          this.setData({
            'form.idCardFront': url
          })
        } catch (err) {
          wx.showToast({
            title: '上传失败：' + err.message,
            icon: 'none'
          })
        } finally {
          wx.hideLoading()
        }
      }
    })
  },

  // 选择身份证反面照片
  chooseIdCardBack() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        wx.showLoading({
          title: '上传中'
        })
        try {
          const url = await uploadFile(res.tempFilePaths[0])
          this.setData({
            'form.idCardBack': url
          })
        } catch (err) {
          wx.showToast({
            title: '上传失败：' + err.message,
            icon: 'none'
          })
        } finally {
          wx.hideLoading()
        }
      }
    })
  },

  // 选择学历证书
  chooseDegree() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImage(res.tempFilePaths[0], 'degree', 'form.degree')
      }
    })
  },

  // 选择职业资格证
  chooseCertificate() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImage(res.tempFilePaths[0], 'certificate', 'form.certificate')
      }
    })
  },

  // 选择健康证明
  chooseHealth() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.uploadImage(res.tempFilePaths[0], 'health_cert', 'form.health')
      }
    })
  },

  // 通用图片上传方法
  async uploadImage(filePath, fileType, targetField) {
    try {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })

      // 先压缩图片
      const compressedPath = await compressImage(filePath, 0.8)

      // 上传图片
      const result = await uploadImage(compressedPath, fileType, (progress) => {
        wx.showLoading({
          title: `上传中...${progress}%`,
          mask: true
        })
      })

      // 更新表单数据
      this.setData({
        [targetField]: result.url
      })

      wx.hideLoading()
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })

      console.log('图片上传成功:', result)

    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: error.message || '上传失败',
        icon: 'none'
      })
      console.error('图片上传失败:', error)
    }
  },

  // 输入工作经历
  inputExperience(e) {
    this.setData({
      'form.experience': e.detail.value
    })
  },

  // 验证身份信息
  validateIdentity() {
    const { realName, idCard, phone, verifyCode, idCardFront, idCardBack } = this.data.form
    if (!realName) {
      wx.showToast({
        title: '请输入真实姓名',
        icon: 'none'
      })
      return false
    }
    if (!idCard) {
      wx.showToast({
        title: '请输入身份证号',
        icon: 'none'
      })
      return false
    }
    if (!/^\d{17}[\dX]$/.test(idCard)) {
      wx.showToast({
        title: '身份证号格式不正确',
        icon: 'none'
      })
      return false
    }
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return false
    }
    if (!verifyCode) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return false
    }
    if (!idCardFront) {
      wx.showToast({
        title: '请上传身份证正面照片',
        icon: 'none'
      })
      return false
    }
    if (!idCardBack) {
      wx.showToast({
        title: '请上传身份证反面照片',
        icon: 'none'
      })
      return false
    }
    return true
  },

  // 验证资质信息
  validateQualification() {
    const { degree, certificate, health, experience } = this.data.form
    if (!degree) {
      wx.showToast({
        title: '请上传学历证书',
        icon: 'none'
      })
      return false
    }
    if (!certificate) {
      wx.showToast({
        title: '请上传职业资格证',
        icon: 'none'
      })
      return false
    }
    if (!health) {
      wx.showToast({
        title: '请上传健康证明',
        icon: 'none'
      })
      return false
    }
    if (!experience) {
      wx.showToast({
        title: '请填写工作经历',
        icon: 'none'
      })
      return false
    }
    return true
  },

  // 上一步
  prevStep() {
    this.setData({
      step: this.data.step - 1
    })
  },

  // 下一步
  nextStep() {
    if (this.data.step === 1) {
      if (!this.validateIdentity()) return
      this.setData({ step: 2 })
    } else if (this.data.step === 2) {
      if (!this.validateQualification()) return
      this.submitVerify()
    }
  },

  // 提交认证
  submitVerify() {
    wx.showLoading({
      title: '提交中...'
    })

    // 准备提交数据
    const verificationData = {
      name: this.data.form.name,
      gender: this.data.form.gender,
      age: parseInt(this.data.form.age),
      phone: this.data.form.phone,
      id_card_front: this.data.form.idCardFront,
      id_card_back: this.data.form.idCardBack,
      health_cert: this.data.form.healthCert,
      experience: this.data.form.experience,
      region: this.data.form.region.join(','),
      introduction: this.data.form.introduction,
      avatar: this.data.form.avatar,
      referrer_phone: this.data.form.referrerPhone || ''
    }
    
    console.log('提交认证数据:', verificationData);
    
    // 调用提交认证接口
    submitAttendantVerification(verificationData)
      .then(res => {
        wx.hideLoading()
        console.log('提交认证响应:', res);
        
        if (res.code === 0) {
          // 设置步骤为3（提交完成页面）
          this.setData({ step: 3 })
          
          // 显示提交成功的提示
          wx.showToast({
            title: res.message || '提交成功',
            icon: 'success',
            duration: 2000
          })
          
          // 设置一个标志，表示提交成功，方便跳转
          this.setData({ submitSuccess: true });
          
          // 使用全局变量存储提交成功的状态
          getApp().globalData = getApp().globalData || {};
          getApp().globalData.verificationSubmitted = true;
          
          // 保存到本地存储，确保数据持久化
          wx.setStorageSync('verificationSubmitted', true);
          
          // 2秒后跳转到首页
          setTimeout(() => {
            console.log('跳转到首页...');
            wx.switchTab({
              url: '/pages/index/index',
              success: () => console.log('跳转到首页成功'),
              fail: (err) => {
                console.error('跳转到首页失败:', err);
                // 如果switchTab失败，尝试使用reLaunch
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }
            });
          }, 2000);
        } else {
          wx.showToast({
            title: res.message || '提交失败',
            icon: 'none'
          })
        }
      })
      .catch(err => {
        wx.hideLoading()
        console.error('提交认证失败:', err)
        
        // 检查是否是"已提交认证申请"的错误
        if (err && err.response && err.response.data && 
            (err.response.data.error === "您已提交认证申请，请等待审核" || 
             err.response.data.message === "您已提交认证申请，请等待审核")) {
          
          console.log('用户已提交过认证申请');
          
          // 这种情况视为成功，更新步骤为3
          this.setData({ step: 3 });
          
          // 显示友好提示
          wx.showToast({
            title: '认证申请已提交，请等待审核',
            icon: 'none',
            duration: 2000
          });
          
          // 记录提交状态
          this.setData({ submitSuccess: true });
          getApp().globalData = getApp().globalData || {};
          getApp().globalData.verificationSubmitted = true;
          wx.setStorageSync('verificationSubmitted', true);
          
          // 2秒后跳转到首页
          setTimeout(() => {
            console.log('跳转到首页...');
            wx.switchTab({
              url: '/pages/index/index',
              success: () => console.log('跳转到首页成功'),
              fail: (err) => {
                console.error('跳转到首页失败:', err);
                // 如果switchTab失败，尝试使用reLaunch
                wx.reLaunch({
                  url: '/pages/index/index'
                });
              }
            });
          }, 2000);
        } else {
          // 其他错误正常显示
          wx.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
        }
      })
  },

  // 联系客服
  contactService() {
    // TODO: 实现联系客服功能
    wx.makePhoneCall({
      phoneNumber: CONFIG.CUSTOMER_SERVICE.PHONE
    })
  },
  
  // 返回首页
  goToHome() {
    console.log('返回首页');
    wx.switchTab({
      url: '/pages/index/index',
      success: function() {
        console.log('返回首页成功');
      },
      fail: function(error) {
        console.error('返回首页失败:', error);
      }
    });
  }
})