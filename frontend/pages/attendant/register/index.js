const {validatePhone} = require('../../../utils/validator'); 
const {uploadFile} = require('../../../utils/upload');
const {getUserInfo, setUserInfo, getToken} = require('../../../utils/storage');
const {getVerificationStatus, submitAttendantVerification, validateReferrer} = require('../../../services/attendant');
const {getUserProfile} = require('../../../services/user');
const config = require('../../../utils/config');

Page({
  data: {
    form: {
      avatar: '',
      name: '',
      gender: '',
      age: '',
      phone: '',
      idCard: '',
      idCardFront: '',
      idCardBack: '',
      healthCert: '',
      experience: '',
      region: [],
      introduction: '',
      referrerPhone: ''
    },
    experienceOptions: ['1年以下', '1-3年', '3-5年', '5年以上'],
    experienceIndex: -1,
    uploadStatus: {
      avatar: false,
      idCardFront: false,
      idCardBack: false,
      healthCert: false
    },
    userAvatar: '',
    userName: '',
    userPhone: '',
    isEditing: false,
    verificationId: 0,
    defaultRegion: ['北京市', '北京市'],
    referrerValidation: {
      validating: false,
      success: false,
      error: false,
      errorMessage: '',
      referrerName: ''
    }
  },

  onLoad() {
    console.log('[Register] 页面加载，开始获取用户信息');
    
    // 先从后端获取最新用户信息
    this.fetchUserProfile();
    
    // 检查是否有现有的认证申请
    this.checkExistingVerification();
  },

  // 通用图片上传处理方法
  async handleImageUpload(filePath, fieldName, displayName) {
    try {
      console.log(`[Register] 开始上传${displayName}:`, filePath)
      
      // 设置上传状态
      this.setData({
        [`uploadStatus.${fieldName}`]: true
      })
      
      // 显示加载提示
      wx.showLoading({
        title: `上传${displayName}中...`,
        mask: true
      })
      
      // 先显示预览图片（iOS兼容处理）
      let tempImagePath = filePath
      const systemInfo = wx.getSystemInfoSync()
      const isIOS = systemInfo.platform === 'ios'
      
      if (isIOS) {
        try {
          const localImgResult = await new Promise((resolve, reject) => {
            wx.getLocalImgData({
              localId: filePath,
              success: resolve,
              fail: reject
            })
          })
          
          if (localImgResult && localImgResult.localData) {
            tempImagePath = localImgResult.localData
          }
        } catch (localImgError) {
          console.log(`[Register] iOS ${displayName}数据获取失败，使用原始路径:`, localImgError)
        }
      }
      
      // 先显示预览图片
      this.setData({
        [`form.${fieldName}`]: tempImagePath
      })
      
      // 上传图片到服务器，带进度回调
      const url = await uploadFile(filePath, {
        compress: true,
        quality: 80,
        onProgress: (progress, sent, total) => {
          console.log(`[Register] ${displayName}上传进度:`, progress + '%')
          wx.showLoading({
            title: `上传${displayName}中...${progress}%`,
            mask: true
          })
        }
      })
      
      console.log(`[Register] ${displayName}上传成功:`, url)
      
      // 设置服务器返回的URL
      this.setData({
        [`form.${fieldName}`]: url,
        [`uploadStatus.${fieldName}`]: false
      })
      
      wx.hideLoading()
      wx.showToast({
        title: `${displayName}上传成功`,
        icon: 'success',
        duration: 1500
      })
      
    } catch (err) {
      console.error(`[Register] ${displayName}上传失败:`, err)
      
      // 清空失败的图片
      this.setData({
        [`uploadStatus.${fieldName}`]: false,
        [`form.${fieldName}`]: ''
      })
      
      wx.hideLoading()
      
      // 显示详细错误信息
      let errorMsg = `${displayName}上传失败`
      if (err.message) {
        if (err.message.includes('超时')) {
          errorMsg = `${displayName}上传超时，请检查网络后重试`
        } else if (err.message.includes('文件过大')) {
          errorMsg = `${displayName}文件过大，请选择小于10MB的图片`
        } else if (err.message.includes('不支持')) {
          errorMsg = `不支持的文件格式，请选择jpg、png等图片格式`
        } else {
          errorMsg = `${displayName}上传失败：${err.message}`
        }
      }
      
      wx.showModal({
        title: '上传失败',
        content: errorMsg,
        showCancel: true,
        cancelText: '取消',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 重试上传
            this.handleImageUpload(filePath, fieldName, displayName)
          }
        }
      })
    }
  },

  onShow() {
    console.log('[Register] 页面显示');
  },
  
  // 从后端获取最新用户信息
  fetchUserProfile() {
    wx.showLoading({
      title: '加载信息...'
    });
    
    // 调用后端API获取最新用户信息
    getUserProfile()
      .then(res => {
        wx.hideLoading();
        console.log('[Register] 获取用户信息成功:', res);
        
        if (res.code === 0 && res.data) {
          const userData = res.data;
          
          // 更新本地存储
          setUserInfo(userData);
          
          // 更新页面数据
          this.setData({
            userAvatar: userData.avatar_url || userData.avatar || '',
            userName: userData.nickname || '',
            userPhone: userData.phone || '',
            'form.phone': userData.phone || ''
          });
          
          console.log('[Register] 用户信息已更新 - 头像:', this.data.userAvatar, '昵称:', this.data.userName);
        } else {
          console.warn('[Register] 获取用户信息返回异常结果:', res);
          this.loadLocalUserInfo();
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('[Register] 获取用户信息失败:', err);
        this.loadLocalUserInfo();
      });
  },
  
  // 从本地缓存加载用户信息作为备选
  loadLocalUserInfo() {
    console.log('[Register] 从本地缓存加载用户信息');
    const userInfo = getUserInfo();
    if (userInfo) {
      if (userInfo.avatar || userInfo.avatar_url) {
        const avatarUrl = userInfo.avatar || userInfo.avatar_url;
        this.setData({
          userAvatar: avatarUrl
        });
      }

      if (userInfo.nickname) {
        this.setData({
          userName: userInfo.nickname
        });
      }

      if (userInfo.phone) {
        this.setData({
          userPhone: userInfo.phone,
          'form.phone': userInfo.phone
        });
      }
    }
  },
  
  // 检查现有的认证申请
  checkExistingVerification() {
    wx.showLoading({
      title: '加载中...'
    });
    
    getVerificationStatus()
      .then(res => {
        wx.hideLoading();
        console.log('[Register] 获取认证信息:', res);
        
        // 如果有认证记录，尤其是被拒绝的
        if (res.code === 0 && res.data) {
          const verification = res.data;
          
          // 设置编辑模式标记
          this.setData({
            isEditing: true,
            verificationId: verification.id
          });
          
          // 先清理数据，确保不会有冲突
          let gender = '';
          if (verification.gender) {
            gender = String(verification.gender);
          }
          
          // 处理工作经验
          let expText = '';
          let expIndex = -1;
          
          if (typeof verification.experience === 'number') {
            console.log('[Register] 工作经验为数字:', verification.experience);
            switch(verification.experience) {
              case 0:
                expText = '1年以下';
                expIndex = 0;
                break;
              case 1:
                expText = '1-3年';
                expIndex = 1;
                break;
              case 2:
                expText = '3-5年';
                expIndex = 2;
                break;
              case 3:
                expText = '5年以上';
                expIndex = 3;
                break;
              default:
                expText = '';
                expIndex = -1;
                break;
            }
          } else if (typeof verification.experience === 'string') {
            expText = verification.experience;
            expIndex = this.data.experienceOptions.indexOf(verification.experience);
            if (expIndex === -1) expIndex = 0;
          }
          
          // 处理服务区域数据
          let regionArray = [];
          if (verification.service_area) {
            if (typeof verification.service_area === 'string') {
              regionArray = verification.service_area.split(',');
            } else if (Array.isArray(verification.service_area)) {
              regionArray = verification.service_area;
            }
          }
          
          // 确保regionArray至少有两个元素
          if (regionArray.length < 2) {
            regionArray = this.data.defaultRegion;
          }
          
          // 填充表单数据
          this.setData({
            'form.avatar': verification.avatar || '',
            'form.name': verification.name || '',
            'form.gender': gender,
            'form.age': verification.age ? String(verification.age) : '',
            'form.phone': verification.phone || '',
            'form.idCard': verification.id_card || '',
            'form.idCardFront': verification.id_card_front || '',
            'form.idCardBack': verification.id_card_back || '',
            'form.healthCert': verification.health_cert || '',
            'form.experience': expText,
            'form.region': regionArray,
            'form.introduction': verification.introduction || '',
            'form.referrerPhone': verification.referrer_phone || '',
            experienceIndex: expIndex
          });
          
          // 如果有推荐人手机号，自动验证
          if (verification.referrer_phone) {
            this.validateReferrerAPI(verification.referrer_phone);
          }
          
          console.log('[Register] 表单数据已填充 - 编辑模式');
          console.log('  经验原始值:', verification.experience);
          console.log('  转换后值:', expText, '索引:', expIndex);
          console.log('  服务区域:', regionArray);
        } else {
          console.log('[Register] 没有现有认证申请 - 新建模式');
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('[Register] 获取认证状态失败:', err);
      });
  },

  // 使用微信头像
  useCurrentAvatar() {
    if (this.data.userAvatar) {
      this.setData({
        'form.avatar': this.data.userAvatar
      });
      
      wx.showToast({
        title: '头像已设置',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '未获取到头像',
        icon: 'none'
      });
    }
  },

  // 使用微信昵称
  useCurrentName() {
    if (this.data.userName) {
      this.setData({
        'form.name': this.data.userName
      });
      
      wx.showToast({
        title: '姓名已设置',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '未获取到昵称',
        icon: 'none'
      });
    }
  },

  // 选择头像
  chooseAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.handleImageUpload(res.tempFilePaths[0], 'avatar', '头像')
      }
    })
  },

  // 输入姓名
  inputName(e) {
    this.setData({
      'form.name': e.detail.value
    })
  },

  // 选择性别
  selectGender(e) {
    this.setData({
      'form.gender': e.detail.value
    })
  },

  // 输入年龄
  inputAge(e) {
    this.setData({
      'form.age': e.detail.value
    })
  },

  // 输入手机号
  inputPhone(e) {
    this.setData({
      'form.phone': e.detail.value
    })
  },

  // 输入身份证号码
  inputIdCard(e) {
    this.setData({
      'form.idCard': e.detail.value
    });
  },

  // 选择身份证正面照片
  chooseIdCardFront() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.handleImageUpload(res.tempFilePaths[0], 'idCardFront', '身份证正面')
      }
    })
  },

  // 选择身份证反面照片
  chooseIdCardBack() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.handleImageUpload(res.tempFilePaths[0], 'idCardBack', '身份证反面')
      }
    })
  },

  // 选择健康证照片
  chooseHealthCert() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.handleImageUpload(res.tempFilePaths[0], 'healthCert', '健康证')
      }
    })
  },

  // 选择工作经验
  selectExperience(e) {
    this.setData({
      experienceIndex: e.detail.value,
      'form.experience': this.data.experienceOptions[e.detail.value]
    })
  },

  // 选择服务区域
  selectRegion(e) {
    console.log('[Register] 选择服务区域:', e.detail.value);
    
    // 只保存省市信息（前两级）
    const regionValue = e.detail.value;
    if (regionValue && regionValue.length >= 2) {
      this.setData({
        'form.region': [regionValue[0], regionValue[1]]
      });
      console.log('[Register] 服务区域已设置:', this.data.form.region);
    } else {
      console.warn('[Register] 区域选择数据不完整:', regionValue);
    }
  },

  // 输入个人简介
  inputIntroduction(e) {
    this.setData({
      'form.introduction': e.detail.value
    })
  },

  // 输入推荐人手机号
  inputReferrerPhone(e) {
    const phone = e.detail.value
    this.setData({
      'form.referrerPhone': phone,
      'referrerValidation.success': false,
      'referrerValidation.error': false,
      'referrerValidation.errorMessage': '',
      'referrerValidation.referrerName': ''
    })
  },

  // 验证推荐人
  validateReferrer(e) {
    const phone = e.detail.value.trim()
    
    // 如果手机号为空，清除验证状态
    if (!phone) {
      this.setData({
        'referrerValidation.validating': false,
        'referrerValidation.success': false,
        'referrerValidation.error': false,
        'referrerValidation.errorMessage': '',
        'referrerValidation.referrerName': ''
      })
      return
    }

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      this.setData({
        'referrerValidation.validating': false,
        'referrerValidation.success': false,
        'referrerValidation.error': true,
        'referrerValidation.errorMessage': '手机号格式不正确',
        'referrerValidation.referrerName': ''
      })
      return
    }

    // 开始验证
    this.setData({
      'referrerValidation.validating': true,
      'referrerValidation.success': false,
      'referrerValidation.error': false,
      'referrerValidation.errorMessage': '',
      'referrerValidation.referrerName': ''
    })

    // 调用验证API
    this.validateReferrerAPI(phone)
  },

  // 调用推荐人验证API
  validateReferrerAPI(phone) {
    // 防抖处理
    if (this.validateReferrerTimer) {
      clearTimeout(this.validateReferrerTimer)
    }

    this.validateReferrerTimer = setTimeout(() => {
      validateReferrer(phone)
        .then(res => {
          console.log('[Register] 推荐人验证结果:', res)
          
          if (res.code === 0 && res.data) {
            const result = res.data
            if (result.valid) {
              this.setData({
                'referrerValidation.validating': false,
                'referrerValidation.success': true,
                'referrerValidation.error': false,
                'referrerValidation.errorMessage': '',
                'referrerValidation.referrerName': result.referrer_name || '推荐人'
              })
            } else {
              this.setData({
                'referrerValidation.validating': false,
                'referrerValidation.success': false,
                'referrerValidation.error': true,
                'referrerValidation.errorMessage': '未发现该手机号陪诊师',
                'referrerValidation.referrerName': ''
              })
            }
          } else {
            this.setData({
              'referrerValidation.validating': false,
              'referrerValidation.success': false,
              'referrerValidation.error': true,
              'referrerValidation.errorMessage': res.message || '验证失败',
              'referrerValidation.referrerName': ''
            })
          }
        })
        .catch(err => {
          console.error('[Register] 推荐人验证失败:', err)
          
          // 根据错误类型提供不同的错误信息
          let errorMessage = '网络错误，请重试'
          if (err.message) {
            if (err.message.includes('手机号格式')) {
              errorMessage = '手机号格式不正确'
            } else if (err.message.includes('未发现')) {
              errorMessage = '未发现该手机号陪诊师'
            } else if (err.message.includes('未认证')) {
              errorMessage = '推荐人未认证'
            } else {
              errorMessage = err.message
            }
          }
          
          this.setData({
            'referrerValidation.validating': false,
            'referrerValidation.success': false,
            'referrerValidation.error': true,
            'referrerValidation.errorMessage': errorMessage,
            'referrerValidation.referrerName': ''
          })
        })
    }, 500) // 500ms防抖
  },

  // 重试验证推荐人
  retryValidateReferrer() {
    const phone = this.data.form.referrerPhone
    if (phone) {
      this.validateReferrerAPI(phone)
    }
  },

  // 验证表单
  validateForm() {
    const { form } = this.data
    if (!form.avatar) {
      wx.showToast({
        title: '请上传头像',
        icon: 'none'
      })
      return false
    }
    if (!form.name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      })
      return false
    }
    if (!form.gender) {
      wx.showToast({
        title: '请选择性别',
        icon: 'none'
      })
      return false
    }
    if (!form.age) {
      wx.showToast({
        title: '请输入年龄',
        icon: 'none'
      })
      return false
    }
    if (!form.phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return false
    }
    if (!/^1[3-9]\d{9}$/.test(form.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return false
    }
    if (!form.idCardFront || !form.idCardBack) {
      wx.showToast({
        title: '请上传身份证照片',
        icon: 'none'
      })
      return false
    }
    if (!form.idCard) {
      wx.showToast({
        title: '请输入身份证号码',
        icon: 'none'
      })
      return false
    }
    if (!/^\d{17}[\dX]$/.test(form.idCard)) {
      wx.showToast({
        title: '身份证号码格式不正确',
        icon: 'none'
      })
      return false
    }
    if (!form.experience) {
      wx.showToast({
        title: '请选择工作经验',
        icon: 'none'
      })
      return false
    }
    if (!form.region || form.region.length < 2) {
      wx.showToast({
        title: '请选择服务区域',
        icon: 'none'
      })
      return false
    }
    if (!form.introduction) {
      wx.showToast({
        title: '请填写个人简介',
        icon: 'none'
      })
      return false
    }
    if (form.introduction.length < 10) {
      wx.showToast({
        title: '个人简介至少10个字符',
        icon: 'none'
      })
      return false
    }
    
    // 验证推荐人字段（如果填写了）
    if (form.referrerPhone && form.referrerPhone.trim() !== '') {
      // 检查推荐人验证状态
      if (this.data.referrerValidation.validating) {
        wx.showToast({
          title: '推荐人验证中，请稍候',
          icon: 'none'
        })
        return false
      }
      
      if (this.data.referrerValidation.error) {
        wx.showToast({
          title: '请修正推荐人信息或清空该字段',
          icon: 'none'
        })
        return false
      }
      
      if (!this.data.referrerValidation.success) {
        wx.showToast({
          title: '请验证推荐人信息',
          icon: 'none'
        })
        return false
      }
    }
    
    return true
  },

  // 上传文件方法 - 内置实现
  uploadFile(filePath) {
    return new Promise((resolve, reject) => {
      // 构建完整的上传URL
      const baseUrl = config.baseUrl.endsWith('/') ? config.baseUrl.slice(0, -1) : config.baseUrl
      const uploadPath = config.uploadUrl.startsWith('/') ? config.uploadUrl : `/${config.uploadUrl}`
      const uploadUrl = `${baseUrl}${uploadPath}`

      console.log('[Upload] 上传URL:', uploadUrl)

      // 陪诊师注册上传无需token认证
      console.log('[Upload] 使用陪诊师专用上传接口，无需认证')

      wx.uploadFile({
        url: uploadUrl,
        filePath: filePath,
        name: 'file',
        header: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 60000, // 增加超时时间到60秒
        timeout: 30000,
        success: (res) => {
          console.log('[Upload] 上传响应状态码:', res.statusCode)
          console.log('[Upload] 上传响应数据:', res.data)
          
          if (res.statusCode === 200) {
            try {
              const data = JSON.parse(res.data)
              if (data.code === 0) {
                console.log('[Upload] 上传成功，文件URL:', data.data.url)
                resolve(data.data.url)
              } else {
                console.error('[Upload] 业务错误:', data)
                reject(new Error(data.message || '上传失败：服务器返回错误'))
              }
            } catch (err) {
              console.error('[Upload] 解析响应数据失败:', res.data, err)
              reject(new Error('上传失败：无法解析响应数据'))
            }
          } else if (res.statusCode === 401) {
            console.error('[Upload] 认证失败，token可能无效')
            reject(new Error('认证失败，请重新登录'))
          } else {
            console.error('[Upload] 上传失败，状态码:', res.statusCode, '响应:', res.data)
            reject(new Error(`上传失败：状态码 ${res.statusCode}`))
          }
        },
        fail: (err) => {
          console.error('[Upload] 上传请求失败:', err)
          // 确保错误对象有message属性
          if (!err.message) {
            if (err.errMsg) {
              err.message = err.errMsg
            } else {
              err = new Error('上传失败：网络错误或服务器无响应')
            }
          }
          reject(err)
        }
      })
    })
  },

  // 提交表单
  submitForm() {
    if (!this.validateForm()) return

    wx.showLoading({
      title: '提交中...'
    })
    
    console.log('[Register] 提交表单数据:', this.data.form);
    
    // 将经验文本转换为数字索引
    let experienceIndex = 0; // 默认为0（1年以下）
    if (this.data.form.experience) {
      const expIndex = this.data.experienceOptions.indexOf(this.data.form.experience);
      if (expIndex !== -1) {
        experienceIndex = expIndex;
      }
    }

    const submitData = {
      avatar: this.data.form.avatar,
      name: this.data.form.name,
      gender: parseInt(this.data.form.gender),
      age: parseInt(this.data.form.age),
      phone: this.data.form.phone,
      id_card: this.data.form.idCard,
      id_card_front: this.data.form.idCardFront,
      id_card_back: this.data.form.idCardBack,
      health_cert: this.data.form.healthCert || '',
      experience: experienceIndex,
      service_area: this.data.form.region.join(','),
      introduction: this.data.form.introduction,
      referrer_phone: this.data.form.referrerPhone || ''
    };

    // 如果是编辑模式，添加ID
    if (this.data.isEditing && this.data.verificationId) {
      submitData.id = this.data.verificationId;
    }

    console.log('[Register] 最终提交数据:', submitData);

    submitAttendantVerification(submitData)
      .then(res => {
        wx.hideLoading()
        console.log('[Register] 提交成功:', res);
        
        if (res.code === 0) {
          wx.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000
          })
          
          setTimeout(() => {
            wx.navigateBack()
          }, 2000)
        } else {
          wx.showToast({
            title: res.message || '提交失败',
            icon: 'none'
          })
        }
      })
      .catch(err => {
        wx.hideLoading()
        console.error('[Register] 提交失败:', err);
        wx.showToast({
          title: '提交失败：' + (err.message || '网络错误'),
          icon: 'none'
        })
      })
  }
})