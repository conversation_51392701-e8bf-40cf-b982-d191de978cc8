/**
 * 文件上传服务
 */

const http = require('../utils/http')
const config = require('../config')

/**
 * 上传图片
 * @param {string} filePath 本地文件路径
 * @param {string} fileType 文件类型（id_card_front, id_card_back, health_cert, degree, certificate等）
 * @param {function} onProgress 上传进度回调
 * @returns {Promise} 上传结果
 */
function uploadImage(filePath, fileType = 'image', onProgress = null) {
  return new Promise((resolve, reject) => {
    console.log('[UploadService] 开始上传图片:', { filePath, fileType })

    // 创建上传任务 - 使用陪诊师专用上传接口（无需认证）
    const uploadTask = wx.uploadFile({
      url: `${config.baseUrl}/api/v1/attendant/upload`,
      filePath: filePath,
      name: 'file',
      formData: {
        'file_type': fileType,
        'category': 'attendant_verify'
      },
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000, // 增加超时时间到60秒
      success: (res) => {
        console.log('[UploadService] 上传原始响应:', res)
        
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            console.log('[UploadService] 上传成功:', data)
            
            if (data.code === 200 && data.data) {
              resolve({
                url: data.data.url,
                fileId: data.data.file_id,
                fileName: data.data.file_name
              })
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (e) {
            console.error('[UploadService] 解析响应数据失败:', e)
            reject(new Error('服务器响应格式错误'))
          }
        } else {
          console.error('[UploadService] 上传失败，状态码:', res.statusCode)
          reject(new Error(`上传失败，状态码: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        console.error('[UploadService] 上传请求失败:', error)
        reject(new Error(error.errMsg || '网络请求失败'))
      }
    })

    // 监听上传进度
    if (onProgress && typeof onProgress === 'function') {
      uploadTask.onProgressUpdate((res) => {
        console.log('[UploadService] 上传进度:', res.progress + '%')
        onProgress(res.progress)
      })
    }
  })
}

/**
 * 批量上传图片
 * @param {Array} files 文件列表 [{filePath: string, fileType: string}]
 * @param {function} onProgress 总体进度回调
 * @returns {Promise} 上传结果数组
 */
function uploadMultipleImages(files, onProgress = null) {
  return new Promise((resolve, reject) => {
    console.log('[UploadService] 开始批量上传:', files.length + '个文件')
    
    const results = []
    let completedCount = 0
    let hasError = false

    // 逐个上传文件
    files.forEach((file, index) => {
      uploadImage(file.filePath, file.fileType, (progress) => {
        // 计算总体进度
        if (onProgress) {
          const totalProgress = Math.floor((completedCount * 100 + progress) / files.length)
          onProgress(totalProgress)
        }
      })
      .then((result) => {
        results[index] = { success: true, data: result }
        completedCount++
        
        // 检查是否全部完成
        if (completedCount === files.length && !hasError) {
          console.log('[UploadService] 批量上传完成:', results)
          resolve(results)
        }
      })
      .catch((error) => {
        console.error('[UploadService] 第' + (index + 1) + '个文件上传失败:', error)
        results[index] = { success: false, error: error.message }
        completedCount++
        hasError = true
        
        // 如果有任何失败，直接返回错误
        if (!hasError) {
          hasError = true
          reject(new Error(`第${index + 1}个文件上传失败: ${error.message}`))
        }
      })
    })
  })
}

/**
 * 删除已上传的文件
 * @param {string} fileId 文件ID
 * @returns {Promise} 删除结果
 */
function deleteUploadedFile(fileId) {
  console.log('[UploadService] 删除文件:', fileId)
  
  return http.delete(`upload/${fileId}`)
    .then(response => {
      console.log('[UploadService] 删除文件成功:', response)
      return response
    })
    .catch(error => {
      console.error('[UploadService] 删除文件失败:', error)
      throw error
    })
}

/**
 * 压缩图片
 * @param {string} filePath 原始文件路径
 * @param {number} quality 压缩质量 (0-1)
 * @returns {Promise} 压缩后的文件路径
 */
function compressImage(filePath, quality = 0.8) {
  return new Promise((resolve, reject) => {
    wx.compressImage({
      src: filePath,
      quality: quality * 100, // 微信API使用0-100
      success: (res) => {
        console.log('[UploadService] 图片压缩成功:', {
          original: filePath,
          compressed: res.tempFilePath
        })
        resolve(res.tempFilePath)
      },
      fail: (error) => {
        console.error('[UploadService] 图片压缩失败:', error)
        // 压缩失败时返回原文件路径
        resolve(filePath)
      }
    })
  })
}

module.exports = {
  uploadImage,
  uploadMultipleImages,
  deleteUploadedFile,
  compressImage
} 