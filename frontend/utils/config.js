// 引入统一的域名配置
const { 
  DOMAINS, 
  ENV_TYPES, 
  CURRENT_ENV,
  getCurrentDomainConfig, 
  getApiBaseUrl 
} = require('../config/domains')

// 使用统一的环境配置（从 domains.js 导入）
let currentEnv = CURRENT_ENV

// 获取当前环境的域名配置
let domainConfig = getCurrentDomainConfig(currentEnv)

// 导出当前环境的配置
const config = {
  get baseUrl() {
    return domainConfig.urls.apiBase;
  },
  apiPrefix: '/api/v1',
  uploadUrl: '/api/v1/attendant/upload',
  get env() {
    return currentEnv;
  },
  // 确保baseURL能被正确导出和使用
  get baseURL() {
    return this.baseUrl;
  }
}

// 设置环境函数
function setEnv(envType) {
  if (Object.values(ENV_TYPES).includes(envType)) {
    currentEnv = envType
    domainConfig = getCurrentDomainConfig(currentEnv)
    console.log(`[Config] 环境已切换为: ${currentEnv}, API地址: ${domainConfig.urls.apiBase}`)
    return true
  }
  return false
}

// 打印当前配置，便于调试
console.log(`[Config] 当前环境: ${config.env}, API地址: ${config.baseUrl}${config.apiPrefix}`)

// 使用CommonJS导出方式
module.exports = config
module.exports.ENV_TYPES = ENV_TYPES
module.exports.setEnv = setEnv