const http = require('./request')
const config = require('./config')

// 上传文件
const uploadFile = (filePath) => {
  return new Promise((resolve, reject) => {
    // 构建完整的上传URL
    // 确保使用config.js中定义的uploadUrl
    const baseUrl = config.baseUrl.endsWith('/') ? config.baseUrl.slice(0, -1) : config.baseUrl
    const uploadPath = config.uploadUrl.startsWith('/') ? config.uploadUrl : `/${config.uploadUrl}`
    const uploadUrl = `${baseUrl}${uploadPath}`
    
    console.log('[Upload] 上传URL:', uploadUrl)
    
    wx.uploadFile({
      url: uploadUrl,
      filePath: filePath,
      name: 'file',
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000, // 增加超时时间到60秒
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data)
            if (data.code === 0) {
              resolve(data.data.url)
            } else {
              reject(new Error(data.message || '上传失败：服务器返回错误'))
            }
          } catch (err) {
            console.error('解析响应数据失败:', res.data, err)
            reject(new Error('上传失败：无法解析响应数据'))
          }
        } else {
          reject(new Error(`上传失败：状态码 ${res.statusCode}`))
        }
      },
      fail: (err) => {
        console.error('上传请求失败:', err)
        // 确保错误对象有message属性
        if (!err.message) {
          if (err.errMsg) {
            err.message = err.errMsg
          } else {
            err = new Error('上传失败：网络错误或服务器无响应')
          }
        }
        reject(err)
      }
    })
  })
}

// 上传多个文件
const uploadFiles = (filePaths) => {
  const promises = filePaths.map(filePath => uploadFile(filePath))
  return Promise.all(promises)
}

module.exports = {
  uploadFile,
  uploadFiles
} 