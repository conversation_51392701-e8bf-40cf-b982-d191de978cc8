<!-- 
  地区选择器模板
  使用示例:
  <import src="/templates/region-picker.wxml" />
  <template is="region-picker" data="{{region: form.region, bindchange: 'selectRegion'}}" />
-->
<template name="region-picker">
  <picker 
    mode="region" 
    bindchange="{{bindchange}}" 
    value="{{region}}"
    level="city"
  >
    <view class="picker {{region.length > 0 ? 'picker-selected' : ''}}">
      {{region.length > 0 ? region[0] + ' - ' + region[1] : '请选择服务区域'}}
    </view>
  </picker>
</template>
