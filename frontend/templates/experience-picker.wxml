<!-- 
  工作经验选择器模板
  使用示例:
  <import src="/templates/experience-picker.wxml" />
  <template is="experience-picker" data="{{experienceIndex: experienceIndex, experienceOptions: experienceOptions, bindchange: 'selectExperience'}}" />
-->
<template name="experience-picker">
  <picker 
    bindchange="{{bindchange}}" 
    value="{{experienceIndex}}" 
    range="{{experienceOptions}}"
  >
    <view class="picker">
      {{experienceIndex === -1 ? '请选择工作经验' : experienceOptions[experienceIndex]}}
    </view>
  </picker>
</template>
