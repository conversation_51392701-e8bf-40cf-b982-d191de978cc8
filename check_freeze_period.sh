#!/bin/bash

# 检查收入冻结期问题
ATTENDANT_ID=13
ORDER_ID=10017
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

echo "=== 检查收入冻结期问题 ==="
echo ""

# 1. 检查系统配置中的冻结天数
echo "1. 检查系统配置中的冻结天数:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '系统配置' as type,
    sc.config_key,
    sc.config_value,
    sc.description
FROM system_configs sc
WHERE sc.config_key = 'finance.freeze_days';
" 2>/dev/null
echo ""

# 2. 检查收入记录的创建时间和冻结状态
echo "2. 检查收入记录的冻结状态:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '收入冻结状态' as type,
    ai.id,
    ai.order_id,
    ai.attendant_id,
    ai.amount,
    ai.created_at as 收入创建时间,
    NOW() as 当前时间,
    TIMESTAMPDIFF(HOUR, ai.created_at, NOW()) as 已过小时数,
    TIMESTAMPDIFF(DAY, ai.created_at, NOW()) as 已过天数,
    CASE 
        WHEN TIMESTAMPDIFF(HOUR, ai.created_at, NOW()) >= 168 THEN '✅ 已解冻(7天)'
        WHEN TIMESTAMPDIFF(HOUR, ai.created_at, NOW()) >= 72 THEN '⏳ 部分解冻(3天)'
        ELSE '❄️ 仍在冻结期'
    END as 冻结状态
FROM attendant_income ai
WHERE ai.attendant_id = $ATTENDANT_ID AND ai.order_id = $ORDER_ID;
" 2>/dev/null
echo ""

# 3. 检查账户余额更新逻辑
echo "3. 检查账户余额:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '账户余额' as type,
    a.user_id,
    a.balance as 当前余额,
    a.frozen_balance as 冻结余额,
    a.total_income as 总收入,
    a.updated_at as 更新时间,
    TIMESTAMPDIFF(HOUR, a.updated_at, NOW()) as 更新后小时数
FROM accounts a
WHERE a.user_id = $ATTENDANT_ID;
" 2>/dev/null
echo ""

# 4. 模拟计算可提现金额
echo "4. 模拟计算可提现金额:"
HOURS_PASSED=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT TIMESTAMPDIFF(HOUR, ai.created_at, NOW()) 
FROM attendant_income ai 
WHERE ai.attendant_id = $ATTENDANT_ID AND ai.order_id = $ORDER_ID;
" 2>/dev/null | tail -n 1)

FREEZE_DAYS=7
FREEZE_HOURS=$((FREEZE_DAYS * 24))

echo "• 收入创建后已过小时数: $HOURS_PASSED"
echo "• 冻结期小时数: $FREEZE_HOURS"

if [ "$HOURS_PASSED" -ge "$FREEZE_HOURS" ]; then
    echo "• 状态: ✅ 收入已解冻，可以提现"
else
    REMAINING_HOURS=$((FREEZE_HOURS - HOURS_PASSED))
    echo "• 状态: ❄️ 收入仍在冻结期，还需等待 $REMAINING_HOURS 小时"
fi
echo ""

# 5. 检查是否需要更新账户余额
echo "5. 检查账户余额同步状态:"
mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    '余额同步检查' as type,
    ai.amount as 收入金额,
    ai.status as 收入状态,
    ai.settle_time as 结算时间,
    a.balance as 账户余额,
    a.updated_at as 账户更新时间,
    CASE 
        WHEN ai.status = 2 AND a.balance = ai.amount THEN '✅ 余额已同步'
        WHEN ai.status = 2 AND a.balance != ai.amount THEN '⚠️ 余额未同步'
        ELSE '❓ 状态异常'
    END as 同步状态
FROM attendant_income ai
LEFT JOIN accounts a ON ai.attendant_id = a.user_id
WHERE ai.attendant_id = $ATTENDANT_ID AND ai.order_id = $ORDER_ID;
" 2>/dev/null

echo ""
echo "=== 分析完成 ==="