#!/bin/bash

echo "=== 微信企业付款环境变量验证 ==="

# 加载环境变量
if [ -f "production.env" ]; then
    source production.env
    echo "✅ 已加载 production.env 文件"
else
    echo "❌ production.env 文件不存在"
    exit 1
fi

echo -e "\n📋 检查必需的环境变量："

# 必需的环境变量列表
required_vars=(
    "WECHAT_TRANSFER_CLIENT_TYPE"
    "WECHAT_TRANSFER_APP_ID"
    "WECHAT_TRANSFER_MCH_ID"
    "WECHAT_TRANSFER_APIV3_KEY"
    "WECHAT_TRANSFER_CERTIFICATE_SERIAL_NUMBER"
    "WECHAT_TRANSFER_PRIVATE_KEY_PATH"
    "WECHAT_TRANSFER_ENVIRONMENT"
    "WECHAT_TRANSFER_NOTIFY_URL"
)

all_set=true

for var in "${required_vars[@]}"; do
    if [ -n "${!var}" ]; then
        echo "✅ $var = ${!var}"
    else
        echo "❌ $var = (未设置)"
        all_set=false
    fi
done

echo -e "\n📋 检查兼容性环境变量："

# 兼容性环境变量
compat_vars=(
    "WECHAT_TRANSFER_SERIAL_NO"
    "WECHAT_TRANSFER_PRIVATE_KEY_FILE"
)

for var in "${compat_vars[@]}"; do
    if [ -n "${!var}" ]; then
        echo "✅ $var = ${!var}"
    else
        echo "⚠️  $var = (未设置，但不是必需的)"
    fi
done

echo -e "\n🔍 检查证书文件："

if [ -f "$WECHAT_TRANSFER_PRIVATE_KEY_PATH" ]; then
    echo "✅ 私钥文件存在: $WECHAT_TRANSFER_PRIVATE_KEY_PATH"
    
    # 检查文件权限
    file_perms=$(stat -c "%a" "$WECHAT_TRANSFER_PRIVATE_KEY_PATH" 2>/dev/null || stat -f "%A" "$WECHAT_TRANSFER_PRIVATE_KEY_PATH" 2>/dev/null)
    if [ "$file_perms" = "600" ] || [ "$file_perms" = "400" ]; then
        echo "✅ 文件权限正确: $file_perms"
    else
        echo "⚠️  文件权限: $file_perms (建议设置为600)"
    fi
else
    echo "❌ 私钥文件不存在: $WECHAT_TRANSFER_PRIVATE_KEY_PATH"
    all_set=false
fi

echo -e "\n📊 配置摘要："
echo "   客户端类型: $WECHAT_TRANSFER_CLIENT_TYPE"
echo "   应用ID: $WECHAT_TRANSFER_APP_ID"
echo "   商户号: $WECHAT_TRANSFER_MCH_ID"
echo "   环境: $WECHAT_TRANSFER_ENVIRONMENT"
echo "   回调地址: $WECHAT_TRANSFER_NOTIFY_URL"

echo -e "\n🎯 验证结果："
if [ "$all_set" = true ]; then
    echo "✅ 所有必需的环境变量都已正确设置"
    echo "✅ 可以使用微信官方SDK进行企业付款"
    echo ""
    echo "🚀 下一步操作："
    echo "   1. 确保服务器已重启以加载新的环境变量"
    echo "   2. 检查应用日志确认使用官方SDK客户端"
    echo "   3. 在管理后台测试确认打款功能"
else
    echo "❌ 存在未设置的必需环境变量"
    echo "❌ 请检查并修复上述问题后重新验证"
fi

echo -e "\n=== 验证完成 ==="