#!/bin/bash

# 微信商户转账权限检查脚本
# 用于诊断 NO_AUTH 错误的根本原因

echo "🔍 微信商户转账权限检查"
echo "=========================="

# 检查是否在正确的目录
if [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 设置环境变量（从生产环境配置读取）
echo "📋 加载环境配置..."

# 检查生产环境配置文件
if [ -f "production.env" ]; then
    echo "   从 production.env 加载配置"
    source production.env
elif [ -f "backend/config/conf/config.prod.yaml" ]; then
    echo "   从 YAML 配置文件读取配置"
    # 从YAML文件提取配置（简化版本）
    export WECHAT_MCH_ID=$(grep "mch_id:" backend/config/conf/config.prod.yaml | awk '{print $2}' | tr -d '"')
    export WECHAT_APP_ID=$(grep "app_id:" backend/config/conf/config.prod.yaml | awk '{print $2}' | tr -d '"')
else
    echo "❌ 未找到配置文件，请确保存在 production.env 或配置文件"
    exit 1
fi

# 显示当前配置
echo ""
echo "🔧 当前配置:"
echo "   商户号: ${WECHAT_MCH_ID:-未设置}"
echo "   应用ID: ${WECHAT_APP_ID:-未设置}"
echo "   私钥路径: ${WECHAT_PRIVATE_KEY_PATH:-未设置}"
echo "   证书序列号: ${WECHAT_CERTIFICATE_SERIAL_NUMBER:-未设置}"

# 检查必要的环境变量
missing_vars=()

if [ -z "$WECHAT_MCH_ID" ]; then
    missing_vars+=("WECHAT_MCH_ID")
fi

if [ -z "$WECHAT_APP_ID" ]; then
    missing_vars+=("WECHAT_APP_ID")
fi

if [ -z "$WECHAT_API_V3_KEY" ]; then
    missing_vars+=("WECHAT_API_V3_KEY")
fi

if [ -z "$WECHAT_CERTIFICATE_SERIAL_NUMBER" ]; then
    missing_vars+=("WECHAT_CERTIFICATE_SERIAL_NUMBER")
fi

if [ -z "$WECHAT_PRIVATE_KEY_PATH" ]; then
    missing_vars+=("WECHAT_PRIVATE_KEY_PATH")
fi

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "❌ 缺少必要的环境变量:"
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "💡 请设置这些环境变量后重新运行"
    echo "   可以在 production.env 文件中设置，或者直接导出："
    echo "   export WECHAT_MCH_ID=\"1717184423\""
    echo "   export WECHAT_APP_ID=\"your_app_id\""
    echo "   # ... 其他变量"
    exit 1
fi

# 检查私钥文件是否存在
if [ ! -f "$WECHAT_PRIVATE_KEY_PATH" ]; then
    echo "❌ 私钥文件不存在: $WECHAT_PRIVATE_KEY_PATH"
    echo "💡 请确保私钥文件路径正确"
    exit 1
fi

echo ""
echo "✅ 环境配置检查通过"

# 编译并运行权限检查工具
echo ""
echo "🔨 编译权限检查工具..."
cd backend
if ! go build -o ../check_permissions check_wechat_merchant_permissions.go; then
    echo "❌ 编译失败"
    exit 1
fi
cd ..

echo "✅ 编译成功"

# 运行权限检查
echo ""
echo "🚀 开始权限检查..."
echo "=================================="

if ./check_permissions; then
    echo ""
    echo "✅ 权限检查完成"
else
    echo ""
    echo "❌ 权限检查发现问题"
    echo ""
    echo "📋 下一步建议:"
    echo "1. 根据上面的错误分析进行相应处理"
    echo "2. 如果是商户号升级问题，请登录微信支付商户平台检查"
    echo "3. 如果需要技术支持，请联系微信支付客服: 95017"
    echo "4. 提供商户号: $WECHAT_MCH_ID"
fi

# 清理临时文件
rm -f check_permissions

echo ""
echo "🎯 权限检查完成！"