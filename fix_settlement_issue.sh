#!/bin/bash

# 修复结算问题脚本
ORDER_NO="ORD202507311203108006"
ORDER_ID=10017
ATTENDANT_ID=13
DB_HOST="*************"
DB_USER="carego_prod_user"
DB_PASS="4leJXDlbDRMiRYutuhCmlebljrPeTTvR!"
DB_NAME="carego_prod"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

run_mysql() {
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$1"
}

echo "=== 修复订单结算问题 ==="
echo "订单号: $ORDER_NO"
echo "订单ID: $ORDER_ID"
echo "陪诊师ID: $ATTENDANT_ID"
echo ""

# 1. 检查当前状态
log_step "1. 检查当前状态..."
run_mysql "
SELECT 
    '当前状态' as type,
    o.id,
    o.order_no,
    o.status as order_status,
    o.settlement_status,
    o.settlement_time,
    ai.status as income_status,
    ai.settle_time,
    CASE 
        WHEN o.status = 13 THEN '✅ 订单已结算'
        WHEN o.status = 10 THEN '⏳ 订单审核通过'
        ELSE CONCAT('❓ 订单状态: ', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 2 THEN '✅ 收入已结算'
        WHEN ai.status = 1 THEN '⏳ 收入待结算'
        ELSE CONCAT('❓ 收入状态: ', ai.status)
    END as income_status_text
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = $ATTENDANT_ID
WHERE o.id = $ORDER_ID;
"
echo ""

# 2. 检查自动结算配置
log_step "2. 检查自动结算配置..."
run_mysql "
SELECT 
    '自动结算配置' as type,
    asc.auto_settlement_enabled,
    asc.review_period_hours,
    asc.updated_at,
    CASE 
        WHEN asc.auto_settlement_enabled = 1 THEN '✅ 已启用'
        ELSE '❌ 未启用'
    END as config_status
FROM auto_settlement_configs asc
ORDER BY asc.updated_at DESC
LIMIT 1;
"
echo ""

# 3. 检查T+2审核记录
log_step "3. 检查T+2审核记录..."
run_mysql "
SELECT 
    '审核记录' as type,
    osr.id as review_id,
    osr.order_id,
    osr.status as review_status,
    osr.review_period_hours,
    osr.created_at,
    osr.expires_at,
    osr.reviewed_at,
    CASE 
        WHEN osr.status = 'approved' THEN '✅ 审核通过'
        WHEN osr.status = 'pending' THEN '⏳ 待审核'
        WHEN osr.status = 'in_progress' THEN '🔄 审核中'
        ELSE CONCAT('❓ 状态: ', osr.status)
    END as review_status_text,
    CASE 
        WHEN osr.expires_at < NOW() THEN '⚠️ 已过期'
        ELSE '✅ 未过期'
    END as expiry_status
FROM order_settlement_reviews osr
WHERE osr.order_id = $ORDER_ID
ORDER BY osr.created_at DESC;
"
echo ""

# 4. 问题分析
log_step "4. 问题分析..."

ORDER_STATUS=$(run_mysql "SELECT status FROM orders WHERE id = $ORDER_ID;" | tail -n 1)
INCOME_STATUS=$(run_mysql "SELECT status FROM attendant_income WHERE order_id = $ORDER_ID AND attendant_id = $ATTENDANT_ID;" | tail -n 1)
SETTLEMENT_STATUS=$(run_mysql "SELECT settlement_status FROM orders WHERE id = $ORDER_ID;" | tail -n 1)

echo "• 订单状态: $ORDER_STATUS"
echo "• 收入状态: $INCOME_STATUS" 
echo "• 结算状态: $SETTLEMENT_STATUS"
echo ""

if [ "$ORDER_STATUS" = "10" ] && [ "$INCOME_STATUS" = "2" ] && [ "$SETTLEMENT_STATUS" = "2" ]; then
    log_warn "发现问题: 收入已结算但订单状态未更新"
    
    # 5. 修复订单状态
    log_step "5. 修复订单状态..."
    
    echo "准备将订单状态从 10 更新为 13..."
    read -p "确认执行修复? (y/N): " confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        log_info "执行修复..."
        
        # 更新订单状态为已结算
        run_mysql "
        UPDATE orders 
        SET status = 13, 
            updated_at = NOW()
        WHERE id = $ORDER_ID AND status = 10;
        "
        
        if [ $? -eq 0 ]; then
            log_info "✅ 订单状态修复成功"
            
            # 记录操作日志
            run_mysql "
            INSERT INTO admin_operation_logs (admin_id, operation_type, target_type, target_id, operation_details, created_at)
            VALUES (1, 'fix_settlement', 'order', $ORDER_ID, '修复订单结算状态不一致问题', NOW());
            "
            
        else
            log_error "❌ 订单状态修复失败"
        fi
    else
        log_info "取消修复操作"
    fi
else
    log_info "状态正常，无需修复"
fi

echo ""

# 6. 验证修复结果
log_step "6. 验证修复结果..."
run_mysql "
SELECT 
    '修复后状态' as type,
    o.id,
    o.order_no,
    o.status as order_status,
    o.settlement_status,
    o.settlement_time,
    ai.status as income_status,
    ai.settle_time,
    CASE 
        WHEN o.status = 13 THEN '✅ 订单已结算'
        WHEN o.status = 10 THEN '⏳ 订单审核通过'
        ELSE CONCAT('❓ 订单状态: ', o.status)
    END as order_status_text,
    CASE 
        WHEN ai.status = 2 THEN '✅ 收入已结算'
        WHEN ai.status = 1 THEN '⏳ 收入待结算'
        ELSE CONCAT('❓ 收入状态: ', ai.status)
    END as income_status_text,
    CASE 
        WHEN o.status = 13 AND ai.status = 2 THEN '✅ 状态一致'
        ELSE '⚠️ 状态不一致'
    END as consistency_check
FROM orders o 
LEFT JOIN attendant_income ai ON o.id = ai.order_id AND ai.attendant_id = $ATTENDANT_ID
WHERE o.id = $ORDER_ID;
"
echo ""

# 7. 检查陪诊师账户余额（如果有accounts表）
log_step "7. 检查陪诊师账户余额..."
run_mysql "
SELECT 
    '账户余额' as type,
    a.user_id as attendant_id,
    a.balance as 可提现金额,
    a.frozen_balance as 冻结金额,
    a.total_income as 总收入,
    a.updated_at as 更新时间
FROM accounts a
WHERE a.user_id = $ATTENDANT_ID AND a.account_type = 'attendant';
" 2>/dev/null || log_warn "accounts表不存在或结构不同"

echo ""
log_info "修复操作完成"