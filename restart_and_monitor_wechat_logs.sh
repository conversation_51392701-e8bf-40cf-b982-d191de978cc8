#!/bin/bash

echo "=== 重启服务并监控微信转账日志 ==="

# 查找可能的服务名
services=("peizhen" "peizhen-backend" "backend" "go-app")
found_service=""

echo "1. 查找后端服务..."
for service in "${services[@]}"; do
    if systemctl is-active --quiet "$service" 2>/dev/null; then
        echo "   ✅ 找到运行中的服务: $service"
        found_service="$service"
        break
    elif systemctl list-units --full -all | grep -q "$service.service"; then
        echo "   ✅ 找到服务定义: $service"
        found_service="$service"
        break
    fi
done

if [ -z "$found_service" ]; then
    echo "   ⚠️  未找到标准systemd服务"
    echo "   请手动提供服务名称或重启方式"
    read -p "   请输入服务名称 (或按Enter跳过): " manual_service
    if [ -n "$manual_service" ]; then
        found_service="$manual_service"
    else
        echo "   跳过服务重启，请手动重启后端服务"
    fi
fi

if [ -n "$found_service" ]; then
    echo -e "\n2. 重启服务: $found_service"
    sudo systemctl restart "$found_service"
    
    echo "   等待服务启动..."
    sleep 5
    
    if systemctl is-active --quiet "$found_service"; then
        echo "   ✅ 服务重启成功"
    else
        echo "   ❌ 服务重启失败"
        echo "   检查服务状态: sudo systemctl status $found_service"
        exit 1
    fi
fi

echo -e "\n3. 查找日志文件..."
log_paths=(
    "/var/www/html/peizhen/logs/app.prod.log"
    "/var/log/peizhen/app.log"
    "/var/www/html/peizhen/logs/app.log"
)

log_file=""
for path in "${log_paths[@]}"; do
    if [ -f "$path" ]; then
        echo "   ✅ 找到日志文件: $path"
        log_file="$path"
        break
    fi
done

if [ -z "$log_file" ]; then
    echo "   ❌ 未找到日志文件"
    echo "   请手动指定日志文件路径"
    exit 1
fi

echo -e "\n4. 开始监控微信转账日志..."
echo "   日志文件: $log_file"
echo "   监控关键词: 微信转账、官方SDK、简化客户端、FUNCTION_DISABLED"
echo "   按 Ctrl+C 停止监控"
echo ""
echo "=== 日志监控开始 ==="

# 显示最近的几行日志作为上下文
echo "最近的日志内容:"
tail -5 "$log_file"
echo ""
echo "实时日志监控:"

# 实时监控日志
tail -f "$log_file" | grep --line-buffered -E "(微信|wechat|transfer|官方SDK|简化客户端|FUNCTION_DISABLED|创建.*客户端|配置验证|API调用)" -i