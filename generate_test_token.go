package main

import (
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

func main() {
	// JWT secret from dev config
	secret := "dev-secret-key"
	
	// Create token for user_id = 1 (attendant)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"sub": "1",           // user_id
		"aud": "attendant",   // audience
		"iss": "peizhen",     // issuer
		"iat": time.Now().Unix(),
		"exp": time.Now().Add(24 * time.Hour).Unix(),
	})

	// Sign token
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		fmt.Printf("Error creating token: %v\n", err)
		return
	}

	fmt.Printf("Test JWT Token: %s\n", tokenString)
}
