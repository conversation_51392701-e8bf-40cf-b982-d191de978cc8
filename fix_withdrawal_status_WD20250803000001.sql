-- 修复提现申请 WD20250803000001 的状态
-- 将其从"转账失败"恢复到"审核通过"状态，以便重新确认打款

-- 查看当前状态
SELECT 
    id,
    withdrawal_no,
    status,
    CASE status
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '已打款'
        WHEN 4 THEN '已驳回'
        WHEN 5 THEN '转账中'
        WHEN 6 THEN '已到账'
        WHEN 7 THEN '转账失败'
        ELSE '未知状态'
    END as status_text,
    amount,
    actual_amount,
    created_at,
    updated_at
FROM withdrawals 
WHERE withdrawal_no = 'WD20250803000001';

-- 查看转账记录状态
SELECT 
    id,
    withdrawal_id,
    transfer_no,
    amount,
    status,
    CASE status
        WHEN 1 THEN '转账中'
        WHEN 2 THEN '转账成功'
        WHEN 3 THEN '转账失败'
        ELSE '未知状态'
    END as status_text,
    retry_count,
    fail_reason,
    wechat_batch_no,
    created_at,
    updated_at
FROM withdrawal_transfers 
WHERE withdrawal_id = 1;

-- 修复提现申请状态：从"转账失败"(7)恢复到"审核通过"(2)
UPDATE withdrawals 
SET 
    status = 2,  -- 审核通过
    updated_at = NOW()
WHERE withdrawal_no = 'WD20250803000001';

-- 验证修复结果
SELECT 
    id,
    withdrawal_no,
    status,
    CASE status
        WHEN 1 THEN '待审核'
        WHEN 2 THEN '审核通过'
        WHEN 3 THEN '已打款'
        WHEN 4 THEN '已驳回'
        WHEN 5 THEN '转账中'
        WHEN 6 THEN '已到账'
        WHEN 7 THEN '转账失败'
        ELSE '未知状态'
    END as status_text,
    amount,
    actual_amount,
    updated_at
FROM withdrawals 
WHERE withdrawal_no = 'WD20250803000001';

-- 注意：转账记录保持失败状态，这样可以看到历史记录
-- 下次点击"确认打款"时，系统会根据重试逻辑处理失败的转账记录