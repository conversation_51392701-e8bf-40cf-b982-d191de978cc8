#!/bin/bash

# 修复微信转账客户端类型问题
# 解决线上环境错误进入简化客户端的问题

echo "🔧 修复微信转账客户端类型问题"
echo "=================================="

# 1. 检查当前环境变量配置
echo -e "\n1. 检查当前环境变量配置："
echo "   WECHAT_TRANSFER_CLIENT_TYPE: ${WECHAT_TRANSFER_CLIENT_TYPE:-未设置}"
echo "   WECHAT_TRANSFER_ENABLED: ${WECHAT_TRANSFER_ENABLED:-未设置}"
echo "   WECHAT_TRANSFER_MOCK_MODE: ${WECHAT_TRANSFER_MOCK_MODE:-未设置}"

# 2. 检查证书文件权限
echo -e "\n2. 检查证书文件权限："
cert_files=(
    "/etc/peizhen/certs/wechat/apiclient_key.pem"
    "/etc/peizhen/certs/wechat/wechat_public_key.pem"
    "/etc/peizhen/certs/wechat/apiclient_cert.pem"
)

for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "   ✅ $cert_file"
        ls -la "$cert_file"
    else
        echo "   ❌ $cert_file (不存在)"
    fi
done

# 3. 修复环境变量配置
echo -e "\n3. 修复环境变量配置："

# 备份当前配置
if [ -f "production.env" ]; then
    cp production.env "production.env.backup.$(date +%Y%m%d_%H%M%S)"
    echo "   ✅ 已备份 production.env"
fi

# 修复 WECHAT_TRANSFER_CLIENT_TYPE
if grep -q "WECHAT_TRANSFER_CLIENT_TYPE=simple" production.env; then
    echo "   🔧 修复 WECHAT_TRANSFER_CLIENT_TYPE: simple -> official"
    sed -i 's/WECHAT_TRANSFER_CLIENT_TYPE=simple/WECHAT_TRANSFER_CLIENT_TYPE=official/' production.env
else
    echo "   ℹ️  WECHAT_TRANSFER_CLIENT_TYPE 已经是正确的值"
fi

# 4. 修复证书文件权限
echo -e "\n4. 修复证书文件权限："

# 获取当前运行用户
current_user=$(whoami)
echo "   当前用户: $current_user"

# 检查应用运行用户
app_user="www-data"  # 根据进程信息确定为www-data
echo "   应用运行用户: $app_user"

# 修复证书文件权限
for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "   🔧 修复 $cert_file 权限"
        sudo chown $app_user:$app_user "$cert_file"
        sudo chmod 600 "$cert_file"
        echo "   ✅ 权限已修复: $(ls -la "$cert_file")"
    fi
done

# 5. 验证修复结果
echo -e "\n5. 验证修复结果："

# 检查环境变量
source production.env
echo "   WECHAT_TRANSFER_CLIENT_TYPE: $WECHAT_TRANSFER_CLIENT_TYPE"
echo "   WECHAT_TRANSFER_ENABLED: $WECHAT_TRANSFER_ENABLED"
echo "   WECHAT_TRANSFER_MOCK_MODE: $WECHAT_TRANSFER_MOCK_MODE"

# 检查证书文件
echo -e "\n   证书文件状态："
for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "   ✅ $cert_file: $(ls -la "$cert_file" | awk '{print $1, $3":"$4}')"
    else
        echo "   ❌ $cert_file: 不存在"
    fi
done

# 6. 创建测试脚本
echo -e "\n6. 创建测试脚本："
cat > test_wechat_transfer_client_type.go << 'EOF'
package main

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/gemeijie/peizhen/backend/config"
	"github.com/gemeijie/peizhen/backend/pkg/wechat"
	"go.uber.org/zap"
)

func main() {
	fmt.Println("🧪 测试微信转账客户端类型")
	fmt.Println("============================")

	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 加载配置
	fmt.Println("\n1. 加载配置...")
	if err := config.LoadConfig(); err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	globalConfig := config.GetGlobalConfig()
	if globalConfig == nil {
		fmt.Println("❌ 无法获取全局配置")
		return
	}

	transferConfig := &globalConfig.WeChat.Transfer

	fmt.Printf("   配置信息:\n")
	fmt.Printf("   - ClientType: %s\n", transferConfig.ClientType)
	fmt.Printf("   - Enabled: %t\n", transferConfig.Enabled)
	fmt.Printf("   - MockMode: %t\n", transferConfig.MockMode)
	fmt.Printf("   - Environment: %s\n", transferConfig.Environment)
	fmt.Printf("   - AppID: %s\n", transferConfig.AppID)
	fmt.Printf("   - MchID: %s\n", transferConfig.MchID)
	fmt.Printf("   - PrivateKeyPath: %s\n", transferConfig.PrivateKeyPath)

	// 检查证书文件
	fmt.Println("\n2. 检查证书文件...")
	certFiles := []string{
		transferConfig.PrivateKeyPath,
		"/etc/peizhen/certs/wechat/wechat_public_key.pem",
		"/etc/peizhen/certs/wechat/apiclient_cert.pem",
	}

	for _, certFile := range certFiles {
		if certFile == "" {
			continue
		}
		
		absPath, _ := filepath.Abs(certFile)
		if _, err := os.Stat(absPath); err == nil {
			info, _ := os.Stat(absPath)
			fmt.Printf("   ✅ %s (权限: %s)\n", absPath, info.Mode())
		} else {
			fmt.Printf("   ❌ %s (不存在或无权限访问)\n", absPath)
		}
	}

	// 尝试创建客户端
	fmt.Println("\n3. 尝试创建转账客户端...")
	
	// 直接尝试创建官方客户端
	fmt.Println("   尝试创建官方客户端...")
	officialClient, err := wechat.NewOfficialWechatTransferClientV2(transferConfig, logger)
	if err != nil {
		fmt.Printf("   ❌ 官方客户端创建失败: %v\n", err)
	} else {
		fmt.Printf("   ✅ 官方客户端创建成功: %T\n", officialClient)
	}

	// 通过工厂创建客户端
	fmt.Println("   通过工厂创建客户端...")
	client, err := wechat.GetGlobalTransferClient()
	if err != nil {
		fmt.Printf("   ❌ 工厂客户端创建失败: %v\n", err)
		return
	}

	fmt.Printf("   ✅ 工厂客户端创建成功: %T\n", client)

	// 判断客户端类型
	switch client.(type) {
	case *wechat.OfficialWechatTransferClientV2:
		fmt.Println("   🎯 当前使用: 官方SDK客户端 V2")
	case *wechat.SimpleWechatTransferClient:
		fmt.Println("   ⚠️  当前使用: 简化客户端 (这可能不是期望的)")
	default:
		fmt.Printf("   ❓ 未知客户端类型: %T\n", client)
	}

	fmt.Println("\n✅ 测试完成")
}
EOF

echo "   ✅ 已创建测试脚本: test_wechat_transfer_client_type.go"

# 7. 提供重启服务的建议
echo -e "\n7. 重启服务建议："
echo "   修复完成后，请重启相关服务："
echo "   sudo systemctl restart peizhen-backend"
echo "   sudo systemctl restart peizhen-admin"

echo -e "\n🎯 修复完成！"
echo "=================================="
echo "主要修复内容："
echo "1. ✅ 修复环境变量 WECHAT_TRANSFER_CLIENT_TYPE: simple -> official"
echo "2. ✅ 修复证书文件权限问题"
echo "3. ✅ 创建测试脚本验证修复结果"
echo ""
echo "下一步操作："
echo "1. 运行测试脚本: go run test_wechat_transfer_client_type.go"
echo "2. 重启服务使配置生效"
echo "3. 验证转账功能是否正常"