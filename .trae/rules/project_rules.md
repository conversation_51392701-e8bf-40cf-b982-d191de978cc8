## 核心原则
- 所有功能开发必须分三阶段执行：  
  1. **需求阶段**：生成 `requirements.md`，明确功能目标和验收标准。  
  2. **设计阶段**：生成 `design.md`，描述技术方案和架构设计。  
  3. **任务阶段**：生成 `tasks.md`，拆解为可执行的开发任务列表。  
- 每个阶段需经人工确认后方可进入下一阶段。

## 文件规范
- 需求文档路径：`.kiro/specs/{功能名}/requirements.md`  
- 设计文档路径：`.kiro/specs/{功能名}/design.md`  
- 任务文档路径：`.kiro/specs/{功能名}/tasks.md`

## 开发流程
- 禁止直接生成代码，必须先完成三阶段文档。  
- 任务阶段需将每个步骤拆解为原子操作（如“创建路由”“实现数据库模型”）。  
- 每个任务需附带验收条件和关联文件路径。