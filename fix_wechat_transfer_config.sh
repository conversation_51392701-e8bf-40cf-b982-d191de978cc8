#!/bin/bash

# 微信转账配置快速修复脚本
# 修复重试转账失败问题

echo "=== 微信转账配置快速修复 ==="

# 1. 备份当前配置
echo "1. 备份当前配置..."
backup_dir="backup/config_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"
cp backend/config/conf/config.prod.yaml "$backup_dir/" 2>/dev/null && echo "✅ 已备份生产配置文件"

# 2. 检查并修复配置文件中的硬编码数据
echo ""
echo "2. 检查配置文件..."
config_file="backend/config/conf/config.prod.yaml"

if [ -f "$config_file" ]; then
    echo "✅ 找到配置文件: $config_file"
    
    # 检查是否有硬编码的证书序列号
    if grep -q "certificate_serial_number.*3B2F1BB6FBF9CD4D2448AB6310720C15CD668247" "$config_file"; then
        echo "⚠️  发现硬编码的证书序列号，正在修复..."
        
        # 使用sed修复硬编码的证书序列号
        sed -i.bak 's/certificate_serial_number: "${WECHAT_CERT_SERIAL_NUMBER:3B2F1BB6FBF9CD4D2448AB6310720C15CD668247}"/certificate_serial_number: "${WECHAT_CERT_SERIAL_NUMBER}"/g' "$config_file"
        sed -i.bak 's/serial_no: "${WECHAT_TRANSFER_SERIAL_NO:3B2F1BB6FBF9CD4D2448AB6310720C15CD668247}"/serial_no: "${WECHAT_TRANSFER_SERIAL_NO}"/g' "$config_file"
        
        echo "✅ 已修复硬编码的证书序列号"
    else
        echo "✅ 配置文件中没有硬编码的证书序列号"
    fi
    
else
    echo "❌ 配置文件不存在: $config_file"
    exit 1
fi

# 3. 验证环境变量
echo ""
echo "3. 验证关键环境变量..."

required_vars=(
    "WECHAT_APP_ID"
    "WECHAT_MCH_ID"
    "WECHAT_API_V3_KEY"
    "WECHAT_CERT_SERIAL_NUMBER"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo "⚠️  以下环境变量未设置："
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "请在 .env 文件或系统环境中设置这些变量。"
else
    echo "✅ 所有必要的环境变量都已设置"
fi

# 4. 检查私钥文件
echo ""
echo "4. 检查私钥文件..."

private_key_path="${WECHAT_TRANSFER_PRIVATE_KEY_PATH:-${WECHAT_PRIVATE_KEY_PATH:-/etc/peizhen/certs/wechat/apiclient_key.pem}}"
echo "私钥文件路径: $private_key_path"

if [ -f "$private_key_path" ]; then
    echo "✅ 私钥文件存在"
    
    # 检查文件权限
    if [ "$(stat -c "%a" "$private_key_path" 2>/dev/null)" = "600" ] || [ "$(stat -c "%a" "$private_key_path" 2>/dev/null)" = "400" ]; then
        echo "✅ 私钥文件权限正确"
    else
        echo "⚠️  修复私钥文件权限..."
        chmod 600 "$private_key_path" && echo "✅ 已修复私钥文件权限"
    fi
else
    echo "❌ 私钥文件不存在: $private_key_path"
    echo ""
    echo "请确保私钥文件存在。如果文件在其他位置，请设置正确的环境变量："
    echo "export WECHAT_TRANSFER_PRIVATE_KEY_PATH=/path/to/your/private_key.pem"
fi

# 5. 测试配置加载
echo ""
echo "5. 测试配置加载..."

cd backend 2>/dev/null || {
    echo "❌ 无法切换到backend目录"
    exit 1
}

# 设置测试环境
export APP_ENV=prod

# 创建简单的配置测试
cat > config_test.go << 'EOF'
package main

import (
    "fmt"
    "os"
    
    "github.com/peizhen-system/backend/config"
)

func main() {
    if err := config.InitConfig(); err != nil {
        fmt.Printf("配置加载失败: %v\n", err)
        os.Exit(1)
    }
    
    cfg := config.GetGlobalConfig()
    if cfg == nil {
        fmt.Println("获取配置失败")
        os.Exit(1)
    }
    
    transfer := cfg.WeChat.Transfer
    
    // 检查关键字段
    if transfer.CertificateSerialNumber == "" {
        fmt.Println("❌ CertificateSerialNumber 为空")
        os.Exit(1)
    }
    
    if transfer.PrivateKeyPath == "" {
        fmt.Println("❌ PrivateKeyPath 为空")
        os.Exit(1)
    }
    
    fmt.Println("✅ 配置加载成功")
    fmt.Printf("CertificateSerialNumber: %s\n", transfer.CertificateSerialNumber)
    fmt.Printf("PrivateKeyPath: %s\n", transfer.PrivateKeyPath)
}
EOF

if go run config_test.go 2>/dev/null; then
    echo "✅ 配置加载测试通过"
else
    echo "❌ 配置加载测试失败，请检查配置和环境变量"
fi

# 清理测试文件
rm -f config_test.go

# 6. 重启建议
echo ""
echo "6. 服务重启建议..."

if pgrep -f "peizhen.*backend\|backend.*main" > /dev/null; then
    echo "✅ 检测到后端服务正在运行"
    echo ""
    echo "🔄 为了应用配置更改，建议重启后端服务："
    echo "   sudo systemctl restart peizhen-backend"
    echo "   或者手动重启："
    echo "   pkill -f 'peizhen.*backend'"
    echo "   nohup ./bin/peizhen > logs/app.log 2>&1 &"
else
    echo "⚠️  后端服务未运行，可以直接启动"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "📋 已完成的修复："
echo "1. ✅ 移除配置文件中硬编码的敏感数据"
echo "2. ✅ 验证环境变量设置"
echo "3. ✅ 检查私钥文件权限"
echo "4. ✅ 测试配置加载功能"
echo ""
echo "🔧 如果问题仍然存在，请："
echo "1. 确保重启了后端服务"
echo "2. 检查日志文件: tail -f backend/logs/app.prod.log"
echo "3. 验证所有环境变量都正确设置"
echo "4. 确认私钥文件路径和内容正确"