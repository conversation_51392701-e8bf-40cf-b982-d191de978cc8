#!/bin/bash

# 修复微信平台证书配置问题
# 解决自动证书模式与手动证书模式的配置冲突

echo "🔧 修复微信平台证书配置问题"
echo "=================================="

# 1. 问题分析
echo -e "\n1. 问题分析："
echo "   发现配置中同时存在自动证书模式和手动证书模式的配置"
echo "   这可能导致微信SDK无法正确选择证书获取方式"

# 2. 检查当前配置
echo -e "\n2. 检查当前配置："
echo "   WECHAT_PUBLIC_KEY_PATH: $(grep WECHAT_PUBLIC_KEY_PATH production.env | cut -d'=' -f2)"
echo "   WECHAT_PUBLIC_KEY_ID: $(grep WECHAT_PUBLIC_KEY_ID production.env | cut -d'=' -f2)"
echo "   WECHAT_TRANSFER_CLIENT_TYPE: $(grep WECHAT_TRANSFER_CLIENT_TYPE production.env | cut -d'=' -f2)"

# 3. 备份配置
echo -e "\n3. 备份配置："
if [ -f "production.env" ]; then
    cp production.env "production.env.backup.$(date +%Y%m%d_%H%M%S)"
    echo "   ✅ 已备份 production.env"
fi

# 4. 修复配置 - 使用纯自动证书模式
echo -e "\n4. 修复配置 - 使用纯自动证书模式："

# 注释掉或删除手动证书模式的配置
echo "   🔧 禁用手动平台证书配置"

# 注释掉平台证书相关配置（自动证书模式不需要）
sed -i 's/^WECHAT_PUBLIC_KEY_PATH=/#WECHAT_PUBLIC_KEY_PATH=/' production.env
sed -i 's/^WECHAT_PUBLIC_KEY_ID=/#WECHAT_PUBLIC_KEY_ID=/' production.env

# 确保使用官方客户端类型
sed -i 's/WECHAT_TRANSFER_CLIENT_TYPE=.*/WECHAT_TRANSFER_CLIENT_TYPE=official/' production.env

# 添加明确的自动证书模式配置
echo "   🔧 添加自动证书模式配置"

# 检查是否已存在，如果不存在则添加
if ! grep -q "APP_WECHAT_USE_AUTO_CERT_MODE" production.env; then
    echo "APP_WECHAT_USE_AUTO_CERT_MODE=true" >> production.env
fi

# 确保不使用公钥模式
sed -i 's/APP_WECHAT_USE_PUBLIC_KEY_MODE=.*/APP_WECHAT_USE_PUBLIC_KEY_MODE=false/' production.env

# 5. 验证修复结果
echo -e "\n5. 验证修复结果："
echo "   WECHAT_TRANSFER_CLIENT_TYPE: $(grep WECHAT_TRANSFER_CLIENT_TYPE production.env | cut -d'=' -f2)"
echo "   APP_WECHAT_USE_AUTO_CERT_MODE: $(grep APP_WECHAT_USE_AUTO_CERT_MODE production.env | cut -d'=' -f2)"
echo "   APP_WECHAT_USE_PUBLIC_KEY_MODE: $(grep APP_WECHAT_USE_PUBLIC_KEY_MODE production.env | cut -d'=' -f2)"

# 检查被注释的配置
echo -e "\n   已禁用的配置："
grep "^#WECHAT_PUBLIC_KEY" production.env || echo "   (无需禁用的配置)"

# 6. 检查必要的配置
echo -e "\n6. 检查必要的配置："
required_vars=(
    "WECHAT_MCH_ID"
    "WECHAT_SERIAL_NO"
    "WECHAT_API_V3_KEY"
    "WECHAT_PRIVATE_KEY_PATH"
    "WECHAT_TRANSFER_CLIENT_TYPE"
)

for var in "${required_vars[@]}"; do
    value=$(grep "^$var=" production.env | cut -d'=' -f2)
    if [ -n "$value" ]; then
        echo "   ✅ $var: $value"
    else
        echo "   ❌ $var: 未设置"
    fi
done

# 7. 创建简化的测试脚本
echo -e "\n7. 创建简化的测试脚本："
cat > test_wechat_auto_cert_mode.go << 'EOF'
package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/certificates"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

func main() {
	fmt.Println("🧪 测试微信自动证书模式")
	fmt.Println("========================")

	// 从环境变量读取配置
	mchID := os.Getenv("WECHAT_MCH_ID")
	serialNo := os.Getenv("WECHAT_SERIAL_NO")
	apiV3Key := os.Getenv("WECHAT_API_V3_KEY")
	privateKeyPath := os.Getenv("WECHAT_PRIVATE_KEY_PATH")

	fmt.Printf("配置信息:\n")
	fmt.Printf("  商户号: %s\n", mchID)
	fmt.Printf("  证书序列号: %s\n", serialNo)
	fmt.Printf("  私钥路径: %s\n", privateKeyPath)
	fmt.Printf("  APIv3密钥: %s\n", maskString(apiV3Key))

	// 检查必要参数
	if mchID == "" || serialNo == "" || apiV3Key == "" || privateKeyPath == "" {
		log.Fatal("❌ 缺少必要的环境变量")
	}

	// 检查私钥文件
	if _, err := os.Stat(privateKeyPath); err != nil {
		log.Fatalf("❌ 私钥文件不存在或无法访问: %v", err)
	}
	fmt.Printf("  ✅ 私钥文件存在: %s\n", privateKeyPath)

	// 加载私钥
	fmt.Println("\n加载商户私钥...")
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(privateKeyPath)
	if err != nil {
		log.Fatalf("❌ 加载商户私钥失败: %v", err)
	}
	fmt.Println("  ✅ 商户私钥加载成功")

	// 初始化客户端 - 使用纯自动证书模式
	fmt.Println("\n初始化微信支付客户端（自动证书模式）...")
	ctx := context.Background()
	
	// 只使用自动证书模式，不混合其他配置
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, serialNo, mchPrivateKey, apiV3Key),
	}

	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		fmt.Printf("❌ 初始化微信支付客户端失败: %v\n", err)
		
		// 详细错误分析
		errStr := err.Error()
		fmt.Println("\n错误分析:")
		if contains(errStr, "RESOURCE_NOT_EXISTS") {
			fmt.Println("  🔍 平台证书不存在 - 可能的原因:")
			fmt.Println("    1. 商户平台API权限未开通")
			fmt.Println("    2. 商户证书未上传到微信商户平台")
			fmt.Println("    3. APIv3密钥配置错误")
			fmt.Println("    4. 证书序列号与商户平台不匹配")
		} else if contains(errStr, "certificate") {
			fmt.Println("  🔍 证书相关错误 - 可能的原因:")
			fmt.Println("    1. 商户私钥格式错误")
			fmt.Println("    2. 商户私钥与商户平台证书不匹配")
		} else {
			fmt.Println("  🔍 其他错误，请检查网络连接和配置")
		}
		return
	}
	fmt.Println("  ✅ 微信支付客户端初始化成功")

	// 尝试下载平台证书
	fmt.Println("\n尝试下载平台证书...")
	svc := certificates.CertificatesApiService{Client: client}
	resp, result, err := svc.DownloadCertificates(ctx)

	if err != nil {
		fmt.Printf("❌ 下载平台证书失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 平台证书下载成功！\n")
	fmt.Printf("  HTTP状态码: %d\n", result.Response.StatusCode)
	fmt.Printf("  证书数量: %d\n", len(resp.Data))
	
	// 显示证书信息
	for i, cert := range resp.Data {
		fmt.Printf("  证书 %d:\n", i+1)
		fmt.Printf("    序列号: %s\n", *cert.SerialNo)
		fmt.Printf("    有效期: %s - %s\n", *cert.EffectiveTime, *cert.ExpireTime)
	}
	
	fmt.Println("\n🎉 自动证书模式工作正常！")
}

func maskString(s string) string {
	if len(s) <= 8 {
		return "****"
	}
	return s[:4] + "****" + s[len(s)-4:]
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr || 
		   len(s) >= len(substr) && s[:len(substr)] == substr ||
		   len(s) > len(substr) && s[len(s)/2-len(substr)/2:len(s)/2+len(substr)/2] == substr
}
EOF

echo "   ✅ 已创建测试脚本: test_wechat_auto_cert_mode.go"

# 8. 提供操作建议
echo -e "\n8. 下一步操作建议："
echo "=================================="
echo "1. 重启服务使配置生效："
echo "   sudo systemctl restart peizhen-backend"
echo "   sudo systemctl restart peizhen-admin"
echo ""
echo "2. 运行测试脚本："
echo "   source production.env"
echo "   go run test_wechat_auto_cert_mode.go"
echo ""
echo "3. 如果测试成功，说明问题已解决"
echo "4. 如果仍然失败，可能需要检查微信商户平台的API权限设置"

echo -e "\n🎯 修复完成！"
echo "=================================="
echo "主要修复内容："
echo "1. ✅ 禁用了手动平台证书配置，避免配置冲突"
echo "2. ✅ 确保使用纯自动证书模式"
echo "3. ✅ 保留了必要的商户私钥配置"
echo "4. ✅ 创建了专门的测试脚本"
echo ""
echo "关键变化："
echo "- 注释了 WECHAT_PUBLIC_KEY_PATH 和 WECHAT_PUBLIC_KEY_ID"
echo "- 启用了 APP_WECHAT_USE_AUTO_CERT_MODE=true"
echo "- 禁用了 APP_WECHAT_USE_PUBLIC_KEY_MODE=false"