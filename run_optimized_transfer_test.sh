#!/bin/bash

# 运行优化的微信转账API测试
# 基于官方文档格式进行测试

echo "🔧 优化的微信转账API测试"
echo "========================="
echo "基于官方文档分析，测试不同的参数组合"

# 检查环境
if [ ! -f "go.work" ] && [ ! -f "go.mod" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 加载环境配置
echo ""
echo "📋 加载环境配置..."
if [ -f "production.env" ]; then
    source production.env
    echo "   ✅ 已加载 production.env"
else
    echo "   ⚠️  未找到 production.env，使用环境变量"
fi

# 显示关键发现
echo ""
echo "🔍 关键发现（基于官方文档）:"
echo "   1. 官方示例使用场景ID 1000"
echo "   2. 用户名可能需要加密处理"
echo "   3. 批次号格式: plfk + 时间戳"
echo "   4. 明细号格式: x23zy + 时间戳"

# 检查必要的环境变量
required_vars=(
    "WECHAT_MCH_ID"
    "WECHAT_APP_ID" 
    "WECHAT_API_V3_KEY"
    "WECHAT_CERTIFICATE_SERIAL_NUMBER"
    "WECHAT_PRIVATE_KEY_PATH"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "❌ 缺少必要的环境变量:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo ""
    echo "💡 请设置这些环境变量后重新运行"
    exit 1
fi

echo ""
echo "✅ 环境变量检查通过"

# 显示配置信息
echo ""
echo "🔧 当前配置:"
echo "   商户号: $WECHAT_MCH_ID"
echo "   应用ID: $WECHAT_APP_ID"
echo "   私钥路径: $WECHAT_PRIVATE_KEY_PATH"
echo "   证书序列号: $WECHAT_CERTIFICATE_SERIAL_NUMBER"

# 编译测试程序
echo ""
echo "🔨 编译优化测试程序..."
if ! go build -o optimized_transfer_test test_optimized_transfer_api.go; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 运行测试
echo ""
echo "🚀 开始优化测试..."
echo "=================================="

# 运行测试并捕获结果
if ./optimized_transfer_test; then
    test_result="success"
    echo ""
    echo "✅ 优化测试完成"
else
    test_result="failed"
    echo ""
    echo "❌ 优化测试发现问题"
fi

# 分析结果
echo ""
echo "📊 测试结果分析:"
echo "=================================="

if [ "$test_result" = "success" ]; then
    echo "🎉 测试成功！找到了可用的参数组合"
    echo ""
    echo "📋 成功的关键因素:"
    echo "   - 使用了正确的场景ID"
    echo "   - 用户名格式正确"
    echo "   - 批次号格式符合要求"
    echo "   - 商户平台权限配置正确"
    echo ""
    echo "🔧 下一步:"
    echo "   1. 将成功的参数应用到生产代码"
    echo "   2. 更新转账客户端实现"
    echo "   3. 进行完整的功能测试"
else
    echo "🔍 测试失败分析:"
    echo ""
    echo "可能的原因："
    echo "   1. 🔴 商户平台配置问题（最可能）"
    echo "      - 转账功能未正确开通"
    echo "      - 场景权限未配置"
    echo "      - 需要重新申请或审核"
    echo ""
    echo "   2. 🟡 用户名加密问题"
    echo "      - 可能需要使用微信支付平台公钥加密"
    echo "      - 明文用户名不被接受"
    echo ""
    echo "   3. 🟢 参数格式问题"
    echo "      - 批次号格式需要调整"
    echo "      - 其他参数需要优化"
    echo ""
    echo "📞 建议的解决步骤:"
    echo "   1. 登录微信支付商户平台检查配置"
    echo "   2. 确认转账功能和场景权限状态"
    echo "   3. 联系微信支付技术支持"
    echo "   4. 提供商户号: $WECHAT_MCH_ID"
fi

# 清理临时文件
rm -f optimized_transfer_test

echo ""
echo "📋 重要提醒:"
echo "=================================="
echo "基于官方文档分析，用户名加密可能是关键因素："
echo ""
echo "官方示例用户名:"
echo "757b340b45ebef5467rter35gf464344v3542sdf4t6re4tb4f54ty45t4yyry45"
echo ""
echo "这明显是加密后的字符串，而我们使用的是明文。"
echo "如果测试仍然失败，用户名加密可能是解决NO_AUTH的关键！"

echo ""
echo "🎯 优化测试完成！"