#!/bin/bash

# 微信转账生产环境配置验证脚本
# 用于验证重试转账问题修复

echo "=== 微信转账生产环境配置验证 ==="

# 检查必要的环境变量
echo "1. 检查必要的环境变量..."

required_env_vars=(
    "WECHAT_APP_ID"
    "WECHAT_MCH_ID" 
    "WECHAT_API_V3_KEY"
    "WECHAT_CERT_SERIAL_NUMBER"
    "WECHAT_TRANSFER_PRIVATE_KEY_PATH"
)

missing_vars=()
for var in "${required_env_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
        echo "❌ $var: 未设置"
    else
        # 对敏感信息进行脱敏显示
        case $var in
            *KEY*|*SECRET*)
                echo "✅ $var: ${!var:0:8}****"
                ;;
            *)
                echo "✅ $var: ${!var}"
                ;;
        esac
    fi
done

if [ ${#missing_vars[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  缺少以下必要的环境变量："
    for var in "${missing_vars[@]}"; do
        echo "   - $var"
    done
    echo ""
    echo "请设置这些环境变量后重新运行验证。"
    exit 1
fi

echo ""
echo "2. 检查证书文件..."

# 检查私钥文件路径
private_key_path="${WECHAT_TRANSFER_PRIVATE_KEY_PATH:-${WECHAT_PRIVATE_KEY_PATH:-/etc/peizhen/certs/wechat/apiclient_key.pem}}"
echo "私钥文件路径: $private_key_path"

if [ -f "$private_key_path" ]; then
    echo "✅ 私钥文件存在"
    
    # 检查文件权限
    file_perms=$(stat -c "%a" "$private_key_path" 2>/dev/null || stat -f "%A" "$private_key_path" 2>/dev/null)
    if [ "$file_perms" = "600" ] || [ "$file_perms" = "400" ]; then
        echo "✅ 私钥文件权限正确: $file_perms"
    else
        echo "⚠️  私钥文件权限可能不安全: $file_perms (建议设置为600或400)"
    fi
    
    # 检查文件内容格式
    if head -1 "$private_key_path" | grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY"; then
        echo "✅ 私钥文件格式正确"
    else
        echo "❌ 私钥文件格式可能不正确"
    fi
else
    echo "❌ 私钥文件不存在: $private_key_path"
    echo ""
    echo "请确保私钥文件存在并且路径正确。"
    exit 1
fi

echo ""
echo "3. 检查配置文件..."

config_file="backend/config/conf/config.prod.yaml"
if [ -f "$config_file" ]; then
    echo "✅ 生产配置文件存在: $config_file"
    
    # 检查配置文件中是否有硬编码的敏感数据
    echo "检查配置文件中的敏感数据..."
    
    # 检查是否有未使用环境变量的配置
    if grep -q ": [^$].*[a-zA-Z0-9]{20,}" "$config_file" | grep -v "# " | grep -v "path:" | grep -v "url:" | grep -v "domain:"; then
        echo "⚠️  配置文件中可能包含硬编码的敏感数据："
        grep ": [^$].*[a-zA-Z0-9]{20,}" "$config_file" | grep -v "# " | grep -v "path:" | grep -v "url:" | grep -v "domain:" | head -5
    else
        echo "✅ 配置文件中没有发现硬编码的敏感数据"
    fi
    
    # 检查微信转账相关配置
    echo "检查微信转账配置..."
    if grep -q "certificate_serial_number.*\${WECHAT_CERT_SERIAL_NUMBER}" "$config_file"; then
        echo "✅ 证书序列号使用环境变量"
    else
        echo "❌ 证书序列号配置可能有问题"
    fi
    
    if grep -q "private_key_path.*\${WECHAT.*PRIVATE_KEY_PATH" "$config_file"; then
        echo "✅ 私钥路径使用环境变量"
    else
        echo "❌ 私钥路径配置可能有问题"
    fi
    
else
    echo "❌ 生产配置文件不存在: $config_file"
    exit 1
fi

echo ""
echo "4. 测试配置加载..."

# 切换到backend目录并测试配置加载
cd backend 2>/dev/null || {
    echo "❌ 无法切换到backend目录"
    exit 1
}

# 创建临时测试程序
cat > test_config_load.go << 'EOF'
package main

import (
    "fmt"
    "log"
    "os"
    
    "github.com/peizhen-system/backend/config"
)

func main() {
    // 设置生产环境
    os.Setenv("APP_ENV", "prod")
    
    // 初始化配置
    if err := config.InitConfig(); err != nil {
        log.Fatalf("配置加载失败: %v", err)
    }
    
    cfg := config.GetGlobalConfig()
    if cfg == nil {
        log.Fatal("获取配置失败")
    }
    
    // 检查微信转账配置
    transfer := cfg.WeChat.Transfer
    
    fmt.Printf("微信转账配置加载结果:\n")
    fmt.Printf("- AppID: %s\n", transfer.AppID)
    fmt.Printf("- MchID: %s\n", transfer.MchID)
    fmt.Printf("- CertificateSerialNumber: %s\n", transfer.CertificateSerialNumber)
    fmt.Printf("- PrivateKeyPath: %s\n", transfer.PrivateKeyPath)
    fmt.Printf("- ClientType: %s\n", transfer.ClientType)
    fmt.Printf("- Enabled: %t\n", transfer.Enabled)
    fmt.Printf("- MockMode: %t\n", transfer.MockMode)
    
    // 验证关键字段是否正确加载
    if transfer.AppID == "" || transfer.MchID == "" || transfer.CertificateSerialNumber == "" || transfer.PrivateKeyPath == "" {
        fmt.Println("❌ 关键配置字段缺失")
        os.Exit(1)
    }
    
    fmt.Println("✅ 配置加载成功")
}
EOF

# 编译并运行测试
if go mod tidy && go run test_config_load.go; then
    echo "✅ 配置加载测试通过"
else
    echo "❌ 配置加载测试失败"
    rm -f test_config_load.go
    exit 1
fi

# 清理临时文件
rm -f test_config_load.go

echo ""
echo "5. 检查服务状态..."

# 检查后端服务是否运行
if pgrep -f "peizhen.*backend\|backend.*main" > /dev/null; then
    echo "✅ 后端服务正在运行"
    
    # 建议重启服务以应用配置更改
    echo ""
    echo "📝 建议操作："
    echo "   由于配置已更新，建议重启后端服务以确保新配置生效："
    echo "   sudo systemctl restart peizhen-backend"
    echo "   或者"
    echo "   pkill -f 'peizhen.*backend' && nohup ./backend/bin/peizhen > logs/app.log 2>&1 &"
    
else
    echo "⚠️  后端服务未运行"
    echo "   可以启动服务: sudo systemctl start peizhen-backend"
fi

echo ""
echo "=== 验证完成 ==="
echo ""
echo "📋 修复总结："
echo "1. ✅ 移除了配置文件中硬编码的证书序列号默认值"
echo "2. ✅ 添加了缺失的环境变量绑定 (certificate_serial_number, private_key_path)"
echo "3. ✅ 修复了 expandEnvironmentVariables 函数中的字段处理"
echo "4. ✅ 验证了配置文件使用环境变量格式"
echo ""
echo "🔧 如果重试转账仍然失败，请检查："
echo "   - 确保所有必要的环境变量都已正确设置"
echo "   - 确保私钥文件路径正确且文件可读"
echo "   - 重启后端服务以应用配置更改"
echo "   - 检查日志文件获取更详细的错误信息"